@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;
@use '_contratto' as *;

// Amministrazione

.popover-help--flex {
  display: flex;
}

@include media('screen', '<#{emConverter(790)}') {
  .gtx-card__row--admin.gtx-card__row--telefonate-smart .gtx-card__row__title {
    width: 100%;
    float: none;
  }
}

@include media('screen', '>#{emConverter(850)}') {
  .gtx-card__row--sito-agenzia {
    .data-item + .data-item {
      margin-left: 5rem;
    }
  }
}

@include media('screen', '>medium', '<#{emConverter(850)}') {
  .gtx-card__row--sito-agenzia,
  .gtx-card__row--telefonate-smart {
    .data-item + .data-item {
      margin-left: 1.7rem;
    }
  }
}

@include media('screen', '>#{emConverter(850)}', '<#{breakpoint(md)}') {
  .gtx-card__row--telefonate-smart {
    .data-item + .data-item {
      margin-left: 3rem;
    }
  }
}

@include media('screen', '>#{breakpoint(md)}', '<#{emConverter(1100)}') {
  .gtx-card__row--telefonate-smart {
    .data-item + .data-item {
      margin-left: 1.7rem;
    }
  }
}

@include media('screen', '>#{emConverter(1100)}') {
  .gtx-card__row--telefonate-smart {
    .data-item + .data-item {
      margin-left: 5rem;
    }
  }
}

.modal-body {
  input.white-readonly {
    background-color: color(background-main);
  }
}

.example-pic {
  max-width: 100%;
}

.table-adm-responsive {
  @include media('<#{breakpoint(md)}') {
    display: block;
    background-color: color(background-alt);

    .gx-table__body {
      background-color: transparent;
    }

    .gx-table__cell {
      @include typography(body-small);
    }

    thead {
      display: none;
    }

    tbody {
      display: flex;
      flex-wrap: wrap;
    }

    tr {
      display: flex;
      flex-wrap: wrap;
      width: calc(50% - #{$gx-unit-size * 4});
      background-color: color(background-main);
      margin: space(md);
      padding: space(md);
      border: 0.1rem solid color(border-main);

      &:last-child {
        border-bottom: 0.1rem solid color(border-main);
      }

      &:hover {
        background-color: color(background-main) !important;
      }
    }

    td {
      display: block;
      width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      border: none !important;
      font-size: 1.4rem;
    }

    .label-cell {
      width: calc(50% - #{space(sm)}) !important;
      margin-bottom: space(md) !important;

      &::before {
        display: block;
        color: color(content-low);
        text-transform: uppercase;
        font-size: 1.2rem;
        margin-bottom: space(xs);
        content: attr(data-title);
      }
    }

    .gx-table__field--actions {
      .gx-button {
        width: 100%;
      }
    }
  }

  @include media('>=#{breakpoint(md)}') {
    .td-actions {
      .gx-button {
        padding: 0;
      }
    }
  }

  @include media('<#{breakpoint(sm)}') {
    tr {
      width: 100%;
      margin-right: 0;
      margin-left: 0;
      border-right: none;
      border-left: none;
    }
  }
}

@include media('<#{breakpoint(md)}') {
  .administration_invoices {
    .gx-container {
      .gx-card-list {
        margin-right: -(space(md));
        margin-left: -(space(md));
      }
    }
  }

  .invoice-status {
    display: flex;
    flex-direction: column;
    gap: space(sm);

    &__item {
      align-self: flex-start;
    }
  }
}

.invoice-landing-page {
  max-width: 90%;
  padding-top: space(3xl);
  margin: 0 auto;
  text-align: center;

  h1 {
    @include typography(diplay-2);
    margin-bottom: space(md);
  }
}
