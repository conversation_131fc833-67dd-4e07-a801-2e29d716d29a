// ==========================================================================
// Table - Components/Table New
// ==========================================================================

@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$gx-table-new: 'gx-table-new';
$gx-table-new-highlight: color(background-selected);
$gx-height-th: 4rem;
$gx-min-width-cell: 8rem;
$gx-max-width-cell: 12rem;
$gx-max-width-cell-main: 40rem;
$gx-min-width-checkCell: 8.4rem;
$gx-min-width-actionsCell: 4.8rem;

.#{$gx-table-new} {
  width: 100%;
  border-collapse: separate;

  &Wrap {
    overflow-x: auto;

    &--checked {
      .#{$gx-table-new} {
        margin-top: -$gx-height-th;

        &OverHead {
          display: flex;
        }
      }
    }
  }

  &OverHead {
    display: none;
    height: $gx-height-th;
    position: sticky;
    top: 0;
    left: 0;
    @include z-index(base, 3);
    width: 100%;
    align-items: center;
    padding: 0 space(md);
    border-radius: radius(sm);
    background-color: $gx-table-new-highlight;
    border: 0.1rem solid color(border-action);

    &.is-sticky {
      position: fixed;
      top: $gx-new-menu-header-height;
      left: $gx-new-menu-width + space(xl);
      width: calc(100% - #{$gx-new-menu-width + ($gx-unit-size * 8)});
      @include z-index(base, 4);

      .has-menu-fixed & {
        // 26rem is fixed menu width size
        width: calc(100% - #{$gx-new-menu-width + ($gx-unit-size * 8) + 26rem});
        left: $gx-new-menu-width + space(xl) + 26rem;
      }

      @include media('screen', '>=#{breakpoint(xl)}') {
        left: ($gx-new-menu-width + space(xl));
        width: calc(100% - #{$gx-side-menu-width + ($gx-unit-size * 8)});

        .has-menu-fixed & {
          left: ($gx-new-menu-width + 26rem + space(xl));
        }
      }
    }

    &__info {
      margin-left: space(lg);
      margin-right: space(md);
    }

    .gx-button + .gx-button {
      margin-left: 0;
    }
  }

  &__order {
    cursor: pointer;

    .gx-order-arrow {
      margin-left: space(xs);
      transition: transform 0.3s ease-in-out;
    }

    &--ascending {
      .gx-order-arrow {
        transform: rotate(0deg);
      }
    }

    &--descending {
      .gx-order-arrow {
        transform: rotate(180deg);
      }
    }

    .gx-icon {
      @include icon-size(sm);
      flex-shrink: 0;
    }
  }

  &__cell {
    padding: space(sm) space(md);
    min-width: $gx-min-width-cell;
    white-space: nowrap;
    border-bottom: 0.1rem solid color(border-main);

    .gx-spin {
      font-size: ($gx-unit-size * 2);
    }

    > div {
      display: flex;
      align-items: center;
    }

    &Info {
      width: 3.2rem;
      flex-shrink: 0;

      .gx-icon {
        @include icon-size(xs);
      }
    }

    &--check {
      position: sticky;
      left: 0;
      @include z-index(base, 2);
      min-width: $gx-min-width-checkCell;
      padding-right: space(xs);
      background-color: color(background-main);

      .gx-checkbox__control {
        margin-right: space(xs);
      }

      .gx-button {
        margin-left: 0;
      }
    }

    &--main {
      position: sticky;
      left: 0;
      @include z-index(base, 2);
      background-color: color(background-main);
      white-space: inherit;
      max-width: $gx-max-width-cell-main;

      .#{$gx-table-new}__cell--check + & {
        left: $gx-min-width-checkCell;
      }

      &::after {
        content: '';
        display: block;
        width: space(md);
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
        background: transparent;
        box-shadow: 0.4rem 0 0.4rem rgba(#000000, 0.05);
        transition: box-shadow 0.3s ease-in-out;

        .start-scroll & {
          box-shadow: 0.4rem 0 0.4rem transparent;
        }
      }
    }

    &--maxWidth {
      max-width: $gx-max-width-cell;

      * {
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    &--actions,
    &--upgrade {
      position: relative;
    }

    &--actions {
      position: sticky;
      @include z-index(base, 1);
      right: 0;
      min-width: $gx-min-width-actionsCell;
      background-color: color(background-main);

      &.is-open {
        @include z-index(base, 5);
      }

      &::before {
        content: '';
        display: block;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        box-shadow: -0.4rem 0 0.8rem rgba(#000000, 0.05);
        transition: box-shadow 0.3s ease-in-out;

        .end-scroll & {
          box-shadow: 0.4rem 0 0.4rem transparent;
        }
      }
    }

    &--with--fixedCellAction {
      display: flex;
      flex-direction: column;
    }
  }

  &__body {
    @include typography(body-small);

    .#{$gx-table-new}__cell {
      padding-top: space(lg);
      padding-bottom: space(lg);
    }
  }

  &__actions {
    position: absolute;
    right: ($gx-unit-size / 2);
    display: flex;
    align-items: center;
    height: 100%;

    > * {
      display: none;

      &:last-child {
        display: flex;
      }
    }

    &Wrap {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      width: ($gx-unit-size * 5 + $gx-unit-size / 2);
      height: calc(100% - 0.1rem);
    }

    .gx-button + .gx-button {
      margin-left: 0;
    }
  }

  &__head {
    @include typography(body-small);

    &.is-sticky {
      position: fixed;
      top: $gx-new-menu-header-height;
      overflow: hidden;
      @include z-index(base, 4);

      .has-menu-fixed & {
        left: $gx-new-menu-width + 26rem;
      }
    }

    th {
      height: $gx-height-th;
      background-color: color(background-alt);
      color: color(content-medium);
      border-bottom: none;

      &:first-child,
      &:last-child {
        position: relative;
        background-color: color(background-alt);

        &.#{$gx-table-new}__cell--check {
          position: sticky;
          background-color: color(background-alt);

          .#{$gx-table-new}__headFirst {
            padding-left: 0;
          }
        }

        &.#{$gx-table-new}__cell--main {
          position: sticky;
          background-color: color(background-alt);
        }

        .#{$gx-table-new}__headFirst {
          background-color: color(background-alt);
        }

        .main > div {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: color(background-main);
        }
      }

      .gx-badge {
        margin-left: space(xs);
      }
    }

    &First,
    &Last {
      display: flex;
      align-items: center;
      width: 100%;
      height: 100%;
      padding: 0 space(md);
      background-color: color(background-alt);
    }

    &First {
      border-radius: radius(sm) 0 0 radius(sm);
    }

    &Last {
      border-radius: 0 radius(sm) radius(sm) 0;
    }
  }

  &__row {
    &:hover.no-main-action {
      cursor: default;
    }

    &:hover,
    &.is-selected {
      background-color: $gx-table-new-highlight;
      cursor: pointer;

      td {
        background-color: $gx-table-new-highlight;
      }
    }

    &.is-selected {
      .#{$gx-table-new}__cell {
        border-color: color(border-selected);
      }

      border-color: color(border-selected);

      &,
      &:hover {
        td {
          box-shadow: 0 -0.1rem 0 color(border-selected) inset;
        }
      }
    }

    &:hover {
      .#{$gx-table-new}__actions {
        background-color: $gx-table-new-highlight;
        @include z-index(base, 5);

        > * {
          display: flex;
        }

        &::before {
          content: '';
          position: absolute;
          left: 0;
          transform: translateX(-100%);
          width: 2rem;
          height: 100%;
          background: linear-gradient(
            90deg,
            rgba(color(background-main, true), 0) 0%,
            rgba(color(background-selected, true), 1) 100%
          );
        }

        &Wrap {
          overflow: inherit;
          width: auto;
        }
      }

      .#{$gx-table-new}__upgrade {
        display: block;
        cursor: pointer;
        @include z-index(base, 1);
      }
    }
  }

  &__upgrade {
    display: none;
    position: absolute;
    left: space(md);
    bottom: space(sm);
    text-transform: uppercase;
    font-weight: 600;
  }

  &__quality {
    display: inline-flex;
    align-items: center;
    white-space: nowrap;

    .gx-icon {
      margin-left: space(xs);
      flex-shrink: 0;
      @include icon-size(xs);
    }

    &--positive {
      .gx-icon {
        color: color(content-success);
      }
    }
  }

  &__index {
    display: inline-flex;
    align-items: center;
    white-space: nowrap;

    .gx-icon {
      @include icon-size(xs);
      margin-left: space(xs);
      color: color(content-info);
    }

    &--firstPage {
      display: flex;
    }
  }

  // fatta per riprendere i colori di quella di windows
  &__scrollbar {
    position: relative;
    left: 12rem;
    height: 1.7rem;
    background: #f1f1f1;

    &Bubble {
      position: absolute;
      top: 0.2rem;
      height: 1.3rem;
      background-color: #c1c1c1;
      cursor: grab;

      &:hover {
        background-color: #9d9d9d;
      }

      &:active {
        cursor: grabbing;
      }
    }

    &.is-sticky {
      position: fixed;
      left: 0;
      bottom: 0;
      @include z-index(base, 4);
      width: 100%;
      background-color: color(background-main);
      box-shadow: elevation(fixed-bottom);

      @include media('screen', '>=#{breakpoint(md)}') {
        left: #{$gx-new-menu-width};
        width: calc(100% - #{$gx-new-menu-width});

        .crm-section:has(.crm-sidebar--left.is-open) & {
          left: #{$gx-new-menu-width + 28rem};
          width: calc(100% - #{$gx-new-menu-width + 28rem});
        }
      }
    }

    .has-menu-fixed & {
      left: 38rem;

      &.is-sticky {
        width: calc(100% - 41.2rem);
      }
    }
  }

  &__fixedCellAction {
    margin-top: space(sm);
    flex-shrink: 0;

    &Wrap {
      display: flex;
    }

    & > span + * {
      margin-left: space(sm);
    }
  }

  // useful to have horizontally-aligned cells with fixedCellAction
  &__contentFixedHeight {
    display: flex;
    align-items: center;
    height: ($gx-unit-size * 3);
    margin-bottom: space(xs);
  }
}
