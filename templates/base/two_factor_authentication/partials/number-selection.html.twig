<div data-component="phone-number-select">
    <h1 class="auth2-box__title gx-title">{{ trans("a2f.number_selection.title", {}, gtxConstants.DOMAIN_CMS) }}</h1>
    <p class="auth2-box__text gx-subtitle">
        {{ trans("a2f.number_selection.text_select", {}, gtxConstants.DOMAIN_CMS) }}
    </p>

    <form data-role="form-phone-number-selection">
        
        <div class="gx-select gx-select--native">
            <select class="gx-select__nativeControl" data-component="phone-number">
                {% for mobileNumber in mobileNumbers %}
                    <option value="{{ mobileNumber.idContattoAgente }}" {% if mobileNumber.preferito %}selected="selected"{% endif %}>{{ mobileNumber.number }}</option>
                {% endfor %}
            </select>
            <svg class="gx-icon"xmlns="http://www.w3.org/2000/svg" id="arrow-down" viewBox="0 0 24 24" fill-rule="evenodd">
                    <path d="M18.53 8.97a.75.75 0 0 0-1.06 0L12 14.44 6.53 8.97a.75.75 0 0 0-1.06 1.06l6 6a.75.75 0 0 0 1.06 0l6-6a.75.75 0 0 0 0-1.06Z"/>
            </svg>
        </div>

        <div data-role="error-message" class="hidden">
            <div class="gx-helper gx-helper--error">
                <svg class="gx-icon gx-icon--negative" xmlns="http://www.w3.org/2000/svg" id="cross-circle" viewBox="0 0 24 24" fill-rule="evenodd">
  <path d="M7.22 7.22a.75.75 0 0 1 1.06 0L12 10.94l3.72-3.72a.75.75 0 1 1 1.06 1.06L13.06 12l3.72 3.72a.75.75 0 0 1-1.06 1.06L12 13.06l-3.72 3.72a.75.75 0 1 1-1.06-1.06L10.94 12 7.22 8.28a.75.75 0 0 1 0-1.06Z"/>
  <path d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm10-8.5a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17Z"/>
</svg>
                <span class="gx-helper__text">
                    
                </span>
            </div>
        </div>
        <button type="button" class="gx-button gx-button--accent gx-button--fullWidth" data-action="submit-phone-number">
            {{ trans("a2f.number_selection.send_sms", {}, gtxConstants.DOMAIN_CMS) }}
        </button>

    </form>

    <div class="a2f-footer">
        {{ trans("a2f.number_selection.if_cant_login", {}, gtxConstants.DOMAIN_CMS) }}
        <a class="auth2-box__help" href="javascript:void(0);" data-action="show-customer-service">{{ trans("a2f.insert_pin.request_assistance", {}, gtxConstants.DOMAIN_CMS) }}</a>
        {{ trans("label.or", {}, gtxConstants.DOMAIN_CMS) }}
        <a class="auth2-box__help" href="{{ path('getrix_app_logout') }}">{{ trans("label.back_to_login", {}, gtxConstants.DOMAIN_CMS)|lower }}</a>
    </div>
</div>
