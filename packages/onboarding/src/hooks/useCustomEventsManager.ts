import { useEffect } from "react"
import { useTourLogicManager } from "./useTourLogicManager"
import { useToursContext } from "./useToursContext"

export const ONBOARDING_CUSTOM_EVENTS = {
    RESCAN_REQUEST: 'onboardingRescanRequest',
    STEP_CHANGE: 'onboardingStepChange',
    ONBOARDING_MOUNT: 'onboardingMount',
    UPDATE_BOX_POSITION_REQUEST: 'onboradingUpdateBoxPositionReq'
}

export const useCustomEventsManager = (updateBoxPosition: React.MutableRefObject<() => void>, isDesktop: boolean) => {
    const { updateValidTourFromDom, stepIndex, showTour } = useToursContext()
    const { currentTour } = useTourLogicManager()

    useEffect(() => {
        const updateBoxPositionRequest = () => {
            updateBoxPosition.current()
        }

        window.addEventListener(ONBOARDING_CUSTOM_EVENTS.UPDATE_BOX_POSITION_REQUEST, updateBoxPositionRequest)

        return () => {
            window.removeEventListener(ONBOARDING_CUSTOM_EVENTS.UPDATE_BOX_POSITION_REQUEST, updateBoxPositionRequest)
        }
    }, [])


    useEffect(() => {
        const rescanRequestHandler = () => {
            updateValidTourFromDom()
        }

        window.addEventListener(ONBOARDING_CUSTOM_EVENTS.RESCAN_REQUEST, rescanRequestHandler)

        return () => {
            window.removeEventListener(ONBOARDING_CUSTOM_EVENTS.RESCAN_REQUEST, rescanRequestHandler)
        }
    }, [updateValidTourFromDom])

    const stepChanged = () => new CustomEvent(
        ONBOARDING_CUSTOM_EVENTS.STEP_CHANGE,
        {
            detail: {
                stepIndex: stepIndex,
                tourName: currentTour?.name
            }
        })


    useEffect(() => {
        if (currentTour) {
            window.dispatchEvent(stepChanged());
        }
    }, [stepIndex, currentTour])


    const onboardingMount = () => new CustomEvent(
        ONBOARDING_CUSTOM_EVENTS.ONBOARDING_MOUNT,
        {
            detail: {
                stepIndex: stepIndex,
                tourName: currentTour?.name
            }
        })


    useEffect(() => {
        if (isDesktop && showTour && currentTour) {
            window.dispatchEvent(onboardingMount());
        }
    }, [isDesktop, showTour, currentTour])

}
