{% extends 'base/security_gtx/base.html.twig' %}

{% block inner_content %}
    {% include 'base/security_gtx/inner-header.html.twig' with {'customer': true} %}

    <div class="login-content" data-role="form-container" data-component="phone-number-insert">
        <form class="form-account" method="post">
            {% if message %}
                <div class="alertNew alertNew-{{ messageType }}">
                    <svg class="gx-icon">
                        <use xlink:href="{{ gtxConstants.ICONS_SVG_SPRITE_FILE_PATH }}#{% if messageType == 'error' %}cross{% else %}check{% endif %}"></use>
                    </svg>
                    <p>{{ message | raw }}</p>
                </div>
                <div class="form-account__footer clearfix">
                    <a href="{{ path('app_login') }}">{{ "label.back_to_login"|trans({}, gtxConstants.DOMAIN_CMS) }}</a>
                </div>
            {% else %}
                <h1>{{ trans("a2f.add_your_number.title", {}, gtxConstants.DOMAIN_CMS) }}</h1>
                <p>
                    {{ trans("a2f.add_your_number.text", {}, gtxConstants.DOMAIN_CMS) }}
                </p>

                <div class="riga">
                    {% include 'base/Vendor/getrix/common/phone-number-entity/views/phone-number-entity.html.twig' with
                        {
                            'componentName': 'phone-number',
                            'countryCallingCodes': countryCallingCodes,
                            'defaultShortCode': gtxConstants.DEFAULT_CONTACT_NUMBER_SHORT_CODE,
                            'field': {
                                'select': {'name': 'contactCallingCode'},
                                'input': {'name': 'contactNumber', 'placeholder': trans("a2f.input.placeholder", {}, gtxConstants.DOMAIN_CMS)}
                            },
                            'classes': {'input': 'input-lg form-control'}
                        }
                    %}

                    <div data-role="error-message" class="parsley-errors-list"></div>
                </div>

                <a href="{{ path('app_login') }}">{{ "label.back_to_login"|trans({}, gtxConstants.DOMAIN_CMS) }}</a>

                <div class="form-account__footer clearfix">
                    <button type="button" class="btn btn-secondary btn-block" data-action="submit-phone-number">
                        {{ trans("label.add_number", {}, gtxConstants.DOMAIN_CMS) }}
                        <i data-role="spinner" class="fa fa-spinner fa-spin hidden"></i>
                    </button>
                </div>
            {% endif %}
        </form>
    </div>

    {% include 'base/security_gtx/footer.html.twig' %}

{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ webpack_asset('base/set-phone-number') }}
{% endblock %}
