import { Routes } from '../../routes';
import {
  BaseHeaderConfig,
  HeaderConfigWithParams,
  HeaderConfiguration,
  SearchParamType,
} from '../../types';
import { getRouteMatchingByRouteKeys } from './getRouteMatchingByRouteKeys';

const removeTrailingSlash = (path: string): string => path.replace(/\/$/, '');

const isSearchParamType = (value: string): value is SearchParamType => {
  return value.startsWith('?');
};

const findRouteBySearchParams = (
  configuration: HeaderConfigWithParams,
  searchParams: URLSearchParams,
) => {
  const foundRoute = Object.keys(configuration).find((key) => {
    if (!isSearchParamType(key)) {
      throw new Error(`Invalid key ${key} in HeaderConfigWithParams`);
    }

    const configSearchParams = [...new URLSearchParams(key)];

    const configSearchParamsMatch = configSearchParams.every(([key, value]) => {
      const searchParamsValue = searchParams.get(key);
      return searchParamsValue === value;
    });

    const configSearchParamsMatchWithWildcard = configSearchParams.every(([key, value]) => {
      return value === '*';
    });

    return configSearchParamsMatch || configSearchParamsMatchWithWildcard;
  });

  return foundRoute;
};

export const getHeaderConfiguration = (
  headerConfiguration: HeaderConfiguration,
  url: URL,
): BaseHeaderConfig | null => {
  const isHome = url.pathname === '/';
  const { searchParams } = url;
  let cleanPath: Routes;

  if (isHome) {
    cleanPath = '/';
  } else {
    cleanPath = removeTrailingSlash(url.pathname) as Routes;
  }

  let configuration = headerConfiguration[cleanPath];

  if (!configuration) {
    const matchingRoute = getRouteMatchingByRouteKeys(Object.keys(headerConfiguration), cleanPath);

    if (!matchingRoute) {
      return null;
    }

    configuration = headerConfiguration[matchingRoute as Routes];

    if (!configuration) {
      return null;
    }
  }

  // If the route has a section, it means that it's a valid HeaderConfig
  if ('section' in configuration) {
    return configuration;
  } else {
    // Otherwise, it *could be* a HeaderConfigWithParams
    // spread "?" params over default params
    const { '?': defaultConfig, ...restConfiguration } = configuration;

    const routeFound = findRouteBySearchParams(restConfiguration, searchParams);

    if (!routeFound) {
      return defaultConfig || null;
    }

    if (routeFound && isSearchParamType(routeFound)) {
      // routeFound is a string like
      // @example '?multimedia=vt'
      return configuration[routeFound] || null;
    }

    return null;
  }
};
