import { createContext, useContext } from 'react';
import { GxNavigationFeatureFlags } from '../../types';

const GxNavigationFeatureFlagsContext = createContext<GxNavigationFeatureFlags | undefined>(
  undefined,
);

export const GxNavigationFeatureFlagsProvider = GxNavigationFeatureFlagsContext.Provider;

export const useGxNavigationFeatureFlagsContext = () => {
  const context = useContext(GxNavigationFeatureFlagsContext);

  if (!context) {
    throw new Error(
      'useGxNavigationFeatureFlagsContext must be used within a GxNavigationFeatureFlagsProvider',
    );
  }

  return context;
};
