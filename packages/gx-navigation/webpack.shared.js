const { WebpackManifestPlugin } = require('webpack-manifest-plugin');
const path = require('path');

const RemoveLicenseFilePlugin = require('./webpack-plugins/remove-license-file-plugin.js');
const {
  BabelfishLoaderWrapperPlugin,
} = require('../webpack-plugins/babelfish-loader-wrapper/dist/index.js');

module.exports = {
  entry: { 'gx-navigation': './src/index.ts' },
  module: {
    rules: [
      {
        test: /(\.ts|\.tsx)$/,
        exclude: /node_modules/,
        include: [path.join(__dirname, 'src')],
        use: {
          loader: 'ts-loader',
        },
      },
    ],
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
    alias: {
      src: path.join(__dirname, 'src'),
    },
  },
  externals: {
    window: 'window',
  },
  output: {
    filename: 'gx-navigation.[fullhash].js',
    // path: path.resolve(__dirname, 'public'), // In local
    path: path.resolve(__dirname, '../../public/bundles/gx-navigation'), // On the container
  },
  plugins: [
    new RemoveLicenseFilePlugin(),
    new BabelfishLoaderWrapperPlugin({
      project: 'gx-nav',
      entrypoint: 'gx-navigation',
      debug: false,
    }),
    new WebpackManifestPlugin({
      fileName: 'nav-manifest.json',
      publicPath: '/bundles/gx-navigation',
    }),
  ],
};
