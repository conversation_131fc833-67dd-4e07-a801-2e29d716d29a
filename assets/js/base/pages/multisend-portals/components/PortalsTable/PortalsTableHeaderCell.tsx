import { PROPERTY_IMAGE_PLACEHOLDER } from 'constants/property';
import { Image } from 'gtx-react/components';
import { ListField } from 'gtx-react/components/List';
import React from 'react';
import { getImageUrl } from '../../utils';
import { LinkedPortals } from './LinkedPortals';
import { Popover } from '@gx-design/popover';

export function PortalsTableHeaderCell({
    children,
    linkedPortals,
    publishMethod,
    imageId,
    portalName,
}: React.PropsWithChildren<{
    imageId: string;
    portalName: string;
    publishMethod: string;
    linkedPortals: React.ComponentProps<typeof LinkedPortals>['portals'];
}>) {
    return (
        <ListField className="portal-publish-all">
            <div className="portal-publish-all__action">
                <Popover
                    position="bottom"
                    title={portalName}
                    large={false}
                    onEdge={false}
                    content={
                        <>
                            {publishMethod}
                            <LinkedPortals portals={linkedPortals} />
                        </>
                    }
                >
                    <div className="logo-square">
                        <Image
                            src={getImageUrl(imageId)}
                            fallbackSrc={PROPERTY_IMAGE_PLACEHOLDER}
                        />
                    </div>
                </Popover>
                {children}
            </div>
        </ListField>
    );
}
