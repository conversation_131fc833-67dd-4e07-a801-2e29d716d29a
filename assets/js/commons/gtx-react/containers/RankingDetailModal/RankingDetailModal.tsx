import React, { Suspense, useState } from 'react';
import { Tabs, TabsItem } from '@gx-design/tabs';
import { Loader } from 'gtx-react/components';
import { useSuspenseQuery } from '@tanstack/react-query';
import { trans } from '@pepita-i18n/babelfish';
import { Alert } from '@gx-design/alert';
import { RankingDetailModalSuggestions } from './RankingDetailModalSuggestions';
import { RankingDetailModalScores } from './RankingDetailModalScores';
import { RankingDetailModalComparison } from './RankingDetailModalComparison';
import { ErrorBoundary } from 'react-error-boundary';
import { createRankingDetailQueryOptions } from 'lib/REST/requests/v2/lista-annunci/query-factory';

export const RankingDetailModal = ({ propertyId }: { propertyId: string }) => {
    return (
        <ErrorBoundary
            FallbackComponent={() => (
                <Alert style="error">
                    {trans('label.error_durin_operation')}
                </Alert>
            )}
        >
            <Suspense
                fallback={
                    <div className="gx-modal__bodyLoading">
                        <Loader
                            loading={true}
                            fixedOverlay={false}
                            centered={false}
                        />
                    </div>
                }
            >
                <RankingDetail propertyId={propertyId} />
            </Suspense>
        </ErrorBoundary>
    );
};

export type RankingDetailModalType = {
    propertyId: string;
};

const RankingDetail: React.FC<RankingDetailModalType> = ({
    propertyId,
}: RankingDetailModalType) => {
    const { data } = useSuspenseQuery({
        ...createRankingDetailQueryOptions({
            query: { adId: propertyId },
        }),
        select: (data) => data.data,
        refetchIntervalInBackground: false,
        refetchOnWindowFocus: false,
        refetchOnMount: false,
        staleTime: 600 * 1000,
    });

    const [rankingDetailModalTabs, setRankingDetailModalTabs] = useState([
        {
            id: 'suggestions',
            label: trans('label.suggestions'),
            active: true,
        },
        {
            id: 'score',
            label: trans('label.score'),
            active: false,
        },
        {
            id: 'compare',
            label: trans('label.compare'),
            active: false,
        },
    ]);

    const handleTabChange = (tabId: string) => {
        setRankingDetailModalTabs(
            rankingDetailModalTabs.map((tab) => ({
                ...tab,
                active: tab.id === tabId,
            }))
        );
    };

    return (
        <div
            className="property-ranking-details-modal"
            data-testid="ranking-detail-modal"
        >
            <Alert style="warning">{trans('ranking.reserved_info')}</Alert>
            <div className="property-ranking-details-modal__qualityIndicator">
                <span>
                    <strong>
                        {trans('label.quality')} {data.value}
                    </strong>{' '}
                    ({trans('label.entropy')} {data.entropy})
                </span>
            </div>
            <Tabs>
                {rankingDetailModalTabs.map((tab) => {
                    return (
                        <TabsItem
                            key={tab.id}
                            active={tab.active}
                            onClick={() => handleTabChange(tab.id)}
                            text={tab.label}
                        ></TabsItem>
                    );
                })}
            </Tabs>
            <div className="property-ranking-details-modal__body">
                {rankingDetailModalTabs.map((tab) => {
                    if (tab.active) {
                        switch (tab.id) {
                            case 'suggestions':
                                return (
                                    <RankingDetailModalSuggestions
                                        key="suggestions"
                                        tips={data.tips}
                                    />
                                );
                            case 'score':
                                return (
                                    <RankingDetailModalScores
                                        key="scores"
                                        scores={data.detail}
                                    />
                                );
                            case 'compare':
                                return (
                                    <RankingDetailModalComparison
                                        key="comparison"
                                        propertyId={propertyId}
                                    />
                                );
                        }
                    }
                })}
            </div>
        </div>
    );
};
