services:
  example:
    example:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  press:
    press:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  mls:
    search:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    contacts:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    bookmarks:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    collaborations:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  omi:
    lookup:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    evaluate:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  lookup:
    provider:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["immobiliare-lookup.staging.rete.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  immobiliare:
    autocomplete:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["immobiliare-autocomplete.staging.rete.farm", 80, "/server_staging.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  geography:
    geography:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  login:
    login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    getrix_login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  immobiliarepro:
    agenzie:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["immopro-agenzie-staging.srv.imm.mi.ns.farm", 80, "/server_staging.php"]
        #xdebug: on
        #xdebug_port: 80
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  fatture:
    getrix:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-agenzie.staging.rete.farm", 80, "/server_staging.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    immobiliarepro:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["immopro-agenzie-staging.srv.imm.mi.ns.farm", 80, "/server_staging.php"]
        #xdebug: on
        #xdebug_port: 80
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  getrix:
    agenti:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-agenzie.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    agenzie:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-agenzie.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    whitelabel_login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-whitelabel-login.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gestionale-login.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    estimates:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gestionale-valutazioni.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    getrix:
      annunci:
        socket:
          className: \Thrift\Transport\THttpClient
          args: ["getrix-annunci.staging.rete.farm", 80, "/server_staging.php"]
        transport:
          className: \Thrift\Transport\TBufferedTransport
          args: [1024, 1024]
        protocol:
          className: \Thrift\Protocol\TBinaryProtocolAccelerated
          args: []
      progetti:
        socket:
          className: \Thrift\Transport\THttpClient
          args: ["getrix-annunci.staging.rete.farm", 80, "/server_staging.php"]
        transport:
          className: \Thrift\Transport\TBufferedTransport
          args: [1024, 1024]
        protocol:
          className: \Thrift\Protocol\TBinaryProtocolAccelerated
          args: []
      acquisizione:
        socket:
          className: \Thrift\Transport\THttpClient
          args: ["getrix-acquisizione.staging.rete.farm", 80, "/server_staging.php"]
        transport:
          className: \Thrift\Transport\TBufferedTransport
          args: [1024, 1024]
        protocol:
          className: \Thrift\Protocol\TBinaryProtocolAccelerated
          args: []
      valutazioni:
        socket:
          className: \Thrift\Transport\THttpClient
          args: ["getrix-valutazioni.staging.rete.farm", 80, "/server_staging.php"]
        transport:
          className: \Thrift\Transport\TBufferedTransport
          args: [1024, 1024]
        protocol:
          className: \Thrift\Protocol\TBinaryProtocolAccelerated
          args: []

  survey:
    agent:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["staging.gestione.mutui.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    lead:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["staging.gestione.mutui.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["staging.gestione.mutui.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    report:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["report-be.staging.mutui.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  aste:
    search:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["staging.aste-be.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    aste:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["staging.aste-be.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  documenti:
    documenti:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  media:
    immagini:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["media-immagini.staging.rete.farm", 80, "/server_staging.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  trace:
    trace:
      socket:
        className: \Thrift\Transport\TSocket
        args: ["gtxpfbe.staging.rete.farm", 9090]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
        mux: false
