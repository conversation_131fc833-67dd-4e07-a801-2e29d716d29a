<?php

declare(strict_types=1);

namespace Tests\Unit\DataProvider\Suggestions\ApiGtx\DataMapper;

use App\DataProvider\Suggestions\ApiGtx\DataMapper\MetadataDataMapper;
use App\DataProvider\Suggestions\ApiGtx\DataMapper\SuggestionDataMapper;
use App\DataProvider\Suggestions\ApiGtx\DataMapper\SuggestionsDataMapper;
use App\Model\Suggestions\Suggestions;
use PHPUnit\Framework\TestCase;

class SuggestionsDataMapperTest extends TestCase
{
    private SuggestionsDataMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = new SuggestionsDataMapper(new SuggestionDataMapper(new MetadataDataMapper()));
    }

    public function testGetDestinationClass(): void
    {
        $reflection = new \ReflectionClass($this->mapper);
        $method = $reflection->getMethod('getDestinationClass');
        $method->setAccessible(true);

        $result = $method->invoke($this->mapper);

        $this->assertEquals(Suggestions::class, $result);
    }

    public function testMapWithCompleteData(): void
    {
        $inputData = [
            'ids' => [
                ['value' => '1', 'metadata' => []],
                ['value' => '2', 'metadata' => []],
            ],
            'cities' => [
                ['value' => 'Rome', 'metadata' => ['cityId' => 10, 'cityName' => 'Rome']],
                ['value' => 'Milan', 'metadata' => ['cityId' => 11, 'cityName' => 'Milan']],
            ],
            'codes' => [
                ['value' => 'C001', 'metadata' => []],
                ['value' => 'C002', 'metadata' => []],
            ],
            'address' => [
                ['value' => 'Via Roma', 'metadata' => ['cityId' => 10, 'cityName' => 'Rome']],
                ['value' => 'Via Milano', 'metadata' => ['cityId' => 10, 'cityName' => 'Rome']],
            ],
            'macroZones' => [
                ['value' => 'North', 'metadata' => ['macroZoneId' => 100]],
                ['value' => 'South', 'metadata' => ['macroZoneId' => 101]],
            ],
            'provinces' => [
                ['value' => 'Rome', 'metadata' => ['provinceId' => 'RM', 'regionId' => 'laz']],
                ['value' => 'Milan', 'metadata' => ['provinceId' => 'MI', 'regionId' => 'lom']],
            ],
            'regions' => [
                ['value' => 'Lazio', 'metadata' => ['regionId' => 'laz']],
                ['value' => 'Lombardia', 'metadata' => ['regionId' => 'lom']],
            ],
        ];

        $result = $this->mapper->map($inputData);

        $this->assertInstanceOf(Suggestions::class, $result);
        $this->assertCount(2, $result->getIds());
        $this->assertCount(2, $result->getCities());
        $this->assertCount(2, $result->getCodes());
        $this->assertCount(2, $result->getAddress());
        $this->assertCount(2, $result->getMacroZones());
        $this->assertCount(2, $result->getProvinces());
        $this->assertCount(2, $result->getRegions());
        $this->assertEquals('Rome', $result->getCities()[0]->getValue());
        $this->assertEquals('Milan', $result->getCities()[1]->getValue());
        $this->assertEquals('C001', $result->getCodes()[0]->getValue());
        $this->assertEquals('C002', $result->getCodes()[1]->getValue());
        $this->assertEquals('Via Roma', $result->getAddress()[0]->getValue());
        $this->assertEquals('Via Milano', $result->getAddress()[1]->getValue());
        $this->assertEquals('North', $result->getMacroZones()[0]->getValue());
        $this->assertEquals('South', $result->getMacroZones()[1]->getValue());
        $this->assertEquals('Rome', $result->getProvinces()[0]->getValue());
        $this->assertEquals('Milan', $result->getProvinces()[1]->getValue());
        $this->assertEquals('Lazio', $result->getRegions()[0]->getValue());
        $this->assertEquals('RM', $result->getProvinces()[0]->getMetadata()->getProvinceId());
    }

    public function testMapWithPartialData(): void
    {
        $inputData = [
            'ids' => [
                ['value' => '1', 'metadata' => []],
                ['value' => '2', 'metadata' => []],
            ],
            'cities' => [
                ['value' => 'Rome', 'metadata' => ['cityId' => 10, 'cityName' => 'Rome']],
                ['value' => 'Milan', 'metadata' => ['cityId' => 11, 'cityName' => 'Milan']],
            ],
        ];

        $result = $this->mapper->map($inputData);

        $this->assertInstanceOf(Suggestions::class, $result);
        $this->assertCount(2, $result->getIds());
        $this->assertCount(2, $result->getCities());
        $this->assertEmpty($result->getCodes());
        $this->assertEmpty($result->getAddress());
        $this->assertEmpty($result->getMacroZones());
        $this->assertEmpty($result->getProvinces());
        $this->assertEmpty($result->getRegions());
    }

    public function testMapWithEmptyArrays(): void
    {
        $inputData = [
            'ids' => [],
            'cities' => [],
            'codes' => [],
            'address' => [],
            'macroZones' => [],
            'provinces' => [],
            'regiones' => [],
        ];

        $result = $this->mapper->map($inputData);

        $this->assertInstanceOf(Suggestions::class, $result);
        $this->assertEmpty($result->getIds());
        $this->assertEmpty($result->getCities());
        $this->assertEmpty($result->getCodes());
        $this->assertEmpty($result->getAddress());
        $this->assertEmpty($result->getMacroZones());
        $this->assertEmpty($result->getProvinces());
        $this->assertEmpty($result->getRegions());
    }

    public function testMapWithNullArrays(): void
    {
        $inputData = [
            'ids' => null,
            'cities' => null,
            'codes' => null,
            'address' => null,
            'macroZones' => null,
            'provinces' => null,
            'regiones' => null,
        ];

        $result = $this->mapper->map($inputData);

        $this->assertInstanceOf(Suggestions::class, $result);
        $this->assertEmpty($result->getIds());
        $this->assertEmpty($result->getCities());
        $this->assertEmpty($result->getCodes());
        $this->assertEmpty($result->getAddress());
        $this->assertEmpty($result->getMacroZones());
        $this->assertEmpty($result->getProvinces());
        $this->assertEmpty($result->getRegions());
    }

    public function testMapWithNullData(): void
    {
        $result = $this->mapper->map(null);
        $this->assertNull($result);
    }

    public function testMapWithInvalidDataType(): void
    {
        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Object or array data-source expected');

        $this->mapper->map('invalid_string_data');
    }

    public function testMapWithEmptyData(): void
    {
        $inputData = [];

        $result = $this->mapper->map($inputData);

        $this->assertInstanceOf(Suggestions::class, $result);
        $this->assertEmpty($result->getIds());
        $this->assertEmpty($result->getCities());
        $this->assertEmpty($result->getCodes());
        $this->assertEmpty($result->getAddress());
        $this->assertEmpty($result->getMacroZones());
        $this->assertEmpty($result->getProvinces());
        $this->assertEmpty($result->getRegions());
    }

    public function testMapWithMixedValidAndInvalidArrayItems(): void
    {
        $inputData = [
            'ids' => [
                ['id' => 1, 'name' => 'Valid ID'],
                null,
                ['id' => 2, 'name' => 'Another Valid ID'],
            ],
        ];

        $result = $this->mapper->map($inputData);

        $this->assertInstanceOf(Suggestions::class, $result);
        $this->assertCount(3, $result->getIds());
    }
}
