// ==========================================================================
// Container - Components
// ==========================================================================
@use '../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$customer-care-width: 10.6rem;

body {
  // Trick for appcues with chrome and edge on windows
  overflow-x: hidden;
}

#container {
  overflow: hidden;
  min-height: 100vh;
}

$root-container: gx-container;

.#{$root-container} {
  padding: space(lg) space(md);

  &--maxWidth {
    max-width: 96rem;

    .#{$root-container}__footerContent {
      max-width: 96rem;
    }
  }

  &--withFooter {
    padding-bottom: 8.8rem;
  }

  &--background {
    background-color: color(background-alt);
  }

  &__footer {
    position: fixed;
    bottom: 0;
    @include z-index(base, 1);
    background: color(background-main);
    width: 100%;
    margin: 0 -#{space(md)};
    border-top: 0.1rem solid color(border-main);

    &Content {
      display: flex;
      justify-content: space-between;
      width: 100%;
      max-width: 128rem;
      padding: space(sm) $customer-care-width space(sm) space(md);
    }

    .gx-button:only-child {
      margin-left: auto;
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    padding: space(xl);

    &--sideBySide {
      display: flex;
    }

    &--withFooter {
      padding-bottom: 10.4rem;
    }

    &__footer {
      width: calc(100% - #{$gx-new-menu-width});
      margin: 0 (-(space(xl)));

      &Content {
        padding: space(md) $customer-care-width space(md) space(xl);
      }
    }
  }

  @include media('screen', '>#{breakpoint(lg)}') {
    .has-menu-fixed &__footer {
      width: calc(100% - #{$gx-new-menu-width} - 26rem);
    }
  }
}
