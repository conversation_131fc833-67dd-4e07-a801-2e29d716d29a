import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { SummaryItem } from '@gx-design/summary-item';
import { trans } from '@pepita-i18n/babelfish';
import { Carousel } from 'gtx-react/components';
import { FC, PropsWithChildren } from 'react';

import { PROPERTY_ITEM_VOID_CONTENT } from 'constants/property';
import { PropertyDetailSections } from 'gtx-react/containers/PropertyDetailSections';
import { IPropertyDetail } from 'types/api/property';
import { PortalEntityDetailProps } from './PortalEntityDetail';
import * as hooks from './hooks';
import { getUseSectionsArgs } from './utils';

const CarouselArrow: FC<{ direction: 'left' | 'right' }> = ({ direction }) => (
    <Button className="nd-carousel__arrowIcon" iconOnly>
        <Icon name={`arrow-${direction}`} />
    </Button>
);

const ItemWrapper: FC<PropsWithChildren & { colSm: number }> = (props) => (
    <div className={`gx-col-xs-12 gx-col-sm-${props.colSm}`}>
        <div className="gx-box-row">{props.children}</div>
    </div>
);

type CodeItemProps = {
    category: PortalEntityDetailProps['category'];
    propertyData: IPropertyDetail;
};
const CodeItem: FC<CodeItemProps> = ({ category, propertyData }) => {
    const code =
        category === 'property'
            ? propertyData?.properties?.[0]?.reference
            : propertyData?.code;

    return (
        <SummaryItem label={trans('label.reference')}>
            {code ? (
                <>
                    <div>{code}</div>
                    <div>({propertyData.id})</div>
                </>
            ) : (
                <span>{propertyData.id}</span>
            )}
        </SummaryItem>
    );
};

const SECTIONS_HOOK: Record<
    PortalEntityDetailProps['category'],
    (...args: unknown[]) => { sections: any[] }
> = {
    auction: hooks.useAuctionDetail,
    newConstruction: hooks.useNewConstructionDetail,
    property: hooks.usePropertyDetail,
};

type PortalEntityDetailContentProps = {
    propertyData?: IPropertyDetail;
    print?: boolean;
    cadastralDataButton?:
        | false
        | {
              url: string;
              label: string;
          };
    category: PortalEntityDetailProps['category'];
};
export const PortalEntityDetailContent: FC<PortalEntityDetailContentProps> = ({
    propertyData,
    print = false,
    cadastralDataButton,
    category = 'property',
}) => {
    const property = propertyData?.properties
        ? propertyData.properties[0]
        : null;

    const useSections = SECTIONS_HOOK[category];
    const { sections } = useSections(
        ...getUseSectionsArgs({ adCategory: category, propertyData })
    );

    return propertyData ? (
        <div className={print ? 'print-property-detail' : undefined}>
            <div className="gx-row">
                {property?.images && (
                    <ItemWrapper colSm={6}>
                        <div className="carousel-container carousel-detail">
                            <Carousel
                                items={property.images}
                                leftIcon={<CarouselArrow direction="left" />}
                                rightIcon={<CarouselArrow direction="right" />}
                            />
                        </div>
                    </ItemWrapper>
                )}
                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        {category === 'property' && (
                            <div className="gx-title-1">
                                {property?.subTypology
                                    ? property.subTypology
                                    : propertyData?.typology}{' '}
                                {trans('label.in')} {propertyData.contract}
                            </div>
                        )}
                        <span>
                            {property?.address
                                ? `${property.address.trim()}, `
                                : null}{' '}
                            {property?.city}{' '}
                            {property?.province
                                ? `(${property.province})`
                                : null}
                        </span>
                    </div>
                    <div className="gx-row">
                        <ItemWrapper colSm={4}>
                            <CodeItem
                                category={category}
                                propertyData={propertyData}
                            />
                        </ItemWrapper>
                        {category !== 'newConstruction' && (
                            <ItemWrapper colSm={4}>
                                <SummaryItem label={trans('label.price')}>
                                    <span>
                                        {propertyData.price ??
                                            PROPERTY_ITEM_VOID_CONTENT}
                                    </span>
                                </SummaryItem>
                            </ItemWrapper>
                        )}
                        {category !== 'newConstruction' && (
                            <ItemWrapper colSm={4}>
                                <SummaryItem label={trans('label.surface')}>
                                    <span>
                                        {property?.surface ??
                                            PROPERTY_ITEM_VOID_CONTENT}
                                    </span>
                                </SummaryItem>
                            </ItemWrapper>
                        )}
                    </div>
                </div>
            </div>
            <PropertyDetailSections
                sections={sections}
                propertyData={propertyData}
                cadastralDataButton={cadastralDataButton}
            />
        </div>
    ) : null;
};
