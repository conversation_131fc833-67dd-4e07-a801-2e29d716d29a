import { useMediaMatch } from '@gx-design/use-media-match';
import { useMemo } from 'react';
import { useActivePath } from '../../menu/useActivePath';
import { useEnsureGxNavigationContext } from '../components/GxNavigationContext';
import { useGxNavigationMenuContext } from '../components/GxNavigationMenuContext';

/**
 * Hook to get the default active menu item. It will return the active menu item based on the current path.
 * Use this hook to get the secondary menu.
 */
export const useSecondaryMenuItemByPathname = () => {
  const props = useEnsureGxNavigationContext();
  const [main1stLevel, main2ndLevel] = useActivePath(props.primaryMenu);
  const [secondary1stLevel, secondary2ndLevel] = useActivePath(props.secondaryMenu);
  const { openMenu } = useGxNavigationMenuContext();
  const isDesktop = useMediaMatch('desktop');

  const activeMenu = useMemo(() => {
    if (!props.primaryMenu) {
      return undefined;
    }

    if (openMenu && !isDesktop && main2ndLevel) {
      return props.primaryMenu[main1stLevel];
    }
  }, [
    isDesktop,
    main1stLevel,
    main2ndLevel,
    openMenu,
    props.primaryMenu,
    props.secondaryMenu,
    secondary1stLevel,
    secondary2ndLevel,
  ]);

  return activeMenu;
};
