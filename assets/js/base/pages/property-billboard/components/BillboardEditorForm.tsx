import { Checkbox } from '@gx-design/checkbox';
import { Loader } from '@gx-design/loader';
import { Colorpicker } from 'gtx-react/components/Colorpicker/Colorpicker';
import { Radio, RadioGroup } from '@gx-design/radio';
import { trans } from '@pepita-i18n/babelfish';
import { useSuspenseQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { createBillboardQueryOptions } from 'lib/REST/requests/api/properties/query-factory';
import { BillboardType, FeaturesType } from 'lib/REST/types/billboard';
import { useAgency } from '../hooks/useAgency';
import { usePropertyIdParam } from '../hooks/usePropertyIdParam';
import { useSaveBillboardMutation } from '../hooks/useSaveBillboardMutation';
import {
    getBillboardColorIdByValue,
    getBillboardColorValue,
    getBillboardImages,
    getBillboardOrientationId,
    getBillboardPhotoNumber,
    getBillboardTypeId,
    getColorsValues,
    getFeatureLabel,
    getFeatures,
    getImagesToShow,
    getOrientations,
    getPhotoNumbers,
    getPhotoNumberSelected,
    getTypes,
    isFeatureSelected,
} from '../utils/selectors';

export const BillboardEditorForm = () => {
    const { agency } = useAgency();

    const id = usePropertyIdParam();

    const {
        data: { billboard, options: editorOptions, property },
    } = useSuspenseQuery(
        createBillboardQueryOptions({
            params: { propertyId: id },
        })
    );
    const { mutate, isPending } = useSaveBillboardMutation(id);

    const save = (data: Partial<BillboardType>): void => {
        mutate({ ...billboard, ...data });
    };

    const onChangeFeature = (feature: FeaturesType, value: boolean): void => {
        save({
            features: value
                ? [...billboard.features, feature]
                : billboard?.features.filter((f) => f !== feature),
        });
    };

    useEffect(() => {
        /**
         * BE contains some inconsistencies between `photoNumber` and number of
         * images IDS saved, which caused a bug later into the `ModalEditPhoto`
         * component; solution is to force a mutation when the pages loads (only
         * if the billboard contains this "dirty" data), sending the array of
         * currently shown images; `else` case done to prevent having a positive
         * photoNumber even if there are no images.
         */
        const checkAndFixPhotos = () => {
            const photoNumber = getBillboardPhotoNumber(billboard);
            const storedImagesNum = getBillboardImages(billboard)?.length;
            if (photoNumber && photoNumber > storedImagesNum) {
                const shownImagesIds = getImagesToShow(
                    billboard,
                    property,
                    getBillboardPhotoNumber(billboard)
                )?.map((elem) => elem.id);
                if (photoNumber === shownImagesIds.length) {
                    mutate({ ...billboard, images: shownImagesIds });
                } else {
                    mutate({
                        ...billboard,
                        photoNumber: shownImagesIds.length,
                    });
                }
            }
        };
        checkAndFixPhotos();
    }, [billboard, property, mutate]);

    return (
        <>
            {isPending && !billboard?.id ? <Loader variant="fixed" /> : null}
            <div
                className="billboard-editor__options"
                data-testid="billboardEditor"
            >
                <div className="billboard-editor__options__item billboard-editor__options__item--fullWidth">
                    <RadioGroup
                        label={trans('label.billboard_type')}
                        variant="button"
                    >
                        {getTypes(editorOptions).map((item) => (
                            <Radio
                                key={`type_${item.value}`}
                                checked={
                                    getBillboardTypeId(billboard) === item.value
                                }
                                onChange={() => save({ type: item.value })}
                                label={item.label}
                                name="type"
                            />
                        ))}
                    </RadioGroup>
                </div>
                <div className="billboard-editor__options__item billboard-editor__options__item--fullWidth billboard-editor__options__item--orientation">
                    <RadioGroup
                        label={trans('label.orientation_2')}
                        variant="button"
                    >
                        {getOrientations(editorOptions).map((item) => (
                            <Radio
                                key={`orientation_${item.value}`}
                                checked={
                                    getBillboardOrientationId(billboard) ===
                                    item.value
                                }
                                onChange={() =>
                                    save({ orientation: item.value })
                                }
                                label={item.label}
                                name="orientation"
                            />
                        ))}
                    </RadioGroup>
                </div>
                <div className="billboard-editor__options__item">
                    <div className="title">{trans('label.color')}</div>
                    <Colorpicker
                        key={`color_${getBillboardColorValue(
                            billboard,
                            editorOptions
                        )}`}
                        onColorSelect={(color) =>
                            save({
                                color: getBillboardColorIdByValue(
                                    editorOptions,
                                    color
                                ),
                            })
                        }
                        colors={getColorsValues(editorOptions)}
                        colorDefault={getBillboardColorValue(
                            billboard,
                            editorOptions
                        )}
                    />
                </div>
                <div className="billboard-editor__options__item">
                    <RadioGroup label={trans('label.photo')} variant="button">
                        {getPhotoNumbers(editorOptions, property).map(
                            (item) => (
                                <Radio
                                    key={`photoNumber_${item.value}`}
                                    checked={
                                        getPhotoNumberSelected(
                                            billboard,
                                            editorOptions,
                                            property
                                        ) === item.value
                                    }
                                    onChange={() =>
                                        save({
                                            photoNumber: item.value,
                                            images: getImagesToShow(
                                                billboard,
                                                property,
                                                item.value
                                            ).map((image) => image.id),
                                        })
                                    }
                                    label={item.label}
                                    name="photoNumber"
                                />
                            )
                        )}
                    </RadioGroup>
                </div>
                <div className="billboard-editor__options__item">
                    <div className="title">{trans('label.show_hide')}</div>
                    {getFeatures(
                        billboard,
                        editorOptions,
                        property,
                        agency
                    ).map((feature) => (
                        <div
                            key={feature}
                            className="billboard-editor__checkItem"
                        >
                            <Checkbox
                                checked={isFeatureSelected(billboard, feature)}
                                onChange={(e) => {
                                    onChangeFeature(feature, e.target.checked);
                                }}
                                label={getFeatureLabel(feature)}
                            />
                        </div>
                    ))}
                </div>
            </div>
        </>
    );
};
