import { CrmFilters } from '../../../js/commons/gtx-react/components/CrmFilters';
import { Button } from '@gx-design/button';
import { Select } from '@gx-design/select';
import { Icon } from '@gx-design/icon';
import { Checkbox } from '@gx-design/checkbox';
import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';

/**
 * CrmFilters component displays filterable content within a modal window.
 * Used for creating filter interfaces in CRM-related views with compound component structure.
 */
const meta: Meta<typeof CrmFilters> = {
    title: 'CRM/CrmFilters',
    component: CrmFilters,
    parameters: {
        componentSubtitle: 'Component',
        componentTitle: 'CrmFilters',
        componentSlug: 'CrmFilters',
        componentImport: 'CrmFilters',
        packageName: 'gtx-react/components/CrmFilters',
        docs: {
            description: {
                component: `
CrmFilters is a compound component that provides a structured layout for filter interfaces in CRM views. It includes:

## Key Features:
- **Modular structure**: Built with compound components for flexibility
- **Filter organization**: Clear separation between filter elements and actions
- **Responsive layout**: Supports both horizontal and vertical layouts
- **Consistent styling**: Uses predefined CSS classes for consistent appearance

## Sub-components:
- **CrmFilters.Element**: Container for individual filter controls with optional direction prop
- **CrmFilters.Element.Title**: Title component for filter sections with optional icon support
- **CrmFilters.Actions**: Container for action buttons (Apply, Reset, etc.)

## Usage Patterns:
The component is designed to work with various form controls and supports both simple and complex filter scenarios.
                `,
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        children: {
            description:
                'Child components to render inside the filter container',
            control: { type: 'text' },
        },
    },
};

export default meta;

type Story = StoryObj<typeof CrmFilters>;

// Mock data for select options
const statusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'pending', label: 'Pending' },
    { value: 'suspended', label: 'Suspended' },
];

const categoryOptions = [
    { value: 'residential', label: 'Residential' },
    { value: 'commercial', label: 'Commercial' },
    { value: 'land', label: 'Land' },
    { value: 'industrial', label: 'Industrial' },
];

const locationOptions = [
    { value: 'rome', label: 'Rome' },
    { value: 'milan', label: 'Milan' },
    { value: 'naples', label: 'Naples' },
    { value: 'turin', label: 'Turin' },
];

/**
 * Basic example showing the fundamental structure of CrmFilters
 */
export const Basic: Story = {
    render: () => (
        <CrmFilters>
            <CrmFilters.Element>
                <CrmFilters.Element.Title>Status</CrmFilters.Element.Title>
                <Select
                    label="Status"
                    options={statusOptions}
                    placeholder="Select status..."
                    onChange={() => {}}
                />
            </CrmFilters.Element>

            <CrmFilters.Actions>
                <Button variant="accent">Apply Filters</Button>
                <Button variant="ghost">Reset</Button>
            </CrmFilters.Actions>
        </CrmFilters>
    ),
};

const InteractiveComponent = () => {
    const [selectedStatus, setSelectedStatus] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('');
    const [selectedLocation, setSelectedLocation] = useState('');
    const [includeArchived, setIncludeArchived] = useState(false);
    const [showOnlyFeatured, setShowOnlyFeatured] = useState(false);

    const handleApply = () => {
        // Filters applied
    };

    const handleReset = () => {
        setSelectedStatus('');
        setSelectedCategory('');
        setSelectedLocation('');
        setIncludeArchived(false);
        setShowOnlyFeatured(false);
    };

    return (
        <CrmFilters>
            <CrmFilters.Element>
                <CrmFilters.Element.Title>
                    <Icon name="list" />
                    Status
                </CrmFilters.Element.Title>
                <Select
                    label="Status"
                    options={statusOptions}
                    value={selectedStatus}
                    placeholder="Select status..."
                    onChange={(ev) => setSelectedStatus(ev.target.value)}
                />
            </CrmFilters.Element>

            <CrmFilters.Element>
                <CrmFilters.Element.Title>
                    <Icon name="home" />
                    Category
                </CrmFilters.Element.Title>
                <Select
                    label="Category"
                    options={categoryOptions}
                    value={selectedCategory}
                    placeholder="Select category..."
                    onChange={(ev) => setSelectedCategory(ev.target.value)}
                />
            </CrmFilters.Element>

            <CrmFilters.Element>
                <CrmFilters.Element.Title>
                    <Icon name="marker" />
                    Location
                </CrmFilters.Element.Title>
                <Select
                    label="Location"
                    options={locationOptions}
                    value={selectedLocation}
                    placeholder="Select location..."
                    onChange={(ev) => setSelectedLocation(ev.target.value)}
                />
            </CrmFilters.Element>

            <CrmFilters.Element>
                <CrmFilters.Element.Title>Options</CrmFilters.Element.Title>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '8px',
                    }}
                >
                    <Checkbox
                        checked={includeArchived}
                        onChange={(ev) => setIncludeArchived(ev.target.checked)}
                        label="Include archived items"
                    />
                    <Checkbox
                        checked={showOnlyFeatured}
                        onChange={(ev) =>
                            setShowOnlyFeatured(ev.target.checked)
                        }
                        label="Show only featured"
                    />
                </div>
            </CrmFilters.Element>

            <CrmFilters.Actions>
                <Button variant="accent" onClick={handleApply}>
                    Apply Filters
                </Button>
                <Button variant="ghost" onClick={handleReset}>
                    Reset All
                </Button>
            </CrmFilters.Actions>
        </CrmFilters>
    );
};

/**
 * Interactive example with multiple filter types and state management
 */
export const Interactive: Story = {
    render: () => <InteractiveComponent />,
};

/**
 * Horizontal layout example showing different direction configurations
 */
export const HorizontalLayout: Story = {
    render: () => (
        <CrmFilters>
            <CrmFilters.Element direction="horizontal">
                <CrmFilters.Element.Title>Status</CrmFilters.Element.Title>
                <Select
                    label="Status"
                    options={statusOptions}
                    placeholder="Select status..."
                    onChange={() => {}}
                />
            </CrmFilters.Element>

            <CrmFilters.Element direction="horizontal">
                <CrmFilters.Element.Title>Category</CrmFilters.Element.Title>
                <Select
                    label="Category"
                    options={categoryOptions}
                    placeholder="Select category..."
                    onChange={() => {}}
                />
            </CrmFilters.Element>

            <CrmFilters.Actions>
                <Button variant="accent">Apply</Button>
                <Button variant="ghost">Clear</Button>
            </CrmFilters.Actions>
        </CrmFilters>
    ),
};

/**
 * Vertical layout example (default behavior)
 */
export const VerticalLayout: Story = {
    render: () => (
        <CrmFilters>
            <CrmFilters.Element direction="vertical">
                <CrmFilters.Element.Title>
                    Property Status
                </CrmFilters.Element.Title>
                <Select
                    label="Property Status"
                    options={statusOptions}
                    placeholder="Select status..."
                    onChange={() => {}}
                />
            </CrmFilters.Element>

            <CrmFilters.Element direction="vertical">
                <CrmFilters.Element.Title>
                    Property Type
                </CrmFilters.Element.Title>
                <Select
                    label="Property Type"
                    options={categoryOptions}
                    placeholder="Select type..."
                    onChange={() => {}}
                />
            </CrmFilters.Element>

            <CrmFilters.Actions>
                <Button variant="accent">Search Properties</Button>
            </CrmFilters.Actions>
        </CrmFilters>
    ),
};

const ComplexComponent = () => {
    const [filters, setFilters] = useState({
        status: '',
        category: '',
        location: '',
        priceRange: '',
        features: [] as string[],
    });

    const priceRangeOptions = [
        { value: '0-100000', label: '€0 - €100,000' },
        { value: '100000-250000', label: '€100,000 - €250,000' },
        { value: '250000-500000', label: '€250,000 - €500,000' },
        { value: '500000+', label: '€500,000+' },
    ];

    const handleFeatureChange = (feature: string, checked: boolean) => {
        setFilters((prev) => ({
            ...prev,
            features: checked
                ? [...prev.features, feature]
                : prev.features.filter((f) => f !== feature),
        }));
    };

    return (
        <CrmFilters>
            <CrmFilters.Element direction="horizontal">
                <CrmFilters.Element.Title>
                    <Icon name="list" />
                    Quick Filters
                </CrmFilters.Element.Title>
                <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
                    <Select
                        label="Status"
                        options={statusOptions}
                        value={filters.status}
                        placeholder="Status"
                        onChange={(ev) =>
                            setFilters((prev) => ({
                                ...prev,
                                status: ev.target.value,
                            }))
                        }
                        style={{ minWidth: '120px' }}
                    />
                    <Select
                        label="Category"
                        options={categoryOptions}
                        value={filters.category}
                        placeholder="Category"
                        onChange={(ev) =>
                            setFilters((prev) => ({
                                ...prev,
                                category: ev.target.value,
                            }))
                        }
                        style={{ minWidth: '120px' }}
                    />
                    <Select
                        label="Location"
                        options={locationOptions}
                        value={filters.location}
                        placeholder="Location"
                        onChange={(ev) =>
                            setFilters((prev) => ({
                                ...prev,
                                location: ev.target.value,
                            }))
                        }
                        style={{ minWidth: '120px' }}
                    />
                </div>
            </CrmFilters.Element>

            <CrmFilters.Element>
                <CrmFilters.Element.Title>
                    <Icon name="tag" />
                    Price Range
                </CrmFilters.Element.Title>
                <Select
                    label="Price Range"
                    options={priceRangeOptions}
                    value={filters.priceRange}
                    placeholder="Select price range..."
                    onChange={(ev) =>
                        setFilters((prev) => ({
                            ...prev,
                            priceRange: ev.target.value,
                        }))
                    }
                />
            </CrmFilters.Element>

            <CrmFilters.Element>
                <CrmFilters.Element.Title>
                    <Icon name="star" />
                    Property Features
                </CrmFilters.Element.Title>
                <div
                    style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(2, 1fr)',
                        gap: '8px',
                    }}
                >
                    {[
                        'Parking',
                        'Garden',
                        'Terrace',
                        'Swimming Pool',
                        'Gym',
                        'Elevator',
                    ].map((feature) => (
                        <Checkbox
                            key={feature}
                            checked={filters.features.includes(feature)}
                            onChange={(ev) =>
                                handleFeatureChange(feature, ev.target.checked)
                            }
                            label={feature}
                        />
                    ))}
                </div>
            </CrmFilters.Element>

            <CrmFilters.Actions>
                <Button variant="accent" onClick={() => {}}>
                    <Icon name="lens" />
                    Search Properties
                </Button>
                <Button
                    variant="ghost"
                    onClick={() =>
                        setFilters({
                            status: '',
                            category: '',
                            location: '',
                            priceRange: '',
                            features: [],
                        })
                    }
                >
                    <Icon name="bin" />
                    Reset All
                </Button>
                <Button variant="default">
                    <Icon name="download" />
                    Save Filter
                </Button>
            </CrmFilters.Actions>
        </CrmFilters>
    );
};

/**
 * Complex example with mixed layouts and multiple filter types
 */
export const ComplexExample: Story = {
    render: () => <ComplexComponent />,
};
