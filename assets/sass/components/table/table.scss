@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

// TABLE
.sticky-header .gtx-first-col {
  min-height: 4.8rem;
}

.sticky-header__padding {
  padding: 2rem 1.6rem;
  background: #ffffff;

  .gtx-section__bar {
    padding: 0;
    border-top: none;
  }
}

@include media('screen', '>=#{breakpoint(sm)}') {
  .sticky-header__padding {
    padding: 2rem 3rem;
  }
}

.table {
  color: color(content-medium);
  margin-bottom: 0;
  @include typography(body-tiny);

  ul {
    list-style: none;
    padding: 0;
  }

  .donut-chart {
    text-align: center;
  }

  tr:last-child {
    border-bottom: 0.1rem solid color(border-main);
  }

  > thead {
    background: color(background-alt);

    > tr > th {
      position: relative;
      padding: 1.5rem;
      border-top: 0.1rem solid color(border-main) !important;
      border-bottom: 0.1rem solid color(border-main);
      @include typography(body-tiny);
      color: color(content-low);
      font-weight: normal;
      text-transform: uppercase;

      .spacer {
        white-space: nowrap;
      }

      .fa {
        font-size: 1.9rem;
      }

      .styled-check .fa {
        @include typography(body-tiny);
      }

      &:first-child {
        padding-left: 3rem;
      }
      &:last-child {
        padding-right: 3rem;
      }

      .spacer--icon {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        margin: auto;
        width: 2rem;
        height: 2rem;
        text-align: center;

        .gx-icon {
          @include icon-size(sm);
        }
      }
    }
  }

  > tbody > tr > td {
    padding: 2rem 1.5rem;

    &:first-child {
      padding-left: 3rem;
    }
    &:last-child {
      padding-right: 3rem;
    }
  }

  > .colonna-checkbox > tr > td {
    padding: 0;
    height: 8rem;

    &:first-child,
    &:last-child {
      padding: 0;
    }
  }

  .td-actions {
    text-align: right;
    min-width: 15rem;

    .btn {
      width: 4rem;
      height: 4rem;
      line-height: 4rem;
      padding: 0;

      i {
        font-size: 2rem;
      }

      span {
        display: none;
      }
    }
  }
}

// TABLE--ZONE
.table--zone {
  .td-zone {
    b {
      display: block;
    }
  }

  .td-richieste span,
  .td-notifiche span {
    display: block;

    & + span {
      margin-top: 0.5rem;
    }
  }

  .td-richieste b,
  .td-notifiche b {
    display: inline-block;
    width: 54%;
  }
}

// TABLE--ZONE | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .table--zone {
    .td-richieste {
      span {
        width: 30rem;
      }
      b {
        width: 19rem;
      }
    }

    .td-notifiche {
      span {
        width: 14rem;
      }

      b {
        width: 10rem;
      }
    }
  }
}

// GTX-TABLE-RESPONSIVE

// GTX-TABLE-RESPONSIVE | SMARTPHONE
@include media('screen', '<#{breakpoint(sm)}') {
  .gtx-table-responsive {
    border: none;

    table {
      > thead {
        display: none;
      }

      > tbody > tr {
        display: block;
        padding: 1rem 1.6rem;
        border-top: 0.1rem solid color(border-main);

        &:hover td {
          border: none;
        }

        > td {
          display: block;
          padding: 1rem 0;
          border: none;
          white-space: normal !important;

          &:first-child,
          &:last-child {
            padding: 1rem 0;
          }
        }
      }

      .td-title:before {
        display: block;
        margin-bottom: 0.8rem;
        content: attr(data-title);
        @include typography(body-tiny);
        color: color(content-low);
        text-transform: uppercase;
      }

      .td-title--inline {
        padding: 0;

        &:before {
          display: inline-block;
          margin-bottom: 0;
          width: 50%;
        }

        & + .td-title--inline {
          margin-top: 0.5rem;
        }
      }

      .td-actions {
        .btn-group {
          width: 100%;

          .btn {
            float: left;
            display: block;
            width: calc(50% - 0.4rem);
            margin-right: 0.4rem;

            & + .btn {
              margin-left: 0.4rem;
            }

            &:last-child {
              margin-right: 0;
            }
          }
        }

        .btn {
          i {
            display: none;
          }

          span {
            display: inline;
          }
        }
      }
    }
  }
}

td[data-component='incroci'],
th[data-field-name='incroci'],
td[data-component='riscontri'],
th[data-field-name='riscontri'],
td[data-component='corrispondenze'],
th[data-field-name='corrispondenze'],
td[data-component='portale'],
th[data-field-name='portale'] {
  text-align: center;
}
