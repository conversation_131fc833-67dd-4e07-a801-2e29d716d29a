import clsx from 'clsx';
import { PropsWithChildren, createContext, useContext } from 'react';
import { useMenu } from './useMenu';

const HeaderContext = createContext<{ toggleMenu: () => void } | undefined>(undefined);

export const useHeaderContext = () => {
  const context = useContext(HeaderContext);

  if (!context) {
    throw new Error('useHeaderContext must be used within a HeaderContextProvider');
  }

  return context;
};

export const Header = (props: PropsWithChildren) => {
  const menu = useMenu();

  return (
    <div
      className={clsx('gx-navigation-header', 'gx-navigation-header--newMenu', {
        'st-menu-open': menu.isOpen,
      })}
    >
      <HeaderContext.Provider value={{ toggleMenu: menu.toggle }}>
        {props.children}
      </HeaderContext.Provider>
    </div>
  );
};
