import { trans } from '../../../shared/translations';
import { getSmartCounterString } from '../../../shared/utils/getSmartCounterString';
import { BaseMenuConfig } from '../../../types';
import { AdapterFC, AdapterHook } from '../types';
import { useCountersContext } from './CountersContext';

export const useMessageCounterAdapter: AdapterHook = (menu: BaseMenuConfig) => {
  const counters = useCountersContext();
  const counter = counters.unreadThreads;
  const shouldShowCounter = counter > 0;

  if (!shouldShowCounter) {
    return menu;
  }

  const number = getSmartCounterString(counter);

  return {
    ...menu,
    item: {
      ...menu.item,
      notificationBadge: {
        number,
      },
      tooltip: trans('label.messaging_counter_menu_tooltip'),
    },
  };
};

export const MessageCounterAdapter: AdapterFC = (props) => {
  const remappedMenu = useMessageCounterAdapter(props.menu, props.level);

  return props.children(remappedMenu);
};
