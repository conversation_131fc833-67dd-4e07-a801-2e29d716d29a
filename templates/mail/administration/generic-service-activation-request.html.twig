{% extends 'mail/administration/service-activation-request.html.twig' %}

{% block service %}
    {% if info.tipo == gtxConstants.SERVICE_REQUEST_TYPE_ACTIVATION %}
        {{ 'mail.SERVICE_REQUEST_TYPE_ACTIVATION'|trans({'%nome%':agency.nome, '%idAgenzia%':agency.idAgenzia}, gtxConstants.DOMAIN_CMS)|raw }}
    {% elseif info.tipo == gtxConstants.SERVICE_REQUEST_TYPE_DEACTIVATION %}
        {{ 'mail.SERVICE_REQUEST_TYPE_DEACTIVATION'|trans({'%nome%':agency.nome, '%idAgenzia%':agency.idAgenzia}, gtxConstants.DOMAIN_CMS)|raw }}
    {% elseif info.tipo == gtxConstants.SERVICE_REQUEST_TYPE_INCREASE_ADV_SPACES %}
        {{ 'mail.SERVICE_REQUEST_TYPE_INCREASE_ADV_SPACES'|trans({'%nome%':agency.nome, '%idAgenzia%':agency.idAgenzia}, gtxConstants.DOMAIN_CMS)|raw }}
    {% elseif info.tipo == gtxConstants.SERVICE_REQUEST_TYPE_INCREASE_VISIBILITY %}
        {{ 'mail.SERVICE_REQUEST_TYPE_INCREASE_VISIBILITY'|trans({'%nome%':agency.nome, '%idAgenzia%':agency.idAgenzia}, gtxConstants.DOMAIN_CMS)|raw }}
    {% elseif info.tipo == gtxConstants.SERVICE_REQUEST_TYPE_MORE_INFO %}
        {{ 'mail.SERVICE_REQUEST_TYPE_MORE_INFO'|trans({'%nome%':agency.nome, '%idAgenzia%':agency.idAgenzia}, gtxConstants.DOMAIN_CMS)|raw }}
    {% endif %}
    "<b>{{ info.servizio }}</b>".
    {% include 'mail/administration/agency-info.html.twig' %}
{% endblock %}
