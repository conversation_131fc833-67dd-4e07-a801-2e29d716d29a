import { format } from 'date-fns';
import {
    AdditionalInformationType,
    ContactInformationType,
    GeographyInformationType,
    PricesType,
    PropertyInfoType,
    SaleRequest,
    StatusType,
} from 'lib/REST/types/acquisition-sales';
import { getMacroZone } from '../../utils/getMacroZone';
import { useMemo, useState } from 'react';
import { currencyFormatter } from 'lib/currencyFormatter';
import { trans } from '@pepita-i18n/babelfish';
import { getOutcomeIcon } from '../../utils/outcomeIcon';
import { Tooltip } from '@gx-design/tooltip';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { DETAIL_PATH } from '../../web-api/endpoints';
import SetStatusModal from '../SetStatusModal';
import { Popover } from '@gx-design/popover';
import { useSaveStatusListMutation } from '../../hooks/list/useSaveStatusListMutation';
import { useMediaMatch } from '@gx-design/use-media-match';

export const DateItem = ({ date }: { date: string }) => (
    <div className="generic-content-block generic-content-block--typology">
        <div className="generic-content-block generic-content-block--typology">
            <div>{format(new Date(date), 'dd/MM/yyyy')}</div>
        </div>
    </div>
);

export const PlaceItem = ({
    geographyInformation,
}: {
    geographyInformation: GeographyInformationType;
}) => {
    const macroZoneName = useMemo(
        () => getMacroZone(geographyInformation),
        [geographyInformation]
    );

    return (
        <div className="generic-content-block generic-content-block--typology">
            <div>
                {geographyInformation?.city?.name} (
                {geographyInformation?.city?.province?.id})
            </div>
            {macroZoneName && <div>{macroZoneName}</div>}
            <div>{geographyInformation?.address}</div>
        </div>
    );
};

export const PropertyItem = ({
    propertyInfo,
}: {
    propertyInfo: PropertyInfoType;
}) => (
    <div className="generic-content-block generic-content-block--typology">
        <div>{propertyInfo?.typology?.name}</div>
        <div>{propertyInfo?.surface}</div>
    </div>
);

export const PricesItem = ({ prices }: { prices: PricesType }) => (
    <div className="generic-content-block generic-content-block--typology">
        <strong>
            {prices?.desired ? (
                <div>{currencyFormatter(prices.desired)}</div>
            ) : (
                <div>---</div>
            )}
        </strong>
        {prices?.lower ? (
            <div>{currencyFormatter(prices?.lower)}</div>
        ) : (
            <div>---</div>
        )}
    </div>
);

export const ContactItem = ({
    contactInformation,
}: {
    contactInformation: ContactInformationType;
}) => (
    <div className="generic-content-block truncate generic-content-block--typology">
        <div>
            {contactInformation?.firstname} {contactInformation?.lastname}
        </div>
        <a href={`mailto:${contactInformation?.email}`}>
            {contactInformation?.email}
        </a>
        <div>{contactInformation?.phone}</div>
    </div>
);

export const SalesInfoItem = ({
    additionalInformation,
}: {
    additionalInformation: AdditionalInformationType;
}) => (
    <div className="generic-content-block generic-content-block--typology">
        <div>
            <strong>
                {additionalInformation?.plannedSale?.label
                    ? trans(additionalInformation.plannedSale.label)
                    : '---'}
            </strong>
        </div>
        <div>
            {additionalInformation?.owner?.label
                ? trans(additionalInformation.owner.label)
                : '---'}
        </div>
    </div>
);

export const StatusItem = ({ saleRequest }: { saleRequest: SaleRequest }) => {
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const iconData = getOutcomeIcon(saleRequest.status?.outcome?.id);
    const isLargeDesktop = useMediaMatch('largeDesktop');
    const { mutate, isPending } = useSaveStatusListMutation();

    return (
        <div className="contentCell contentCell--outcome">
            {!saleRequest.status?.outcome?.id && !saleRequest.status?.note ? (
                <div>---</div>
            ) : (
                <>
                    {saleRequest.status?.outcome?.id ? (
                        <Tooltip
                            position="top"
                            text={trans(
                                `db_sales_requests_outcome.id_${saleRequest.status?.outcome?.id}`
                            )}
                        >
                            <Icon
                                name={iconData.icon!}
                                className={`gx-text-${iconData.style}`}
                                aria-label={`db_sales_requests_outcome.id_${saleRequest.status?.outcome?.id}`}
                            />
                        </Tooltip>
                    ) : null}
                    {saleRequest.status?.note ? (
                        <>
                            {isLargeDesktop ? (
                                <Popover
                                    title=""
                                    onEdge={false}
                                    large={false}
                                    position="top"
                                    content={
                                        <PopoverContent
                                            status={saleRequest.status}
                                            onReadMoreClick={() =>
                                                setIsModalOpen(true)
                                            }
                                        />
                                    }
                                >
                                    <Icon
                                        name="note"
                                        aria-label={
                                            saleRequest.status.note || ''
                                        }
                                    />
                                </Popover>
                            ) : (
                                <a
                                    onClick={() => setIsModalOpen(true)}
                                    className="gx-pointer"
                                >
                                    <Icon
                                        name="note"
                                        aria-label={
                                            saleRequest.status.note || ''
                                        }
                                    />
                                </a>
                            )}
                            <SetStatusModal
                                saleRequest={saleRequest}
                                isOpen={isModalOpen}
                                close={() => setIsModalOpen(false)}
                                isPending={isPending}
                                onSave={(data) => {
                                    mutate(data, {
                                        onSettled: () => {
                                            setIsModalOpen(false);
                                        },
                                    });
                                }}
                            />
                        </>
                    ) : null}
                </>
            )}
        </div>
    );
};

export const ActionsItem = ({ saleRequest }: { saleRequest: SaleRequest }) => (
    <div className="gx-multiButton gx-multiButton--separeted-xs">
        <EditButton saleRequest={saleRequest} />
        <PreviewButton id={saleRequest.id} />
    </div>
);

const EditButton = ({ saleRequest }: { saleRequest: SaleRequest }) => {
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const isLargeDesktop = useMediaMatch('largeDesktop');
    const { mutate, isPending } = useSaveStatusListMutation();

    return (
        <>
            <Tooltip
                position="top"
                text={trans('label.sales_requests.list.action.edit_status')}
            >
                <Button
                    iconOnly={isLargeDesktop}
                    onClick={() => setIsModalOpen(true)}
                >
                    <Icon name="pencil" />
                    <span hidden={isLargeDesktop}>
                        {' '}
                        {trans('label.sales_requests.list.action.edit_status')}
                    </span>
                </Button>
            </Tooltip>
            <SetStatusModal
                saleRequest={saleRequest}
                isOpen={isModalOpen}
                close={() => setIsModalOpen(false)}
                isPending={isPending}
                onSave={(data) => {
                    mutate(data, {
                        onSettled: () => {
                            setIsModalOpen(false);
                        },
                    });
                }}
            />
        </>
    );
};

export const PreviewButton = ({ id }: { id: string }) => {
    const isLargeDesktop = useMediaMatch('largeDesktop');

    return (
        <Tooltip position="top" text={trans('label.detail')}>
            <Button
                as="a"
                href={`${DETAIL_PATH.replace(':id', id)}`}
                iconOnly={isLargeDesktop}
            >
                <Icon name="eye" />
                <span hidden={isLargeDesktop}> {trans('label.detail')}</span>
            </Button>
        </Tooltip>
    );
};

const PopoverContent = ({
    status,
    onReadMoreClick,
}: {
    status: StatusType;
    onReadMoreClick: () => void;
}) => {
    return (
        <>
            <span>
                {status?.ellipsedNote ? status?.ellipsedNote : status?.note}
            </span>
            {status?.ellipsedNote ? (
                <a className="read-more" onClick={() => onReadMoreClick()}>
                    {trans('label.sales_requests.outcome.read_more')}
                </a>
            ) : null}
        </>
    );
};
