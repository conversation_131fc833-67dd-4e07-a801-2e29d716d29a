@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

// Contratto

.privacy-auth-container {
  position: relative;
}

.privacy-auth-check {
  position: absolute;
  top: 0;
  left: 0;
}

.privacy-auth-label {
  padding-left: 2.5rem;
}

.administration-planimetrie-img {
  width: 40rem;
  height: 16.7rem;
  margin: 1.5rem 6rem;
  float: left;
  background: url('/bundles/base/getrix/common/img/planimetrie/Plan_3D.jpg');

  & + p {
    clear: both;
  }
}

.administration-vt360-modal {
  position: relative;
  text-align: center;

  a.example {
    display: inline-block;
    margin-top: 3rem;
  }
}

.administration-vt360-text {
  color: #333333;
  font-family: 'Open Sans';
  font-size: 1.6rem;
  font-weight: 300;
  line-height: 3.2rem;
}

.administration-vt360-logo {
  width: 100%;
  height: 2.8rem;
  margin-top: 1.1rem;
  margin-bottom: 2.2rem;
  background: url('/bundles/base/getrix/common/img/virtual-tour-360-logo.png');
  background-repeat: no-repeat;
  background-position: center;
}

.administration-vt360-img {
  width: 17.8rem;
  height: 10.5rem;
  margin-right: 5.5rem;
  margin-bottom: 1rem;
  margin-left: 5.5rem;
  float: left;
}

.administration-vt360-scatta {
  @extend .administration-vt360-img;
  background: url('/bundles/base/getrix/common/img/virtual-tour-360/scatta.png');
  background-size: cover;
}

.administration-vt360-carica {
  @extend .administration-vt360-img;
  background: url('/bundles/base/getrix/common/img/virtual-tour-360/carica.png');
  background-size: cover;
}

.administration-vt360-visualizza {
  @extend .administration-vt360-img;
  background: url('/bundles/base/getrix/common/img/virtual-tour-360/visualizza.png');
  background-size: cover;
}

// ADMINISTRATION-BAR
.administration-bar {
  padding: 2rem 1.6rem;
  border-bottom: 0.1rem solid color(border-main);

  .gtx-card__data {
    margin-bottom: 0;
    padding: 0;

    .data-item {
      margin-bottom: 0;
    }
  }
}

.visibilita-type .icon-immopro {
  top: -0.1rem;
}

// ADMINISTRATION-BAR | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .administration-bar {
    padding: 2rem 3rem;
  }
}

// GTX-CARD-WRAPPER
.gtx-card-wrapper--administration {
  padding-top: 3rem;
}

// GTX-CARD-WRAPPER | DESKTOP
@include media('screen', '>=#{breakpoint(md)}') {
  .gtx-card-wrapper--administration {
    padding-bottom: 3rem;
  }

  .gtx-card-content-withside {
    width: calc(100% - 23rem);
    float: left;
  }
}

@include media('screen', '>=#{breakpoint(lg)}') {
  .gtx-card-content-withside {
    width: calc(100% - 28rem);
    max-width: 86rem;
  }
}

// GTX-CARD-WRAPPER__SIDEBAR
.gtx-card-wrapper__sidebar {
  margin: 0 1.6rem;

  .gtx-card {
    padding: 3rem 0;
    border: none;
    background: none;

    & + .gtx-card {
      margin-top: 0;
      border-top: 0.1rem solid color(border-main);
    }

    .btn-action {
      width: 100%;
    }
  }
}

// GTX-CARD-WRAPPER__SIDEBAR | TABLET
@include media('screen', '>=#{breakpoint(sm)}', '<#{breakpoint(md)}') {
  .gtx-card-wrapper__sidebar {
    margin-right: 3rem;
    margin-left: 3rem;

    .gtx-card__head {
      margin-bottom: 0;
      float: left;
    }

    .gtx-card {
      position: relative;

      .btn-action {
        width: auto;
        float: right;
      }
    }

    .gtx-card--wallet {
      .data-item {
        position: absolute;
        top: 2.5rem;
        left: 50%;
        transform: translateX(-50%);
      }
    }

    .gtx-card--services {
      .gtx-card__head .title {
        margin-bottom: 2.5rem;
      }

      .web-marketing {
        left: 28rem;
        margin: 0;
      }

      .btn-action {
        right: 0;
        padding-right: 1.6rem;
        padding-left: 1.6rem;
      }
    }
  }
}

// GTX-CARD-WRAPPER__SIDEBAR | DESKTOP
@include media('screen', '>=#{breakpoint(md)}') {
  .gtx-card-wrapper__sidebar {
    width: 16rem;
    margin: 0 2rem 0 5rem;
    float: left;

    .gtx-card--services {
      .gtx-card__head {
        width: auto;
        float: none;

        h3 {
          line-height: 1.2;
        }
      }

      .web-marketing {
        position: static;
        width: 100%;
        height: auto;
        padding-bottom: 42%;
        background-size: cover;
      }

      p {
        width: auto;
        margin: 2rem 0;
      }

      .btn-action {
        position: static;
        margin-top: 0;
        clear: both;
      }
    }
  }
}

@include media('screen', '>=#{breakpoint(lg)}') {
  .gtx-card-wrapper__sidebar {
    width: 21rem;
  }
}

// GTX-CARD-MULTIPLE--PUBBLICITA
.gtx-card-multiple--pubblicita {
  flex-wrap: wrap;

  @include media('<#{breakpoint(sm)}') {
    .btn-aumenta-immobiliare {
      margin-top: space(md) !important;
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    position: relative;

    .btn-aumenta-immobiliare {
      position: absolute;
      top: 3rem;
      right: 3rem;
    }
  }
}

.gtx-card--immobiliare {
  width: 100%;

  .btn-action {
    width: 100%;
  }
}

.gtx-card__row--extra-visibilita {
  table {
    width: 100%;

    tr + tr {
      border-top: 0.1rem solid color(border-main);
    }

    td {
      padding: 1rem 0;
    }

    .icon-immopro {
      display: inline-block;
      vertical-align: middle;
    }

    .visibilita-name {
      margin-left: 0.3rem;
      font-size: 1.3rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .visibilita-num {
      padding-right: 2rem;
      padding-left: 2rem;
      color: color(content-action);
    }

    .visibilita-status {
      .scadenza {
        color: color(content-low);
        font-size: 1.2rem;
        text-transform: uppercase;
      }

      .data {
        margin-left: 0.5rem;
        color: color(content-action);
        font-size: 1.3rem;
      }

      a {
        color: color(content-action);
        font-size: 1.3rem;
        font-weight: 600;
        text-transform: uppercase;
      }
    }
  }
}

.gtx-card--prestigio,
.gtx-card--estero {
  .gtx-card__row__content {
    margin-bottom: 0;
  }

  .btn-action {
    width: 100%;
  }
}

.gtx-card--estero {
  .data-item:last-child {
    margin-bottom: 2rem;
  }
}

// GTX-CARD-MULTIPLE--PUBBLICITA | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .gtx-card--immobiliare {
    .btn-action {
      width: auto;
    }
  }

  .gtx-card__row--extra-visibilita {
    table {
      .visibilita-type,
      .visibilita-num {
        width: 14rem;
      }
      .visibilita-num {
        padding-right: 0;
        padding-left: 0;
      }
    }
  }

  .gtx-card__row .gtx-card__data .data-item {
    width: auto;
    min-width: 14rem;

    &.activation {
      padding-top: 0.6rem;

      & > a {
        font-size: 1.6rem;
      }
    }
  }

  .gtx-card--prestigio,
  .gtx-card--estero {
    position: relative;

    .gtx-card__head {
      margin-bottom: 0;
    }

    .data-item {
      margin-bottom: 0;
    }

    .btn-action {
      margin-top: 1.6rem;
    }
  }

  .gtx-card--estero {
    margin-left: -0.1rem;
  }
}

// GTX-CARD-MULTIPLE--PUBBLICITA | DESKTOP
@include media('screen', '>=#{breakpoint(md)}') {
  .gtx-card__row--extra-visibilita {
    table {
      .visibilita-type,
      .visibilita-num {
        width: 12rem;
      }
    }
  }

  .gtx-card__row .gtx-card__data .data-item {
    min-width: 12rem;

    &.activation {
      padding-top: 0.6rem;

      & > a {
        font-size: 1.6rem;
      }
    }
  }
}

@include media('screen', '>=#{breakpoint(lg)}') {
  .gtx-card__row--extra-visibilita {
    table {
      .visibilita-type,
      .visibilita-num {
        width: 19rem;
      }
    }
  }

  .gtx-card__row .gtx-card__data .data-item {
    min-width: 16rem;

    &.activation {
      padding-top: 0.6rem;

      & > a {
        font-size: 1.6rem;
      }
    }
  }

  .gtx-card--prestigio,
  .gtx-card--estero {
    width: 50%;
    float: left;

    .gtx-card__head {
      margin-right: 0;
      margin-bottom: 4rem;
      float: none;
    }

    .btn-action {
      position: absolute;
      top: 5rem;
      right: 3rem;
      width: auto;
      min-width: 9.2rem;
      margin-top: 1.6rem;
      margin-top: -2rem !important;
    }
  }
}

//GTX-CARD__ROW
.gtx-card__row {
  padding-bottom: 3rem;

  & + .gtx-card__row {
    padding-top: 3rem;
    border-top: 0.1rem solid color(border-main);
  }

  &:last-child {
    padding-bottom: 0;
  }
}

.gtx-card__row__title {
  margin-bottom: 2rem;
  font-size: 1.6rem;
  font-weight: 600;
}

.gtx-card__row__column {
  margin-bottom: 0;

  & + .btn-action {
    margin-top: 2rem;
  }
}

.gtx-card__row--admin {
  position: relative;

  .btn-action {
    width: 100%;
    margin-top: 1rem;
  }

  .scopri {
    font-size: 1.3rem;
    font-weight: 600;
    text-transform: uppercase;
  }
}

.gtx-card__row--pagina-agenzia {
  .gtx-card__data {
    .data-item {
      width: 100%;
    }
  }
}

//GTX-CARD__ROW | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .gtx-card__row {
    width: 100%;
    padding-bottom: 0;

    .gtx-card__row__title {
      width: 20rem;
      vertical-align: middle;
    }

    .gtx-card__row__column {
      padding: 3rem 0;
      border-bottom: 0.1rem solid color(border-main);
    }

    &:last-child {
      .gtx-card__row__column {
        border-bottom: none;
      }
    }

    & + .gtx-card__row {
      padding-top: 0;
    }
  }

  .gtx-card-multiple--pubblicita {
    .gtx-card__row {
      display: table;

      .gtx-card__row__title {
        display: table-cell;
      }

      .gtx-card__row__column {
        display: table-cell;
      }
    }

    .gtx-card__row + .gtx-card__row {
      border-top: none;
    }

    .gtx-card__data {
      .data-item {
        width: auto;
        margin-bottom: 0;
        margin-left: 2rem;
      }
    }

    .gtx-card--prestigio,
    .gtx-card--estero {
      .gtx-card__data .data-item {
        width: 10rem;
      }
    }
  }

  .gtx-card__row__title {
    margin-bottom: 0;
  }

  .gtx-card__row--admin {
    padding-bottom: 3rem;

    & + .gtx-card__row--admin {
      padding-top: 3rem;
    }

    .gtx-card__row__title {
      height: 4rem;
      float: left;
      line-height: 4rem;
    }

    .gtx-card__row__content {
      float: left;

      p {
        max-width: 43rem;
      }
    }

    .gtx-card__row__link {
      height: 4rem;
      margin-left: 4rem;
      float: left;
      line-height: 4rem;
    }

    .btn-action {
      width: auto;
      min-width: 9.2rem;
      margin-top: 0;
      float: right;
    }
  }

  .gtx-card__row--sito-agenzia {
    padding-bottom: 0;

    .gtx-card__row__title {
      width: 20rem;
    }

    .gtx-card__data {
      float: left;
    }

    .data-item + .data-item {
      margin-left: 0;
    }

    .gtx-card__data .data-item {
      margin-bottom: 0;
    }
  }

  .gtx-card__row--telefonate-smart {
    .data-item .num {
      font-size: 1.6rem;
    }

    .data-item.expiration-item {
      min-width: 9rem;
    }

    .btn-action {
      position: absolute;
      top: 0;
      right: 0;
    }

    .gtx-card__row + & .btn-action {
      top: 3rem;
    }
  }

  .gtx-card__row--paragraph {
    .gtx-card__row__title {
      float: none;
    }

    .gtx-card__row__content {
      width: 100%;
      padding-right: 12rem;
      float: none;
    }

    .btn-action {
      position: absolute;
      top: 0;
      right: 0;
    }

    .gtx-card__row + & .btn-action {
      top: 3rem;
    }
  }
}

.gtx-card__row__column {
  & + .btn-action {
    margin-top: 0;
  }
}

//GTX-CARD__ROW | DESKTOP
@include media('screen', '>=#{breakpoint(md)}') {
  .gtx-card__row {
    .gtx-card__row__title {
      width: 20rem;
    }
  }

  .gtx-card__row--sito-agenzia {
    .data-item + .data-item {
      margin-left: 5rem;
    }
  }
}

@include media('screen', '>=#{breakpoint(lg)}') {
  .gtx-card__row--paragraph {
    .gtx-card__row__title {
      float: left;
    }

    .gtx-card__row__content {
      width: calc(100% - 34rem);
      padding-right: 0;
      float: left;

      p {
        display: table-cell;
        height: 4rem;
        margin-bottom: 0;
        vertical-align: middle;
      }
    }

    .box-visibilita-wrapper {
      float: left;
    }
  }
}

//BOX-VISIBILITA
.box-visibilita-wrapper {
  margin: 1.5rem 0 2rem;
  clear: both;
  border: 0.1rem solid color(border-main);
}

.box-visibilita {
  padding: 3rem 1.6rem 1.5rem;

  .esempio {
    font-size: 1.3rem;
    font-weight: 600;
    text-transform: uppercase;
  }

  p {
    margin-bottom: 0.5rem;
  }

  & + .box-visibilita {
    border-top: 0.1rem solid color(border-main);
  }
}

.box-visibilita__title {
  margin: 0 0 0.5rem;
  font-size: 1.6rem;
}

.box-visibilita__item {
  padding: 1.5rem 0;

  & + .box-visibilita__item {
    border-top: 0.1rem solid color(border-main);
  }
}

//BOX-VISIBILITA | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .box-visibilita-wrapper {
    margin-top: 2rem;
    margin-bottom: 0;

    .box-visibilita {
      width: 50%;
      padding-right: 3rem;
      padding-left: 3rem;
      float: left;

      & + .box-visibilita {
        border-top: none;
        border-left: 0.1rem solid color(border-main);
      }

      .esempio {
        position: absolute;
        top: 1.5rem;
        right: 0;
      }
    }

    .box-visibilita__item {
      position: relative;
      padding-right: 7rem;
    }
  }
}

//BOX-VISIBILITA | DESKTOP
@include media('screen', '>=#{breakpoint(md)}', '<#{breakpoint(lg)}') {
  .box-visibilita-wrapper {
    .box-visibilita {
      width: 100%;

      & + .box-visibilita {
        border-top: 0.1rem solid color(border-main);
        border-left: none;
      }
    }
  }
}

//FORM-VISIBILITA
.form-visibilita {
  margin-top: 2rem;
  clear: both;

  .salva {
    width: 100%;
    margin-top: 2rem;
  }
}

.form-visibilita__row {
  label {
    margin-bottom: 0.8rem;
  }

  .form-control {
    border-color: color(border-main);
  }

  & + .form-visibilita__row {
    margin-top: 3.4rem;
  }
}

.form-visibilita__anteprima {
  position: relative;
  padding: 2rem;
  border: 0.1rem solid color(border-main);
  font-size: 1.3rem;

  .evidenza {
    font-size: 1.6rem;
  }

  .testo {
    display: block;
    margin-top: 1rem;
  }

  img {
    width: auto;
    max-width: 10.7rem;
    height: 4rem;
    margin-top: 0.5rem;
    float: left;
  }

  .num {
    position: absolute;
    right: 2rem;
    bottom: 2rem;
    font-size: 1.4rem;
  }
}

//FORM-VISIBILITA | TABLET
.form-visibilita {
  .salva {
    width: auto;
    margin-top: 1.6rem;
    float: right;
  }
}

.form-visibilita__column {
  width: calc(100% - 31.9rem);
  padding-right: 1.5rem;
  float: left;

  & + .form-visibilita__column {
    padding-right: 0;
    padding-left: 1.5rem;
  }

  &--anteprima {
    width: 31.9rem;
    float: right;
    font-family: Arial, Helvetica, sans-serif;

    .evidenza {
      color: color(content-medium);
      font-size: 1.8rem;
    }

    .evidenza--vetrina {
      color: #987703;
    }

    .testo {
      color: color(content-medium);
      font-size: 1.4rem;
      overflow-wrap: break-word;
      word-wrap: break-word;
      hyphens: auto;
    }
  }
}

@include media('screen', '<#{breakpoint(sm)}') {
  .administration-planimetrie-img {
    width: 31rem;
    height: 16.7rem;
    margin: 1.5rem 0;
    float: left;
  }
}

// VIRTUAL-TOUR-STEPS
.virtual-tour-steps {
  margin: 2rem 0 0;
  padding: 0;
  list-style: none;

  li {
    color: color(content-low);
    font-size: 1.6rem;
    text-align: center;

    & + li {
      margin-top: 2rem;
    }

    .administration-vt360-img {
      width: 17.8rem;
      height: 10.5rem;
      margin-right: 5.5rem;
      margin-bottom: 1rem;
      margin-left: 8.5rem;
      float: left;
    }
  }
}

// VIRTUAL-TOUR-STEPS | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .virtual-tour-steps {
    margin-top: 4rem;

    li {
      width: 33%;
      float: left;

      & + li {
        margin-top: 0;
      }

      .administration-vt360-img {
        width: 17.8rem;
        height: 10.5rem;
        margin-right: 5.5rem;
        margin-bottom: 1rem;
        margin-left: 5.5rem;
        float: left;
      }
    }
  }
}
