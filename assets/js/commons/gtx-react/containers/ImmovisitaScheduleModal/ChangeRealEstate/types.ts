export type IApiRealEstateListItem = {
    id: number;
    reference?: string;
    contract: {
        id: number;
        name: string;
    };
    surface: number;
    price: number;
    geographyInformation: IGeographyInfo;
    typologyV2: {
        id: number;
        name: string;
    };
    virtualTour: boolean;
    photoplan: boolean;
    images: number;
    plans: number;
    mainImage: string;
    video: boolean;
};

export interface IGeographyInfo {
    city: {
        id: number;
        name: string;
        province: {
            id: string;
            name: string;
            region: {
                id: string;
                name: string;
                country: {
                    id: string;
                    name: string;
                };
            };
        };
        cityMacroZoneType: {
            id: number;
            name: string;
        };
    };
    locality: {
        id: number;
        name: string;
    };
    address: {
        street: string;
        number: string;
    };
    macroZone: {
        id: number;
        name: string;
    };
}

export type GetPropertiesFilters = {
    q?: string;
    priceMin?: string | number;
    priceMax?: string | number;
    category?: string;
    contract?: string;
    typology?: string;
    surfaceMin?: string | number;
    surfaceMax?: string | number;
    withVirtualTour?: string;
    page?: number;
    results?: string;
};
