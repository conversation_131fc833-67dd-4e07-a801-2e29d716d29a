import { IToursApiResponse } from '../types/api';

export const getToursListApi = (): Promise<IToursApiResponse> => {
    return new Promise((resolve, reject) => {
        setTimeout(
            () =>
                resolve({
                    data: [
                        {
                            id: 1,
                            order: 1,
                            name: 'searches-tour',
                            steps: [
                                {
                                    id: 1233,
                                    order: 1,
                                    body: `<p>Le vecchie “Richieste” si dividono in due voci del menu: <strong>Messaggi</strong> e <strong>Ricerche</strong>. <br><br> <strong>1. Messaggi</strong> è il luogo dove gestire tutti i contatti diretti che ricevi dai clienti.</p>`,
                                    imageUrl: null,
                                    title: 'Le richieste si dividono',
                                },
                                {
                                    id: 1234,
                                    order: 2,
                                    body: `<p><strong>2. Ricerche</strong> è dove puoi gestire le ricerche dei clienti e inviare i tuoi immobili corrispondenti.</p>`,
                                    imageUrl: null,
                                    title: null,
                                },
                            ],
                        },
                        {
                            id: 1,
                            order: 1,
                            name: 'messages-set_all_read_tour',
                            steps: [
                                {
                                    id: 12333009,
                                    order: 1,
                                    body: `<p>Vuoi azzerare il contatore? Usa il nuovo pulsante che ti consente di segnare tutte le conversazioni come "lette", così da concentrarti solo sulle nuove richieste.</p>`,
                                    imageUrl: null,
                                    title: 'Azzera in contatore dei Messaggi',
                                },
                            ],
                        },
                    ],
                }),
            1500
        );
    });
};

export const postTourReadApi = (tourId: number) => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve({
                status: 'ok',
            });
        }, 1000);
    });
};
