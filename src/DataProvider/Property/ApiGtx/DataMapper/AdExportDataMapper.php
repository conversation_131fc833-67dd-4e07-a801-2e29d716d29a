<?php

declare(strict_types=1);

namespace App\DataProvider\Property\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Property\AdExport;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class AdExportDataMapper extends AbstractDataMapper
{
    protected function getDestinationClass(): string
    {
        return AdExport::class;
    }

    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('buildingUse', function () use ($data) {
                return !empty($data['buildingUse']) ?
                    \explode(',', $data['buildingUse']) :
                    null;
            })
        ;

        return $config;
    }
}
