import { ReactNode, createContext, useContext, useMemo } from 'react';
import { BaseNavigationContextType } from '../../config/types';

type GxNavigationContextProps = Omit<
  BaseNavigationContextType,
  | 'agencyImage'
  | 'agencyName'
  | 'agentName'
  | 'agentEmail'
  | 'agentAvatar'
  | 'agencyId'
  | 'adPortal'
  | 'isMasterAccount'
  | 'hasTwoFactorAuth'
  | 'profileUrl'
  | 'beamerNewsEnabled'
  | 'helpJuiceEnabled'
  | 'serviceRemoteVisit'
  | 'isAdmin'
  | 'saleforceLiveChatEnabled'
  | 'featureFlags'
>;

export const GxNavigationContext = createContext<GxNavigationContextProps | undefined>(undefined);

export const GxNavigationProvider = (props: {
  value: GxNavigationContextProps;
  children: ReactNode;
}) => {
  return (
    <GxNavigationContext.Provider value={props.value}>
      {props.children}
    </GxNavigationContext.Provider>
  );
};

/**
 * Hook to get the navigation context
 * ensuring that the context is provided by the GxNavigationProvider
 */
export const useEnsureGxNavigationContext = () => {
  const context = useContext(GxNavigationContext);

  const value = useMemo(() => {
    if (!context) {
      throw new Error('useGxNavigationContext must be used within a GxNavigationProvider');
    }

    const { primaryMenu, secondaryMenu, ...rest } = context;

    if (primaryMenu && secondaryMenu) {
      return { primaryMenu, secondaryMenu, ...rest };
    }

    throw new Error('Primary and secondary menus are required');
  }, [context]);

  return value;
};

export const useGxNavigationContext = () => {
  const context = useContext(GxNavigationContext);

  return context;
};
