<?php

declare(strict_types=1);

namespace App\Controller\Searches\Internal\API;

use App\Constants\Base\MatchConstants;
use App\DataMapper\EntityToResponse\Searches\Match\MatchBulkOperationOutcomeResponseDataMapper;
use App\Exception\InvalidArgumentException;
use App\Handler\Searches\MatchHandler;
use App\Model\Match\MatchBulkOperationOutcome;
use App\Model\Request\Searches\BulkMatchesToSentRequest;
use App\Response\SuccessResponse;
use Ekbl\StatsBundle\Annotation\Stats;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/searches")
 * @Stats
 */
class SetMatchesToSentController extends Controller
{
    /** @var MatchHandler */
    private $matchHandler;

    /** @var MatchBulkOperationOutcomeResponseDataMapper */
    private $matchBulkOperationOutcomeResponseDataMapper;

    public function __construct(
        MatchHandler $matchHandler,
        MatchBulkOperationOutcomeResponseDataMapper $matchBulkOperationOutcomeResponseDataMapper
    ) {
        $this->matchHandler                                = $matchHandler;
        $this->matchBulkOperationOutcomeResponseDataMapper = $matchBulkOperationOutcomeResponseDataMapper;
    }

    /**
     * @Route("/agency/{agencyId}/matches/bulk-sent",
     *     name="app_search_internal_api_bulk_set_matches_to_sent",
     *     methods={"PATCH"}
     * )
     */
    public function index(
        int $agencyId,
        BulkMatchesToSentRequest $bulkMatchesToSentRequest
    ): Response {
        if (\count($bulkMatchesToSentRequest->getMatchIds()) > MatchConstants::LIMIT_MATCH_IDS_BULK_REQUESTS) {
            throw new InvalidArgumentException(MatchConstants::EXCEPTION_MESSAGES['exceeded_match_ids_limit']);
        }

        $outcomes = $this->matchHandler->sendMatchesEmails(
            $bulkMatchesToSentRequest->getMatchIds(),
            $agencyId
        );

        return SuccessResponse::ok(
            array_map(function (MatchBulkOperationOutcome $outcome) {
                return $this->matchBulkOperationOutcomeResponseDataMapper->map($outcome);
            }, $outcomes)
        );
    }
}
