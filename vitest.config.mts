import { vitestGitlabReporterConfig } from '@pepita-tools/vitest-gitlab-reporter';
import react from '@vitejs/plugin-react';
import path from 'path';
import tsconfigPaths from 'vite-tsconfig-paths';
import { defineConfig, mergeConfig } from 'vitest/config';

export default mergeConfig(
    vitestGitlabReporterConfig,
    defineConfig({
        plugins: [
            tsconfigPaths(),
            react({
                babel: {
                    plugins: [
                        [
                            '@babel/plugin-transform-react-jsx',
                            {
                                runtime: 'automatic',
                            },
                        ],
                    ],
                },
            }),
        ],
        test: {
            testTimeout: 15000,
            fakeTimers: {
                toFake: ['setTimeout', 'clearTimeout', 'Date'],
            },
            server: {
                deps: {
                    inline: ['clsx', '@pepita/querystring', 'react-redux'], // <-- This is a workaround for commonjs modules
                },
            },
            outputFile: {
                junit: './junit.xml',
            },
            setupFiles: [path.join(__dirname, 'tests/vitest/setup.ts')],
            globals: true,
            name: 'MLS',
            dir: path.join(__dirname, 'assets/js'),
            environment: 'jsdom',
            include: ['./**/*.test.(ts|tsx)'],
            coverage: {
                provider: 'v8',
                all: false,
            },
            alias: {
                'bootstrap-extend':
                    'bootstrap-sass-official/assets/javascripts/bootstrap.js',
                datetimepicker:
                    'eonasdan-bootstrap-datetimepicker/src/js/bootstrap-datetimepicker.js',
                'gtx-bootbox': '@getrix/common/js/gtx-bootbox.js',
                'gtx-notify': '@getrix/common/js/gtx-notify.js',
                'gtx-constants': '@getrix/common/js/gtx-constants.js',
                imgeditor: '@immobiliare-labs/imgeditor',
                'jquery-extend': '@getrix/common/js/jquery-plugins.js',
                'styled-select':
                    'core-utils/modules/styled-select/js/styled-select',
                swig: '@getrix/common/js/gtx-swig.js',
                'swig-root': path.join(__dirname, 'node_modules/swig'),
                '#tests': path.join(__dirname, 'tests'),
                'gtx-react': path.join(
                    __dirname,
                    'assets/js/commons/gtx-react'
                ),
            },
        },
    })
);
