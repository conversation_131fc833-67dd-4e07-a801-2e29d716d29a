import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Alert } from '@gx-design/alert';
import { isModalOpen } from '../../selectors/list';
import { trans } from '@pepita-i18n/babelfish';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import {
    openAllMessagesReadModal,
    setAllMessagesRead,
} from '../../actions/list';
import { Modal } from '@gx-design/modal';
import { useTrackMixpanelEvents } from '../../hooks/useTrackMixpanelEvents';
import { GxNavigationBus } from 'lib/gx-navigation-bus';
import { OPEN_MARK_AS_READ_EVENT } from '../../constants';

export const AllReadModal = () => {
    const dispatch = useDispatch();
    const allReadModalOpen = useSelector(isModalOpen);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const { trackReadAll } = useTrackMixpanelEvents();

    const setAllMessagesReadHandler = () => {
        setIsLoading(true);
        /* @ts-ignore */
        dispatch(setAllMessagesRead()).then(() => {
            trackReadAll();
            setIsLoading(false);
            dispatch(openAllMessagesReadModal(false));
        });
    };

    useEffect(() => {
        const unsubscribe = GxNavigationBus.addListener(
            OPEN_MARK_AS_READ_EVENT,
            () => {
                dispatch(openAllMessagesReadModal(true));
            }
        );

        return () => {
            unsubscribe();
        };
    }, [dispatch]);

    return (
        <Modal
            isOpen={allReadModalOpen}
            size="small"
            title={trans('label.mark_all_read')}
            onClose={() => dispatch(openAllMessagesReadModal(false))}
            footer={
                <>
                    <Button
                        variant="ghost"
                        onClick={() =>
                            dispatch(openAllMessagesReadModal(false))
                        }
                    >
                        {trans('label.cancel')}
                    </Button>
                    <Button
                        variant="accent"
                        disabled={isLoading}
                        iconOnly={isLoading}
                        onClick={setAllMessagesReadHandler}
                    >
                        {isLoading ? (
                            <Icon name="loader" className="gx-spin" />
                        ) : (
                            trans('label.confirm')
                        )}
                    </Button>
                </>
            }
        >
            <>
                <Alert withMarginBottom style="warning">
                    {trans('label.action_cannot_be_reverted')}
                </Alert>
                <span>{trans('label.are_you_sure_mark_all_reads')}</span>
            </>
        </Modal>
    );
};
