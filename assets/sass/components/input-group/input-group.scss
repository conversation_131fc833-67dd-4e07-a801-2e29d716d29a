@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$root-input-group: gx-input-group;

.#{$root-input-group} {
  display: flex;
  position: relative;

  input {
    min-width: 0;
  }

  > * {
    & + * {
      margin-left: -0.1rem;
      border-radius: 0;
    }

    &:first-child:not(last-child) {
      border-radius: radius(sm) 0 0 radius(sm);
    }

    &:last-child:not(first-child) {
      border-radius: 0 radius(sm) radius(sm) 0;
    }

    &:not(.#{$root-input-group}__icon) {
      flex-grow: 1;
    }
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    min-width: ($gx-unit-size * 5);
    height: ($gx-unit-size * 5);
    padding: 0 space(sm);
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);
  }

  &__delete {
    position: absolute;
    right: space(sm);
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: color(content-medium);
    font-size: ($gx-unit-size * 2);
    opacity: 0.6;

    &:hover {
      opacity: 1;
    }
  }
}
