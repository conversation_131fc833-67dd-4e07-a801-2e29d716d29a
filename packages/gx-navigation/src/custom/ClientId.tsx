import { Icon } from '@gx-design/icon';
import { Popover } from '@gx-design/popover';
import { useMediaMatch } from '@gx-design/use-media-match';
import React from 'react';
import { useAgencyContext } from '../shared/components/AgencyContext';
import { trans } from '../shared/translations';

export const ClientId: React.FC = () => {
  const { agencyId, adPortal } = useAgencyContext();
  const isDesktop = useMediaMatch('largeDesktop');

  return agencyId && !isDesktop ? (
    <div className='gx-head-section__agency-id'>
      <span className='gx-is-hidden-xsm-down'>{trans('label.id_customer')} </span>
      <span className='code'>{agencyId}</span>
      <span className='gx-tip popover-help popover-help--flex'>
        <Popover
          title={''}
          onEdge={false}
          large={false}
          position='bottomRight'
          content={
            <div
              dangerouslySetInnerHTML={{
                __html: trans('contract.popover.agency_id', {
                  AD_PORTAL: adPortal,
                }),
              }}
            />
          }
        >
          <Icon name='info-circle--active' className='gx-icon--info' />
        </Popover>
      </span>
    </div>
  ) : null;
};
