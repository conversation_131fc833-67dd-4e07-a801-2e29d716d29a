import * as React from 'react';
import { useContext } from 'react';
import { ApiCallOnVisibleRefContext } from './ApiCallOnVisibleProvider';


export const useApiCallOnVisibleContext = () => {
    const context = useContext(ApiCallOnVisibleRefContext);
    if (!context) {
        throw new Error(
            'useApiCallOnVisibleContext must be used within a ApiCallOnVisibleProvider'
        );
    }
    return context;
};
