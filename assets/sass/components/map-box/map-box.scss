// ==========================================================================
// Map box - Components
// ==========================================================================
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.gx-map-box {
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);

  &__image {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 20rem;

    img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  &__content {
    display: flex;
    justify-content: space-between;
    padding: space(md);
  }

  &__adress {
    span {
      display: block;
    }
  }

  &--marginBottom {
    margin-bottom: space(xl);
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    &__image {
      height: 32rem;
    }
  }
}
