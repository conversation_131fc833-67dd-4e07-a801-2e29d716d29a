import { CrmAutocomplete } from '../../../js/commons/gtx-react/components/CrmAutocomplete/CrmAutocomplete';
import { Icon } from '@gx-design/icon';
import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { useCombobox } from 'downshift';

/**
 * CrmAutocomplete is a composite component for creating searchable dropdown interfaces.
 * It includes input field, suggestions dropdown, loading states, highlight text, and reset functionality.
 */
const meta: Meta<typeof CrmAutocomplete> = {
    title: 'CRM/CrmAutocomplete',
    component: CrmAutocomplete,
    parameters: {
        componentSubtitle: 'Component',
        componentTitle: 'CrmAutocomplete',
        componentSlug: 'CrmAutocomplete',
        componentImport: 'CrmAutocomplete',
        packageName: 'gtx-react/components/CrmAutocomplete/CrmAutocomplete',
        docs: {
            description: {
                component: `
CrmAutocomplete is a compound component that provides autocomplete functionality with the following features:
- Input field with selection and error states
- Dropdown suggestions with portal rendering
- Text highlighting for search terms
- Loading indicator
- Reset button functionality
- Keyboard navigation support via downshift

## Sub-components:
- **CrmAutocomplete.Input**: Input field with isSelected and isError props
- **CrmAutocomplete.Suggestions**: Portal-rendered dropdown with isOpen prop
- **CrmAutocomplete.HighlightText**: Text component that highlights matching terms
- **CrmAutocomplete.Loading**: Loading spinner component
- **CrmAutocomplete.ResetButton**: Button to clear the input
                `,
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        children: {
            description:
                'Child components to render inside the autocomplete container',
            control: { type: 'text' },
        },
    },
};

export default meta;

type Story = StoryObj<typeof CrmAutocomplete>;

// Mock data for demonstration
const mockSuggestions = [
    { id: 1, name: 'Rome, Italy', type: 'city' },
    { id: 2, name: 'Via Roma 123, Milan', type: 'address' },
    { id: 3, name: 'Property #ROC123', type: 'code' },
    { id: 4, name: 'Central Rome Zone', type: 'zone' },
    { id: 5, name: 'REF-2024-001', type: 'ref' },
    { id: 6, name: 'Search for "apartment"', type: 'search' },
];

// Simulate API call delay
const mockSearch = async (query: string) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    return mockSuggestions.filter((item) =>
        item.name.toLowerCase().includes(query.toLowerCase())
    );
};

const IconType = ({ type }: { type: string }) => {
    switch (type) {
        case 'address':
        case 'city':
        case 'zone':
            return <Icon name="marker" />;
        case 'search':
            return <Icon name="lens" />;
        case 'code':
        case 'ref':
            return <Icon name="home" />;
        default:
            return <Icon name="lens" />;
    }
};

const DefaultComponent = (args: any) => {
    const [inputValue, setInputValue] = useState('');
    const [suggestions, setSuggestions] = useState<typeof mockSuggestions>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedItem, setSelectedItem] = useState<
        (typeof mockSuggestions)[0] | null
    >(null);

    const { isOpen, getInputProps, getMenuProps, getItemProps, reset } =
        useCombobox({
            items: suggestions,
            inputValue,
            selectedItem,
            onInputValueChange: async ({ inputValue }) => {
                setInputValue(inputValue || '');
                if (inputValue && inputValue.length > 1) {
                    setIsLoading(true);
                    const results = await mockSearch(inputValue);
                    setSuggestions(results);
                    setIsLoading(false);
                }
            },
            onSelectedItemChange: ({ selectedItem }) => {
                setSelectedItem(selectedItem || null);
            },
            itemToString: (item) => item?.name || '',
        });

    return (
        <CrmAutocomplete {...args}>
            <CrmAutocomplete.Input
                {...getInputProps({
                    placeholder:
                        'Search for properties, addresses, or codes...',
                })}
                isSelected={!!selectedItem}
            />

            <Icon
                className={`crm-autocomplete__iconSearch ${
                    inputValue ? 'is-hidden' : ''
                }`}
                name="lens"
            />

            <CrmAutocomplete.Loading isLoading={isLoading} />

            <CrmAutocomplete.ResetButton
                onClick={() => {
                    reset();
                    setSelectedItem(null);
                    setSuggestions([]);
                }}
                hasInput={!!inputValue}
            />

            <CrmAutocomplete.Suggestions
                isOpen={isOpen && inputValue.length > 0}
            >
                <ul {...getMenuProps()}>
                    {suggestions.map((item, index) => (
                        <li key={item.id} {...getItemProps({ item, index })}>
                            <IconType type={item.type} />
                            <CrmAutocomplete.HighlightText
                                text={item.name}
                                term={inputValue}
                            />
                            {item.type === 'search' && (
                                <>
                                    <Icon
                                        className="crm-autocomplete__enterIcon"
                                        name="arrow-enter"
                                    />
                                    <span className="crm-autocomplete__enterText">
                                        Press Enter
                                    </span>
                                </>
                            )}
                        </li>
                    ))}
                </ul>
            </CrmAutocomplete.Suggestions>
        </CrmAutocomplete>
    );
};

export const Default: Story = {
    render: DefaultComponent,
};

export const WithSelectedState: Story = {
    render: (args) => {
        return (
            <CrmAutocomplete {...args}>
                <CrmAutocomplete.Input
                    placeholder="Property already selected"
                    value="Via Roma 123, Milan"
                    isSelected={true}
                    readOnly
                />

                <CrmAutocomplete.ResetButton
                    onClick={() => {}}
                    hasInput={true}
                />
            </CrmAutocomplete>
        );
    },
};

export const WithErrorState: Story = {
    render: (args) => {
        return (
            <CrmAutocomplete {...args}>
                <CrmAutocomplete.Input
                    placeholder="Search with error state"
                    isSelected={false}
                    isError={true}
                />

                <Icon className="crm-autocomplete__iconSearch" name="lens" />
            </CrmAutocomplete>
        );
    },
};

export const LoadingState: Story = {
    render: (args) => {
        return (
            <CrmAutocomplete {...args}>
                <CrmAutocomplete.Input
                    placeholder="Searching..."
                    value="rom"
                    isSelected={false}
                />

                <CrmAutocomplete.Loading isLoading={true} />

                <CrmAutocomplete.ResetButton
                    onClick={() => {}}
                    hasInput={true}
                />
            </CrmAutocomplete>
        );
    },
};

export const HighlightTextShowcase: Story = {
    render: (args) => {
        const examples = [
            { text: 'Rome, Italy', term: 'rome' },
            { text: 'Via Roma 123, Milan', term: 'via' },
            { text: 'Property #ROC123', term: 'ROC' },
            { text: 'Central Rome Zone', term: 'Rome' },
            { text: 'No matches here', term: 'xyz' },
        ];

        return (
            <div style={{ padding: '20px' }}>
                <h3>CrmAutocomplete.HighlightText Examples</h3>
                {examples.map((example, index) => (
                    <div
                        key={index}
                        style={{
                            marginBottom: '10px',
                            padding: '10px',
                            border: '1px solid #ccc',
                        }}
                    >
                        <strong>Text:</strong> "{example.text}" |{' '}
                        <strong>Term:</strong> "{example.term}"
                        <br />
                        <strong>Result:</strong>{' '}
                        <CrmAutocomplete.HighlightText
                            text={example.text}
                            term={example.term}
                        />
                    </div>
                ))}
            </div>
        );
    },
};

export const AllStates: Story = {
    render: (args) => {
        return (
            <div
                style={{
                    display: 'grid',
                    gap: '20px',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                    padding: '20px',
                    minHeight: '30vh',
                }}
            >
                <div>
                    <h4>Normal State</h4>
                    <CrmAutocomplete>
                        <CrmAutocomplete.Input
                            placeholder="Normal input"
                            isSelected={false}
                        />
                        <Icon
                            className="crm-autocomplete__iconSearch"
                            name="lens"
                        />
                    </CrmAutocomplete>
                </div>

                <div>
                    <h4>Selected State</h4>
                    <CrmAutocomplete>
                        <CrmAutocomplete.Input
                            value="Selected item"
                            isSelected={true}
                            readOnly
                        />
                        <CrmAutocomplete.ResetButton
                            onClick={() => {}}
                            hasInput={true}
                        />
                    </CrmAutocomplete>
                </div>

                <div>
                    <h4>Error State</h4>
                    <CrmAutocomplete>
                        <CrmAutocomplete.Input
                            placeholder="Error input"
                            isSelected={false}
                            isError={true}
                        />
                        <Icon
                            className="crm-autocomplete__iconSearch"
                            name="lens"
                        />
                    </CrmAutocomplete>
                </div>

                <div>
                    <h4>Loading State</h4>
                    <CrmAutocomplete>
                        <CrmAutocomplete.Input
                            placeholder="Loading..."
                            value="searching"
                            isSelected={false}
                        />
                        <CrmAutocomplete.Loading isLoading={true} />
                        <CrmAutocomplete.ResetButton
                            onClick={() => {}}
                            hasInput={true}
                        />
                    </CrmAutocomplete>
                </div>
            </div>
        );
    },
};

Default.storyName = 'Default';
WithSelectedState.storyName = 'Selected State';
WithErrorState.storyName = 'Error State';
LoadingState.storyName = 'Loading State';
HighlightTextShowcase.storyName = 'Highlight Text Examples';
AllStates.storyName = 'All States Overview';
