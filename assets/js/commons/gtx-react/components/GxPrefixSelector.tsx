import { ActionList, ActionListItem } from '@gx-design/action-list';
import { Dropdown } from '@gx-design/dropdown';
import { checkAndOverrideFlagIcon } from 'gtx-react/utils/checkAndOverrideFlagIcon';
import { CountryCallingCode } from 'gtx-react/utils/extractProfileData';
import React, { FC, useMemo } from 'react';

type PrefixSelctorProps = {
    value: CountryCallingCode;
    name: string;
    onChange: (_newShortCode: CountryCallingCode | undefined) => void;
};

const extractCountryCallingCodes = (): Array<CountryCallingCode> => {
    const elem = document.getElementById('country-calling-codes');
    if (!elem) {
        throw new Error('country-calling-codes list element not found');
    } else {
        try {
            const codeList = JSON.parse(elem.innerHTML);
            return codeList;
        } catch (error) {
            throw new Error(
                'Problems parsing json in country-calling-codes element'
            );
        }
    }
};

export const GxPrefixSelector: FC<PrefixSelctorProps> = ({
    value,
    onChange,
}) => {
    const countryCallingCodes = useMemo(() => extractCountryCallingCodes(), []);

    return (
        <Dropdown
            buttonClassName="gx-select"
            position="topLeft"
            showCaret
            maxHeight
            buttonContent={
                <>
                    <div
                        className={`flag-icon flag-icon-${checkAndOverrideFlagIcon(
                            value.shortCode || ''
                        )}`}
                    />
                    <span>{value.callingCode}</span>
                </>
            }
        >
            <ActionList>
                {countryCallingCodes.map((elem, idx) => (
                    <ActionListItem
                        key={`al-country-${idx}`}
                        startElement={
                            <div
                                className={`flag-icon flag-icon-${checkAndOverrideFlagIcon(
                                    elem.shortCode || ''
                                )}`}
                            />
                        }
                        text={`${elem.name} ${elem.callingCode}`}
                        active={elem.shortCode === value}
                        onClick={() => onChange(elem)}
                    />
                ))}
            </ActionList>
        </Dropdown>
    );
};
