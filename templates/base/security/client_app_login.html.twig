{% extends 'base.html.twig' %}

{% block rawContent %}
    <div class="login-wrapper">
        <div class="login-content-wrap">
            <div class="login-content">
                <form class="form-account" action="{{ path('getrix_app_login_check') }}" method="post">
                    <h1 class="gx-title-1">{{ "label.client_app_login.login_form.title"|trans({'%AD_PORTAL%': gtxConstants.AD_PORTAL}, gtxConstants.DOMAIN_CMS) }}</h1>
                    <p>{{ "login.form.credentials"|trans({}, gtxConstants.DOMAIN_CMS) }}</p>
                    {% if error %}
                        <div class="gx-alert gx-alert--margin gx-alert--error">
                            <svg class="gx-icon gx-icon--negative" xmlns="http://www.w3.org/2000/svg" id="cross-circle" viewBox="0 0 24 24" fill-rule="evenodd">
                                <path d="M7.22 7.22a.75.75 0 0 1 1.06 0L12 10.94l3.72-3.72a.75.75 0 1 1 1.06 1.06L13.06 12l3.72 3.72a.75.75 0 0 1-1.06 1.06L12 13.06l-3.72 3.72a.75.75 0 1 1-1.06-1.06L10.94 12 7.22 8.28a.75.75 0 0 1 0-1.06Z"/>
                                <path d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm10-8.5a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17Z"/>
                            </svg>
                            <span>
                                {% if error.messageKey == 'Agenzia non attiva.' %}
                                    {{ "login.form.error.inactive"|trans({'%TEL_SERVIZIO_CLIENTI%': gtxConstants.TEL_SERVIZIO_CLIENTI}, gtxConstants.DOMAIN_CMS)|raw }}
                                {% elseif error.messageKey == 'Utente non attivo.' %}
                                    {{ "login.form.error.user_disabled"|trans({}, gtxConstants.DOMAIN_CMS) }}
                                {% else %}
                                    {{ error.messageKey|trans(error.messageData, 'security') }}
                                {% endif %}
                            </span>
                        </div>
                    {% endif %}
                    {% if backurl %}
                        <input type="hidden" name="_target_path" value="{{ backurl }}">
                    {% endif %}
                    <input type="hidden" name="_failure_path" value="{{ app.request.uri }}" />
                    <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
                    <div class="gx-input-wrapper">
                        <label class="gx-sr-only" for="username">{{ "label.mail"|trans({}, gtxConstants.DOMAIN_CMS) }}</label>
                        <div class="gx-input-addon-wrapper">
                            <div class="gx-input-addon gx-input-addon--withIcon">
                                <svg class="gx-icon" xmlns="http://www.w3.org/2000/svg" id="mail" viewBox="0 0 24 24" fill-rule="evenodd">
                                    <path d="M4.75 5A2.75 2.75 0 0 0 2 7.75v8.5A2.75 2.75 0 0 0 4.75 19h14.5A2.75 2.75 0 0 0 22 16.25v-8.5A2.75 2.75 0 0 0 19.25 5H4.75Zm-.068 1.502L4.75 6.5h14.5a.76.76 0 0 1 .068.002l-7.16 5.896a.25.25 0 0 1-.317 0L4.682 6.502Zm-1.156.991a1.256 1.256 0 0 0-.026.257v8.5c0 .*************.177l4.906-4.905-4.892-4.03ZM4.75 17.5c-.06 0-.12-.004-.177-.012l5.008-5.008 1.306 1.075a1.75 1.75 0 0 0 2.226 0l1.306-1.075 5.008 5.008a1.258 1.258 0 0 1-.177.012H4.75Zm15.738-1.073c.008-.058.012-.117.012-.177v-8.5c0-.088-.01-.174-.026-.257l-4.892 4.029 4.906 4.905Z"/>
                                </svg>
                            </div>
                            <input type="email" id="username" name="username" class="gx-input gx-input--withAddon" placeholder="{{ "label.mail_address"|trans({}, gtxConstants.DOMAIN_CMS) }}" required autofocus value="{{ username }}" />
                        </div>
                    </div>
                    <div class="gx-input-password-recovery-wrapper">
                        <label class="gx-sr-only" for="password">{{ "label.password"|trans({}, gtxConstants.DOMAIN_CMS) }}</label>
                        <div class="gx-input-addon-wrapper">
                            <div class="gx-input-addon gx-input-addon--withIcon">
                                <svg class="gx-icon" xmlns="http://www.w3.org/2000/svg" id="lock" viewBox="0 0 24 24" fill-rule="evenodd">
                                    <path d="M9 6.5a3 3 0 1 1 6 0V11H9V6.5ZM7.5 11V6.5a4.5 4.5 0 0 1 9 0V11h.75A2.75 2.75 0 0 1 20 13.75v5.5A2.75 2.75 0 0 1 17.25 22H6.75A2.75 2.75 0 0 1 4 19.25v-5.5A2.75 2.75 0 0 1 6.75 11h.75Zm-2 2.75c0-.69.56-1.25 1.25-1.25h10.5c.69 0 1.25.56 1.25 1.25v5.5c0 .69-.56 1.25-1.25 1.25H6.75c-.69 0-1.25-.56-1.25-1.25v-5.5Z"/>
                                </svg>
                            </div>
                            <input type="password" id="password" name="password" class="gx-input gx-input--withAddon" placeholder="{{ "login.form.placeholder.password"|trans({}, gtxConstants.DOMAIN_CMS) }}" required />
                        </div>
                    </div>
                    <input type="hidden" name="jwt_token" value="{{ jwtToken }}">
                    <div class="form-account__footer clearfix">
                        <button type="submit" class="gx-button gx-button--accent gx-button--fullWidth invia">
                            {{ "btn.label.login"|trans({}, gtxConstants.DOMAIN_CMS) }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ webpack_asset('base/login') }}
{% endblock %}
