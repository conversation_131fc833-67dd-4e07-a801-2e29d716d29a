import * as React from 'react';
import Fill from './Fill';
import Manager, { Component as ManagerComponent } from '../Manager';
import { SlotFillContext } from './Provider';

export interface Props {
    name: string | Symbol;
    fillChildProps?: { [key: string]: any };
    children?:
        | ((elements: React.ReactNode[]) => React.ReactNode)
        | React.ReactNode;
}

export function Slot(props: Props) {
    const { name, fillChildProps, children } = props;
    const context = React.useContext(SlotFillContext);

    const [components, setComponents] = React.useState<ManagerComponent[]>([]);

    React.useEffect(() => {
        if (!context) return;
        const handleComponentChange = (comps: ManagerComponent[]) =>
            setComponents(comps);

        context.manager.onComponentsChange(name, handleComponentChange);

        // Cleanup
        return () => {
            context.manager.removeOnComponentsChange(
                name,
                handleComponentChange
            );
        };
    }, [context, name]);

    // Ricostruisci fills come getter
    const fills: Fill[] = React.useMemo(
        () => components.map((c) => c.fill),
        [components]
    );

    // Aggrega gli elementi come nel vecchio render
    const aggElements: React.ReactNode[] = React.useMemo(() => {
        const result: React.ReactNode[] = [];
        components.forEach((component, index) => {
            const { fill, children: fillChildren } = component;

            if (fillChildProps) {
                const fillChildProps2 = Object.keys(fillChildProps).reduce(
                    (acc, key) => {
                        const value = fillChildProps[key];
                        acc[key] =
                            typeof value === 'function'
                                ? () => value(fill, fills)
                                : value;
                        return acc;
                    },
                    {} as Record<string, any>
                );

                fillChildren.forEach((child, index2) => {
                    if (
                        typeof child === 'number' ||
                        typeof child === 'string'
                    ) {
                        throw new Error('Only element children will work here');
                    }
                    result.push(
                        React.cloneElement(child, {
                            key: index.toString() + index2.toString(),
                            ...fillChildProps2,
                        })
                    );
                });
            } else {
                fillChildren.forEach((child, index2) => {
                    if (
                        typeof child === 'number' ||
                        typeof child === 'string'
                    ) {
                        throw new Error('Only element children will work here');
                    }
                    result.push(
                        React.cloneElement(child, {
                            key: index.toString() + index2.toString(),
                        })
                    );
                });
            }
        });
        return result;
    }, [components, fillChildProps, fills]);

    if (typeof children === 'function') {
        const element = children(aggElements);
        if (React.isValidElement(element) || element === null) {
            return element;
        } else {
            throw new Error(
                `Slot rendered with function must return a valid React Element.`
            );
        }
    } else {
        return <>{aggElements}</>;
    }
}

export default Slot;
