import type { IconProps } from '@gx-design/icon';
import type { createAction } from './config/header';
import { Routes } from './routes';
import { BadgeProps } from '@gx-design/badge';
import type { ContextMapKey } from './menu/FeaturesProviders';

export type AnyString<T> = T | (string & NonNullable<unknown>);

export type ImageProps = {
  alt?: string;
  imageUrl: string;
};

export type Action = ReturnType<typeof createAction>;

export type PathnameWithoutTrailingSlash = `/${string}`;
export type PathnameStartingWithHash = `#${string}`;
export type Pathname = PathnameWithoutTrailingSlash | PathnameStartingWithHash;

export type BaseHeaderConfig = {
  /**
   * The title of the header.
   * @example ['parentSection', 'section'] | 'section' | null // undefined is used for the homepage.
   */
  section: string | [string, string] | null;
  actions?: Action[];
  mobileHidden?: boolean;
};

export type MenuConfigItem = Partial<{
  hidden: boolean;
  href: AnyString<Routes>;
  icon: IconProps['name'];
  tooltip: string;
  label: string;
  group: string;
  badge: {
    style: BadgeProps['style'];
    text: string;
  };
  notificationBadge: {
    /**
     * it is called number because it match the NotificationBadge component prop
     * but it is a string because the prop accept string and in app could be e.g '99+'
     * and not only number
     */
    number: string;
  };
  contexts?: ContextMapKey[];
  expanded?: boolean;
}>;

export type BaseMenuConfig = {
  item?: MenuConfigItem;
  subMenu?: MenuConfig;
  /**
   * it is an edge case, but it is possible to have a dynamic url
   */
  params?: Record<string, string>;
};

export type RootMenuConfig = {
  [urlMatch: Pathname]: BaseMenuConfig;
};

export type MenuConfig = BaseMenuConfig & RootMenuConfig;

export type SearchParamType = `?${string}`;
export type HeaderConfigWithParams = Record<SearchParamType, BaseHeaderConfig>;

export type HeaderConfiguration = Record<Routes, BaseHeaderConfig | HeaderConfigWithParams>;

export type HeaderConfigMap = Record<Routes, BaseHeaderConfig>;

export type GxNavigationConfig = {
  [urlMatch: Pathname]: BaseHeaderConfig & MenuConfig;
};

export const isMenuItemDefined = (item: MenuConfig): item is MenuConfigItem => {
  return item !== undefined;
};

export type CustomMenuComponent = {
  customMenuComponent: 'profile' | 'customerCare';
};

export type CountersApiRespObj = {
  unreadThreads: number;
  newMatches: number;
  newAgencyActiveSearches: number;
};

/**
 * The feature flags for the GxNavigation component.
 * These flags are used to enable or disable certain features in the component.
 */
export type GxNavigationFeatureFlags = {
  addPropertyEnabled: boolean;
  beamerNewsEnabled: boolean;
  saleforceLiveChatEnabled: boolean;
  helpJuiceEnabled: boolean;
};

declare global {
  interface Window {
    globalVars: any;
  }
}
