@use './mixins' as mix;
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.nd-carousel {
  width: 100%;
  height: 100%;
  overflow: hidden;
  contain: content;

  &Wrapper {
    height: 100%;
    flex-shrink: 0;
    position: relative;
    box-sizing: content-box;
    overflow: hidden;

    &--outsideArrow {
      @include mix.in-carousel-outside-arrow();
    }

    &--withItemSpace {
      @include mix.in-carousel-item-spaces();
    }
  }

  // EXPERIMENTAL:
  // keep the same layout/style to use native scroll and handle scrolling with js where's necessary
  // note: these styles override the original package design (for non-touch device)
  // [1] scroll-snapping, as defined in the original package
  // [2] hide the scrollbars
  &__content[class] {
    position: relative;
    overflow-x: auto;
    overflow-y: hidden;
    height: 100%;
    overscroll-behavior-x: contain;
    overscroll-behavior-y: auto;
    scroll-behavior: smooth;
    scroll-snap-type: x mandatory;
    scroll-snap-points-x: repeat(90vw); // [1] for Firefox 39 and Safari 9 support
    -webkit-overflow-scrolling: touch; // [1] for iOS devices
    -ms-overflow-style: none; // [2] IE and Edge
    scrollbar-width: none; // [2] Firefox

    transition: transform 250ms cubic-bezier(0.4, 0, 0.2, 1);
    display: block;
    top: 0;
    left: 0;
    font-size: 0;
    white-space: nowrap;

    &::-webkit-scrollbar {
      display: none; // [2] Chrome, Safari, Opera, Webkit Edge
    }
  }

  &__item {
    position: relative;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    scroll-snap-align: start;
    scroll-snap-stop: always;
    white-space: normal;
    width: 100%;
    height: 100%;
    padding: 0.1rem;
    flex: 0 0 auto;

    img {
      position: absolute;
      max-width: 100%;
      max-height: 100%;
    }

    .is-horizontal {
      height: auto;
      width: 100%;
    }

    .is-vertical {
      width: auto;
      height: 100%;
    }
  }

  // overrides button style
  &__arrowIcon {
    user-select: none;
    appearance: none;
    border: none;
    width: $gx-unit-size * 5;
    height: $gx-unit-size * 5;
  }

  &__arrow {
    backface-visibility: hidden;
    contain: content;

    &--prev {
      left: 0;
    }

    &--next {
      right: 0;
    }

    &.is-disabled {
      opacity: 0.5;
      pointer-events: none;
    }
  }

  &__dotsNavigation {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    @include z-index(base, 1);

    // the dot hasn't  visisble styles
    // the actual visible dot is the ::before pseudo element
    // the animated dot is the ::after pseudo element
    > * {
      width: 0.4rem;
      height: 0.4rem;
      position: relative;
      border-radius: radius(rounded);

      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: color(content-action);
        border-radius: inherit;
        contain: strict;
      }

      &::before {
        opacity: 0.5;
      }

      // 1. the dot size is 3x (the starting dot, the end and the space between)
      // it's moved to the right
      &::after {
        // @include motion.nd-motion-transition(
        //     null,
        //     map.get(motion.$nd-motion-duration-in, slow)
        // );

        transform: translateX(100%) scaleX(3); // [1]
        opacity: 0;
      }

      &.is-current {
        // current dot place above its sibling
        &::after {
          opacity: 1;
          transform: translateX(0) scaleX(1);
        }

        // all the dots after the current are placed on their left, again 3x sized
        & ~ *:after {
          transform: translateX(-100%) scaleX(3);
        }
      }

      // the space between dots is equal to the dot' size
      &:not(:last-child) {
        margin-right: space(sm);
      }
    }
  }
}

.carousel-auctions {
  height: 30rem;

  &-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;

    .gx-summary-list {
      margin-top: space(md);
    }
  }
}
