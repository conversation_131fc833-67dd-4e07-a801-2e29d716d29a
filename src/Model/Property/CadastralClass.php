<?php

declare(strict_types=1);

namespace App\Model\Property;

class CadastralClass
{
    public int $id;
    public ?string $name = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }
}
