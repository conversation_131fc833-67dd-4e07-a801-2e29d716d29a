// ==========================================================================
// Page - Components/Page
// ==========================================================================
@use '../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$sidebar-width: 32rem;

.gx-page {
  &--withSidebar {
    display: flex;
    height: 100%;
    transition: 0.25s;
    flex-direction: column;
  }

  &--sidebarOpen {
    transition: 0.25s;
    width: 100%;

    .research-actionbar {
      transition: 0.25s;
      width: calc(100% - #{$sidebar-width});
    }

    .gx-page-sidebar {
      transform: translateX(0);
      transition: transform 0.25s;

      &__headerClose {
        display: flex;
        align-items: center;
        color: color(content-low);
        font-size: 1.8rem;
        cursor: pointer;
      }
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    flex-direction: row;
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    &--sidebarOpen {
      width: calc(100% - #{$sidebar-width});
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    &--sidebarOpen {
      width: 100%;
    }
  }

  &__info,
  &__head {
    padding: space(md);
    border-bottom: 0.1rem solid color(border-main);

    @include media('screen', '>=#{breakpoint(sm)}') {
      padding-left: space(xl);
      padding-right: space(xl);
    }
  }
}

.fixed-height .gx-page--sidebarOpen {
  .gx-head-section__actions {
    & > .gx-button {
      display: none;
    }
  }
}

.gx-page-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;
  padding-top: 0;

  @include media('screen', '>=#{breakpoint(md)}') {
    padding-top: 0;
  }

  .gx-head-section__title {
    @include media('screen', '<#{breakpoint(sm)}') {
      display: block !important;
      max-width: 14rem;
      height: auto;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  .gx-head-section__actions {
    & > * + * {
      margin-left: space(md) !important;
    }

    @include media('screen', '<#{breakpoint(md)}') {
      .gx-button--dropdown {
        min-width: auto;
      }
    }

    @include media('screen', '<#{breakpoint(sm)}') {
      display: flex !important;
      justify-content: flex-end;
    }
  }
}

// Sidebar
.gx-page-sidebar {
  display: flex;
  flex: 1;
  flex-direction: column;
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  transform: translateX(100%);
  transition: transform 0.25s;
  @include z-index(sidebar);
  height: 100%;
  border-left: 0.1rem solid color(border-main);
  background-color: color(background-main);

  // Sidebar header
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 space(md);
    box-shadow: elevation(fixed-top);

    &Close {
      display: block;
    }

    @include media('screen', '>=#{breakpoint(md)}') {
      box-shadow: 0 -0.1rem 0 color(border-main) inset;

      &Close {
        display: none;
      }
    }

    @include media('screen', '>=#{breakpoint(sm)}') {
      padding: 0 space(lg);
    }
  }

  // Sidebar content
  &__content {
    height: 100%;
    overflow: auto;
  }

  // Sidebar box
  &__box {
    border-top: space(sm) solid color(background-alt);
    padding: space(md);

    &:first-child {
      border-top: 0;
    }

    @include media('screen', '>=#{breakpoint(sm)}') {
      padding: space(lg);
    }
  }

  &__boxHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: space(md);

    h5 {
      @include typography(title-2);
      margin: 0;
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    max-width: $sidebar-width;
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    position: relative;
    @include z-index(base, 9);
    transform: none;
    width: $sidebar-width;
  }
}
