FROM icr.pepita.io/qa/docker-images/php/7.4-fpm-nginx:1.0

# Add files to image
ADD vhost/it.conf /etc/nginx/sites-available/getrix.conf
ADD vhost/es.conf /etc/nginx/sites-available/indomiopro-es.conf
ADD vhost/lu.conf /etc/nginx/sites-available/immotop-lu.conf
ADD vhost/hr.conf /etc/nginx/sites-available/nekretnine-hr.conf
ADD vhost/rs.conf /etc/nginx/sites-available/kuce-rs.conf
ADD docker-entrypoint.sh /usr/bin/docker-entrypoint.sh
ADD php-ini-overrides.ini /etc/php/7.4/fpm/conf.d/99-overrides.ini
ADD php-ini-overrides.ini /usr/local/etc/php/conf.d/99-overrides.ini

RUN ln -s /etc/nginx/sites-available/getrix.conf /etc/nginx/sites-enabled/it.conf
RUN ln -s /etc/nginx/sites-available/indomiopro-es.conf /etc/nginx/sites-enabled/es.conf
RUN ln -s /etc/nginx/sites-available/immotop-lu.conf /etc/nginx/sites-enabled/lu.conf
RUN ln -s /etc/nginx/sites-available/nekretnine-hr.conf /etc/nginx/sites-enabled/hr.conf
RUN ln -s /etc/nginx/sites-available/kuce-rs.conf /etc/nginx/sites-enabled/rs.conf

RUN mkdir -m 777 -p /var/log/apps

RUN curl https://get.volta.sh | bash
ENV VOLTA_HOME "/root/.volta"
ENV PATH "$VOLTA_HOME/bin:$PATH"
RUN volta setup

CMD ["nginx", "-g", "daemon off;"]

ENTRYPOINT ["/usr/bin/docker-entrypoint.sh"]

# Clean up APT when done.
RUN apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/
