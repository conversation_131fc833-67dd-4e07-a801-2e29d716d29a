import * as React from 'react';
import { useFormikContext } from 'formik';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { trans } from '@pepita-i18n/babelfish';

interface IImmovisitaScheduleModalFooterProps {
    submitEnabled: boolean;
    onClose: () => void;
    isSubmitting: boolean;
}

export const IvSchModalFooter: React.FC<
    IImmovisitaScheduleModalFooterProps
> = ({ submitEnabled, onClose, isSubmitting }) => {
    const { submitForm } = useFormikContext();

    return (
        <div>
            <Button variant="ghost" onClick={onClose}>
                {trans('label.cancel')}
            </Button>
            <Button
                disabled={!submitEnabled}
                variant="accent"
                type="submit"
                iconOnly={isSubmitting}
                form={'scheduled_visit'}
                onClick={() => submitForm()}
            >
                {isSubmitting ? (
                    <Icon className="gx-spin" name="loader" />
                ) : (
                    trans('label.confirm')
                )}
            </Button>
        </div>
    );
};
