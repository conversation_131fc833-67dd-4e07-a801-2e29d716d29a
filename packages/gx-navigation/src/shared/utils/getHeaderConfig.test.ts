import { describe, expect, it } from 'vitest';
import { createConfig } from '../../config/header';
import { getHeaderConfiguration } from './getHeaderConfig';

describe('getHeaderConfiguration', () => {
  const headerConfiguration = createConfig({
    adPortal: 'immobiliare.it',
    serviceRemoteVisit: 'https://www.example.com',
    featureFlags: {
      addPropertyEnabled: true,
      beamerNewsEnabled: true,
      helpJuiceEnabled: true,
      saleforceLiveChatEnabled: true,
    },
  });

  it('return the home configuration for the root path', () => {
    const url = new URL('http://example.com/');
    const result = getHeaderConfiguration(headerConfiguration, url);
    expect(result).toStrictEqual({
      section: null,
    });
  });

  it('return the configuration for all path having search params id', () => {
    const url = new URL('http://example.com/v2/agenda/appuntamento?id=123');
    const result = getHeaderConfiguration(headerConfiguration, url);

    expect(result).toStrictEqual({
      section: 'label.edit_appointment',
    });
  });

  it('return the configuration for all path having search params multimedia=vt', () => {
    const url = new URL('http://example.com/servizi_extra.php?multimedia=vt');
    const result = getHeaderConfiguration(headerConfiguration, url);

    expect(result).toStrictEqual({
      section: ['label.virtual_tour_3d', 'label.ads'],
    });
  });
});
