import { useState } from 'react';
import { createCustomerApi, updateCustomerApi } from './web-api/api/customer';

export const useCustomerModal = () => {
    const [customerModalOpen, setCustomerModalOpen] = useState<boolean>(false);

    const [isLoadingCustomerForm, setIsLoadingCustomerForm] = useState(false);

    const handleEditCustomer = (
        data,
        config: {
            successCallback?: () => void;
            errorCallback?: () => void;
        }
    ) => {
        setIsLoadingCustomerForm(true);
        updateCustomerApi(data)
            .then(() => {
                if (config?.successCallback) {
                    config.successCallback();
                }
                setCustomerModalOpen(false);
                setIsLoadingCustomerForm(false);
            })
            .catch(() => {
                if (config?.errorCallback) {
                    config?.errorCallback();
                }
                setIsLoadingCustomerForm(false);
            });
    };

    const handleCreateCustomer = (
        data,
        config: {
            successCallback?: (resp) => void;
            errorCallback?: () => void;
        }
    ) => {
        setIsLoadingCustomerForm(true);
        createCustomerApi(data)
            .then((resp) => {
                setIsLoadingCustomerForm(false);
                setCustomerModalOpen(false);

                if (config?.successCallback) {
                    config.successCallback(resp);
                }
            })
            .catch((error) => {
                setIsLoadingCustomerForm(false);
                if (config?.errorCallback) {
                    config.errorCallback();
                }
            });
    };

    return {
        customerModalOpen,
        setCustomerModalOpen,
        isLoadingCustomerForm,
        handleEditCustomer,
        handleCreateCustomer,
    };
};
