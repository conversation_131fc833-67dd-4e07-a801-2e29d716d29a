@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.crm-visibility-summary {
  position: sticky;
  left: 0;
  display: flex;
  flex-wrap: wrap;
  gap: space(sm);
  padding: 0 space(lg);
  margin-bottom: space(sm);

  &__item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: space(sm);
    height: 4.8rem;
    padding: 0 1.2rem;
    border: 1px solid color(border-main);
    border-radius: radius(md);

    &CounterStatus {
      display: flex;
      flex-wrap: nowrap;

      li {
        &:not(:first-child) {
          padding-left: space(sm);
          border-left: 1px solid color(border-main);
        }

        &:not(:last-child) {
          padding-right: space(sm);
        }
      }
    }
  }
}
