export const BASE_PATH = '/immovisita/api';

export const endpoints = {
    LO<PERSON>UP_CATEGORIES: '/immobili/lookup/get-categories',
    LO<PERSON>UP_CONTRACTS: '/immobili/lookup/get-contracts',
    LOOKUP_TYPOLOGIES: '/immobili/lookup/get-tipologies',
    GET_PROPERTY: (propertyId) => `${BASE_PATH}/properties/${propertyId}`,
    GET_PROPERTY_LIST: `${BASE_PATH}/properties`,
    GET_AGENTS: `/api/agents`,
    ROOMS: `${BASE_PATH}/rooms`,
    V1: {
        SCHEDULED_VISIT_VIRTUAL: `/api/scheduled-visits/virtual`,
        SCHEDULED_VISIT_REAL: `/api/scheduled-visits/in-person`,
    },
    // for new messaging, we still need to use old endpoints, for the mail issue (reverse double write)
    // V2: {
    //     SCHEDULED_VISIT_VIRTUAL: `/api/v2/messaging/threads/planned-visit/virtual`,
    //     SCHEDULED_VISIT_REAL: `/api/v2/messaging/threads/planned-visit/in-person`,
    // },
};
