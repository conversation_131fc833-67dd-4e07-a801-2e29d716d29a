<div data-component="pin-verify">
    <h1 class="auth2-box__title gx-title">{{ trans("a2f.insert_pin.title", {}, gtxConstants.DOMAIN_CMS) }}</h1>
    <p class="auth2-box__text gx-subtitle">{{ trans("a2f.insert_pin.text", {}, gtxConstants.DOMAIN_CMS) }}</p>

    <form data-role="form-pin-input">
        <div class="gx-input-password-recovery-wrapper">
            <input class="gx-input" type="text" data-component="pin-input" name="pin{{ randSeed }}" placeholder="{{ trans("a2f.insert_pin.insert_code", {}, gtxConstants.DOMAIN_CMS) }}" autocomplete="off">
            <a class="gx-input-password-recovery" href="javascript:void(0);" data-component="resend-pin">
                {{ trans("a2f.insert_pin.resend", {}, gtxConstants.DOMAIN_CMS) }}
                <span data-role="spinner" class="hidden">
                    <svg class="gx-icon gx-spin"xmlns="http://www.w3.org/2000/svg" id="loader" viewBox="0 0 24 24" fill-rule="evenodd">
                        <path d="M16.445 18.652A8 8 0 0 1 12 20a.75.75 0 0 1 0-1.5A6.5 6.5 0 1 0 5.5 12 .75.75 0 0 1 4 12a8 8 0 1 1 12.445 6.652Z"/>
                        </svg>
                </span>
            </a>
        </div>
        <div data-role="error-message" class="hidden">
            <div class="gx-helper gx-helper--error">
                <svg class="gx-icon gx-icon--negative" xmlns="http://www.w3.org/2000/svg" id="cross-circle" viewBox="0 0 24 24" fill-rule="evenodd">
  <path d="M7.22 7.22a.75.75 0 0 1 1.06 0L12 10.94l3.72-3.72a.75.75 0 1 1 1.06 1.06L13.06 12l3.72 3.72a.75.75 0 0 1-1.06 1.06L12 13.06l-3.72 3.72a.75.75 0 1 1-1.06-1.06L10.94 12 7.22 8.28a.75.75 0 0 1 0-1.06Z"/>
  <path d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm10-8.5a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17Z"/>
</svg>
                <span class="gx-helper__text">

                </span>
            </div>
        </div>
        <button type="button" class="gx-button gx-button--accent gx-button--fullWidth" data-action="submit-code">
            {{ trans("a2f.insert_pin.check", {}, gtxConstants.DOMAIN_CMS) }}
        </button>
    </form>

    <div class="a2f-footer">
        {{ trans("a2f.insert_pin.cant_receive_sms", {}, gtxConstants.DOMAIN_CMS) }}
        <a class="auth2-box__help" href="javascript:void(0);" data-action="show-customer-service">{{ trans("a2f.insert_pin.request_assistance", {}, gtxConstants.DOMAIN_CMS) }}</a>
        {{ trans("label.or", {}, gtxConstants.DOMAIN_CMS) }}
        {% if nPhoneNumbers != 1 %}
            <a class="auth2-box__help" href="/login/a2f/{% if logged_agency %}?agency={{ logged_agency.idAgenzia }}{% endif %}">{{ trans("a2f.insert_pin.change_numer", {}, gtxConstants.DOMAIN_CMS) }}</a>
        {% else %}
            <a class="auth2-box__help" href="/logout">{{ trans("label.back_to_login", {}, gtxConstants.DOMAIN_CMS)|lower }}</a>
        {% endif %}
    </div>
</div>
