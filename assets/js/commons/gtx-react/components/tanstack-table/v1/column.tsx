import {
    Children,
    createContext,
    PropsWithChildren,
    ReactNode,
    useContext,
    useRef,
    useState,
    useLayoutEffect,
} from 'react';
import { createPortal } from 'react-dom';
import { useTableHeaderContext } from '../contexts';
import { useOnClickOutside } from '@gx-design/use-on-click-outside';

const ColumnSortContext = createContext<
    ((isOpen: boolean) => void) | undefined
>(undefined);

/**
 * It is in the Figma, but product team chose not to implement it.
 * Let's keep it for now, in case they change their mind.
 * @returns
 */
export function ColumnSort(props: PropsWithChildren<{ label: ReactNode }>) {
    const [isOpen, setIsOpen] = useState(false);
    const header = useTableHeaderContext();
    const [position, setPosition] = useState({ top: 0, left: 0 });

    const containerRef = useRef<HTMLDivElement>(null);
    const buttonRef = useRef<HTMLButtonElement>(null);

    useOnClickOutside({
        refs: [containerRef, buttonRef],
        handler: () => setIsOpen(false),
        shouldListen: isOpen,
    });

    // Update position when the dropdown is opened
    useLayoutEffect(() => {
        if (isOpen && buttonRef.current) {
            const rect = buttonRef.current.getBoundingClientRect();
            setPosition({
                top: rect.bottom,
                left: rect.left,
            });
        }
    }, [isOpen]);

    if (!header.column.getCanSort() || Children.count(props.children) <= 0) {
        return <>{props.label}</>;
    }

    return header.column.getCanSort() ? (
        <>
            <button
                ref={buttonRef}
                // className="gx-sr-only"
                onClick={() => setIsOpen((prev) => !prev)}
            >
                {props.label}
            </button>
            {isOpen ? (
                <ColumnSortContext.Provider value={setIsOpen}>
                    {createPortal(
                        <div
                            ref={containerRef}
                            className="crm-column-sort__container"
                            style={{
                                position: 'fixed',
                                top: `${position.top}px`,
                                left: `${position.left}px`,
                                zIndex: 1000,
                            }}
                        >
                            {props.children}
                        </div>,
                        document.body
                    )}
                </ColumnSortContext.Provider>
            ) : null}
        </>
    ) : null;
}

const useColumnSortContext = () => {
    const context = useContext(ColumnSortContext);

    if (!context) {
        throw new Error(
            'useColumnSortContext must be used within a ColumnSortContext'
        );
    }

    return context;
};

function ColumnSortElement(
    props: PropsWithChildren<{
        /**
         * * Sort direction
         * * `asc` for ascending
         * * `desc` for descending
         */
        direction: 'asc' | 'desc';
    }>
) {
    const header = useTableHeaderContext();
    const setIsOpen = useColumnSortContext();

    const onChangeDirection =
        props.direction === header.column.getIsSorted()
            ? () => header.column.clearSorting()
            : () => {
                  header.column.toggleSorting(props.direction === 'desc');
              };

    if (!header.column.getCanSort()) {
        return null;
    }

    return (
        <span
            className="crm-column-sort__item"
            onClick={() => {
                setIsOpen(false);
            }}
        >
            <button onClick={onChangeDirection}>{props.children}</button>
        </span>
    );
}

ColumnSort.Item = ColumnSortElement;
ColumnSortElement.displayName = 'ColumnSort.Item';
