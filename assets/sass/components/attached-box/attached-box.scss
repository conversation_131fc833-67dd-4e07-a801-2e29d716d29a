@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.gx-attached-box {
  &__head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: ($gx-unit-size * 5);

    label {
      margin-bottom: 0;
    }

    .gx-character-counter {
      margin-top: 0;
    }
  }

  &__upload {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: space(md);
    background-color: color(background-alt);
    border: 0.2rem dashed color(border-main);

    &--active {
      background-color: color(background-selected);
    }

    &--disabled {
      opacity: 0.8;
      cursor: not-allowed;

      a {
        cursor: not-allowed;
        text-decoration: none;
      }
    }

    &Text {
      margin-bottom: space(md);

      a {
        font-weight: 600;
      }
    }

    &Requirements {
      color: color(content-low);
    }
  }

  &__thumbs {
    display: flex;
    flex-wrap: wrap;
    margin-left: -(space(xs));
    margin-right: -(space(xs));
  }

  &__thumb {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    float: left;
    max-width: calc(25% - #{$gx-unit-size});
    width: ($gx-unit-size * 14);
    margin: space(sm) space(xs) 0;
    border: 0.1rem solid color(border-main);
    background-color: color(background-main);
    position: relative;
    color: color(content-action);

    &--disabled {
      cursor: not-allowed;
    }

    div:not(.gx-loader-overlay):not(.gx-loader) {
      position: relative;
      width: 100%;
      height: 0;
      padding-bottom: 70%;

      .img-delete {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(50%, -50%);
        display: flex;
        width: ($gx-unit-size * 2);
        height: ($gx-unit-size * 2);
        padding: 0;
        overflow: hidden;
        font-size: ($gx-unit-size * 2);
        background: color(background-main);
        border-radius: radius(rounded);
        color: color(content-error);
      }
    }

    .gx-loader-overlay {
      height: 100%;
    }

    .gx-loader-overlay ~ .img-delete {
      display: none;
    }

    img {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }

    .gx-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      @include icon-size(md);
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    &__upload {
      padding: space(xl) space(md) space(sm);

      &Text {
        margin-bottom: space(xl);
      }
    }
  }
}
