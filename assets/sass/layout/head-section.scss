// ==========================================================================
// Head Section - Components
// ==========================================================================
@use '../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

%head-section-element {
  display: flex;
  align-items: center;
  height: ($gx-unit-size * 7);
}

.gx-head-section {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0 space(md);
  background-color: color(background-alt);
  box-shadow: 0 -0.1rem 0 color(border-main) inset; //evita che si veda il border-bottom quando il blocco collassa

  @include media('screen', '<#{breakpoint(md)}') {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    @include z-index(head-section);

    .gx-head-section__back {
      width: $gx-new-menu-header-height;
      height: $gx-new-menu-header-height;
      top: 0;
    }
  }

  &__back.gx-button {
    margin-right: space(md);

    @include media('screen', '<#{breakpoint(md)}') {
      position: absolute;
      @include z-index(head-section, 1);
      left: 0;
      top: -$gx-header-size;
      width: $gx-new-menu-header-height;
      height: $gx-new-menu-header-height;
      border-width: 0;
      border-radius: 0;
      background-color: color(background-brand);
      color: color(content-accent);

      &:hover {
        color: color(content-accent);
        background-color: color(background-brand);
      }
    }
  }

  &__propertyImg {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 5.6rem;
    height: 4rem;
    overflow: hidden;
    margin-right: space(md);
    border-radius: radius(sm);

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  &__title {
    @extend %head-section-element;
    min-width: 0;
    flex: 1;
    margin-right: space(lg);

    // TODO: gestire così o mettere *?
    .gx-tag {
      margin-left: space(sm);
    }
  }

  &__actions {
    margin-left: auto;

    @include media('screen', '<#{breakpoint(md)}') {
      .gx-button {
        margin-top: space(md);
        margin-bottom: space(md);
      }
    }

    @include media('screen', '<#{breakpoint(sm)}') {
      flex-grow: 1;

      .gx-button {
        flex-grow: 1;
      }

      .gx-is-hidden-sm-down + .gx-button {
        margin-left: 0; //TODO: valutare se automatizzare questo comporatemento
      }
    }
  }

  &__counter {
    @extend %head-section-element;
    flex-grow: 1;
    color: color(content-action);

    &Wrap {
      display: flex;
    }

    & + & {
      margin-left: space(md);
    }

    &Type {
      color: color(content-low);
    }
  }

  &__agency-id {
    @extend %head-section-element;
    margin-left: auto;

    .code {
      color: color(content-action);
      margin-left: space(xs);
    }
  }

  &__visibilityInfo {
    @extend %head-section-element;
    width: 100%;
    padding: 0;
    margin: 0;
    list-style: none;

    li {
      color: color(content-low);

      & + li {
        margin-left: space(lg);
      }

      .used-counter {
        color: color(content-action);
      }
    }

    .icon-immopro {
      top: 0.1rem;
      margin-right: space(xs);
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    padding: 0 space(lg);

    &__visibilityInfo {
      width: auto;
      margin-left: space(xl);
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    padding: 0 space(xl);

    &__counter {
      &Wrap {
        margin-left: space(xl);
      }
    }

    &__agency-id {
      flex-grow: 0;
    }

    &__title,
    &__counter,
    &__agency-id,
    &__visibilityInfo {
      height: ($gx-unit-size * 9);
    }
  }
}

.gx-head-section__back--top.gx-button {
  top: 0;
}
