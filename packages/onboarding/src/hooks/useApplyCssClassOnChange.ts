import { useEffect } from "react"
import { IAppTour, IAppTourStep } from "src/types/tour"
import { usePrevious } from "./usePrevious"
import { useToursContext } from "./useToursContext"

const activeBodyClass = 'onboarding-open'
const activeStepClass = 'onboarding-item-active'

export const useApplyCssClassOnChange = (currentStep: IAppTourStep | null, isDesktop: boolean, currentTour: IAppTour | null) => {

    const prevStep = usePrevious(currentStep)
    const { showTour, appTours, stepIndex, tourIndex } = useToursContext()
    const prevTour = usePrevious(currentTour)



    useEffect(() => {
        prevStep?.el.classList.remove(activeStepClass)

        if (isDesktop && currentStep && showTour) {
            appTours[tourIndex].steps[stepIndex].el.classList.add(activeStepClass)
        }

    }, [currentStep, isDesktop, showTour])

    useEffect(() => {
        if (prevTour) {
            document.body.classList.remove(`onboarding-name__${prevTour.name}`)
        }

        if (isDesktop && currentTour && showTour) {
            document.body.classList.add(`onboarding-name__${currentTour.name}`)
        }

    }, [currentTour, isDesktop, showTour])


    useEffect(() => {
        if (showTour && isDesktop) {
            document.body.classList.add(activeBodyClass)
        } else {
            document.body.classList.remove(activeBodyClass)
        }
    }, [showTour, isDesktop])

}
