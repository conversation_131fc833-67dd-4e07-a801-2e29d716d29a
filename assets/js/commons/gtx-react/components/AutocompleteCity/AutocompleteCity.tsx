import React, { FC } from 'react';
import AutocompleteFormik from 'gtx-react/containers/AutocompleteFormik';
import { trans } from '@pepita-i18n/babelfish';
import {
    CityAutocompleteOption,
    getCitiesAutocompleteApi,
} from './web-api/api';

type AutocompleteCityProps = {
    name: string;
    label?: string;
    placeholder?: string;
    defaultValue?: any;
    onChange?: (_newValue: any) => void;
    format?: (_item: CityAutocompleteOption) => string;
    disabled?: boolean;
};

const formatAutocompleteValue = (item: CityAutocompleteOption) =>
    `${item.comune_nome}${
        item.provincia_nome && item.comune_nome !== item.provincia_nome
            ? ` (${item.provincia_nome})`
            : ''
    }${item.nazione_nome ? ', ' + item.nazione_nome : ''}`;

export const AutocompleteCity: FC<AutocompleteCityProps> = ({
    name,
    label,
    placeholder,
    defaultValue,
    onChange,
    format,
    disabled,
}) => {
    return (
        <AutocompleteFormik
            name={name}
            key={name}
            label={label || trans('label.municipality')}
            placeholder={placeholder || trans('label.municipality_required')}
            defaultValue={defaultValue}
            getAutocompleteApi={getCitiesAutocompleteApi}
            minLength={2}
            onChange={onChange}
            classWrap="gx-box-row"
            itemKey="idComune"
            selectedLabelKey="comune_nome"
            showRequiredSymbol={true}
            formatFn={format || formatAutocompleteValue}
            disabled={disabled}
        />
    );
};
