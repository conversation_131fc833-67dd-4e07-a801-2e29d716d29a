import react from '@vitejs/plugin-react';
import path from 'path';
import tsconfigPaths from 'vite-tsconfig-paths';
import { defineProject } from 'vitest/config';

export default defineProject({
  plugins: [tsconfigPaths(), react()],
  test: {
    setupFiles: [path.join(__dirname, '../../tests/vitest/setup.ts')],
    globals: true,
    name: 'gx-navigation',
    dir: path.join(__dirname, 'src'),
    environment: 'jsdom',
    include: ['./**/*.test.(ts|tsx)'],
    alias: {
      '@getrix/common/js/gtx-constants': path.join(__dirname, '../../tests/vitest/setup.ts'),
    },
  },
});
