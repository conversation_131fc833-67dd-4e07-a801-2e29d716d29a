@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.top-section {
  padding: 3rem 0 0;
  background: #e5f0f8;
  text-align: center;

  &--city {
    padding-bottom: 3rem;

    @include media('screen', '>=#{breakpoint(sm)}') {
      height: 46rem;
      padding-bottom: 0;
      background-image: url('/bundles/base/img/landing-page/illustrazione_city.png');
      background-position: center bottom;
      background-repeat: no-repeat;

      @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi), (min-resolution: 2dppx) {
        background-image: url('/bundles/base/img/landing-page/<EMAIL>');
        background-size: 143.6rem 32.7rem;
      }
    }

    @include media('screen', '>=#{breakpoint(md)}') {
      height: 52rem;
    }
  }
}

.top-section .logo {
  display: block;
  width: 20rem;
  height: 2.4rem;
  margin: 0 auto 2rem;
}

.top-section .subtitle1 {
  font-weight: 500;
  font-family: 'Raleway', sans-serif;
  font-size: 1.8rem;
  line-height: 1;
  margin-bottom: 4rem;
}

.top-section .subtitle1 {
  margin-bottom: 6rem;
}

@media screen and (min-width: 76.8rem) {
  .top-section .logo {
    width: 38.6rem;
    height: 4.6rem;
    margin: 0 auto 3rem;
  }
  .top-section h1.subtitle1 {
    font-size: 2.9rem;
    margin-bottom: 8rem;
  }
}

@media screen and (min-width: 102.4rem) {
  .top-section {
    padding-top: 9rem;
  }
}

.list-section {
  padding: 4rem 0;
  li {
    display: block;
    width: 80%;
    margin: 0 auto;
    text-align: center;
    .icon {
      display: block;
      width: 7rem;
      height: 7rem;
      margin: 0 auto 2rem;
    }
    h3 {
      font-weight: 500;
      margin-bottom: 2rem;
      font-family: 'Raleway', sans-serif;
      font-size: 2rem;
      color: #666666;
    }
    p {
      font-size: 1.4rem;
      color: #999999;
      line-height: 1.6;
    }
  }
  li + li {
    margin-top: 4rem;
  }
}

@include media('screen', '>=#{breakpoint(sm)}') {
  .list-section {
    padding: 6rem 0;
  }
  .list-section li {
    float: left;
    width: 33%;
    padding: 0 2rem;
  }
  .list-section li + li {
    margin-top: 0;
  }
}

@include media('screen', '>=#{breakpoint(md)}') {
  .list-section {
    max-width: 90rem;
    padding: 7rem 0;
    margin: 0 auto;
  }
  .list-section li {
    padding: 0 3rem;
  }
}
