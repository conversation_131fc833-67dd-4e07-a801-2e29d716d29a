{% extends '@WebProfiler/Profiler/layout.html.twig' %}

{% block backendCollectorIcon %}
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Capa_1" x="0px" y="0px" viewBox="0 0 380 380" style="enable-background:new 0 0 380 380;" xml:space="preserve" width="24" height="24">
<g>
    <path d="M91.425,157.435c-1.065-2.297-3.366-3.767-5.897-3.767c-0.005,0-0.01,0-0.015,0c-2.537,0.006-4.839,1.487-5.895,3.794   L47.402,227.83c-1.494,3.264-0.06,7.122,3.204,8.616c0.877,0.401,1.797,0.591,2.702,0.591c2.464,0,4.821-1.409,5.914-3.796   l8.08-17.649h36.75l8.193,17.677c1.51,3.257,5.373,4.674,8.631,3.164c3.257-1.51,4.673-5.374,3.164-8.631L91.425,157.435z    M73.254,202.592l12.311-26.888l12.462,26.888H73.254z" fill="#aaaaaa"/>
    <path d="M166.496,152.865H135.07c-3.59,0-6.5,2.91-6.5,6.5v71.17c0,3.59,2.91,6.5,6.5,6.5c3.59,0,6.5-2.91,6.5-6.5V203.5h24.926   c13.96,0,25.316-11.357,25.316-25.316C191.813,164.223,180.456,152.865,166.496,152.865z M166.496,190.5H141.57v-24.635h24.926   c6.792,0,12.316,5.526,12.316,12.318C178.813,184.975,173.288,190.5,166.496,190.5z" fill="#aaaaaa"/>
    <path d="M203.313,152.865c-3.59,0-6.5,2.91-6.5,6.5v71.17c0,3.59,2.91,6.5,6.5,6.5s6.5-2.91,6.5-6.5v-71.17   C209.813,155.775,206.902,152.865,203.313,152.865z" fill="#aaaaaa"/>
    <path d="M368.317,46.487H11.684C5.241,46.487,0,51.73,0,58.173v263.656c0,6.442,5.241,11.684,11.684,11.684h356.634   c6.442,0,11.683-5.242,11.683-11.684V58.173C380,51.73,374.76,46.487,368.317,46.487z M280.949,259.268   c5.162-12.192,17.053-20.07,30.295-20.07c4.411,0,8.721,0.878,12.813,2.61c16.696,7.072,24.529,26.408,17.461,43.105   c-5.167,12.191-17.058,20.068-30.294,20.068c0,0,0,0-0.001,0c-4.411,0-8.721-0.878-12.81-2.611   c-8.089-3.424-14.361-9.793-17.659-17.935C277.455,276.294,277.524,267.356,280.949,259.268z M320.868,79.771   c6.844,0,12.387,5.547,12.387,12.387c0,6.842-5.543,12.387-12.387,12.387c-6.839,0-12.385-5.545-12.385-12.387   C308.483,85.318,314.029,79.771,320.868,79.771z M283.715,79.771c6.839,0,12.386,5.547,12.386,12.387   c0,6.842-5.547,12.387-12.386,12.387c-6.839,0-12.387-5.545-12.387-12.387C271.328,85.318,276.876,79.771,283.715,79.771z    M246.561,79.771c6.839,0,12.381,5.547,12.381,12.387c0,6.842-5.542,12.387-12.381,12.387c-6.844,0-12.387-5.545-12.387-12.387   C234.175,85.318,239.718,79.771,246.561,79.771z M21.27,314.01V141.424H358.73v67.274c-1.537-2.202-3.677-4.02-6.327-5.143   l-20.614-8.726c-1.835-0.778-3.771-1.172-5.754-1.172c-5.956,0-11.303,3.54-13.623,9.02l-1.253,2.959   c-9.227,0.129-18.381,2.255-26.728,6.193l-2.426-2.107c-1.174-1.018-2.499-1.841-3.932-2.447c-1.841-0.779-3.782-1.174-5.771-1.174   c-4.276,0-8.341,1.858-11.154,5.099l-14.671,16.915c-2.583,2.986-3.855,6.794-3.581,10.735c0.285,3.941,2.084,7.535,5.064,10.115   l2.417,2.098c-2.755,8.849-3.563,18.179-2.365,27.344l-2.749,1.654c-6.981,4.2-9.242,13.302-5.039,20.293L249.044,315H22.26   C21.714,315,21.27,314.556,21.27,314.01z" fill="#aaaaaa"/>
</g>
</svg>
{% endblock %}

{% block toolbar %}
    {% if collector.callsCount %}
        {% set icon %}
            {{ block('backendCollectorIcon') }}
            <span class="sf-toolbar-value">{{ collector.callsCount }} Backend Call(s)</span>
        {% endset %}

        {% set text %}
            {% set time = 0 %}
            {% set mem = 0 %}
            {% for item in collector.calls %}
                {% set time = time + item.timing %}
                {% set mem = mem + item.memory %}
                <div class="sf-toolbar-info-piece" style="{% if item.cached is same as(null) %}text-decoration: line-through !important;{% elseif item.cached %}color: lightgreen !important;{% else %}color: orangered !important;{% endif %}">
                    <span style="{% if item.cached is same as(null) %}text-decoration: line-through !important;{% elseif item.cached %}color: lightgreen !important;{% else %}color: orangered !important;{% endif %}"><strong>{{ item.service }}</strong>::<em>{{ item.method }}</em>()</span> -
                    <small>{{ item.timing | number_format(2) }} sec / {{ item.memory | number_format(2) }} MB</small>
                </div>
            {% endfor %}
            <hr />
            <strong>Time: </strong> {{ time | number_format(2) }} sec<br />
            <strong>Mem: </strong> {{ mem | number_format(2) }} MB
            <img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" onload="var h = this.parentNode.innerHTML, rx=/<script>(.*?)<\/script>/g, s; while (s = rx.exec(h)) {eval(s[1]);};" />
        {% endset %}

        {{ include('@WebProfiler/Profiler/toolbar_item.html.twig', { 'link': true }) }}
    {% endif %}
{% endblock %}

{% block menu %}
    <span class="label {{ collector.callsCount == 0 ? 'disabled' }}">
        <span class="icon">{{ block('backendCollectorIcon') }}</span>
        <strong>Backend Call(s)</strong>
    </span>
{% endblock %}

{% block panel %}
    <h2>Backend Call(s)</h2>

    {% for item in collector.calls %}
        <div class="sf-dump sf-reset">
            <h3>
                <span><strong>{{ item.service }}</strong>::<em>{{ item.method }}</em>()</span>
            </h3>
            <small>{{ item.timing | number_format(2) }} sec / {{ item.memory | number_format(2) }} MB</small>
            <a rel="nofollow" role="button" class="text-small sf-toggle" data-toggle-selector="#sf-trace-{{ loop.index0 }}" data-toggle-alt-content="Hide additional information">Show additional information</a>

            <div class="sf-dump-compact hidden" id="sf-trace-{{ loop.index0 }}">
                <h4>Input</h4>
                {{ dump(item.input) }}

                <h4>Output</h4>
                {{ dump(item.output) }}

                <h4>Cache</h4>
                {% if item.cached is same as(null) %}Skipped{% elseif item.cached %}Cached{% else %}Not cached{% endif %}

                <h4>Stack Trace</h4>
                {% for step, detail in item.stackTrace %}
                    <small>#{{ step }} - {{ detail.file | default('') }}:{{ detail.line | default('') }} - {{ detail.class | default('') }}::{{ detail.function | default('') }}</small><br />
                {% endfor %}
            </div>
        </div>
    {% else %}
        <div class="empty">
            <p>No backend calls were made.</p>
        </div>
    {% endfor %}
{% endblock %}
