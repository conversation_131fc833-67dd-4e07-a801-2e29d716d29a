import { format } from 'date-fns';
import { VisitType } from '../types';
import { ImmovisitaScheduleModalFormFields } from '../types/form';

export const initialFormValues = (): ImmovisitaScheduleModalFormFields => {
    const idAgente = (window as any).gtxLoggedUser?.idAgente as number;
    const now = new Date();

    return {
        id: null,
        propertyId: undefined,
        agent: idAgente,
        note: '',
        guests: [{ email: '' }],
        ['scheduled_date']: format(now, 'dd/MM/yyyy'),
        ['scheduled_time']: format(now, 'HH:mm'),
        visitType: VisitType.VIRTUAL,
        threadId: null,
    };
};
