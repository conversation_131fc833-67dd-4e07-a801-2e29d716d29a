@use '../../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

// BILLBOARD
.billboard-editor__preview {
  padding: space(xl) space(md);
  background: color(background-alt);
}

.billboard-iframe-container {
  position: relative;
  width: 100%;
  box-shadow: elevation(raised);

  &--Verticale {
    padding-bottom: 141%;
  }

  &--Orizzontale {
    padding-bottom: 70%;
  }

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

.billboard-editor__checkItem + .billboard-editor__checkItem {
  margin-top: space(md);
}

.billboard-editor__options__item {
  .gx-label {
    margin-bottom: space(sm);
  }

  &--fullWidth {
    .gx-radio-wrapper {
      width: 100%;

      label {
        width: 50%;
      }
    }
  }

  &--orientation {
    label {
      .gx-radio-button__control {
        display: flex;
        flex-direction: column;
        height: 8rem;

        &:before {
          display: block;
          width: 1.5rem;
          height: 2.1rem;
          margin: 0 auto 0.5rem;
          border: 0.2rem solid color(content-selected);
          border-radius: radius(sm);
          content: '';
        }
      }

      & + label {
        .gx-radio-button__control {
          &:before {
            width: 2.1rem;
            height: 1.5rem;
            margin: 0.3rem auto 0.8rem;
          }
        }
      }
    }
  }

  .title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: space(sm);
  }

  & + .billboard-editor__options__item {
    margin-top: space(xl);
  }

  .checkbox-list-item {
    color: color(content-medium);
    cursor: pointer;

    & + .checkbox-list-item {
      margin-top: space(md);
    }

    .ge-checkbox {
      margin-right: space(md);
      float: left;
    }
  }
}

.billboard-editor__options {
  background-color: color(background-main);
}

.billboard-tooltip {
  background: rgba(#{color(background-reversed)}, 0.8) !important;
  color: color(content-accent);
  font-size: 1.2rem;
  box-shadow: none;

  .popover-content {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .arrow {
    border-top-color: transparent !important;

    &:after {
      border-top-color: rgba(#{color(background-reversed)}, 0.8) !important;
    }
  }
}

.billboard-editor__options-modal-opener {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  border-top: 0.1rem solid color(border-main);
  background-color: color(background-main);
  text-align: center;
  @include z-index(base, 1);
}

.color-picker__selected {
  width: 12.4rem;
  height: 2.4rem;
  margin-right: space(sm);
  float: left;
  border-radius: radius(sm);
}

.color-picker__item-value {
  display: block;
  width: 100%;
  height: space(xl);
  border-radius: radius(sm);
  cursor: pointer;
}

.color-picker__item-value + span {
  display: none;
}

.gx-action-list__item > a {
  padding-right: space(md);
}

.modal-tool--text {
  display: flex;
  justify-content: space-between;

  .gx-label {
    display: none;

    & + .gx-radio-wrapper {
      margin-top: 0;
    }
  }

  .modal-tool__item--fontsize .styled-select {
    width: 14rem;
  }
}

#description {
  line-height: 100%;
  height: 30rem;
  font-weight: 100;
}

.form-control--auto {
  height: auto;
}

.section-billboard {
  min-height: calc(100vh - 9rem);
  background: color(background-alt);

  & + .gtx-ask-help {
    bottom: 6rem;

    + .gtx-ah-footer-spacing {
      background: color(background-alt);
    }
  }
}

@include media('screen', '<#{breakpoint(sm)}') {
  .modal-tool--text .modal-tool__item--fontsize .styled-select {
    width: 12rem;
  }

  .btn-group--billboard {
    width: 100%;

    .print {
      display: none;
    }

    .download-pdf {
      width: 100%;
    }
  }
}

// BILLBOARD | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .billboard-editor {
    display: flex;
  }

  .billboard-editor__preview {
    flex-grow: 1;
    padding: space(2xl) space(md);
  }

  .billboard-iframe-container {
    width: 47rem;
    margin: 0 auto;

    &--Verticale {
      height: 66.5rem;
      padding-bottom: 0;
    }

    &--Orizzontale {
      height: 33.2rem;
      padding-bottom: 0;
    }
  }

  .billboard-editor__options {
    width: 26rem;
    padding: space(xl);
  }

  .modal-tool--text {
    justify-content: flex-start;

    label {
      display: block;
    }

    .modal-tool__item--fontsize {
      .styled-select {
        display: inline-block;
        vertical-align: middle;
      }
    }
  }
  .billboard-editor__options-modal-opener {
    display: none;
  }

  .section-billboard {
    .billboard-editor__preview,
    .billboard-editor__options {
      padding-bottom: 9.6rem;
    }

    & + .gtx-ask-help {
      bottom: 2rem;

      + .gtx-ah-footer-spacing {
        display: none;
      }
    }
  }
}

// BILLBOARD | DESKTOP
@include media('screen', '>=#{breakpoint(md)}') {
  .billboard-editor__preview {
    padding: space(xl);
  }

  .billboard-iframe-container {
    width: 64rem;

    &--Verticale {
      height: 90.4rem;
    }

    &--Orizzontale {
      height: 45.2rem;
    }
  }
}

@include media('screen', '>=#{breakpoint(lg)}') {
  .billboard-iframe-container {
    width: 75.4rem;

    &--Verticale {
      height: 106.6rem;
    }

    &--Orizzontale {
      height: 53.3rem;
    }
  }

  .billboard-editor__options {
    width: 34rem;
  }

  .billboard-editor__options__item {
    .btn-group--orientation {
      width: 100%;

      .gx-button {
        display: block;
        width: 50%;
        height: 4rem;

        &:before {
          display: inline-block;
          margin: 0 1rem 0 0;
          content: '';
          vertical-align: middle;
        }

        & + .gx-button:before {
          position: relative;
          top: -0.2rem;
          margin: 0 1rem 0 0;
        }
      }
    }
  }
}

@include media('screen', '>=#{breakpoint(xl)}') {
  .billboard-iframe-container--Orizzontale {
    width: 80rem;
    height: 63.5rem;
  }
}

.modal-tool {
  margin: 0 -#{space(lg)} space(md);
  padding: 1.2rem space(md);
  border-bottom: 0.1rem solid color(border-main);
  background: color(background-alt);

  .gx-radio-button__control {
    display: flex;
    width: 4rem;
    padding: 0;

    svg {
      flex-shrink: 0;
      @include icon-size(md);
    }

    span {
      display: none;
    }
  }
}

@include media('screen', '>=#{breakpoint(sm)}') {
  .modal-tool {
    margin: 0 -3rem space(xl);
    padding: 1.2rem space(xl);

    .modal-tool__item {
      & + .modal-tool__item {
        margin-left: space(xl);
      }
    }
  }
}

.modal__change-image__photo {
  position: relative;
  width: 100%;
  padding-bottom: 75%;
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);
  cursor: pointer;
  overflow: hidden;

  & + .modal__change-image__photo {
    margin-top: 2rem;
  }

  &.select {
    outline: 0.3rem solid color(border-selected);
    cursor: default;
  }

  img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    &.horizontal {
      width: auto;
      height: 100%;
    }

    &.vertical {
      width: 100%;
      height: auto;
    }
  }
}

@include media('screen', '>=#{breakpoint(sm)}') {
  .modal__change-image {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -1rem;
  }

  .modal__change-image__photo {
    width: calc(33.33% - 2rem);
    min-height: 15rem;
    margin: 0 1rem 2rem;
    padding-bottom: 24%;

    & + .modal__change-image__photo {
      margin-top: 0;
    }
  }
}

.noPaddingTop {
  margin-top: -1.6rem;
}
