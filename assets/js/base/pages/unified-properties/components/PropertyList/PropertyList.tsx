import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import {
    ColumnPinningState,
    getCoreRowModel,
    OnChangeFn,
    PaginationState,
    SortingState,
} from '@tanstack/react-table';
import { Fragment, ReactNode, useMemo, useState } from 'react';
import { useColumns } from './columns';
import { PaginationBar } from '@gx-design/pagination-bar';
import { Tooltip } from '@gx-design/tooltip';
import { trans } from '@pepita-i18n/babelfish';
import { CrmSummary } from 'gtx-react/components/CrmSummary';
import { SortableButton } from 'gtx-react/components/tanstack-table/Sortable';
import { getDefaultColumnOrder } from 'gtx-react/components/tanstack-table/helpers';
import {
    useColumnOrderState,
    useColumnSizingInfoState,
    useColumnSizingState,
    useColumnVisibilityState,
    useDefaultOrderingColumns,
    useSortingSearchParams,
} from 'gtx-react/components/tanstack-table/hooks';
import {
    CrmTable,
    useReactTableV1,
} from 'gtx-react/components/tanstack-table/v1';
import { ErrorBoundary } from 'react-error-boundary';
import { PropertyStatus } from '../../types';
import { useOpenPropertyDeleteDialog } from '../dialogs/PropertyDeleteDialog';
import { VisibilitySummary } from '../dialogs/VisibilitySummary/VisibilitySummary';
import PropertyActivateBulkButton from './PropertyActivateBulkButton';
import PropertyArchiveBulkButton from './PropertyArchiveBulkButton';
import { usePropertyFiltersSearchParams } from './filters';
import {
    useActivePropertyTableStatePersist,
    useActivePropertyTableStatePersistentValues,
} from './persistence';
import { Property } from './types';
import { useConfigContext } from '../ConfigProvider';
import { PropertyListEmptyState } from './PropertyListEmptyState';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { convertCase, ucFirst } from 'lib/strings-formatter';
import { ActivationError } from '../ActivationError/ActivationError';
import { useDebouncedHandler } from 'gtx-react/hooks/useDebouncedHandler';
import { SkeletonCell } from './PropertyListSkeleton';

type TableProps = {
    filterElement: ReactNode;
    status: PropertyStatus;
    list: Array<Property>;
    pagination: PaginationState;
    pageCount: number;
    sorting: SortingState;
    isFetching: boolean;
    onPageChange: OnChangeFn<PaginationState>;
    onSortingChange: OnChangeFn<SortingState>;
    totalResults: number;
};

export function PropertyList(props: TableProps) {
    const columns = useColumns(props.status);
    const persistentTableState = useActivePropertyTableStatePersistentValues(
        props.status
    );
    const defaultColumns = useDefaultOrderingColumns(columns);
    const [sort, setSort] = useSortingSearchParams();
    const [columnOrder, setColumnOrder] = useColumnOrderState(
        getDefaultColumnOrder(persistentTableState.columnOrder, defaultColumns)
    );
    const [filters, setFilters] = usePropertyFiltersSearchParams(props.status);
    const [columnPinningState, setColumnPinningState] =
        useState<ColumnPinningState>({
            left: ['code'],
            right: ['actions'],
        });
    const [columnSizing, setColumnSizing] = useColumnSizingState(
        persistentTableState.columnSizing
    );
    const [columnSizingInfo, setColumnSizingInfo] = useColumnSizingInfoState();
    const [columnVisibility, setColumnVisibility] = useColumnVisibilityState(
        persistentTableState.columnVisibility
    );

    const { open: openDeleteDialog } = useOpenPropertyDeleteDialog();

    const { isGetrix } = useConfigContext();

    const debouncedResizeTracker = useDebouncedHandler(
        (columnName: string, newSizing: number) => {
            trackEvent({
                event: 'properties_list_column_size_changed',
                extra: {
                    ['column_size']: newSizing,
                    ['column_name']: columnName,
                },
            });
        },
        1000
    );

    const setFiltersMemoized = useMemo(
        () => setFilters('Listing List'),
        [setFilters]
    );

    const table = useReactTableV1({
        getCoreRowModel: getCoreRowModel(),
        data: props.list,
        columns,
        // debugAll: true,
        enableMultiSort: true,
        columnResizeMode: 'onChange',
        enableColumnResizing: true,
        enableDnd: true,
        pageCount: props.pageCount,
        manualSorting: true,
        manualPagination: true,
        manualFiltering: true,
        state: {
            dnd: {
                code: false,
                actions: false,
            },
            columnSizingInfo,
            columnSizing,
            columnOrder: columnOrder,
            columnVisibility: columnVisibility,
            pagination: props.pagination,
            sorting: sort,
            globalFilter: filters,
            columnPinning: columnPinningState,
        },
        initialState: {
            columnSizing: {
                code: 340,
                stats: 190,
            },
        },
        onColumnSizingChange: setColumnSizing,
        onColumnSizingInfoChange: (infoUpdater) => {
            if (typeof infoUpdater === 'function') {
                const sizingInfo = infoUpdater(
                    table.getState().columnSizingInfo
                );

                // sizingInfo contains information about the currently resized column
                if (
                    sizingInfo.isResizingColumn &&
                    sizingInfo.columnSizingStart
                ) {
                    const columnId = sizingInfo.isResizingColumn;
                    // Use the updated sizing from table state, not from stale columnSizing
                    const currentSize =
                        table.getState().columnSizing[columnId] || 0;

                    debouncedResizeTracker(
                        convertCase(columnId, 'snake'),
                        currentSize
                    );
                }
            }
            setColumnSizingInfo(infoUpdater);
        },
        onColumnPinningChange: setColumnPinningState,
        onColumnVisibilityChange: setColumnVisibility,
        onColumnOrderChange: setColumnOrder,
        onPaginationChange: props.onPageChange,
        onGlobalFilterChange: setFiltersMemoized,
        onSortingChange: setSort,
    });

    useActivePropertyTableStatePersist(
        {
            columnSizing,
            columnOrder,
            columnVisibility,
        },
        props.status
    );

    const { trackEvent } = useMixpanelContext();

    return (
        <>
            <ActivationError />
            {props.status === 'sold' ? null : <VisibilitySummary />}
            {props.filterElement}
            <CrmSummary>
                <CrmSummary.Results
                    itemsLength={props.list.length}
                    pageSize={props.pagination.pageSize}
                    pageIndex={props.pagination.pageIndex}
                    pageCount={props.pageCount}
                />

                <CrmSummary.Actions>
                    <Tooltip text={trans('label.export_csv')}>
                        <Button
                            as="a"
                            href={
                                isGetrix
                                    ? '/annunciCSV.php'
                                    : '/v2/annunci-portale/csv'
                            }
                            target="_blank"
                            iconOnly
                            size="small"
                        >
                            <Icon name="download" />
                        </Button>
                    </Tooltip>

                    <SortableButton
                        defaultItems={defaultColumns}
                        table={table}
                        items={columnOrder}
                        onChange={setColumnOrder}
                        onReset={(items) => setColumnOrder(items)}
                        renderItem={(item) => {
                            const itemLabel =
                                table.getColumn(item)?.columnDef.meta?.label;

                            return (
                                <SortableButton.Item id={item}>
                                    <SortableButton.Checkbox
                                        label={itemLabel}
                                        onChange={(value) => {
                                            trackEvent({
                                                event: 'properties_list_column_visibility_changed',
                                                extra: {
                                                    ['visibility_state']: value
                                                        ? 'Show'
                                                        : 'Hide',
                                                    ['column_name']:
                                                        convertCase(
                                                            item,
                                                            'snake'
                                                        ),
                                                },
                                            });
                                        }}
                                    />
                                </SortableButton.Item>
                            );
                        }}
                    />
                </CrmSummary.Actions>
            </CrmSummary>
            <div className="crm-section__contentList">
                <CrmTable table={table}>
                    <CrmTable.Thead>
                        {table.getHeaderGroups().map(({ id, headers }) => (
                            <Fragment key={id}>
                                <CrmTable.Tr>
                                    {headers.map((header) => (
                                        <CrmTable.HorizontalDropping
                                            key={header.id}
                                            items={columnOrder}
                                        >
                                            <CrmTable.Th
                                                onSort={() => {
                                                    trackEvent({
                                                        event: 'properties_sort_list',
                                                        extra: {
                                                            ['sorting_column']:
                                                                header.id,
                                                            ['sorting_criteria']:
                                                                header.column.getIsSorted()
                                                                    ? 'DESC'
                                                                    : 'ASC',
                                                            ['listings_number']:
                                                                props.totalResults,
                                                            ['list_view']:
                                                                ucFirst(
                                                                    props.status
                                                                ),
                                                        },
                                                    });
                                                }}
                                                header={header}
                                            >
                                                <CrmTable.Th.PinningControls />
                                                <CrmTable.Th.ResizebleControls />
                                                <CrmTable.Th.DraggableButton />
                                            </CrmTable.Th>
                                        </CrmTable.HorizontalDropping>
                                    ))}
                                </CrmTable.Tr>
                                <CrmTable.TrSelectable>
                                    <CrmTable.TrSelectable.Actions>
                                        {props.status === 'archived' ? (
                                            <PropertyActivateBulkButton
                                                status={props.status}
                                                toggleAllRowsSelected={() =>
                                                    table.resetRowSelection()
                                                }
                                                ids={table
                                                    .getSelectedRowModel()
                                                    .rows.map((row) =>
                                                        Number(row.original.id)
                                                    )}
                                            />
                                        ) : (
                                            <PropertyArchiveBulkButton
                                                status={props.status}
                                                toggleAllRowsSelected={() =>
                                                    table.resetRowSelection()
                                                }
                                                ids={table
                                                    .getSelectedRowModel()
                                                    .rows.map((row) =>
                                                        Number(row.original.id)
                                                    )}
                                            />
                                        )}
                                        <Tooltip text={trans('label.remove')}>
                                            <Button
                                                onClick={() => {
                                                    table.resetRowSelection();
                                                    openDeleteDialog(
                                                        table
                                                            .getSelectedRowModel()
                                                            .rows.map((row) =>
                                                                Number(
                                                                    row.original
                                                                        .id
                                                                )
                                                            )
                                                    );
                                                }}
                                                iconOnly
                                                size="small"
                                                variant="ghost"
                                            >
                                                <Icon name="bin"></Icon>
                                            </Button>
                                        </Tooltip>
                                    </CrmTable.TrSelectable.Actions>
                                </CrmTable.TrSelectable>
                            </Fragment>
                        ))}
                    </CrmTable.Thead>
                    <CrmTable.TableBody>
                        {table.getRowModel().rows.map((row) => (
                            <CrmTable.BodyRow row={row} key={row.id}>
                                {row.getVisibleCells().map((cell) => (
                                    <CrmTable.HorizontalDropping
                                        key={cell.id}
                                        items={columnOrder}
                                    >
                                        <CrmTable.Td
                                            key={cell.column.id}
                                            cell={cell}
                                            isLoading={props.isFetching}
                                            skeletonElement={
                                                <SkeletonCell
                                                    type={cell.column.id}
                                                />
                                            }
                                        >
                                            <CrmTable.Td.ResizebleControls
                                                column={cell.column}
                                            />
                                        </CrmTable.Td>
                                    </CrmTable.HorizontalDropping>
                                ))}
                            </CrmTable.BodyRow>
                        ))}
                    </CrmTable.TableBody>
                </CrmTable>
            </div>
            {table.getRowModel().rows.length === 0 && (
                <div className="crm-section__contentEmptyState">
                    <PropertyListEmptyState
                        filter={filters}
                        status={props.status}
                        hide={props.isFetching}
                    />
                </div>
            )}
            <ErrorBoundary
                fallback={null}
                key={props?.pageCount + '-' + props?.list?.length}
            >
                <PaginationBar
                    separatorString={trans('label.out_of_2')}
                    resultString={trans('label.results')}
                    currentResults={props.pagination.pageSize}
                    totalResults={props.pageCount}
                >
                    <PaginationBar.DropDown
                        onResultsChange={(value) => {
                            table.resetRowSelection();
                            table.setPagination({
                                pageIndex: 0,
                                pageSize: value,
                            });
                        }}
                        options={[10, 30, 50]}
                        value={props.pagination.pageSize}
                        resultString={trans('label.results')}
                    />
                    <PaginationBar.Pager
                        activePage={props.pagination.pageIndex + 1}
                        maxPagesToShow={3}
                        onPageClick={(page) => {
                            table.resetRowSelection();
                            table.setPagination({
                                pageIndex: page - 1,
                                pageSize: props.pagination.pageSize,
                            });
                        }}
                        totalPages={Math.ceil(
                            props.pageCount / props.pagination.pageSize
                        )}
                    />
                </PaginationBar>
            </ErrorBoundary>
        </>
    );
}
