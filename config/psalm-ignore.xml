<?xml version="1.0" encoding="UTF-8"?>
<files psalm-version="4.30.0@d0bc6e25d89f649e4f36a534f330f8bb4643dd69">
  <file src="src/Component/FeatureToggle/Feature.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$enabled</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Component/FeatureToggle/Group.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$enabled</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Component/LandingPage/Handler/MessagingHandler.php">
    <InvalidPropertyFetch occurrences="2">
      <code>$data-&gt;idAgenzia</code>
      <code>$data-&gt;idLandingPage</code>
    </InvalidPropertyFetch>
    <InvalidReturnType occurrences="1">
      <code>mixed</code>
    </InvalidReturnType>
  </file>
  <file src="src/Component/LandingPage/Handler/TelefonoSmartHandler.php">
    <InvalidPropertyFetch occurrences="2">
      <code>$data-&gt;idAgenzia</code>
      <code>$data-&gt;idLandingPage</code>
    </InvalidPropertyFetch>
  </file>
  <file src="src/Component/TrustedRequestValidator/Handler/TrustedRequestValidatorHandler.php">
    <InvalidReturnType occurrences="1">
      <code>void</code>
    </InvalidReturnType>
  </file>
  <file src="src/Controller/Base/AuctionsCatalogueController.php">
    <InvalidPropertyAssignmentValue occurrences="1"/>
  </file>
  <file src="src/Controller/Base/CustomersController.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$tokenStorage</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Controller/Base/ProjectsLookupController.php">
    <InvalidReturnType occurrences="1">
      <code>string</code>
    </InvalidReturnType>
  </file>
  <file src="src/Controller/Base/PropertiesLookupController.php">
    <InvalidReturnType occurrences="1">
      <code>string</code>
    </InvalidReturnType>
  </file>
  <file src="src/Controller/Lookup/API/GetPropertyMultimediaFloorController.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$lookup</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Controller/Messaging/API/V2/ActivateMultipleThreadsController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/ActivateThreadController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/ArchiveMultipleThreadsController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/ArchiveThreadController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/CreateMessageController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/CreatePlannedVisitController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/DeleteMultipleThreadsController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/DeleteThreadController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/SetAnsweredByPhoneController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/SetArchivedThreadController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/SetFavouriteThreadController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/SetNotFavouriteThreadController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/SetReadAllThreadsController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/SetReadMultipleThreadsController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Messaging/API/V2/SetReadThreadController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;messagingHandler</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/PortalNewConstruction/API/ActivatePortalNewConstructionController.php">
    <InvalidArgument occurrences="1">
      <code>$e-&gt;getCode()</code>
    </InvalidArgument>
  </file>
  <file src="src/Controller/PortalNewConstruction/API/BulkActivatePortalNewConstructionsController.php">
    <InvalidArgument occurrences="1">
      <code>$e-&gt;getCode()</code>
    </InvalidArgument>
  </file>
  <file src="src/Controller/PortalNewConstruction/API/BulkArchivePortalNewConstructionsController.php">
    <InvalidArgument occurrences="1">
      <code>$e-&gt;getCode()</code>
    </InvalidArgument>
  </file>
  <file src="src/Controller/PortalProperty/API/ActivatePortalPropertyController.php">
    <InvalidArgument occurrences="1">
      <code>$e-&gt;getCode()</code>
    </InvalidArgument>
  </file>
  <file src="src/Controller/PortalProperty/API/BulkArchivePortalPropertiesController.php">
    <InvalidArgument occurrences="1">
      <code>$e-&gt;getCode()</code>
    </InvalidArgument>
  </file>
  <file src="src/Controller/PortalProperty/API/GetPortalPropertiesSpacesController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;dashboardHelper</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Settings/API/GetVisitRequestFlagController.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;getrixSession</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Controller/Settings/UserController.php">
    <InvalidArgument occurrences="5">
      <code>$loggedUser</code>
      <code>$loggedUser</code>
      <code>$user</code>
      <code>$user</code>
      <code>$user</code>
    </InvalidArgument>
  </file>
  <file src="src/DataMapper/EntityToResponse/Property/CategoryDataMapper.php">
    <InvalidArgument occurrences="1">
      <code>$data</code>
    </InvalidArgument>
  </file>
  <file src="src/DataMapper/EntityToResponse/Property/PropertyDetailDataMapper.php">
    <InvalidArgument occurrences="1">
      <code>$data</code>
    </InvalidArgument>
    <InvalidMethodCall occurrences="42">
      <code>getCadastralData</code>
      <code>getComposition</code>
      <code>getComposition</code>
      <code>getCondominiumInformation</code>
      <code>getConsistences</code>
      <code>getConsistences</code>
      <code>getContractInformation</code>
      <code>getContractInformation</code>
      <code>getContractInformation</code>
      <code>getContractInformation</code>
      <code>getContractInformation</code>
      <code>getEnergyClass</code>
      <code>getEnergyClass</code>
      <code>getFeatures</code>
      <code>getFeatures</code>
      <code>getGarage</code>
      <code>getGarage</code>
      <code>getGeographyInformation</code>
      <code>getGeographyInformation</code>
      <code>getGeographyInformation</code>
      <code>getGeographyInformation</code>
      <code>getGeographyInformation</code>
      <code>getImagesList</code>
      <code>getImagesList</code>
      <code>getIndustrial</code>
      <code>getIndustrial</code>
      <code>getLand</code>
      <code>getLand</code>
      <code>getPlansList</code>
      <code>getPlansList</code>
      <code>getSharedApartmentInformation</code>
      <code>getSharedApartmentInformation</code>
      <code>getShop</code>
      <code>getShop</code>
      <code>getSurface</code>
      <code>getSurface</code>
      <code>getTypologyV2</code>
      <code>getTypologyV2</code>
      <code>getTypologyV2</code>
      <code>getTypologyV2</code>
      <code>getTypologyV2</code>
      <code>getTypologyV2</code>
    </InvalidMethodCall>
  </file>
  <file src="src/DataMapper/EntityToResponse/Searches/AgencyActiveSearch/PolygonAreaResponseDataMapper.php">
    <InvalidArgument occurrences="1"/>
  </file>
  <file src="src/DataMapper/EntityToResponse/Searches/Match/PolygonAreaResponseDataMapper.php">
    <InvalidArgument occurrences="1"/>
  </file>
  <file src="src/DataMapper/RequestModelToDto/Portal/PortalDataMapper.php">
    <InvalidArgument occurrences="2">
      <code>$data-&gt;getContractBegin()</code>
      <code>$data-&gt;getContractExpiration()</code>
    </InvalidArgument>
  </file>
  <file src="src/DataProvider/Message/ApiGtx/Authenticated/AuthenticatedMessageProxy.php">
    <UndefinedThisPropertyFetch occurrences="2">
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agentId</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/DataProvider/Messaging/ApiGtx/Messaging.php">
    <InvalidReturnType occurrences="2">
      <code>array</code>
      <code>array</code>
    </InvalidReturnType>
  </file>
  <file src="src/DataProvider/Search/ApiGtx/RequestData/ListSearchParams.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$categories</code>
    </InvalidPropertyAssignmentValue>
    <InvalidReturnType occurrences="1">
      <code>array</code>
    </InvalidReturnType>
  </file>
  <file src="src/DataProvider/VirtualTour/ApiGtx/Authenticated/AuthenticatedVirtualTourProxy.php">
    <ParamNameMismatch occurrences="1">
      <code>$raw360Id</code>
    </ParamNameMismatch>
    <UndefinedThisPropertyFetch occurrences="14">
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/DataProvider/VirtualTour/ApiGtx/Proxy/VirtualTourProxy.php">
    <ParamNameMismatch occurrences="1">
      <code>$raw360Id</code>
    </ParamNameMismatch>
  </file>
  <file src="src/DataProvider/VirtualTour/ApiGtx/VirtualTour.php">
    <ParamNameMismatch occurrences="1">
      <code>$setVirtualTourDTO</code>
    </ParamNameMismatch>
  </file>
  <file src="src/DataTransformer/StructTransformer.php">
    <ParamNameMismatch occurrences="1">
      <code>$object</code>
    </ParamNameMismatch>
  </file>
  <file src="src/Entity/Agency.php">
    <InvalidPropertyAssignmentValue occurrences="3">
      <code>isset($parameters['comunicazioni']) ? filter_var($parameters['comunicazioni'], \FILTER_VALIDATE_BOOLEAN) : null</code>
      <code>isset($parameters['disponibilitaAppuntamento']) ? filter_var($parameters['disponibilitaAppuntamento'], \FILTER_VALIDATE_BOOLEAN) : null</code>
      <code>isset($parameters['socioUnico']) ? filter_var($parameters['socioUnico'], \FILTER_VALIDATE_BOOLEAN) : null</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Entity/Agent.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>true</code>
    </InvalidPropertyAssignmentValue>
    <InvalidReturnType occurrences="1">
      <code>string</code>
    </InvalidReturnType>
    <UndefinedPropertyAssignment occurrences="1">
      <code>$this-&gt;profilo-&gt;fBiografia</code>
    </UndefinedPropertyAssignment>
  </file>
  <file src="src/Entity/Property/Category.php">
    <InvalidReturnType occurrences="1">
      <code>int|null</code>
    </InvalidReturnType>
  </file>
  <file src="src/Entity/Property/Contract.php">
    <InvalidReturnType occurrences="1">
      <code>int|null</code>
    </InvalidReturnType>
  </file>
  <file src="src/Entity/Property/SubTypology.php">
    <InvalidReturnType occurrences="1">
      <code>int|null</code>
    </InvalidReturnType>
  </file>
  <file src="src/Entity/Property/Typology.php">
    <InvalidReturnType occurrences="1">
      <code>int|null</code>
    </InvalidReturnType>
  </file>
  <file src="src/Event/DataProvider/CacheAfterEvent.php">
    <InvalidArgument occurrences="1">
      <code>false</code>
    </InvalidArgument>
  </file>
  <file src="src/Event/DataProvider/CacheBeforeEvent.php">
    <InvalidArgument occurrences="1">
      <code>false</code>
    </InvalidArgument>
  </file>
  <file src="src/EventListener/FeatureToggleListener.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;sideMenuHelper</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/EventSubscriber/SearchEventSubscriber.php">
    <InvalidPropertyAssignmentValue occurrences="1"/>
  </file>
  <file src="src/Formatter/Agency.php">
    <InvalidMethodCall occurrences="2">
      <code>getAgencyLogo</code>
      <code>getAgencyLogo</code>
    </InvalidMethodCall>
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$imageFormatter</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Formatter/Estimates/EstimateValueFormatter.php">
    <NullArgument occurrences="1">
      <code>null</code>
    </NullArgument>
  </file>
  <file src="src/Formatter/Immovisita/ListingImmovisitaAdFormatter.php">
    <NullArgument occurrences="2">
      <code>null</code>
      <code>null</code>
    </NullArgument>
  </file>
  <file src="src/Formatter/Messaging/MessageVisitPlanFormatter.php">
    <UndefinedMethod occurrences="1">
      <code>$data</code>
    </UndefinedMethod>
  </file>
  <file src="src/Formatter/Messaging/ProjectFormatter.php">
    <NullArgument occurrences="1">
      <code>null</code>
    </NullArgument>
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;immobiliareUrlFormatter</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Formatter/Portal/ProjectPortalFormatter.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;fieldToFormatMethods</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Formatter/Portal/PropertyPortalFormatter.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;fieldToFormatMethods</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Formatter/Property/AdFormatter.php">
    <InvalidArgument occurrences="1">
      <code>$property-&gt;getSurface() || !is_numeric($property-&gt;getSurface())</code>
    </InvalidArgument>
    <NullArgument occurrences="1">
      <code>null</code>
    </NullArgument>
  </file>
  <file src="src/Formatter/Property/ListingAdFormatter.php">
    <InvalidArgument occurrences="1">
      <code>$property-&gt;getSurface() || !is_numeric($property-&gt;getSurface())</code>
    </InvalidArgument>
  </file>
  <file src="src/Formatter/Property/PriceFormatter.php">
    <NullArgument occurrences="2">
      <code>null</code>
      <code>null</code>
    </NullArgument>
  </file>
  <file src="src/Formatter/Property/RentPriceFormatter.php">
    <NullArgument occurrences="1">
      <code>null</code>
    </NullArgument>
    <ParamNameMismatch occurrences="1">
      <code>$data</code>
    </ParamNameMismatch>
  </file>
  <file src="src/Formatter/Property/SalePriceFormatter.php">
    <NullArgument occurrences="1">
      <code>null</code>
    </NullArgument>
    <ParamNameMismatch occurrences="1">
      <code>$data</code>
    </ParamNameMismatch>
  </file>
  <file src="src/Formatter/Property/SurfaceFormatter.php">
    <NullArgument occurrences="1">
      <code>null</code>
    </NullArgument>
    <ParamNameMismatch occurrences="1">
      <code>$data</code>
    </ParamNameMismatch>
  </file>
  <file src="src/Formatter/PropertyFormatter.php">
    <NullArgument occurrences="1">
      <code>null</code>
    </NullArgument>
  </file>
  <file src="src/Formatter/RangeRentPriceFormatter.php">
    <NullArgument occurrences="5">
      <code>null</code>
      <code>null</code>
      <code>null</code>
      <code>null</code>
      <code>null</code>
    </NullArgument>
  </file>
  <file src="src/Formatter/RangeSalePriceFormatter.php">
    <NullArgument occurrences="5">
      <code>null</code>
      <code>null</code>
      <code>null</code>
      <code>null</code>
      <code>null</code>
    </NullArgument>
  </file>
  <file src="src/Formatter/RangeSurfaceFormatter.php">
    <NullArgument occurrences="5">
      <code>null</code>
      <code>null</code>
      <code>null</code>
      <code>null</code>
      <code>null</code>
    </NullArgument>
  </file>
  <file src="src/Formatter/ScheduledVisit/ScheduledVisitFormatter.php">
    <InvalidArgument occurrences="1">
      <code>$data['agent']</code>
    </InvalidArgument>
  </file>
  <file src="src/Formatter/Shared/DetailedRelativeDateFormatter.php">
    <ParamNameMismatch occurrences="1">
      <code>$date</code>
    </ParamNameMismatch>
  </file>
  <file src="src/Formatter/User/UserSearchFormatter.php">
    <InvalidArgument occurrences="1">
      <code>$value</code>
    </InvalidArgument>
    <InvalidPropertyAssignmentValue occurrences="3">
      <code>$fValue</code>
      <code>$fValue</code>
      <code>RangeSalePriceFormatter::format($value-&gt;getMin(), $value-&gt;getMax())</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Handler/Administration/ContractHandler.php">
    <InvalidReturnType occurrences="1">
      <code>array</code>
    </InvalidReturnType>
  </file>
  <file src="src/Handler/Property/AdHandler.php">
    <InvalidArgument occurrences="1">
      <code>$status</code>
    </InvalidArgument>
  </file>
  <file src="src/Helper/AcquisitionHelper.php">
    <InvalidArrayOffset occurrences="1"/>
  </file>
  <file src="src/Helper/Adapter/AuthenticatedAgencyApiClientHelper.php">
    <InvalidReturnType occurrences="1">
      <code>bool</code>
    </InvalidReturnType>
  </file>
  <file src="src/Helper/Adapter/AuthenticatedAgencyLeadApiClientHelper.php">
    <InvalidReturnType occurrences="2">
      <code>string</code>
      <code>string</code>
    </InvalidReturnType>
  </file>
  <file src="src/Helper/Adapter/AuthenticatedMessagingApiClientHelper.php">
    <UndefinedThisPropertyFetch occurrences="26">
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agencyId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
      <code>$this-&gt;agentId</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Helper/Adapter/AuthenticatedScheduledVisitApiClientHelper.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$client</code>
    </InvalidPropertyAssignmentValue>
    <UndefinedMethod occurrences="1">
      <code>create</code>
    </UndefinedMethod>
  </file>
  <file src="src/Helper/AgencyEstimatesApiClientHelper.php">
    <InvalidArgument occurrences="1">
      <code>$result['agentDetails']['show']</code>
    </InvalidArgument>
  </file>
  <file src="src/Helper/Base/Contract.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$this-&gt;formatBorsellino($contract-&gt;borsellino)</code>
    </InvalidPropertyAssignmentValue>
    <UndefinedPropertyAssignment occurrences="8">
      <code>$contract-&gt;fAgenzia</code>
      <code>$contract-&gt;fAnnunciEstero</code>
      <code>$contract-&gt;fAnnunciPrestigio</code>
      <code>$contract-&gt;fDataFine</code>
      <code>$contract-&gt;fProdottiEcommerce</code>
      <code>$contract-&gt;fSitoAgenzia</code>
      <code>$contract-&gt;fTelefonateSmart</code>
      <code>$contract-&gt;fVirtualTour</code>
    </UndefinedPropertyAssignment>
  </file>
  <file src="src/Helper/Base/TelefonoSmart.php">
    <InvalidArrayOffset occurrences="6">
      <code>$extensionData['active']</code>
      <code>$extensionData['extraInfo']</code>
      <code>$extensionData['extraInfo']</code>
      <code>$extensionData['extraInfo']</code>
      <code>$extensionData['extraInfo']</code>
      <code>$extensionData['extraInfo']</code>
    </InvalidArrayOffset>
  </file>
  <file src="src/Helper/Base/Validator.php">
    <InvalidReturnType occurrences="4">
      <code>bool</code>
      <code>bool</code>
      <code>bool</code>
      <code>bool</code>
    </InvalidReturnType>
  </file>
  <file src="src/Helper/CommonHelper.php">
    <NullArgument occurrences="1">
      <code>null</code>
    </NullArgument>
  </file>
  <file src="src/Helper/CustomersApiClientHelper.php">
    <InvalidReturnType occurrences="2">
      <code>LookupResult</code>
      <code>string</code>
    </InvalidReturnType>
  </file>
  <file src="src/Helper/LoginHelper.php">
    <InvalidReturnType occurrences="1">
      <code>string|null</code>
    </InvalidReturnType>
  </file>
  <file src="src/Helper/MessagingApiClientHelper.php">
    <InvalidArgument occurrences="1"/>
  </file>
  <file src="src/Helper/PortalsHelper.php">
    <NullArgument occurrences="1">
      <code>null</code>
    </NullArgument>
  </file>
  <file src="src/Helper/PropertiesApiClientHelper.php">
    <InvalidReturnType occurrences="1">
      <code>LookupResult</code>
    </InvalidReturnType>
  </file>
  <file src="src/Helper/PropertiesHelper.php">
    <InvalidReturnType occurrences="1">
      <code>int</code>
    </InvalidReturnType>
  </file>
  <file src="src/Helper/Security.php">
    <InvalidArgument occurrences="2">
      <code>Response::HTTP_BAD_REQUEST</code>
      <code>Response::HTTP_BAD_REQUEST</code>
    </InvalidArgument>
  </file>
  <file src="src/Helper/VisibilitiesHelper.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;serviceMappingFromApiToMlsName</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Kernel.php">
    <ParamNameMismatch occurrences="1">
      <code>$container</code>
    </ParamNameMismatch>
  </file>
  <file src="src/Model/Acquisition/Privates/SearchFilters/AcquisitionFiltersForView.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;zone</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Model/ApiModel/Agency/AgencyPropertiesRequest.php">
    <InvalidReturnType occurrences="2">
      <code>self</code>
      <code>self</code>
    </InvalidReturnType>
  </file>
  <file src="src/Model/Estimates/EstimateDetailForView.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;date</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Model/Estimates/UserInformation.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$userSearches</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Model/Geography/City.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$id</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Model/Lookup/InternationalPhonePrefixes/InternationalPhonePrefixItem.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;countryShortCode</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Model/Lookup/InternationalPhonePrefixes/InternationalPhonePrefixResponse.php">
    <InvalidPropertyAssignmentValue occurrences="3">
      <code>$temp + (array) $this-&gt;data</code>
      <code>$tmp</code>
      <code>array_values((array) $this-&gt;data)</code>
    </InvalidPropertyAssignmentValue>
    <UndefinedMethod occurrences="1">
      <code>$this-&gt;data</code>
    </UndefinedMethod>
  </file>
  <file src="src/Model/Match/Geography/MacroZone.php">
    <NullArgument occurrences="1">
      <code>$this-&gt;number</code>
    </NullArgument>
  </file>
  <file src="src/Model/Messaging/V2/ListThreads.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;mode</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Model/Messaging/V2/Thread.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;mode</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Model/Property/Composition.php">
    <InvalidPropertyAssignmentValue occurrences="2">
      <code>$airConditioning</code>
      <code>$heating</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Model/Property/SearchFilters/AdStatsFilter.php">
    <InvalidReturnType occurrences="1">
      <code>string[]|null</code>
    </InvalidReturnType>
  </file>
  <file src="src/Model/Property/SearchFilters/AuctionsSearchFilter.php">
    <InvalidReturnType occurrences="1">
      <code>array</code>
    </InvalidReturnType>
  </file>
  <file src="src/Model/Response/Geography/CityResponse.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$coordinates</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Model/Response/Messaging/Property/Geography/CityResponse.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$id</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Model/Response/Property/AdResponse.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$properties</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Model/Response/Property/AuctionResponse.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$sales</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Model/Response/Property/CompositionResponse.php">
    <InvalidPropertyAssignmentValue occurrences="2">
      <code>$furniture</code>
      <code>$roomHeight</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Model/User/UserSearch.php">
    <InvalidPropertyAssignmentValue occurrences="2">
      <code>$typologies</code>
      <code>$zones</code>
    </InvalidPropertyAssignmentValue>
  </file>
  <file src="src/Repository/PropertiesRepository.php">
    <InvalidArgument occurrences="1">
      <code>$result-&gt;annunciSpaziDisponibili &amp;&amp; !empty($result-&gt;annunciSpaziDisponibili['tutti'])</code>
    </InvalidArgument>
  </file>
  <file src="src/Resolver/StatsNameResolver.php">
    <InvalidArrayOffset occurrences="1">
      <code>self::$CACHE[$key]</code>
    </InvalidArrayOffset>
  </file>
  <file src="src/Security/Authentication/Provider/LoginProvider.php">
    <UndefinedThisPropertyFetch occurrences="1">
      <code>$this-&gt;notifyLoginAttemptDisabledAgencyEnpoint</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Service/Base/Adapter/AgencyAdapter.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>!empty($agencyView-&gt;responsabile) ? $agencyView-&gt;responsabile-&gt;chatbot : 0</code>
    </InvalidPropertyAssignmentValue>
    <UndefinedMethod occurrences="10">
      <code>$this-&gt;parameters</code>
      <code>$this-&gt;parameters</code>
      <code>$this-&gt;parameters</code>
      <code>$this-&gt;parameters</code>
      <code>$this-&gt;parameters</code>
      <code>$this-&gt;parameters</code>
      <code>$this-&gt;parameters</code>
      <code>$this-&gt;parameters</code>
      <code>$this-&gt;parameters</code>
      <code>$this-&gt;parameters</code>
    </UndefinedMethod>
  </file>
  <file src="src/Service/Base/Builder/EstimateLookupRequestBuilder.php">
    <InvalidArgument occurrences="1"/>
  </file>
  <file src="src/Service/Base/Sdk/Projects.php">
    <InvalidArgument occurrences="5">
      <code>Response::HTTP_BAD_REQUEST</code>
      <code>Response::HTTP_BAD_REQUEST</code>
      <code>Response::HTTP_BAD_REQUEST</code>
      <code>Response::HTTP_BAD_REQUEST</code>
      <code>Response::HTTP_BAD_REQUEST</code>
    </InvalidArgument>
  </file>
  <file src="src/Service/Base/Validation/VATNumber/Pattern/IsItalian.php">
    <ParamNameMismatch occurrences="1">
      <code>$pi</code>
    </ParamNameMismatch>
  </file>
  <file src="src/Service/Base/YouTube.php">
    <InvalidReturnType occurrences="1">
      <code>int|string</code>
    </InvalidReturnType>
  </file>
  <file src="src/Service/Mail/Entity/AbstractAgencyInvitationEntity.php">
    <UndefinedThisPropertyFetch occurrences="2">
      <code>$this-&gt;template</code>
      <code>$this-&gt;translator</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Service/Mail/Strategy/PortalServiceRequest.php">
    <InvalidArgument occurrences="1">
      <code>$entity-&gt;getCC()</code>
    </InvalidArgument>
  </file>
  <file src="src/Service/Mail/Strategy/TelefonoSmartServiceRequest.php">
    <UndefinedMethod occurrences="3">
      <code>getBody</code>
      <code>getSubject</code>
      <code>getTo</code>
    </UndefinedMethod>
  </file>
  <file src="src/Service/Rest/Adapter/SdkAutocomplete.php">
    <ParamNameMismatch occurrences="1">
      <code>$params</code>
    </ParamNameMismatch>
    <UndefinedThisPropertyFetch occurrences="3">
      <code>$this-&gt;autocomplete</code>
      <code>$this-&gt;autocomplete</code>
      <code>$this-&gt;autocomplete</code>
    </UndefinedThisPropertyFetch>
  </file>
  <file src="src/Service/Rest/Geo.php">
    <InvalidPropertyAssignmentValue occurrences="1">
      <code>$requestStack-&gt;getCurrentRequest()</code>
    </InvalidPropertyAssignmentValue>
    <UndefinedMethod occurrences="2">
      <code>get</code>
      <code>get</code>
    </UndefinedMethod>
  </file>
  <file src="src/Service/Sdk/AbstractSdkClient.php">
    <InvalidArgument occurrences="1">
      <code>$params</code>
    </InvalidArgument>
  </file>
  <file src="src/Service/Sdk/Event/SdkEventAfter.php">
    <InvalidArgument occurrences="1">
      <code>false</code>
    </InvalidArgument>
  </file>
  <file src="src/Service/Sdk/Event/SdkEventBefore.php">
    <InvalidArgument occurrences="1">
      <code>false</code>
    </InvalidArgument>
  </file>
  <file src="src/Twig/FeatureToggle/FeatureToggleTokenParser.php">
    <MethodSignatureMismatch occurrences="1">
      <code>$token</code>
    </MethodSignatureMismatch>
  </file>
  <file src="src/Validator/Entity/Agent/AgentValidator.php">
    <InvalidArgument occurrences="2">
      <code>$agent-&gt;profilo-&gt;cap</code>
      <code>$agent-&gt;profilo-&gt;codiceFiscale</code>
    </InvalidArgument>
  </file>
</files>
