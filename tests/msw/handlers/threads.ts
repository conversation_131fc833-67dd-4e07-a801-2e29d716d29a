import { HttpResponse, http } from 'msw';

import messaggingList from '../../mock/old-api/old-messagging/GET_threads.json';
import messaggingDetail from '../../mock/old-api/old-messagging/GET_threads-uuid.json';

const UUID: string = '548612e0-85e2-5b0d-a853-5bdaa39251ba';

export const handlers = [
    http.get('/api/v2/messaging/threads', ({ request }) => {
        return HttpResponse.json(messaggingList);
    }),

    http.get(`/api/v2/messaging/threads/${UUID}?page=1&results=50`, ({ request }) => {
        return HttpResponse.json(messaggingDetail);
    }),
];
