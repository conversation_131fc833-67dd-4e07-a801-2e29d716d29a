@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

// GTX-SECTION__DESCRIPTION
.gtx-section__description {
  padding: 2rem 1.6rem;

  .alert {
    margin: 0 0 2rem;
  }
}

.gtx-section__description__title {
  padding: 1rem 0;
  @include typography(body-title-2);
  color: color(content-medium);
}

// GTX-SECTION__DESCRIPTION | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .gtx-section__description {
    padding: 2rem 3rem;
  }
}

// GTX-SECTION__BAR
.gtx-section__bar {
  padding: 2rem 1.6rem;
  background-color: color(background-main);

  .row {
    margin: 0 -1.1rem;
  }

  .gtx-form [class*='col-xs-']:not(.gtx-form) {
    padding-bottom: 0;
  }
}

.gtx-section__bar--border-top {
  border-top: 0.1rem solid color(border-main);
}

.gtx-section__bar--border-bottom {
  border-bottom: 0.1rem solid color(border-main);
}

.gtx-section__bar__legend {
  width: calc(100% - 25rem);

  .gtx-std-margin-right {
    white-space: nowrap;
    display: inline-block;

    &:first-child {
      margin-bottom: 0.8rem;
    }
  }
}

// GTX-SECTION__BAR | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .gtx-section__bar {
    padding: 2rem 3rem;
  }

  .gtx-section__bar__legend {
    .gtx-std-margin-right {
      &:first-child {
        display: inline-block;
      }
    }
  }
}

// GTX-SECTION__BAR | DESKTOP
@include media('screen', '>=#{breakpoint(md)}') {
  .gtx-section__bar__legend {
    .gtx-std-margin-right {
      &:first-child {
        display: inline;
        margin-bottom: 0;
      }
    }
  }
}

// GTX-SECTION__CONTENT
.gtx-section__content {
  padding: 2rem 1.6rem;

  .gtx-section__description + & {
    padding-top: 0;
  }

  &--maxWidth {
    max-width: 76.8rem;

    @include media('screen', '>=#{breakpoint(lg)}') {
      max-width: 96rem;
    }
  }

  &__title {
    @include typography(title-1);
    color: color(content-medium);
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    padding: 2rem 3rem;
  }

  &--paddingBottom {
    padding-bottom: space(4xl);
  }
}

// GTX-SECTION__FOOTER
.gtx-section__footer {
  padding: 0.8rem 1.6rem;
  border-top: 0.1rem solid color(border-main);

  .double-button .btn {
    float: left;
    width: calc(50% - 0.4rem);
    margin-right: 0.4rem;
    text-transform: uppercase;

    & + .btn {
      margin-left: 0.4rem;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}

// GTX-SECTION__FOOTER | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .gtx-section__footer {
    padding: 2rem 3rem;
    text-align: right;

    .double-button {
      display: inline-block;

      .btn {
        float: none;
        width: auto;
        margin-right: 0;

        & + .btn {
          margin-left: 1.6rem;
        }
      }
    }

    .btn + .double-button {
      padding-left: 3rem;
      margin-left: 2.6rem;
      border-left: 0.1rem solid color(border-main);
    }
  }

  .gtx-section__footer--center {
    text-align: center;
  }
}

// GTX-SECTION__PAGER
.gtx-section__pager {
  padding: 2rem 1.6rem;
}

// GTX-SECTION__PAGER | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .gtx-section__pager {
    padding-left: 3rem;
    padding-right: 3rem;

    .show-page {
      float: left;
      width: 12rem;
    }

    .pager {
      float: right;
    }
  }
}

// TODO: RIVEDERE QUANTO SARà FATTO IL COMPONENTE
.toolbar-item {
  display: inline-flex;
  align-items: center;
  margin-right: space(sm);
  @include typography(body-small);

  * + * {
    margin-left: space(xs);
  }
}

.customer-search-bar {
  padding: space(md);
  border-bottom: 0.1rem solid color(border-main);

  @include media('screen', '>=#{breakpoint(sm)}') {
    padding: space(md) space(xl);
  }

  &__firstRow {
    white-space: nowrap;
  }

  @include media('screen', '<#{breakpoint(sm)}') {
    &__firstRow {
      margin-bottom: space(sm);
    }
  }
}
