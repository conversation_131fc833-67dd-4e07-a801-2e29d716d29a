@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

// MODAL

.modal-open {
  .modal {
    padding: 0;
  }

  .gtx-ask-help {
    display: none;
  }
}

.modal {
  @include z-index(modal);

  &-backdrop {
    @include z-index(modal, -1);
  }

  .modal-dialog {
    width: 100%;
    height: 100%;
    margin: 0;
  }

  &.modal-full-screen {
    padding-right: 0 !important;

    .modal-dialog {
      width: 100%;
      height: 100%;
      padding: 0;
      margin: 0;

      .modal-body {
        height: 100%;

        @include media('screen', '>=#{breakpoint(sm)}') {
          height: 100vh;
        }
      }
    }

    #frame_immagini_tour360 {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      margin: 0;
    }

    #frame_fotoplan {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      margin: 0;
    }

    &--withHeader {
      .modal-dialog {
        .modal-body {
          height: calc(100% - 4.9rem);

          @include media('screen', '>=#{breakpoint(sm)}') {
            height: calc(100vh - 6.1rem);
          }
        }
      }
    }
  }

  .modal-content {
    position: absolute;
    top: 0;
    right: 0;
    width: inherit;
    height: 100%;
    border: none;
    border-radius: 0;

    // Mobile modal version NOT full screen
    &--noFullScreen {
      position: static;
      height: auto;
      margin-left: auto;
      margin-right: auto;
      max-width: 90%;

      .modal-header {
        text-align: left;
      }
    }

    @include media('screen', '<#{breakpoint(sm)}') {
      //fix per iOS
      overflow-y: auto;
      -webkit-overflow-scrolling: touch;
    }
  }

  .modal-header {
    position: relative;
    min-height: 4.8rem;
    line-height: 4.8rem;
    padding: 0 4.8rem 0 0;
    text-align: center;

    .close {
      position: absolute;
      top: 0;
      right: 0;
      width: 4.8rem;
      height: 4.8rem;
      margin: 0;
      opacity: 0.2;
      text-indent: -999rem;
      padding: 0;
      cursor: pointer;
      background: transparent;
      border: 0;
      -webkit-appearance: none;

      span {
        display: none;
      }

      &:before,
      &:after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        margin: auto;
        background-color: color(background-reversed);
        content: '';
        width: 2rem;
        height: 0.2rem;
        border-radius: radius(sm);
      }

      &:before {
        transform: rotate(45deg);
      }

      &:after {
        transform: rotate(-45deg);
      }

      &:hover {
        opacity: 1;
      }
    }
  }

  .modal-header h4,
  //una volta tolti altri elementi oltre a titolo e pulsante close all'interno dell'header togliere questa riga e il margin-right negativo
  .modal-title {
    display: block;
    height: 4.8rem;
    padding-left: 1.8rem;
    margin: 0;
    margin-right: -4.8rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 100%;
    @include typography(title-1);
    color: color(content-high);
    line-height: 4.8rem;
  }

  .modal-body {
    height: calc(100% - 10.6rem);
    padding: 2rem 1.8rem;
    overflow-y: auto;

    .row + .row {
      margin-top: 2rem;
    }
  }

  .modal-body__old-modal {
    margin-left: -1.1rem;
    margin-right: -1.1rem;
  }

  .modal-footer {
    background-color: color(background-main);
    padding: 0.8rem 1.6rem;

    .btn {
      text-transform: uppercase;
    }

    .double-button .btn {
      float: left;
      width: calc(50% - 0.4rem);
      margin-right: 0.4rem;

      & + .btn {
        margin-left: 0.4rem;
      }

      &:last-child {
        margin-right: 0;
      }
    }

    .btn-group .btn + .btn {
      margin-left: -0.1rem;
    }
  }

  .bootbox-body {
    @include typography(body-small);
    color: color(content-medium);
  }

  .modal-xlg {
    .vertical-align-center {
      padding: 0;
    }

    .modal-body {
      height: calc(100% - 4.8rem); //tolgo altezza header
      padding: 0;
    }
  }
}

.loading {
  .modal-body {
    min-height: 11rem;
  }
}

.iframe-modal.editor-planimetria {
  iframe {
    position: absolute;
    left: 0;
    right: 0;
    display: block;
    height: calc(100vh - 6.1rem);
    //dimensioni minime iframe
    min-width: 80rem;
    min-height: 53rem;
  }
}

@include media('screen', '<#{breakpoint(sm)}') {
  .modal-open {
    .modal {
      padding-right: 0 !important;
    }
  }
}

// MODAL | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .modal-open {
    .modal {
      padding-right: 1.5rem !important;

      &-full-screen {
        padding-right: 0 !important;
      }
    }
  }

  .modal {
    .modal-dialog {
      width: 60rem; //misura standard da mantenere per retro-compatibilità
      height: auto;
      margin: 5.5rem auto 0;

      &.modal-md {
        width: 60rem;
      }

      &.modal-sm {
        width: 40rem;
      }

      &.modal-lg {
        width: 90%;
      }
    }

    .modal-header {
      min-height: 6rem;
      line-height: 6rem;
      text-align: left;
      padding-left: 3rem;

      .close {
        top: 0.6rem;
      }
    }

    .modal-header h4,
    //una volta tolti altri elementi oltre a titolo e pulsante close all'interno dell'header togliere questa riga e il margin-right negativo
    .modal-title {
      height: 6rem;
      line-height: 6rem;
      padding-left: 0;
      margin-right: 0;
    }

    .modal-content {
      position: static;
      height: auto;
      margin-left: auto;
      margin-right: auto;
    }

    .modal-body {
      height: auto;
      overflow-y: auto;
      padding: 3rem;

      &--minHeight {
        min-height: 20rem;
      }
    }

    &--noPadding-md-up {
      @include media('screen', '>=#{breakpoint(sm)}') {
        .modal-body {
          padding: 0;
        }
      }
    }

    .modal-body--max-height {
      max-height: 40rem;
      padding-bottom: 0;

      &:after {
        content: '';
        display: block;
        height: 3rem;
      }
    }

    .modal-footer {
      background-color: color(background-main);
      padding: 2rem 3rem;

      .btn {
        text-transform: uppercase;

        & + .btn {
          margin-left: 0.8rem;
        }
      }

      .double-button .btn {
        float: none;
        width: auto;
        margin-right: 0;

        & + .btn {
          margin-left: 0.8rem;
        }
      }

      .btn-group .btn + .btn {
        margin-left: -0.1rem;
      }
    }

    .modal-xlg {
      width: 90%;
      height: 90%;
      padding: 0;
      margin: auto;

      .modal-dialog {
        width: 100%;
      }

      .modal-body {
        height: calc(90vh - 6.1rem); //tolgo altezza header
        padding: 0;
      }
    }
  }

  .extra-small-size {
    & > .modal-dialog {
      width: 40rem;
    }
  }

  .small-medium-size {
    & > .modal-dialog {
      width: 60rem;
    }
  }

  .iframe-modal.editor-planimetria {
    iframe {
      height: calc(90vh - 6.1rem);
    }
  }
}

// MODAL | DESKTOP
@include media('screen', '>=#{breakpoint(md)}') {
  .modal {
    .modal-dialog.modal-lg {
      width: 90rem;
    }
  }
}

// GTX-MODAL-LIST SELECT
// lista per selezionare una voce
.gtx-modal-list-select {
  padding: 0;
  margin: -2rem -1.8rem 0;
  list-style: none;

  li {
    border-bottom: 0.1rem solid color(border-main);

    a {
      display: block;
      padding: 1rem 1.5rem;
      color: color(content-medium);
      cursor: pointer;

      &:hover,
      &.active {
        background: #e5f0f6;
        color: color(content-action);
        text-decoration: none;
      }
    }
  }
}

// GTX-MODAL-LIST SELECT | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .gtx-modal-list-select {
    margin: -3rem -3rem 0;
  }
}

.gx-wizardModal {
  .gx-modal__footer {
    display: flex;
    align-items: center;

    > * {
      width: 33.33%;
    }
  }
}

.gx-wizardModal-body {
  text-align: center;

  img {
    width: 100%;
    height: auto;
  }

  p {
    margin: space(md) 0 0;
    color: color(content-medium);
  }
}

.gx-wizardModal-steps {
  display: flex;
  align-items: center;
  justify-content: center;

  &__step {
    width: 0.6rem;
    height: 0.6rem;
    border-radius: radius(rounded);
    background-color: #dfdfdf;

    &--active {
      @extend .gx-wizardModal-steps__step;
      background-color: color(background-brand);
    }

    & + & {
      margin-left: 0.6rem;
    }
  }
}

//Fix close bootbox
.bootbox-close-button {
  position: absolute;
  top: space(sm);
  right: 0;
  width: 4.8rem;
  height: 4.8rem;
  margin: 0;
  opacity: 0.2;
  text-indent: -999rem;
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;

  span {
    display: none;
  }

  &:before,
  &:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    margin: auto;
    background-color: color(background-reversed);
    content: '';
    width: 2rem;
    height: 0.2rem;
    border-radius: radius(sm);
  }

  &:before {
    transform: rotate(45deg);
  }

  &:after {
    transform: rotate(-45deg);
  }

  &:hover {
    opacity: 1;
  }
}

.modal.loading {
  .gx-loader-overlay {
    top: 0;

    .gx-loader {
      top: 3.2rem;
    }
  }

  .modal-footer {
    display: none;
  }
}

.modal {
  .modal-body {
    .gx-loader-overlay {
      top: 0;
    }
  }
}

.vertical-alignment-helper {
  display: table;
  height: 100%;
  width: 100%;
}

.vertical-align-center {
  display: table-cell;
  vertical-align: middle;
  padding: 4em 0;

  .modal-content {
    box-shadow: none;
    border: 0.1rem solid #777;
    width: inherit;
    height: inherit;
    margin: 0 auto;
  }
}
