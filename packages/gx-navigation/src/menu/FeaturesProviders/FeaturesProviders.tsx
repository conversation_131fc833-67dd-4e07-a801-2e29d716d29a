import type { ReactNode } from 'react';
import { CountersProvider } from './contexts/CountersContext';

const providersArray = [CountersProvider];

/**
 * In this component we can add all the providers that we need to use in the menu
 */
export const FeaturesProviders = (props: { children: ReactNode }) => {
  return providersArray.reduce((acc, Provider) => {
    return <Provider>{acc}</Provider>;
  }, props.children);
};
