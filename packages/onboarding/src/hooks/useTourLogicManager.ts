import { useCallback, useEffect, useState } from "react"
import { IAppTour, IAppTourStep } from "src/types/tour"
import { postTourReadApi } from "src/web-api"
import { useToursContext } from "./useToursContext"


/**
 * all'aggiornamento degli appTours controlla se ci sono tour validi
 * se non è già mostrato, ritorna lo step da visualizzare
 */
export const useTourLogicManager = () => {
    const { appTours, showTour, setTourIndex, setStepIndex, stepIndex, tourIndex, setShowTour, updateTourListFromApi } = useToursContext()

    const [currentTourStep, setCurrentTourStep] = useState<IAppTourStep | null>(null)
    const [currentTour, setCurrentTour] = useState<IAppTour | null>(null)

    const showTourIfAvailable = useCallback(() => {
        if (!showTour
            && appTours.length > 0
            && appTours[0].steps.length > 0
        ) {
            setTourIndex(0)
            setStepIndex(0)
            setShowTour(true)
        }

    }, [showTour, appTours])



    useEffect(() => showTourIfAvailable(), [appTours])


    useEffect(() => {

        if (showTour
            && appTours[tourIndex]?.steps[stepIndex]) {
            setCurrentTour(appTours[tourIndex])
            setCurrentTourStep(appTours[tourIndex].steps[stepIndex])
        }

    }, [stepIndex, tourIndex, showTour])

    const onNextClick = () => {
        if (
            showTour
            && appTours[tourIndex].steps[stepIndex + 1]
        ) {
            setStepIndex(currentStep => currentStep + 1)
        }
    }

    const onPrevClick = () => {
        if (
            showTour
            && (stepIndex - 1) >= 0
            && appTours[tourIndex].steps[stepIndex - 1]
        ) {
            setStepIndex(currentStep => currentStep - 1)
        }
    }

    const onClose = () => {
        setShowTour(false)

        if (currentTour) {
            //se ci sono altri tour avanza e mostra il prossimo
            //altrimenti prova a fare il fetch ritriggerando il check all'update degli appTours
            if (appTours[tourIndex + 1] && appTours[tourIndex + 1].steps?.length > 0) {
                postTourReadApi(currentTour.id)
                setTourIndex(currentIndex => currentIndex + 1)
                setStepIndex(0)
                setShowTour(true)
            } else {
                postTourReadApi(currentTour.id).then(() => updateTourListFromApi())
            }

        }
    }


    return {
        currentTourStep,
        currentTour,
        onNextClick,
        onPrevClick,
        onClose
    }
}
