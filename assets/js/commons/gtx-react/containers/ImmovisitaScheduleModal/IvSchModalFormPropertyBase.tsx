import * as React from 'react';
import { Loader } from '../../../gtx-react/components';
import { IvSchModalRefBox } from './IvSchModalRefBox';
import { IIvSchModalFormPropertyProps } from './IvSchModalForm';
import classNames from 'classnames';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { trans } from '@pepita-i18n/babelfish';

export const IvSchModalFormPropertyBase: React.FC<
    IIvSchModalFormPropertyProps
> = ({
    loadingError,
    onChangeRefClick,
    loading,
    realEstateObj,
    customClass,
}) => {
    return (
        <div
            className={classNames(
                'gx-scheduled-visit-modal__property',
                customClass
            )}
        >
            {loadingError && (
                <div className="gx-scheduled-visit-modal__propertyError">
                    <p>{trans('label.immovisita_scheduled.selected_error')}</p>
                    <Button
                        onClick={() => {
                            onChangeRefClick();
                        }}
                    >
                        {trans('label.immovisita_scheduled.select_property')}
                    </Button>
                </div>
            )}
            {loading ? (
                <Loader
                    loading={true}
                    fixedOverlay={false}
                    inlineOverlay={true}
                />
            ) : !loading && realEstateObj ? (
                <>
                    <IvSchModalRefBox realEstateObj={realEstateObj} />
                    <Button
                        onClick={() => {
                            onChangeRefClick();
                        }}
                        iconOnly={true}
                    >
                        <Icon name="pencil" />
                    </Button>
                </>
            ) : (
                !realEstateObj &&
                !loadingError && (
                    <Button
                        onClick={() => {
                            onChangeRefClick();
                        }}
                    >
                        {trans('label.immovisita_scheduled.select_property')}
                    </Button>
                )
            )}
        </div>
    );
};
