import React from 'react';
import { Icon, IconProps } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { Loader } from 'gtx-react/components';
import { trans } from '@pepita-i18n/babelfish';
import { PerformanceBadge } from 'gtx-react/components/PerformanceBadge';

type QualityItem = {
    key: string;
    title: string;
    isCompleted: boolean;
    issueType: 'warning' | 'positive' | 'negative';
    description?: string;
    actionUrl?: string;
    actionLabel?: string;
    actionIcon?: IconProps['name'];
    isVisible: boolean;
};

type AgencyContact = {
    name: string;
    phone?: string;
    email?: string;
};

type ImprovePropertyQualityProps = {
    propertyRanking: number;
    propertyId: string;
    qualityItems: QualityItem[];
    agencyContact?: AgencyContact | null;
    isLoading?: boolean;
    portalName: string;
};

const QualityItemComponent = ({ item }: { item: QualityItem }) => {
    if (!item.isVisible) {
        return null;
    }

    return (
        <div className="improve-list-item">
            {item.isCompleted ? (
                <Icon
                    name="check-circle--active"
                    className="gx-text-positive"
                />
            ) : (
                <Icon
                    name={`${
                        item.issueType === 'warning'
                            ? 'exclamation-mark-circle--active'
                            : 'cross-circle--active'
                    }`}
                    className={`gx-text-${item.issueType}`}
                />
            )}
            <div className="improve-list-item__content">
                <div className="improve-list-item__title">{item.title}</div>
                {!item.isCompleted && item.description && (
                    <ul>
                        <li>{item.description}</li>
                    </ul>
                )}
            </div>
            {!item.isCompleted && item.actionUrl && (
                <Button
                    className="improve-list-item__action"
                    variant="accent"
                    as="a"
                    href={item.actionUrl}
                >
                    <Icon name={item.actionIcon || 'plus'} />
                    <span>{item.actionLabel || trans('label.add')}</span>
                </Button>
            )}
        </div>
    );
};

const AgencyContactInfo = ({ contact }: { contact: AgencyContact }) => (
    <div className="gx-alert gx-alert--info">
        <div className="improve-alert">
            <div className="gx-title-2">
                {trans('label.improve_ad_quality.reach_max')}
            </div>
            <p className="gx-body-small">
                {trans('label.improve_ad_quality.reach_max_desc')}
            </p>
            <div className="improve-alert__agent">{contact.name}</div>
            <div className="improve-alert__contact">
                {contact.phone && (
                    <span className="gx-body-small">
                        <Icon name="phone" />
                        {contact.phone}
                    </span>
                )}
                {contact.email && (
                    <span className="gx-body-small">
                        <Icon name="mail" />
                        {contact.email}
                    </span>
                )}
            </div>
        </div>
    </div>
);

export const ImprovePropertyQuality = ({
    propertyRanking,
    qualityItems,
    agencyContact,
    isLoading = false,
    portalName,
}: ImprovePropertyQualityProps) => {
    if (isLoading) {
        return (
            <div className="gx-modal__bodyLoading">
                <Loader loading={true} fixedOverlay={false} centered={true} />
            </div>
        );
    }

    const allItemsCompleted = qualityItems
        .filter((item) => item.isVisible)
        .every((item) => item.isCompleted);

    return (
        <>
            <PerformanceBadge
                className="performance-badge--modal"
                performance={allItemsCompleted ? 'high' : 'medium'}
                large={true}
            >
                <>
                    <span>
                        <b>{`${trans('label.ad_quality_short')}: `}</b>
                        {`${propertyRanking}%`}
                    </span>
                    <Icon
                        name={
                            allItemsCompleted
                                ? 'check-circle--active'
                                : 'exclamation-mark-circle--active'
                        }
                    />
                </>
            </PerformanceBadge>
            <p
                dangerouslySetInnerHTML={{
                    __html: trans('label.improve_ad_quality_explain', {
                        PORTAL_NAME: portalName,
                    }),
                }}
            />

            {allItemsCompleted && agencyContact && (
                <AgencyContactInfo contact={agencyContact} />
            )}
            <div className="improve-list">
                {qualityItems.map((item) => (
                    <QualityItemComponent
                        key={`improve_item_${item.key}`}
                        item={item}
                    />
                ))}
            </div>
        </>
    );
};
