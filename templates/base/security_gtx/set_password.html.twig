{% extends 'base/security_gtx/base.html.twig' %}

{% block inner_content %}
	{% include 'base/security_gtx/inner-header.html.twig' with {'customer': true} %}
	<div class="login-content">
		{{ form_start(
			form,
			{ 'attr':
				{
					'class': 'form-account',
					'data-component': 'form-password',
					'autocomplete': 'off'
				}
			})
		}}
		<h1>{{ panelTitle }}</h1>
		<p>{{ panelSubTitle }}</p>
		{% for flashMessage in app.session.flashbag.get('error') %}
			<div class="alertNew alertNew-error">
				<svg class="gx-icon">
					<use xlink:href="{{ gtxConstants.ICONS_SVG_SPRITE_FILE_PATH }}#cross"></use>
				</svg>
				<p>{{ flashMessage }}</p>
			</div>
		{% endfor %}
		<div class="alert-container"></div>
		{# trick per impedire l'autocomplete del campo password #}
		<input type="password" name="fake-password" class="hidden" />
		<div class="form-group riga gtx-error-container strength-meter-container">
			{{ form_label(
				form.password.first, form.password.first,
				{'label_attr':
					{
						'class': 'sr-only'
					}
				})
			}}
			{{ form_widget(
				form.password.first,
				{'attr':
					{
						'class': 'input-lg form-control',
						'data-component': 'password-new',
						'placeholder': 'Inserisci la password',
						'data-parsley-required': 'true',
						'data-parsley-required-message': 'Inserire la password',
						'data-parsley-minlength': '6',
						'data-parsley-minlength-message': 'La password deve essere di almeno 6 caratteri',
						'autofocus': 'true',
						'autocomplete': 'off'
					}
				})
			}}
			<div class="progress" id="strength-meter" style="display: none;"></div>
		</div>
		<div class="form-group riga gtx-error-container">
			{{ form_label(
				form.password.second, form.password.second,
				{'label_attr':
					{
						'class': 'sr-only'
					}
				})
			}}
			{{ form_widget(
				form.password.second,
				{'attr':
					{
						'class': 'input-lg form-control',
						'data-component': 'password-repeat',
						'placeholder': 'Ripeti la password',
						'data-parsley-required': 'true',
						'data-parsley-required-message': 'Ripetere la password',
						'data-parsley-equalto': '#form_password_first',
						'data-parsley-equalto-message': 'Ripetere la password correttamente',
						'autofocus': 'true',
						'autocomplete': 'off'
					}
				})
			}}
		</div>
		<div class="form-account__footer clearfix">
			<a href="{{ path('app_login') }}">Torna al login</a>
			<input type="submit" class="btn btn-lg btn-secondary invia" data-action="submit" value="Conferma">
		</div>
		{{ form_end(form) }}
	</div>
	{% include 'base/security_gtx/footer.html.twig' %}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ webpack_asset('base/set-password') }}
{% endblock %}
