@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.gx-error {
  min-height: calc(100vh - #{$gx-new-menu-header-height});
  padding: space(4xl) space(md) space(2xl);
  color: color(content-high);
  text-align: center;
  display: flex;
  justify-content: center;

  &__pic {
    margin: 0 auto space(lg);
    display: flex;
    justify-content: center;

    img {
      width: 18.8rem;
      height: 18rem;
    }
  }

  &__title {
    margin-bottom: space(md);
    @include typography(title-1);
  }

  &__text {
    @include typography(body);
  }
}
