import { Routes } from '../../routes';
import { HeaderConfigMap } from '../../types';

const removeTrailingSlash = (path: string): string => path.replace(/\/$/, '');

/**
 * @deprecated Use `getHeaderConfiguration` instead
 */
export const getRouteConfigByPathname = (routes: HeaderConfigMap, pathname: string) => {
  const clearPathname = removeTrailingSlash(pathname);

  if (clearPathname in routes) {
    return routes[clearPathname as Routes];
  }

  return null;
};
