import {
  createContext,
  memo,
  PropsWithChildren,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { MenuConfig, Pathname } from '../../types';
import { useMenuConfigSegmentContext } from '../MenuProvider';
import { useGxNavigationMenuContext } from '../../shared/components/GxNavigationMenuContext';

type PathRecordType = Record<string, boolean>;

type MenuCurrentOpenedSectionContextProps = {
  pathRecord: PathRecordType;
};

const MenuCurrentOpenedSectionContext = createContext<
  MenuCurrentOpenedSectionContextProps | undefined
>(undefined);

const MenuCurrentOpenedSectionDispatcher = createContext<
  React.Dispatch<React.SetStateAction<PathRecordType>> | undefined
>(undefined);

export const MenuCurrentOpenedSectionProvider = memo(function MenuCurrentOpenedSectionProviderMemo({
  menuItem,
  children,
}: PropsWithChildren<{
  menuItem: MenuConfig;
}>) {
  const { activePathNames } = useMenuConfigSegmentContext();
  const { openSideMenu } = useGxNavigationMenuContext();

  const init = useCallback(() => {
    const activePathFound = Object.keys(menuItem).find((key) => {
      return activePathNames ? activePathNames[1] === key : null;
    });

    const pathRecord = Object.keys(menuItem).reduce<PathRecordType>((acc, key) => {
      // If the active path is found (manually set), set the state related to the path to true
      if (activePathFound) {
        acc[key] = key === activePathFound;
      } else {
        // Otherwise, set the state to false
        acc[key] = menuItem[key as Pathname].item?.expanded || false;
      }
      return acc;
    }, {});

    return pathRecord;
  }, [activePathNames, menuItem]);

  const [pathRecord, setPathRecord] = useState<PathRecordType>(init());

  useEffect(() => {
    setPathRecord(init());
  }, [init, openSideMenu]);

  return (
    <MenuCurrentOpenedSectionContext.Provider value={{ pathRecord }}>
      <MenuCurrentOpenedSectionDispatcher.Provider value={setPathRecord}>
        {children}
      </MenuCurrentOpenedSectionDispatcher.Provider>
    </MenuCurrentOpenedSectionContext.Provider>
  );
});

export const useMenuCurrentOpenedSectionContext = () => {
  const context = useContext(MenuCurrentOpenedSectionContext);

  if (!context) {
    throw new Error(
      'useMenuCurrentOpenedSectionContext must be used within a MenuCurrentOpenedSectionProvider',
    );
  }

  return context;
};

export const useMenuCurrentOpenedSectionDispatcher = () => {
  const context = useContext(MenuCurrentOpenedSectionDispatcher);

  if (!context) {
    throw new Error(
      'useMenuCurrentOpenedSectionDispatcher must be used within a MenuCurrentOpenedSectionProvider',
    );
  }

  return context;
};
