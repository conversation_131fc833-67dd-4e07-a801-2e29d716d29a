<?php

declare(strict_types=1);

namespace App\DataMapper\EntityToResponse\Property;

use App\DataMapper\AbstractDataMapper;
use App\Model\Property\CadastralData;
use App\Model\Response\Property\CadastralDataResponse;
use App\Utils\ValuteTrait;
use ObjectMapper\Configuration\ObjectMapperConfig;

class CadastralDataDataMapper extends AbstractDataMapper
{
    use ValuteTrait;

    private CadastralClassDataMapper $cadastralClassDataMapper;

    private CadastralTypeDataMapper $cadastralTypeDataMapper;

    private CadastralDataAddressDataMapper $cadastralDataAddressDataMapper;

    public function __construct(
        CadastralClassDataMapper $cadastralClassDataMapper,
        CadastralTypeDataMapper $cadastralTypeDataMapper,
        CadastralDataAddressDataMapper $cadastralDataAddressDataMapper
    ) {
        $this->cadastralClassDataMapper = $cadastralClassDataMapper;
        $this->cadastralTypeDataMapper = $cadastralTypeDataMapper;
        $this->cadastralDataAddressDataMapper = $cadastralDataAddressDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return CadastralDataResponse::class;
    }

    protected function getConfiguration($data): ObjectMapperConfig
    {
        $config = $this->getObjectMapperConfig();
        /** @var CadastralData $data */
        $config
            ->registerMapping(\get_class($data), $this->getDestinationClass())
            ->forMember('cadastralIncome', function () use ($data) {
                $cadastralIncome = $data->getCadastralIncome();

                if (null === $cadastralIncome) {
                    return null;
                }

                return $this->formatValute($cadastralIncome, 0);
            })
            ->forMember('cadastralClass', function () use ($data) {
                return $this->cadastralClassDataMapper->map($data->getCadastralClass());
            })
            ->forMember('cadastralType', function () use ($data) {
                return $this->cadastralTypeDataMapper->map($data->getCadastralType());
            })
            ->forMember('address', function () use ($data) {
                return $this->cadastralDataAddressDataMapper->map($data->getAddress());
            })
        ;

        return $config;
    }
}
