import { Icon } from '@gx-design/icon';
import clsx from 'clsx';
import { IApiRealEstateListItem } from '../types';
import { formatAddress } from '../../utils/formatAddress';

interface IRealEstateHorizontalCard {
    selected: boolean;
    data: IApiRealEstateListItem;
    onClick: () => void;
}

export const RealEstateHorizontalCard: React.FC<IRealEstateHorizontalCard> = ({
    selected = false,
    data,
    onClick,
}) => {
    return (
        <div
            className={clsx('iv-realEstateHorizontalCard', {
                'iv-realEstateHorizontalCard--selected': selected,
            })}
            onClick={onClick}
            role="button"
            tabIndex={-1}
        >
            <figure
                className={clsx('iv-realEstateHorizontalCard__image', {
                    'iv-realEstateHorizontalCard__image--placeholder':
                        !data.mainImage,
                })}
            >
                <img
                    src={
                        data.mainImage ||
                        '/bundles/base/getrix/common/img/img-placeholder.png'
                    }
                />
            </figure>
            <div className="iv-realEstateHorizontalCard__content">
                <div className="iv-realEstateHorizontalCard__contentTop">
                    <p>
                        <strong>{data.typologyV2?.name}</strong>
                        {data.typologyV2?.name && data.contract?.name && ' • '}
                        {data.contract?.name}
                    </p>
                    <p>{formatAddress(data.geographyInformation)}</p>
                    {[data.price, data.surface].filter((x) => x).join(' • ')}
                    <p>RIF. {data.id}</p>
                </div>
                <div className="iv-realEstateHorizontalCard__contentBottom">
                    {data.virtualTour && (
                        <div className="iv-realEstateHorizontalCard__icon">
                            <Icon name="tour" />
                        </div>
                    )}
                    {data.images > 0 && (
                        <div className="iv-realEstateHorizontalCard__icon">
                            <Icon name="photo" />
                            <span>{data.images}</span>
                        </div>
                    )}
                    {data.photoplan && (
                        <div className="iv-realEstateHorizontalCard__icon">
                            <Icon name="planimetry-cursor" />
                        </div>
                    )}
                    {data.plans > 0 && (
                        <div className="iv-realEstateHorizontalCard__icon">
                            <Icon name="planimetry" />
                            <span>{data.plans}</span>
                        </div>
                    )}
                    {data.video && (
                        <div className="iv-realEstateHorizontalCard__icon">
                            <Icon name="video" />
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};
