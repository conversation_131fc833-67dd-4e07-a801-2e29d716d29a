parameters:
  thrifter_rpc_services: config/%env(COUNTRY_TAG)%/sdk/rpc_services_staging.yml
  maps.consumerKey: 'immobiliarepro'
  sapi.config:
    staging:
      services.getrix.getrix.annunci.immagini:
        baseUrl: "http://sapi-raw01-staging.vrt.ies.rm.ns.farm:9977/"
        rootPath: "/storage/indomio-staging/images/"
        auth: "indomio-es-staging"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.getrix.annunci.planimetrie:
        baseUrl: "http://sapi-raw01-staging.vrt.ies.rm.ns.farm:9977/"
        rootPath: "/storage/indomio-staging/plans/"
        auth: "indomio-es-staging"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.agenzie:
        baseUrl: "http://sapi-raw01-staging.vrt.ies.rm.ns.farm:9977/"
        rootPath: "/storage/indomio-staging/images"
        auth: "indomio-es-staging"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.agenti:
        baseUrl: "http://sapi-raw01-staging.vrt.ies.rm.ns.farm:9977/"
        rootPath: "/storage/indomio-staging/profiles/agents/images"
        auth: "indomio-es-staging"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.immobiliare.media:
        baseUrl: "http://sapi-raw01-staging.vrt.ies.rm.ns.farm:9977/"
        rootPath: "/storage/indomio-staging/documents"
        auth: "indomio-es-staging"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
