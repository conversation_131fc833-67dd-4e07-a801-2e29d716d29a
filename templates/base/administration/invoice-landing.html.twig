{% extends 'base.html.twig' %}

{% block header %}
    <div id="header"></div>
{% endblock %}

{% block content %}
    <div class="invoice-landing-page">
        <h1>{{ 'label.invoice_plural'|trans({}, gtxConstants.DOMAIN_CMS) }}</h1>
        <p>
            {{ 'label.invoices.customer_care_phrase'|trans({'%CUSTOMER_CARE_NUMBER%': gtxConstants.TEL_SERVIZIO_CLIENTI}, gtxConstants.DOMAIN_CMS)|raw }}
        </p>
    </div>
{% endblock %}

{% block reactApp %}
    <div id="root"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script id="site-section-title" type="application/json">{{ title }}</script>
    {% include 'commons/core/js-scripts.html.twig' %}
    {{ webpack_asset('base/app-shell') }}
{% endblock %}
