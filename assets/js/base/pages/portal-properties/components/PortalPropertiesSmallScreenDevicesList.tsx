import gtxConstants from '@getrix/common/js/gtx-constants';
import gtxLoggedUser from '@getrix/common/js/gtx-logged-user';
import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { List, ListItem } from '@gx-design/list';
import { trans } from '@pepita-i18n/babelfish';
import { useQueryClient } from '@tanstack/react-query';
import { CardList } from 'gtx-react/components/CardList/CardList';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { isTuristicProperty } from 'lib/propertyCategories';
import { ucFirst } from 'lib/strings-formatter';
import React from 'react';
import { useSelector } from 'react-redux';
import { Loader as ListLoader, MediaQuery } from 'gtx-react/components';
import { PortalPropertiesListAlert } from '../components/PortalPropertiesListAlert';
import {
    CACHE_CONFIGS,
    CACHE_QUALITY_DETAILS_KEY_PREFIX,
    CARD_LIST_LABELS,
    PORTAL_PROPERTIES_VIEWPORT_MEDIA_QUERIES,
} from '../constants';
import { usePortalPropertiesListContext } from '../hooks/usePortalPropertiesListContext';
import { IPortalPropertiesListItem } from 'types/api/property';
import { IReduxRootState } from '../types/redux';
import { isSearchActive } from '../utils/searchFilters';
import { getPropertyQualityDetailsApi } from '../web-api/api';
import { PERFORMANCE_PATH } from '../web-api/endpoints';

function renderImage(data: IPortalPropertiesListItem) {
    return <img src={data.mainImageUrl} loading="lazy" />;
}

function StatisticsButton({ data }: { data: IPortalPropertiesListItem }) {
    const { trackEvent } = useMixpanelContext();
    const searchFilters = useSelector(
        (data: IReduxRootState) => data.searchFilters
    );

    return (
        <div className="gx-table-new__fixedCellActionWrap">
            <Button
                onClick={() => {
                    trackEvent({
                        event: 'properties_view_stats_listing',
                        extra: {
                            ['property_id']: data.id,
                            ['list_tab']: ucFirst('' + searchFilters.status),
                        },
                    });

                    window.open(
                        `${PERFORMANCE_PATH.replace(':id', data.id)}?view=stats`
                    );
                }}
                size="small"
                variant="default"
                className="explore-cta-promotion gx-table-new__fixedCellAction"
            >
                <Icon name="statistics" />
                <span>{trans('label.compare')}</span>
            </Button>
        </div>
    );
}

function renderReference(data: IPortalPropertiesListItem) {
    if (!data) {
        return null;
    }

    const property = data.properties[0];

    const reference = property?.reference ? property.reference : data.id;

    const typology = property.subTypology
        ? property.subTypology
        : property.typology;

    return (
        <List>
            <ListItem content={reference} />
            <ListItem content={typology} />
            <ListItem content={data.contract} />
        </List>
    );
}

export const PortalPropertiesSmallScreenDevicesList: React.FC<{}> = () => {
    const { listConfigs, listActions, listItems, renderMapping } =
        usePortalPropertiesListContext();
    const queryClient = useQueryClient();
    const searchFilters = useSelector(
        (data: IReduxRootState) => data.searchFilters
    );
    const agency = useSelector((data: IReduxRootState) => data.agency);
    const fields = React.useMemo(
        () => [
            {
                key: 'image',
                main: true,
                renderContent: (data: IPortalPropertiesListItem) =>
                    renderImage(data),
            },
            {
                key: 'reference',
                label: trans('label.reference'),
                renderContent: (data: IPortalPropertiesListItem) =>
                    renderReference(data),
            },
            {
                key: 'place',
                label: trans('label.place'),
                renderContent: renderMapping['place'],
            },
            {
                key: 'visibility',
                label: trans('label.visibility'),
                renderContent: renderMapping['visibility'],
            },
            {
                key: 'quality',
                label: trans('label.quality'),
                asyncContentRender: async (data: IPortalPropertiesListItem) => {
                    if (!data?.id) {
                        return;
                    }

                    // turistic real estates exception
                    if (isTuristicProperty(data)) {
                        return new Promise((_, reject) => reject());
                    }

                    const qualityData = await queryClient.fetchQuery({
                        queryKey: [
                            `${CACHE_QUALITY_DETAILS_KEY_PREFIX}${data.id}`,
                        ],
                        queryFn: () =>
                            getPropertyQualityDetailsApi(data.id).then(
                                (res) => res.data
                            ),
                        ...CACHE_CONFIGS,
                    });

                    return renderMapping['quality'](data, null, qualityData);
                },
            },
            {
                key: 'position',
                label: trans('label.list_position'),
                renderContent: renderMapping['position'],
            },
            {
                key: 'statistics',
                label: trans('label.statistics'),
                renderContent: (data) => {
                    return (
                        <>
                            {renderMapping['statistics'](data)}
                            <StatisticsButton data={data} />
                        </>
                    );
                },
            },
            {
                key: 'performance',
                label:
                    Boolean(gtxLoggedUser('roles.ROLE_USER_BACKOFFICE')) &&
                    !agency.isPerformancesModuleOn
                        ? `${trans('label.performances')} (${trans(
                              'label.beta'
                          ).toUpperCase()})`
                        : `${trans('label.performances')}`,
                renderContent: renderMapping['performance'],
                visible:
                    agency.isPerformancesModuleOn &&
                    Boolean(gtxConstants('PROPERTY_LIST_PERFORMANCE')),
            },
            {
                key: 'threads',
                label: trans('label.messages'),
                renderContent: renderMapping['threads'],
                sortable: false,
                visible: Boolean(gtxConstants('PROPERTY_LIST_MATCH_COLUMN')),
            },
            {
                key: 'match',
                label: trans('label.matches.page_title'),
                renderContent: renderMapping['match'],
                sortable: false,
                visible: Boolean(gtxConstants('PROPERTY_LIST_MATCH_COLUMN')),
            },
            {
                key: 'date',
                label: trans('label.modified_date'),
                renderContent: renderMapping['date'],
            },
        ],
        []
    );

    const data = {
        items: listItems,
        extraItemFields: ['id', 'status', 'favourite'],
    };

    const configs = {
        ...listConfigs,
        labels: CARD_LIST_LABELS,
    };

    const listLoader = useSelector((data: IReduxRootState) => data.listLoader);

    return (
        <div style={{ position: 'relative' }}>
            <PortalPropertiesListAlert />
            <ListLoader
                loading={listLoader.isLoading}
                fixedOverlay={false}
                centered={false}
            />
            <MediaQuery queries={PORTAL_PROPERTIES_VIEWPORT_MEDIA_QUERIES}>
                {(match) => (
                    <CardList
                        actions={listActions}
                        cardFields={fields}
                        cardsData={data}
                        configs={configs}
                        twoColumnsView={match.isTablet}
                        noInitialItems={
                            !data.items && !isSearchActive(searchFilters)
                        }
                    />
                )}
            </MediaQuery>
        </div>
    );
};
