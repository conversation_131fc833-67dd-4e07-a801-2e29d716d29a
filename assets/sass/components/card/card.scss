// gx-card
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$gx-card: 'gx-card';

.gx-card-list {
  background-color: color(background-alt);

  &--light {
    background-color: color(background-main);
  }

  @include media('>=#{breakpoint(sm)}') {
    display: flex;
    flex-wrap: wrap;
    padding: space(xl);
    grid-gap: space(xl);
  }

  &__bulkActions {
    position: fixed;
    @include z-index(base, 9);
    bottom: space(md);
    left: space(sm);
    display: flex;
    align-items: center;
    width: calc(100% - #{space(md)});
    padding: 0 space(md);
    border-radius: radius(sm);
    background-color: color(background-brand-alt);
    border: 0.1rem solid color(border-action);

    > span {
      margin-right: auto;
    }

    .gx-button + .gx-button {
      margin-left: 0;
    }
  }
}

.#{$gx-card} {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-top: 0.1rem solid color(border-main);
  border-bottom: 0.1rem solid color(border-main);
  background: color(background-main);

  & + & {
    margin-top: space(md);
  }

  &__header {
    display: flex;
    align-items: center;
    height: ($gx-unit-size * 6);
    padding: 0 space(md);
    border-bottom: 0.1rem solid color(border-main);

    &Actions {
      display: flex;
      margin-left: auto;

      .gx-button + .gx-button {
        margin-left: 0;
      }
    }
  }

  &__content {
    padding: space(md);

    &--noPadding {
      padding: 0;
    }

    &--paddingDouble {
      @include media('>=#{breakpoint(sm)}') {
        padding: space(xl);
      }
    }

    &.is-close {
      .#{$gx-card}-row:nth-child(n + 6) {
        max-height: 0;
        padding: 0 space(md);
        overflow: hidden;
      }
    }

    &.is-open {
      .#{$gx-card}-row:nth-child(n + 6) {
        max-height: 60rem; //TODO: modificare?
      }
    }
  }

  &__section {
    & + .#{$gx-card}__section {
      border-top: 0.1rem solid color(border-main);
    }

    &:not(:first-child) {
      padding-top: space(sm);
    }

    &:not(:last-child) {
      padding-bottom: space(sm);
    }
  }

  &__topBar {
    display: flex;
    justify-content: space-between;
    border-bottom: 0.1rem solid color(border-main);
    padding-bottom: space(sm);
  }

  &__footer {
    display: flex;
    padding: space(md);
    margin-top: auto;

    &--noPaddingTop {
      padding-top: 0;
    }

    > * {
      flex-grow: 1;
    }
  }

  &__showMore {
    display: flex;
    justify-content: center;
    padding: space(xs) space(md);
    margin-top: auto;
    border-top: 0.1rem solid color(border-main);
  }

  @include media('>=#{breakpoint(sm)}') {
    border: 0.1rem solid color(border-main);
    border-radius: radius(md);

    & + & {
      margin-top: 0;
    }

    &--col2,
    &--col3 {
      width: calc(50% - #{space(md)});
    }
  }

  @include media('>=#{breakpoint(lg)}') {
    &--col3 {
      width: calc(33.33% - #{space(lg)});
    }
  }
}

.#{$gx-card}-row {
  display: flex;
  grid-gap: space(md);
  padding: space(md);
  transition: all 0.3s ease-in-out;
  @include typography(body-small);

  &__label,
  &__value {
    width: 50%;
  }

  &__label {
    color: color(content-medium);
  }

  &__value {
    > * + * {
      margin-left: space(xs);
    }
  }

  &--property {
    padding-top: space(sm);
    padding-bottom: space(sm);
    border-bottom: 0.1rem solid color(border-main);
  }
}

.#{$gx-card}-photo {
  width: 100%;
  position: relative;
  padding-bottom: percentage(9/16);
  overflow: hidden;
  background-color: color(background-alt);

  img {
    position: absolute;
    top: 50%;
    left: 50%;
    max-width: 100%;
    max-height: 100%;
    transform: translate(-50%, -50%);
  }
}
