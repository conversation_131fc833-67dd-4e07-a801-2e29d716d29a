import { Icon, IconProps } from '@gx-design/icon';
import { TabsItem, TabsItemProps } from '@gx-design/tabs';
import clsx from 'clsx';
import { cloneElement, Fragment, ImgHTMLAttributes } from 'react';
import { Navigation, Pagination } from 'swiper/modules';
import { Swiper, SwiperProps, SwiperSlide } from 'swiper/react';

/** Pagination mechanism */
const PAGINATION_PARAMS: SwiperProps['pagination'] = {
    el: '.swiper-carousel__pagination',
    type: 'custom',
    renderCustom: function (swiper, current, total) {
        return current + '/' + total;
    },
};
/** Navigation mechanism */
const NAVIGATION: SwiperProps['navigation'] = {
    nextEl: '.swiper-carousel__button-next',
    prevEl: '.swiper-carousel__button-prev',
    disabledClass: 'disabled',
};

const MODULES: Record<SwiperCarouselProps['variant'], SwiperProps['modules']> =
    {
        gallery: [Pagination, Navigation],
        tabs: [Navigation],
        cards: [Navigation],
    };

const HAS_PAGINATION: Record<SwiperCarouselProps['variant'], boolean> = {
    gallery: true,
    tabs: false,
    cards: false,
};

const SLIDESPERVIEW: Record<
    SwiperCarouselProps['variant'],
    SwiperProps['slidesPerView']
> = {
    gallery: 1,
    tabs: 'auto',
    cards: 'auto',
};

const BREAKPOINTS: Record<
    SwiperCarouselProps['variant'],
    SwiperProps['breakpoints']
> = {
    cards: {
        480: {
            slidesPerView: 1.5,
        },
        768: {
            slidesPerView: 2.5,
            spaceBetween: 16,
        },
    },
    gallery: undefined,
    tabs: undefined,
};

type NavButtonProps = {
    type: 'prev' | 'next';
    showFade?: SwiperCarouselProps['showFade'];
};
const NavButton: React.FC<NavButtonProps> = ({ type, showFade }) => (
    <div
        className={clsx([
            'swiper-carousel__button-container',
            `swiper-carousel__button-container_${type}`,
            { fadeMargin: showFade },
        ])}
    >
        <button className={`swiper-carousel__button-${type}`}>
            <Icon name={`arrow-${type === 'prev' ? 'left' : 'right'}`} />
        </button>
    </div>
);

type SwiperCardProps = {
    icon?: IconProps['name'];
    title?: string | null;
    fields?: Array<{ label: string; value: string | number | null }>;
};
const SwiperCard: React.FC<SwiperCardProps> = (props) => (
    <div className="propertyDetail-card swiper-carousel__card">
        <div className="surfaceDetail">
            {props.icon && <Icon name={props.icon} />}
            {props.title && <h4 className="gx-title-2">{props.title}</h4>}
            {props.fields?.length && (
                <dl className="gx-body-small">
                    {props.fields.map(({ label, value }, i) => (
                        <Fragment key={`card-summary-field-${i}`}>
                            <dt>{label}</dt>
                            <dd>{value}</dd>
                        </Fragment>
                    ))}
                </dl>
            )}
        </div>
    </div>
);

const COMPONENTS: Record<SwiperCarouselProps['variant'], React.ReactElement> = {
    cards: <SwiperCard />,
    gallery: <img />,
    tabs: <TabsItem />,
};

type SwiperCarouselGalleryProps = {
    variant: 'gallery';
    items: ImgHTMLAttributes<HTMLImageElement>[];
};
type SwiperCarouselCardProps = {
    variant: 'cards';
    items: SwiperCardProps[];
};
type SwiperCarouselTabProps = {
    variant: 'tabs';
    items: TabsItemProps[];
};
type SwiperCarouselProps = (
    | SwiperCarouselGalleryProps
    | SwiperCarouselCardProps
    | SwiperCarouselTabProps
) & {
    /** Set this to `true` to show a transparent fade below the navigation arrows. */
    showFade?: boolean;
};
/**
 * New Carousel component, based on `swiperjs`. Three variants have been developed: `gallery`, `tabs` and `cards`, all of them to be found in the new detail modal.
 *
 * List of items will consists into an array of possible props to be passed to the single-slide component (an `<img />` tag for `gallery`, a `<TabsItem />` for `tabs`, and a new `<SwiperCard />` component for `cards` variant.)
 */
export const SwiperCarousel: React.FC<SwiperCarouselProps> = ({
    items,
    variant,
    showFade,
}) => (
    <Swiper
        className={`swiper-carousel swiper-carousel--${variant}`}
        modules={MODULES[variant]}
        slidesPerView={SLIDESPERVIEW[variant]}
        spaceBetween={variant === 'tabs' ? 0 : undefined}
        navigation={NAVIGATION}
        pagination={HAS_PAGINATION[variant] ? PAGINATION_PARAMS : undefined}
        breakpoints={BREAKPOINTS[variant]}
        wrapperClass="swiper-carousel__slideWrapper"
    >
        {items?.map((item, index) => (
            <SwiperSlide
                className="swiper-carousel__slide"
                key={`swiper-carousel-${variant}-slide-${index}`}
            >
                {cloneElement(COMPONENTS[variant], { ...item })}
            </SwiperSlide>
        ))}
        <NavButton type="prev" showFade={showFade} />
        <NavButton type="next" showFade={showFade} />
        {HAS_PAGINATION[variant] && (
            <div className="swiper-carousel__pagination" />
        )}
    </Swiper>
);
