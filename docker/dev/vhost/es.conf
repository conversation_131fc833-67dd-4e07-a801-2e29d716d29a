server {
    listen 80;
    listen 443 ssl;
    ssl_certificate     /application/docker/dev/certs/localhost.cert;
    ssl_certificate_key /application/docker/dev/certs/localhost.key;
    root /application/public;
    server_name mls.es.localhost gestionale.es.localhost;

    location / {
        rewrite ^/assets/.*?/bundles/(.*)$ /bundles/$1 last;
        rewrite ^/img/getrix/common/(.+)$ /bundles/base/getrix/common/img/$1 last;
        rewrite ^/(css|js|img|fonts)/(.+)$ /bundles/base/$1/$2 last;
        try_files $uri /index.php$is_args$args;
    }

    # DEV
    # This rule should only be placed on your development environment
    # In production, don't include this and don't deploy app_dev.php or config.php
    location ~ ^/index\.php(/|$) {
        fastcgi_pass unix:/run/php/php7.4-fpm.sock;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        # When you are using symlinks to link the document root to the
        # current version of your application, you should pass the real
        # application path instead of the path to the symlink to PHP
        # FPM.
        # Otherwise, PHP's OPcache may not properly detect changes to
        # your PHP files (see https://github.com/zendtech/ZendOptimizerPlus/issues/126
        # for more information).
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        # CUSTOM ENVS
        fastcgi_param COUNTRY_TAG "es";
    }

    # return 404 for all other php files not matching the front controller
    # this prevents access to other php files you don't want to be accessible.
    location ~ \.php$ {
        return 404;
    }

    access_log /var/log/nginx/gestionale.es.localhost.access.log;
    error_log  /var/log/nginx/gestionale.es.localhost.error.log;
}
