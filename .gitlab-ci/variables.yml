default:
    image: $CI_REGISTRY/qa/docker-images/php/7.4-cli:latest

variables:
    COMPOSER_RUN_SCRIPTS: post-scripts
    FILES_TO_UPDATE_VERSION: ~
    IGNORE_PHP_LINTING: true
    IGNORE_PHP_SECURITY: true
    K8S_TOKEN: c16450b1b26c6705f2a10843e4cf20
    K8S_TRIGGER: $CI_API_V4_URL/projects/1669/trigger/pipeline
    NODE_PACKAGE_MANAGER: yarn
    PHPUNIT_ENGINE: phpunit
    PHP_VERSION: 7.4
    PIPELINE_AFTER_DEPLOY: $CI_API_V4_URL/projects/5580/trigger/pipeline
    SKIP_JSTEST: ~
    SKIP_YAML_LINTING: true
    VITEST_BEFORE_TEST_CMD: $NODE_PACKAGE_MANAGER install
