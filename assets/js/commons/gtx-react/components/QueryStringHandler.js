import React, { Component } from 'react';
import {
    setQueryString,
    deleteQueryString,
    isEmpty,
    areEqualShallowCompare,
    removeObjectProps,
} from '../../lib/utility';

/**
 * @typedef {Object} QueryStringHandlerProps
 * @property {string} basePath
 * @property {Object} params
 * @property {string[]} [paramsToRemove]
 */

/**
 * @extends {Component<QueryStringHandlerProps>}
 */
export class QueryStringHandler extends Component {
    static defaultProps = {
        paramsToRemove: [],
    };

    componentDidUpdate(prevProps) {
        let oldParams = prevProps.params;
        let newParams = this.props.params;

        if (!areEqualShallowCompare(oldParams, newParams)) {
            if (isEmpty(newParams)) {
                deleteQueryString(this.props.basePath);
            } else {
                let qs = this.props.paramsToRemove
                    ? removeObjectProps(newParams, this.props.paramsToRemove)
                    : newParams;
                setQueryString(this.props.basePath, qs);
            }
        }
    }

    render() {
        return null;
    }
}
