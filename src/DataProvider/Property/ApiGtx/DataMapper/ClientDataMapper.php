<?php

declare(strict_types=1);

namespace App\DataProvider\Property\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Property\Client;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class ClientDataMapper extends AbstractDataMapper
{
    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return Client::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var array $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('firstName', static function () use ($data) {
                return !empty($data['firstname']) ? $data['firstname'] : null;
            })
            ->forMember('lastName', static function () use ($data) {
                return !empty($data['lastname']) ? $data['lastname'] : null;
            })
        ;

        return $config;
    }
}
