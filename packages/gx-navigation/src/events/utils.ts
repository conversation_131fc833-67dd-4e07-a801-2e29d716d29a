import { AnyString } from '../types';

const inputEventTypePrefix = 'gx-navigation:in:' as const;
const outputEventTypePrefix = 'gx-navigation:out:' as const;

export function createEventType<T extends string>(type: T) {
  return type;
}

export const createOutputEventType = <T extends string>(type: T) => {
  return `${outputEventTypePrefix}${type}` as const;
};

export const createInputEventType = <T extends string>(type: T) => {
  return `${inputEventTypePrefix}${type}` as const;
};

export const createInputEvent = <T extends string, Payload>(type: T, payload?: Payload) => {
  return createCustomEvent(`${inputEventTypePrefix}${type}` as const, payload);
};
export const createOutputEvent = <T extends string, Payload>(type: T, payload?: Payload) => {
  return createCustomEvent(`${outputEventTypePrefix}${type}` as const, payload);
};

export function createCustomEvent<EventType extends string, Payload>(
  type: EventType,
  payload?: Payload,
): {
  type: EventType;
  payload?: Payload;
} {
  return {
    type,
    payload,
  };
}

type EventAction<T> = {
  type: AnyString<
    ReturnType<typeof createOutputEventType> & ReturnType<typeof createInputEventType>
  >;
  payload?: T;
};

export function dispatchEvent<T>(action: EventAction<T>) {
  const event = new CustomEvent(action.type, { detail: action.payload });
  if (process.env.NODE_ENV === 'development') {
    console.log(
      `%c🚢 ${action.type}`,
      'color: white; font-weight: bold; background: #428CC6; border-radius: 3px; padding: 2px 4px',
      action.payload,
    );
  }
  window.dispatchEvent(event);
}

export function on(
  eventType: AnyString<
    ReturnType<typeof createOutputEventType> | ReturnType<typeof createInputEventType>
  >,
  callback: (payload: unknown) => void,
): () => void {
  const fn = (e: CustomEvent) => {
    callback(e.detail);
  };

  window.addEventListener(eventType, fn);

  return () => {
    window.removeEventListener(eventType, fn);
  };
}
