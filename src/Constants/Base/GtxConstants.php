<?php

namespace App\Constants\Base;

abstract class GtxConstants
{
    const AZIONE_DUPLICA_ANNUNCIO_ATTIVA = 1;
    const CATEGORIA_ASTE = 14;
    const CATEGORIA_COMMERCIALE = 2;
    const CATEGORIA_CAPANNONI = 25;
    const CATEGORIA_GARAGE_POSTI_AUTO = 22;
    const CATEGORIA_MAGAZZINI_DEPOSITI = 21;
    const CATEGORIA_NEGOZI_LOCALI = 26;
    const CATEGORIA_NUOVE_COSTRUZIONI = 6;
    const CATEGORIA_PALAZZI_EDIFICI = 20;
    const CATEGORIA_PROGETTI = 27;
    const CATEGORIA_RESIDENZIALE = 1;
    const CATEGORIA_STANZE = 4;
    const CATEGORIA_TERRENI = 24;
    const CATEGORIA_TERRENI_VIRTUALE = 10;
    const CATEGORIA_UFFICI_COWORKING = 23;
    const CATEGORIA_VACANZE = 3;
    const CATEGORIA_VACANZE_V1 = 6;
    const CATEGORIA_RICERCA_INTERNAZIONALE = 5;
    public const CATEGORIES_USING_IMMOBILE = [
        self::CATEGORIA_PALAZZI_EDIFICI,
        self::CATEGORIA_GARAGE_POSTI_AUTO,
        self::CATEGORIA_TERRENI,
        self::CATEGORIA_MAGAZZINI_DEPOSITI,
        self::CATEGORIA_UFFICI_COWORKING,
        self::CATEGORIA_CAPANNONI,
        self::CATEGORIA_NEGOZI_LOCALI,
    ];
    const CONTRATTO_AFFITTO = 2;
    const CONTRATTO_VENDITA = 1;
    const DEFAULT_CONTACT_NUMBER_SHORT_CODE = 'it';
    const DEFAULT_CURRENCY_SYMBOL = '€';
    const SURFACE_UNIT_MEASUREMENT = 'm²';
    const SPACE_UNIT_MEASUREMENT = 'm';
    const HUNDREDS_WEIGHT_MEASUREMENT = 'q';
    const LOGOUT_ROUTE = '/logout';
    const PHONE_CALLS_ROUTE = '/v2/telefonate';
    const EMAIL_GTX_INFO = '<EMAIL>';
    const GTX_VERSION_MULTI_USERS = 3;
    const EXTENSION_SOCIAL = 34;
    const EXTENSION_SUBITO_IT = 35;
    const EXTENSION_YOUDOMUS_IT = 36;
    const EXTENSION_TELEFONO_SMART = 39;
    const EXTENSION_MESSAGING = 51;
    const ID_COMUNE_SAN_MARINO = 12602;
    const MAP_MARKER_ICON = '/img/getrix/common/getrix-pin.png';
    const MAP_MARKER_ICON_2X = '/img/getrix/common/<EMAIL>';
    const PROPERTY_IMAGE_PLACEHOLDER = '/img/getrix/common/img-placeholder.png';
    const PROPERTY_IMAGE_PLACEHOLDER_NOBORDER = '/img/getrix/common/img-placeholder-no-border.png';
    const AGENCY_PAGE_IMAGE_PLACEHOLDER = '/bundles/base/getrix/common/img/img-agenzia-placeholder.png';
    const AGENCY_PAGE_IMAGE_PLACEHOLDER_2X = '/bundles/base/getrix/common/img/<EMAIL>';
    const NATION_ITALY = 'IT';
    const PHONE_MIN_LENGTH = 5;
    const DESCRIPTION_MIN_WORDS_AGENCY = 10;
    const DESCRIPTION_MAX_LENGTH_AGENCY = 3000;
    const REGEX_DESC_MIN_WORDS_LEN = '^\s*\S+(?:\s+\S+){9,}\s*$';
    const REGEX_PHONE_MOBILE = '^\+((?:(?!39).)\d+|(393))\d+$';
    const REGEX_PHONE = '^(\+)?[\s\d()\-\/.]+$'; //no lettere, può iniziare con ' + ', può contenere numeri, spazi e ( ) / - .
    const REGEX_PHONE_STRICT = '^[+]?\d+$'; //no lettere, può iniziare con ' + ', può contenere solo numeri
    const REGEX_PHONE_STRICT_NO_PREFIX = '^((?!(0{2}))\d{5,})$'; //no lettere e simboli, può contenere solo numeri
    const REGEX_PHONE_STRICT_WITH_PREFIX = '^(?:(\+)+|0{2})'; //no lettere e simboli, può contenere solo numeri
    const REGEX_TIME = '^([0-2][0-3]|[01]?[1-9]):([0-5]?[0-9]):([0-5]?[0-9])$'; //09:00:00
    const REGEX_TIME_SHORT = '^([0-2][0-3]|[01]?[1-9]):([0-5]?[0-9])$'; //09:00
    const REGEX_PIVA = '^[0-9]{11}$';
    // TODO: da ripristinare quando arriva la regex per CIF spagnolo
    //const REGEX_CF = '^[A-Za-z]{6}[0-9]{2}[A-Za-z]{1}[0-9]{2}[A-Za-z]{1}[0-9]{3}[A-Za-z]{1}$';
    const REGEX_CF = '^[A-Za-z0-9]+$';
    const REGEX_EMAIL = '^[A-Za-z0-9!#$%&\'*+=?^_`{|}~-]+(?:\.[a-z0-9!#$%&\'*+=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$';
    const REGEX_IMAGE_MIME_TYPE = '^image\/(png|jpe?g|gif)$';
    const REGEX_SDI = '^[A-Z0-9]{6,7}$';
    const REGEX_URL = '^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*)$';
    const SESSION_NAME_AGENCY = 'logged_agency';
    const SESSION_NAME_USER = 'user';
    const TIPOLOGIA_TERRENI = 28;
    const TIPOLOGIA_V2_COWORKING = 259;
    const TEL_SERVIZIO_CLIENTI = '02 87 11 87';
    const EMAIL_SERVIZIO_CLIENTI = '<EMAIL>';
    const EMAIL_ASSISTENZA_CLIENTI = '<EMAIL>';
    const EXTENSION_PROPERTY_LIST = 47;
    const EXTENSION_CLIENTS = 45;
    const USER_STATUS_TO_VERIFY = 0;
    const USER_STATUS_VERIFYING = 1;
    const USER_STATUS_ACTIVE = 2;
    const USER_STATUS_NOT_ACTIVE = 3;
    const USER_STATUS_DELETED = 999;
    const USER_ROLE_ADMINISTRATOR = 1;
    const USER_ROLE_SEGRETARY = 3;
    const USER_ROLE_AGENT = 5;
    const USER_ROLE_SALE_RESPONSIBLE = 9;
    const USER_CONTACT_PHONE = 1;
    const USER_CONTACT_MOBILE = 2;
    const USER_CONTACT_FAX = 3;
    const USER_CONTACT_OTHER = 4;
    const USER_EMAIL_TOKEN_ACTIVATION = 1;
    const USER_EMAIL_TOKEN_CHANGE_PASSWORD = 2;
    const USER_EMAIL_TOKEN_CHANGE_EMAIL = 3;
    const HOME_PATH = '/';
    const GETRIX_MULTIMEDIA_ATTIVO = 1;
    const GETRIX_MULTIMEDIA_DISATTIVO = 0;
    const GETRIX_MULTIMEDIA_SOSPESO = 2;
    const GO_TO_GETRIX_PATH = '/profile/go-to-getrix';
    const SECTION_ADMINISTRATION = 'amministrazione';
    const SECTION_DASHBOARD = 'riepilogo';
    const SECTION_LANDING_AUTOMATIC = 'landing-auto';
    const SERVICE_REQUEST_TYPE_ACTIVATION = 'activate';
    const SERVICE_REQUEST_TYPE_DEACTIVATION = 'deactivate';
    const SERVICE_REQUEST_TYPE_INCREASE_ADV_SPACES = 'increaseadvspaces';
    const SERVICE_REQUEST_TYPE_INCREASE_VISIBILITY = 'increasevisibility';
    const SERVICE_POSITION_SEARCH_AD = 'position';
    const SERVICE_REQUEST_TYPE_MORE_INFO = 'moreinfo';
    const SERVICE_SITO_AGENZIA = 'sitoagenzia';
    const SERVICE_RICHIESTE_GESTITE = 'richieste';
    const SERVICE_LISTA_ANNUNCI = 'listaannunci';
    const SERVICE_PLANIMETRIE_INTERATTIVE = 'planimetrie';
    const SERVICE_VT360 = 'vt360';
    const SERVICE_VIRTUAL_TOUR_3D = 'virtualtour3d';
    const SERVICE_ADV_SPACES_ANNUNCI_NUOVE_COSTRUZIONI = 'nuovecostruzioni';
    const SERVICE_ANNUNCI_PRESTIGIO = 'prestigio';
    const SERVICE_ANNUNCI_ESTERO = 'estero';
    const SERVICE_IMMOBILIARE = 'immobiliare';
    const SERVICE_PORTAL = 'portale';
    const SERVICE_MULTINVIO = 'multinvio';
    const SERVICE_ACQUISIZIONE_PRIVATI = 'acquisizioneprivati';
    const SERVICE_ASTE = 'aste';
    const SERVICE_CLIENTI = 'clienti';
    const SERVICE_RICHIESTE_VALUTAZIONE = 'richiestevalutazione';
    const SERVICE_REPORT = 'report';
    const SERVICE_IMMOBILE_GARANTITO = 'garantito';
    const SERVICE_AGENCY_VISIBILITY = 'visibilita';
    const SERVICE_VISIBILITA_PREMIUM = 'premium';
    const SERVICE_EXTRA_VISIBILITA_VETRINA = 'vetrina';
    const SERVICE_EXTRA_VISIBILITA_SHOWCASE = 'showcase';
    const SERVICE_EXTRA_VISIBILITA_STAR = 'star';
    const SERVICE_EXTRA_VISIBILITA_TOP = 'top';
    const SERVICE_EXTRA_VISIBILITA_IMMOBILE_SEGRETO = 'immobile segreto';
    const SERVICE_EXTRA_VISIBILITA_SECRET_PROPERTY = 'secretProperty';
    const SERVICE_LUXURY_PUBLICATION = 'luxurypublication';
    const SERVICE_ZONE_PACK = 'zonepack';
    const SERVICE_TELEFONATE_SMART = 'telefonatesmart';
    const SERVICE_WEB_MARKETING = 'webmarketing';
    const SERVICE_AGENDA = 'agenda';
    const SERVICE_YOUDOMUS = 'youdomus';
    const SERVICE_YOUDOMUS_MODULE_NAME = 'Youdomus.it';
    const SERVICE_CATALOGO_ASTE = 'catalogoaste';
    const SERVICE_SALES_REQUESTS = 'salesrequest';
    const AGENCY_VISIBILITY_STANDARD = 0;
    const AGENCY_VISIBILITY_INEVIDENZA = 1;
    const AGENCY_VISIBILITY_INVETRINA = 2;
    const AGENCY_VISIBILITY_STANDARD_LABEL = 'label.standard';
    const AGENCY_VISIBILITY_INEVIDENZA_LABEL = 'visibility.profile_1.name';
    const AGENCY_VISIBILITY_INVETRINA_LABEL = 'visibility.profile_2.name';
    const TIPOLOGIA_AGENZIA_CONTRATTO = 2;
    const SOTTOTIPOLOGIA_AGENZIA_LIGHT = 24;
    const STATO_GETRIX_LIGHT = 1;
    const STATO_GETRIX_PLUS = 2;
    const STATO_GETRIX_BUSINESS = 3;
    const URL_GETRIX_BUSINESS = 'http://business.getrix.it';
    const VIRTUAL_TOUR_360_EXAMPLE_URL = '//tour360.getrix.it/vtour/197/00/197/17/tour.html';
    const IMMOBILIARE_PATH_AGENCY_PAGE = 'agenzie-immobiliari';
    const IMMOBILIARE_PORTAL_ID = 1;
    const IMMOBILIARE_WEB_SITE_PORTAL_ID = 32;
    const GETRIX_WEB_SITE_PORTAL_ID = 207;
    const LISTGLOBALLY_PORTAL_ID = 25;
    const LOADING_ADS_GROUP_GETRIX_OK = 'getrix_ok';
    const PORTAL_ALL_PROPERTIES = 1; // sdk TipoSpaziPubblicitari::ANNUNCI_TUTTI
    const PORTAL_SALE_PROPERTIES = 2; // sdk TipoSpaziPubblicitari::ANNUNCI_VENDITA
    const PORTAL_RENT_PROPERTIES = 3; // sdk TipoSpaziPubblicitari::ANNUNCI_AFFITTO

    // I introduce these constants for export because in trunk has Hard-Coded
    public const TIPOLOGIES_ACTIVE_VERSION = [
        GtxConstants::PORTAL_SALE_PROPERTIES,
        GtxConstants::PORTAL_RENT_PROPERTIES,
    ];

    const PORTAL_IMMOBILIARE_PROPERTIES = 4; // sdk TipoSpaziPubblicitari::ANNUNCI_IMMOBILIARE
    const PORTAL_PROJECTS = 5; // sdk TipoSpaziPubblicitari::ANNUNCI_PROGETTI
    const PORTAL_CONFIG_NOT_ACTIVE_FIELD = 1; // sdk PortaleTipologyConfiguration::DISATTIVO
    const PORTAL_CONFIG_ACTIVE_FIELD = 2; //sdk PortaleTipologyConfiguration::ATTIVO
    const PORTAL_CONFIG_MANDATORY_FIELD = 3; // sdk PortaleTipologyConfiguration::OBBLIGATORIO
    const PORTAL_UNLIMITED_PROPERTIES_LABEL = 'illimitati';
    const PORTAL_UNLIMITED_PROPERTIES_VALUE = -1;
    const PORTAL_NO_DATA_LABEL = 'n.d.';
    const PROPERTY_RENT_CONTRACT = 2;
    const PROPERTY_SALE_CONTRACT = 1;
    const PROPERTY_RENT_CONTRACT_LABEL = 'Affitto';
    const PROPERTY_SALE_CONTRACT_LABEL = 'Vendita';
    const PROPERTY_ACTIVE_STATUS = 21;
    const PROPERTY_NOT_ACTIVE_STATUS = 22;
    const PROPERTY_SOLD_STATUS = 23;
    const PROPERTY_ARCHIVED_STATUS = 24;
    const PROPERTY_DRAFT_STATUS = 27;
    const PROPERTY_SOLD_ARCHIVED_PORTAL_STATUS = 13;
    const PROPERTY_SOLD_ARCHIVED_STATUS = 33;
    const PROPERTY_DELETED_STATUS = 999;
    const PROPERTY_STATUS_PORTAL_OFFSET = 20;
    const PROPERTY_SECRET_PORTAL_STATUS = 14;
    const PROPERTY_ACTIVE_PORTAL_STATUS = 21 - self::PROPERTY_STATUS_PORTAL_OFFSET;
    const PROPERTY_NOT_ACTIVE_PORTAL_STATUS = 22 - self::PROPERTY_STATUS_PORTAL_OFFSET;
    const PROPERTY_SOLD_PORTAL_STATUS = 23 - self::PROPERTY_STATUS_PORTAL_OFFSET;
    const PROPERTY_ARCHIVED_PORTAL_STATUS = 24 - self::PROPERTY_STATUS_PORTAL_OFFSET;
    const PROPERTY_DRAFT_PORTAL_STATUS = 27 - self::PROPERTY_STATUS_PORTAL_OFFSET;

    public const AD_STATUS = [
        self::PROPERTY_ACTIVE_STATUS,
        self::PROPERTY_ARCHIVED_STATUS,
        self::PROPERTY_NOT_ACTIVE_STATUS,
        self::PROPERTY_DRAFT_STATUS,

        self::PROPERTY_ACTIVE_PORTAL_STATUS,
        self::PROPERTY_ARCHIVED_PORTAL_STATUS,
        self::PROPERTY_NOT_ACTIVE_PORTAL_STATUS,
        self::PROPERTY_DRAFT_PORTAL_STATUS,
    ];
    public const AD_STATUS_PRO = [
        self::PROPERTY_ACTIVE_PORTAL_STATUS,
        self::PROPERTY_SOLD_ARCHIVED_PORTAL_STATUS,
        self::PROPERTY_ARCHIVED_PORTAL_STATUS,
        self::PROPERTY_NOT_ACTIVE_PORTAL_STATUS,
        self::PROPERTY_DRAFT_PORTAL_STATUS,
    ];

    const PROJECTS_MULTISEND = 1;
    const GTX_VERSION_MULTI_SEND = 6;
    const EMPTY_DATE_TIME = '0000-00-00 00:00:00';
    const PORTAL_PUBLISH_ALL_LABEL = 'Qualsiasi';
    const PORTAL_PUBLISH_SENT_LABEL = 'Inviati';
    const PORTAL_PUBLISH_NOT_SENT_LABEL = 'Non inviati';
    const PORTAL_SET_BY_MODAL = 1;
    const PORTAL_PUBLISH_ALL_STATUS = 0;
    const PORTAL_PUBLISH_SENT_STATUS = 1;
    const PORTAL_PUBLISH_NOT_SENT_STATUS = 2;
    const PORTAL_PROPERTIES_PUBLISH_STATUS_NONE = 0;
    const PORTAL_PROPERTIES_PUBLISH_STATUS_ALL = 1;
    const PORTAL_PROPERTIES_PUBLISH_STATUS_PARTIAL = 2;
    const PORTAL_PROPERTIES_PUBLISH_STATUS_DISABLED = 3;
    const PORTAL_ACTIVE_PRESELECTION_LABEL = 'PRESELEZIONE ATTIVA';
    const PORTAL_NOT_ACTIVE_PRESELECTION_LABEL = 'PRESELEZIONE NON ATTIVA';
    const PRIVATES_ACQUISITION_V2 = 1;
    const PUBLISH_NO_MORE_ADV_SPACE_ERROR_CODE = '003'; // sdk PropertyExceptionsMsg::PROPERTY_NO_MORE_ADV_SPACE
    const PROPERTY_LIST_DEFAULT_RESULTS = 30;
    const BACKLINK_STATUS_SUCCESS = 200;
    const BACKLINK_STATUS_WARNING = 210;
    const BACKLINK_STATUS_NOMEDIA = 220;
    const BACKLINK_STATUS_NOAGENCY = 403;
    const BACKLINK_STATUS_NOTFOUND = 404;
    const BACKLINK_STATUS_UNLOCATED = 440;
    const BACKLINK_STATUS_NOTYPE = 441;
    const BACKLINK_STATUS_NOTVALID = 442;
    const BACKLINK_STATUS_UNKNOWN = 500;
    const BACKLINK_STATUS_REJECTED = 501;
    const MODAL_MOBILE_BREAKPOINT = 768;
    const VOX_NOTIFICHE_ON = 1;
    const VOX_NOTIFICHE_OFF = 0;
    const EMPTY_USER_PROFILE_PIC_URL = '/bundles/base/getrix/common/img/<EMAIL>';
    const ESTIMATES_LABEL = 'Valutazioni immobiliari';
    const AGENCY_ESTIMATES_LABEL = 'Report immobiliare';
    const VIRTUAL_TOUR_360_LABEL = 'VirtualTour 360';
    const SALES_REQUESTS_LABEL = 'Richieste vendita';
    const YOUDOMUS_LABEL = 'Youdomus.it';
    const AUCTIONS_CATALOGUE_LABEL = 'Catalogo aste';
    const GETRIX_MODULE_NAME = 'getrix';
    const MESSAGING_MODULE_NAME = 'Messaggistica Gestionale';
    const PERFORMANCE_MODULE_NAME = 'Reportistica';
    const MATCHES_MODULE_NAME = 'Matches';
    const ZONES_MODULE_NAME = 'Zones';
    const SOLD_RENT_ADS_VERSION = 7;
    const PHOTOPLAN_LABEL = 'FotoPlan';
    const IMAGES_UPLOAD_LIMIT = 5 * 1024 * 1024; //5MB
    const RAW_360_UPLOAD_LIMIT = 10 * 1024 * 1024; //10MB
    const MIN_HQ_RESOLUTION = 786432; //Dimensioni minime HQ: 1024x768
    const GETRIX_VERSION_IMMO_GTX = 8;
    const GTX_SOURCE = 1;
    const MONITORIMM_SOURCE = 2;
    const NEWS_IMMOBILIARE_SOURCE = 3;
    const STATO_RICERCA_IMMOBILE_PROPOSTA_ACQUISTO = '9';
    const ESTENSIONE_VIRTUAL_TOUR_360 = 37;
    const ESTENSIONE_CONSULENZA_MUTUO = 48;
    const ESTENSIONE_IMMOVISITA = 50;
    const ESTENSIONE_REPORTISTICA = 53;
    const IMMOVISITA_MODULE_NAME = 'Immovisita';
    const MEDIA_ASSETS_VERSION = 1;
    const AGENCY_PAGE_VISIBILITY_KEY = 'agencyPage';
    const SECRET_PROPERTY_VISIBILITY_KEY = 'secretProperty';
    const PREMIUM_VISIBILITY_ID = 1;
    const PREMIUM_VISIBILITY_KEY = 'searchable';
    const SHOWCASE_VISIBILITY_KEY = 'showcase';
    const STAR_VISIBILITY_KEY = 'star';
    const TOP_VISIBILITY_KEY = 'top';
    const SEARCHES_GTX_VERSION = 9;
    const MATCHES_GTX_VERSION = 9;
    const REPORT_INSIGHTS_GTX_VERSION = 11;

    const RELEASE_BOOK_VISIT = 'Rilascio Prenota Visita';

    public static $user = [
        'ruolo' => [
            self::USER_ROLE_ADMINISTRATOR => 'db_agent_role.id_' . self::USER_ROLE_ADMINISTRATOR,
            self::USER_ROLE_SEGRETARY => 'db_agent_role.id_' . self::USER_ROLE_SEGRETARY,
            self::USER_ROLE_AGENT => 'db_agent_role.id_' . self::USER_ROLE_AGENT,
            self::USER_ROLE_SALE_RESPONSIBLE => 'db_agent_role.id_' . self::USER_ROLE_SALE_RESPONSIBLE,
        ],
        'status' => [
            self::USER_STATUS_ACTIVE => [
                'static' => false,
                'text' => 'Attivo',
                'transKey' => 'label.active',
                'dropdownClass' => 'status-active btn-colortext-success',
                'labelClass' => 'gx-tag--positiveFilled',
            ],
            self::USER_STATUS_NOT_ACTIVE => [
                'static' => false,
                'text' => 'Disattivo',
                'transKey' => 'label.not_active',
                'dropdownClass' => 'status-not-active btn-colortext-error',
                'labelClass' => 'gx-tag--negativeFilled',
            ],
            self::USER_STATUS_VERIFYING => [
                'static' => true,
                'text' => 'In attesa di attivazione',
                'transKey' => 'label.wait_activation',
                'buttonClass' => 'status-verifying',
                'labelClass' => 'gx-tag--warningFilled',
            ],
            self::USER_STATUS_TO_VERIFY => [
                'static' => true,
                'text' => 'Attiva utente',
                'transKey' => 'label.activate_user',
                'buttonClass' => 'status-to-verify',
                'labelClass' => 'gx-tag--primaryFilled',
            ],
        ],
        'contatto' => [
            self::USER_CONTACT_PHONE => 'Fisso',
            self::USER_CONTACT_MOBILE => 'Cellulare',
            self::USER_CONTACT_FAX => 'Fax',
            self::USER_CONTACT_OTHER => 'Altro recapito',
        ],
    ];

    public static $agencyVisibilityLabel = [
        self::AGENCY_VISIBILITY_STANDARD => self::AGENCY_VISIBILITY_STANDARD_LABEL,
        self::AGENCY_VISIBILITY_INEVIDENZA => self::AGENCY_VISIBILITY_INEVIDENZA_LABEL,
        self::AGENCY_VISIBILITY_INVETRINA => self::AGENCY_VISIBILITY_INVETRINA_LABEL,
    ];

    public static $services = [
        self::SERVICE_SITO_AGENZIA => 'label.website',
        self::SERVICE_RICHIESTE_GESTITE => 'label.requests',
        self::SERVICE_LISTA_ANNUNCI => 'Lista annunci - Getrix',
        self::SERVICE_YOUDOMUS => 'Youdomus',
        self::SERVICE_PLANIMETRIE_INTERATTIVE => 'label.interactive_plans',
        self::SERVICE_ADV_SPACES_ANNUNCI_NUOVE_COSTRUZIONI => 'service_request.adv_spaces_nc',
        self::SERVICE_ANNUNCI_PRESTIGIO => 'label.prestigious_ads',
        self::SERVICE_ANNUNCI_ESTERO => 'label.foreign_channel_ads',
        self::SERVICE_IMMOBILIARE => 'label.ad_portal',
        self::SERVICE_PORTAL => 'label.portal',
        self::SERVICE_IMMOBILE_GARANTITO => 'label.guaranteed_property',
        self::SERVICE_AGENCY_VISIBILITY => 'label.agency_visibility',
        self::SERVICE_EXTRA_VISIBILITA_VETRINA => 'label.extra_visibility_vetrina', // to delete, replaced by self::SERVICE_EXTRA_VISIBILITA_SHOWCASE
        self::SERVICE_EXTRA_VISIBILITA_SHOWCASE => 'label.extra_visibility_vetrina',
        self::SERVICE_EXTRA_VISIBILITA_STAR => 'label.extra_visibility_star',
        self::SERVICE_EXTRA_VISIBILITA_TOP => 'label.extra_visibility_top',
        self::SERVICE_WEB_MARKETING => 'label.web_marketing_services',
        self::SERVICE_LUXURY_PUBLICATION => 'label.luxury_publication',
        self::SERVICE_ZONE_PACK => 'label.zone_pack',
        self::SERVICE_TELEFONATE_SMART => 'label.smart_phone',
        self::SERVICE_POSITION_SEARCH_AD => 'label.position_search_ad',
        self::SERVICE_MULTINVIO => 'Multinvio',
        self::SERVICE_ACQUISIZIONE_PRIVATI => 'Acquisizione Privati',
        self::SERVICE_CLIENTI => 'Clienti - Getrix',
        self::SERVICE_RICHIESTE_VALUTAZIONE => 'label.property_valuations',
        self::SERVICE_REPORT => 'Report Immobiliare',
        self::SERVICE_VT360 => 'label.virtual_tour_3d',
        self::SERVICE_AGENDA => 'Agenda - Getrix',
        self::SERVICE_CATALOGO_ASTE => 'Catalogo Aste',
        self::SERVICE_SALES_REQUESTS => 'Richieste vendita',
        self::SECRET_PROPERTY_VISIBILITY_KEY => 'Immobile Segreto',
        'secret' => 'Immobile Segreto', //secretProperty service name used in trunk
    ];

    public static $allForClient = [
        'CATEGORIA_ASTE' => self::CATEGORIA_ASTE,
        'CATEGORIA_VACANZE' => self::CATEGORIA_VACANZE,
        'CATEGORIA_VACANZE_V1' => self::CATEGORIA_VACANZE_V1,
        'CATEGORIA_STANZE' => self::CATEGORIA_STANZE,
        'CATEGORIA_NUOVE_COSTRUZIONI' => self::CATEGORIA_NUOVE_COSTRUZIONI,
        'CATEGORIA_PROGETTI' => self::CATEGORIA_PROGETTI,
        'CATEGORIA_RICERCA_INTERNAZIONALE' => self::CATEGORIA_RICERCA_INTERNAZIONALE,
        'TIPOLOGIA_V2_COWORKING' => self::TIPOLOGIA_V2_COWORKING,
        'CONTRATTO_AFFITTO' => self::CONTRATTO_AFFITTO,
        'CONTRATTO_VENDITA' => self::CONTRATTO_VENDITA,
        'LOGOUT_ROUTE' => self::LOGOUT_ROUTE,
        'PHONE_CALLS_ROUTE' => self::PHONE_CALLS_ROUTE,
        'ID_COMUNE_SAN_MARINO' => self::ID_COMUNE_SAN_MARINO,
        'NATION_ITALY' => self::NATION_ITALY,
        'REGEX_PHONE_MOBILE' => self::REGEX_PHONE_MOBILE,
        'DEFAULT_CONTACT_NUMBER_SHORT_CODE' => self::DEFAULT_CONTACT_NUMBER_SHORT_CODE,
        'DEFAULT_CURRENCY_SYMBOL' => self::DEFAULT_CURRENCY_SYMBOL,
        'SURFACE_UNIT_MEASUREMENT' => self::SURFACE_UNIT_MEASUREMENT,
        'REGEX_DESC_MIN_WORDS_LEN' => self::REGEX_DESC_MIN_WORDS_LEN,
        'REGEX_EMAIL' => self::REGEX_EMAIL,
        'REGEX_PHONE' => self::REGEX_PHONE,
        'REGEX_PHONE_STRICT' => self::REGEX_PHONE_STRICT,
        'REGEX_PHONE_STRICT_NO_PREFIX' => self::REGEX_PHONE_STRICT_NO_PREFIX,
        'REGEX_TIME' => self::REGEX_TIME,
        'REGEX_TIME_SHORT' => self::REGEX_TIME_SHORT,
        'REGEX_PIVA' => self::REGEX_PIVA,
        'REGEX_CF' => self::REGEX_CF,
        'REGEX_SDI' => self::REGEX_SDI,
        'REGEX_URL' => self::REGEX_URL,
        'TEL_SERVIZIO_CLIENTI' => self::TEL_SERVIZIO_CLIENTI,
        'EMAIL_SERVIZIO_CLIENTI' => self::EMAIL_SERVIZIO_CLIENTI,
        'HOME_PATH' => self::HOME_PATH,
        'GO_TO_GETRIX_PATH' => self::GO_TO_GETRIX_PATH,
        'GTX_VERSION_MULTI_USERS' => self::GTX_VERSION_MULTI_USERS,
        'GETRIX_MULTIMEDIA_ATTIVO' => self::GETRIX_MULTIMEDIA_ATTIVO,
        'GETRIX_MULTIMEDIA_DISATTIVO' => self::GETRIX_MULTIMEDIA_DISATTIVO,
        'GETRIX_MULTIMEDIA_SOSPESO' => self::GETRIX_MULTIMEDIA_SOSPESO,
        'USER_STATUS_TO_VERIFY' => self::USER_STATUS_TO_VERIFY,
        'USER_STATUS_VERIFYING' => self::USER_STATUS_VERIFYING,
        'USER_STATUS_ACTIVE' => self::USER_STATUS_ACTIVE,
        'USER_STATUS_NOT_ACTIVE' => self::USER_STATUS_NOT_ACTIVE,
        'USER_STATUS_DELETED' => self::USER_STATUS_DELETED,
        'USER_EMAIL_TOKEN_ACTIVATION' => self::USER_EMAIL_TOKEN_ACTIVATION,
        'USER_EMAIL_TOKEN_CHANGE_PASSWORD' => self::USER_EMAIL_TOKEN_CHANGE_PASSWORD,
        'USER_EMAIL_TOKEN_CHANGE_EMAIL' => self::USER_EMAIL_TOKEN_CHANGE_EMAIL,
        'SECTION_ADMINISTRATION' => self::SECTION_ADMINISTRATION,
        'SECTION_DASHBOARD' => self::SECTION_DASHBOARD,
        'SECTION_LANDING_AUTOMATIC' => self::SECTION_LANDING_AUTOMATIC,
        'SERVICE_REQUEST_TYPE_ACTIVATION' => self::SERVICE_REQUEST_TYPE_ACTIVATION,
        'SERVICE_REQUEST_TYPE_DEACTIVATION' => self::SERVICE_REQUEST_TYPE_DEACTIVATION,
        'SERVICE_REQUEST_TYPE_INCREASE_ADV_SPACES' => self::SERVICE_REQUEST_TYPE_INCREASE_ADV_SPACES,
        'SERVICE_REQUEST_TYPE_INCREASE_VISIBILITY' => self::SERVICE_REQUEST_TYPE_INCREASE_VISIBILITY,
        'SERVICE_REQUEST_TYPE_MORE_INFO' => self::SERVICE_REQUEST_TYPE_MORE_INFO,
        'SERVICE_SITO_AGENZIA' => self::SERVICE_SITO_AGENZIA,
        'SERVICE_RICHIESTE_GESTITE' => self::SERVICE_RICHIESTE_GESTITE,
        'SERVICE_LISTA_ANNUNCI' => self::SERVICE_LISTA_ANNUNCI,
        'SERVICE_PLANIMETRIE_INTERATTIVE' => self::SERVICE_PLANIMETRIE_INTERATTIVE,
        'SERVICE_VT360' => self::SERVICE_VT360,
        'SERVICE_VIRTUAL_TOUR_3D' => self::SERVICE_VIRTUAL_TOUR_3D,
        'SERVICE_ADV_SPACES_ANNUNCI_NUOVE_COSTRUZIONI' => self::SERVICE_ADV_SPACES_ANNUNCI_NUOVE_COSTRUZIONI,
        'SERVICE_ANNUNCI_PRESTIGIO' => self::SERVICE_ANNUNCI_PRESTIGIO,
        'SERVICE_ANNUNCI_ESTERO' => self::SERVICE_ANNUNCI_ESTERO,
        'SERVICE_IMMOBILIARE' => self::SERVICE_IMMOBILIARE,
        'SERVICE_MULTINVIO' => self::SERVICE_MULTINVIO,
        'SERVICE_PORTAL' => self::SERVICE_PORTAL,
        'SERVICE_IMMOBILE_GARANTITO' => self::SERVICE_IMMOBILE_GARANTITO,
        'SERVICE_AGENCY_VISIBILITY' => self::SERVICE_AGENCY_VISIBILITY,
        'SERVICE_EXTRA_VISIBILITA_VETRINA' => self::SERVICE_EXTRA_VISIBILITA_VETRINA,
        'SERVICE_EXTRA_VISIBILITA_STAR' => self::SERVICE_EXTRA_VISIBILITA_STAR,
        'SERVICE_EXTRA_VISIBILITA_TOP' => self::SERVICE_EXTRA_VISIBILITA_TOP,
        'SERVICE_LUXURY_PUBLICATION' => self::SERVICE_LUXURY_PUBLICATION,
        'SERVICE_ZONE_PACK' => self::SERVICE_ZONE_PACK,
        'SERVICE_TELEFONATE_SMART' => self::SERVICE_TELEFONATE_SMART,
        'SERVICE_WEB_MARKETING' => self::SERVICE_WEB_MARKETING,
        'SERVICE_AGENDA' => self::SERVICE_AGENDA,
        'SERVICE_POSITION_SEARCH_AD' => self::SERVICE_POSITION_SEARCH_AD,
        'SERVICE_YOUDOMUS' => self::SERVICE_YOUDOMUS,
        'SERVICE_CATALOGO_ASTE' => self::SERVICE_CATALOGO_ASTE,
        'AGENCY_VISIBILITY_STANDARD' => self::AGENCY_VISIBILITY_STANDARD,
        'AGENCY_VISIBILITY_INEVIDENZA' => self::AGENCY_VISIBILITY_INEVIDENZA,
        'AGENCY_VISIBILITY_INVETRINA' => self::AGENCY_VISIBILITY_INVETRINA,
        'VIRTUAL_TOUR_360_EXAMPLE_URL' => self::VIRTUAL_TOUR_360_EXAMPLE_URL,
        'PORTAL_SET_BY_MODAL' => self::PORTAL_SET_BY_MODAL,
        'PORTAL_ALL_PROPERTIES' => self::PORTAL_ALL_PROPERTIES,
        'PORTAL_SALE_PROPERTIES' => self::PORTAL_SALE_PROPERTIES,
        'PORTAL_RENT_PROPERTIES' => self::PORTAL_RENT_PROPERTIES,
        'PORTAL_IMMOBILIARE_PROPERTIES' => self::PORTAL_IMMOBILIARE_PROPERTIES,
        'PORTAL_PROJECTS' => self::PORTAL_PROJECTS,
        'PORTAL_CONFIG_NOT_ACTIVE_FIELD' => self::PORTAL_CONFIG_NOT_ACTIVE_FIELD,
        'PORTAL_CONFIG_ACTIVE_FIELD' => self::PORTAL_CONFIG_ACTIVE_FIELD,
        'PORTAL_CONFIG_MANDATORY_FIELD' => self::PORTAL_CONFIG_MANDATORY_FIELD,
        'PORTAL_NO_DATA_LABEL' => self::PORTAL_NO_DATA_LABEL,
        'PORTAL_PROPERTIES_PUBLISH_STATUS_NONE' => self::PORTAL_PROPERTIES_PUBLISH_STATUS_NONE,
        'PORTAL_PROPERTIES_PUBLISH_STATUS_ALL' => self::PORTAL_PROPERTIES_PUBLISH_STATUS_ALL,
        'PORTAL_PROPERTIES_PUBLISH_STATUS_PARTIAL' => self::PORTAL_PROPERTIES_PUBLISH_STATUS_PARTIAL,
        'PORTAL_PROPERTIES_PUBLISH_STATUS_DISABLED' => self::PORTAL_PROPERTIES_PUBLISH_STATUS_DISABLED,
        'PORTAL_ACTIVE_PRESELECTION_LABEL' => self::PORTAL_ACTIVE_PRESELECTION_LABEL,
        'PORTAL_NOT_ACTIVE_PRESELECTION_LABEL' => self::PORTAL_NOT_ACTIVE_PRESELECTION_LABEL,
        'PHOTOPLAN_LABEL' => self::PHOTOPLAN_LABEL,
        'YOUDOMUS_LABEL' => self::YOUDOMUS_LABEL,
        'AUCTIONS_CATALOGUE_LABEL' => self::AUCTIONS_CATALOGUE_LABEL,
        'PRIVATES_ACQUISITION_V2' => self::PRIVATES_ACQUISITION_V2,
        'PROJECTS_MULTISEND' => self::PROJECTS_MULTISEND,
        'IMMOBILIARE_PORTAL_ID' => self::IMMOBILIARE_PORTAL_ID,
        'PROPERTY_RENT_CONTRACT' => self::PROPERTY_RENT_CONTRACT,
        'PROPERTY_SALE_CONTRACT' => self::PROPERTY_SALE_CONTRACT,
        'GTX_VERSION_MULTI_SEND' => self::GTX_VERSION_MULTI_SEND,
        'LISTGLOBALLY_PORTAL_ID' => self::LISTGLOBALLY_PORTAL_ID,
        'PUBLISH_NO_MORE_ADV_SPACE_ERROR_CODE' => self::PUBLISH_NO_MORE_ADV_SPACE_ERROR_CODE,
        'PROPERTY_LIST_DEFAULT_RESULTS' => self::PROPERTY_LIST_DEFAULT_RESULTS,
        'PORTAL_UNLIMITED_PROPERTIES_LABEL' => self::PORTAL_UNLIMITED_PROPERTIES_LABEL,
        'MODAL_MOBILE_BREAKPOINT' => self::MODAL_MOBILE_BREAKPOINT,
        'EMPTY_USER_PROFILE_PIC_URL' => self::EMPTY_USER_PROFILE_PIC_URL,
        'DESCRIPTION_MIN_WORDS_AGENCY' => self::DESCRIPTION_MIN_WORDS_AGENCY,
        'DESCRIPTION_MAX_LENGTH_AGENCY' => self::DESCRIPTION_MAX_LENGTH_AGENCY,
        'SERVICE_ACQUISIZIONE_PRIVATI' => self::SERVICE_ACQUISIZIONE_PRIVATI,
        'SERVICE_CLIENTI' => self::SERVICE_CLIENTI,
        'SERVICE_RICHIESTE_VALUTAZIONE' => self::SERVICE_RICHIESTE_VALUTAZIONE,
        'SERVICE_REPORT' => self::SERVICE_REPORT,
        'SOLD_RENT_ADS_VERSION' => self::SOLD_RENT_ADS_VERSION,
        'IMAGES_UPLOAD_LIMIT' => self::IMAGES_UPLOAD_LIMIT,
        'RAW_360_UPLOAD_LIMIT' => self::RAW_360_UPLOAD_LIMIT,
        'TIPO_MACROZONE_COMUNE_MACROZONE' => GeographyConstants::TIPO_MACROZONE_COMUNE_MACROZONE,
        'TIPO_MACROZONE_COMUNE_AREE_URBANE' => GeographyConstants::TIPO_MACROZONE_COMUNE_AREE_URBANE,
        'GTX_SOURCE' => self::GTX_SOURCE,
        'MONITORIMM_SOURCE' => self::MONITORIMM_SOURCE,
        'NEWS_IMMOBILIARE_SOURCE' => self::NEWS_IMMOBILIARE_SOURCE,
        'STATO_RICERCA_IMMOBILE_PROPOSTA_ACQUISTO' => self::STATO_RICERCA_IMMOBILE_PROPOSTA_ACQUISTO,
        'MEDIA_ASSETS_VERSION' => self::MEDIA_ASSETS_VERSION,
        'SERVICE_SALES_REQUESTS' => self::SERVICE_SALES_REQUESTS,
        'PROPERTY_ACTIVE_STATUS' => self::PROPERTY_ACTIVE_STATUS,
        'PROPERTY_ARCHIVED_STATUS' => self::PROPERTY_ARCHIVED_STATUS,
        'PROPERTY_DRAFT_STATUS' => self::PROPERTY_DRAFT_STATUS,
        'PROPERTY_ACTIVE_PORTAL_STATUS' => self::PROPERTY_ACTIVE_PORTAL_STATUS,
        'PROPERTY_ARCHIVED_PORTAL_STATUS' => self::PROPERTY_ARCHIVED_PORTAL_STATUS,
        'PROPERTY_NOT_ACTIVE_PORTAL_STATUS' => self::PROPERTY_NOT_ACTIVE_PORTAL_STATUS,
        'PROPERTY_SOLD_PORTAL_STATUS' => self::PROPERTY_SOLD_PORTAL_STATUS,
        'PROPERTY_DRAFT_PORTAL_STATUS' => self::PROPERTY_DRAFT_PORTAL_STATUS,
        'AGENCY_PAGE_VISIBILITY_KEY' => self::AGENCY_PAGE_VISIBILITY_KEY,
        'PREMIUM_VISIBILITY_ID' => self::PREMIUM_VISIBILITY_ID,
        'PREMIUM_VISIBILITY_KEY' => self::PREMIUM_VISIBILITY_KEY,
        'SHOWCASE_VISIBILITY_KEY' => self::SHOWCASE_VISIBILITY_KEY,
        'STAR_VISIBILITY_KEY' => self::STAR_VISIBILITY_KEY,
        'TOP_VISIBILITY_KEY' => self::TOP_VISIBILITY_KEY,
        'SEARCHES_GTX_VERSION' => self::SEARCHES_GTX_VERSION,
        'CATEGORIA_RESIDENZIALE' => self::CATEGORIA_RESIDENZIALE,
        'MATCHES_GTX_VERSION' => self::MATCHES_GTX_VERSION,
        'SECRET_PROPERTY_VISIBILITY_KEY' => self::SECRET_PROPERTY_VISIBILITY_KEY,
        'PROPERTY_SECRET_PORTAL_STATUS' => self::PROPERTY_SECRET_PORTAL_STATUS,
        'SERVICE_EXTRA_VISIBILITA_IMMOBILE_SEGRETO' => self::SERVICE_EXTRA_VISIBILITA_IMMOBILE_SEGRETO,
    ];

    public static function getValue($name)
    {
        return \constant("self::{$name}");
    }
}
