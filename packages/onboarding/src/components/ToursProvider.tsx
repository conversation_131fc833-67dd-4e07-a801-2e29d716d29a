import React from 'react';
import { createContext, useState } from 'react';
import { useBuildTourAppArrayFromDom } from 'src/hooks/useBuildTourAppArray';
import { useTourListFromApi } from 'src/hooks/useTourListFromApi';
import { IToursApiResponse } from 'src/types/api';
import { IAppTour } from 'src/types/tour';

export interface IAppTourContext {
    appTours: IAppTour[];
    setAppTours: React.Dispatch<React.SetStateAction<IAppTour[]>>;
    tourIndex: number;
    setTourIndex: React.Dispatch<React.SetStateAction<number>>;
    stepIndex: number;
    setStepIndex: React.Dispatch<React.SetStateAction<number>>;
    // getValidToursFromDom: () => IAppTour[]
    updateValidTourFromDom: () => void;
    tourListFromApi: IToursApiResponse['data'];
    showTour: boolean;
    setShowTour: (showTour: boolean) => void;
    updateTourListFromApi: () => void;
}

interface ToursProviderProps {
    children: React.ReactNode;
}

export const appToursContext = createContext<IAppTourContext | null>(null);

export const ToursProvider: React.FC<ToursProviderProps> = ({ children }) => {
    const [appTours, setAppTours] = useState<IAppTour[]>([]);
    const [tourIndex, setTourIndex] = useState<number>(0);
    const [stepIndex, setStepIndex] = useState<number>(0);
    const [showTour, setShowTour] = useState<boolean>(false);

    const { tourListFromApi, updateTourListFromApi } = useTourListFromApi();

    const { getValidToursFromDom } = useBuildTourAppArrayFromDom(
        tourListFromApi
    );

    const updateValidTourFromDom = () => {
        const validTours = getValidToursFromDom();
        setAppTours(validTours);
    };
    return (
        <appToursContext.Provider
            value={{
                appTours,
                setAppTours,
                tourIndex,
                setTourIndex,
                stepIndex,
                setStepIndex,
                // getValidToursFromDom,
                tourListFromApi,
                showTour,
                setShowTour,
                updateValidTourFromDom,
                updateTourListFromApi,
            }}
        >
            {children}
        </appToursContext.Provider>
    );
};
