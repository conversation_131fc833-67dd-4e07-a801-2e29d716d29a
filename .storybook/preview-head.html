<script>
  // https://github.com/pmmmwh/react-refresh-webpack-plugin/issues/176#issuecomment-683150213
  window.$RefreshReg$ = () => {};
  window.$RefreshSig$ = () => () => {};
</script>

<script>
    window.gtxLoggedUser = {
        'getrixVersion': 9,
        'hasGetrixPlus': 1,
        'hasMessaging': true,
        'hasMatches': true,
        'language': 'en_EN',
        'roles': {
            'ROLE_SUPER_AMMINISTRATORE': 1,
            'ROLE_AMMINISTRATORE': 1,
            'ROLE_USER_BACKOFFICE': 0
        },
        'idAgente': '205908',
        'agencyId': '93434',
        'agentUuid': '93f00def-d326-5704-a24c-10636d1ab490',
        'name': '<PERSON>',
        'surname': '<PERSON><PERSON><PERSON><PERSON>',
        'email': '<EMAIL>',
        'telephone': '+************',
        'isAgencyLight': 0
    };
 </script>

<link id="js-translations-source" href="./locale.json">

