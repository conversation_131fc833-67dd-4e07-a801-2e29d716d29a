import { Icon } from '@gx-design/icon';
import { forwardRef } from 'react';
import { AgencyConsumer, useAgencyContext } from '../../shared/components/AgencyContext';
import { LOGOUT_URL, MULTI_AGENCY_URL, SECURITY_URL } from '../../shared/constants/constants';
import { trans } from '../../shared/translations';
import { useProfileHref } from '../../shared/hooks/useProfileHref';

export const MenuProfile = forwardRef<HTMLDivElement>((props, ref) => {
  const { agentAvatar, agentEmail, agentName, hasTwoFactorAuth, profileUrl, isAdmin } =
    useAgencyContext();
  const profileHref = useProfileHref(profileUrl);

  return (
    <div ref={ref} className='gx-navigation-menu__profile'>
      <div className='gx-navigation-menu__profileInfo'>
        {agentAvatar ? <img src={agentAvatar} /> : <Icon name='user-round--active' />}
        <div>
          <div>{agentName}</div>
          <div className='gx-navigation-menu__profileInfoEmail'>{agentEmail}</div>
        </div>
      </div>
      <div className='gx-navigation-menu__profileActionsGroup'>
        <a href={profileHref}>{trans('label.my_profile')}</a>
        {!isAdmin && hasTwoFactorAuth ? <a href={SECURITY_URL}>{trans('label.security')}</a> : null}
      </div>
      <AgencyConsumer>
        {({ isMasterAccount }) =>
          isMasterAccount ? (
            <div className='gx-navigation-menu__profileActionsGroup'>
              <a href={MULTI_AGENCY_URL}>{trans('label.switch_agency')}</a>
            </div>
          ) : null
        }
      </AgencyConsumer>

      <div className='gx-navigation-menu__profileActionsGroup'>
        <a href={LOGOUT_URL}>
          <span>{trans('label.exit')}</span>
          <Icon name='exit' />
        </a>
      </div>
    </div>
  );
});

MenuProfile.displayName = 'MenuProfile';
