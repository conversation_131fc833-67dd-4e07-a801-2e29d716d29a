{"name": "@getrix/babelfish-loader-wrapper", "version": "1.0.0", "main": "dist/index.js", "license": "MIT", "scripts": {"build": "yarn tsc --project ./tsconfig.json"}, "dependencies": {"@types/gettext-parser": "^4.0.2", "gettext-parser": "^6.0.0", "glob": "^10.2.2", "ts-loader": "^9.4.2", "typescript": "^5.1.6", "webpack": "^5.87.0"}, "volta": {"node": "18.16.0", "yarn": "3.4.1"}, "repository": {"type": "git", "url": "https://gitlab.pepita.io/getrix/mls-site.git", "directory": "packages/webpack-plugins/babelfish-loader-wrapper"}}