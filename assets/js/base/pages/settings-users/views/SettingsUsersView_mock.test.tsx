import { screen, within } from '#tests/react/testing-library-enhanced';
import { describe, expect, it, vi } from 'vitest';
import { renderSettingsUsersView, stubBianchino } from '../utils-test';

/**
 * I split the tests into two separate files to avoid complicated logic within the files
 * because the other tests don't need mocks.
 */

const mocks = vi.hoisted(() => {
    return {
        goTo: vi.fn(),
    };
});

vi.mock('../utils', async () => {
    const actual = await vi.importActual<typeof import('../utils')>('../utils');

    return {
        ...actual,
        goTo: mocks.goTo,
    };
});

describe('User settings integration tests as @superadmin - with mock', () => {
    it('show the status with a label if the user is in pending of activation', async () => {
        const { user } = renderSettingsUsersView({
            users: [
                {
                    ...stubBianchino,
                    // ? If the user is not the current user nor the owner, the role and status should be enabled
                    titolare: false, // If is not the owner
                    idAgente: 1, // If is not the current user
                    status: 1, // If is not active
                },
            ],
            usersCounts: { total: 1, active: 1 },
        });

        const row = await screen.findByRole('row', {
            name: new RegExp('Francesco Bianchino'),
        });

        const role = within(row).getByRole('combobox', { name: /label.role/ });

        const cells = within(row).getAllByRole('cell');
        const lastCell = cells[cells.length - 1];
        const status = within(row).getByText('label.wait_activation');

        await user.hover(status);

        await screen.findByText('settings.user.wait_activation.tooltip');

        const [, actions] = within(lastCell).getAllByRole('button');

        await user.click(actions);
        const editUser = screen.getByText('label.edit_user');
        const removeUser = screen.queryByText('label.remove');
        const resendUserEmail = screen.queryByText(
            'label.resend_activation_mail'
        );

        expect(editUser).toBeInTheDocument();
        expect(removeUser).toBeInTheDocument();
        expect(resendUserEmail).toBeInTheDocument();
        expect(row).toBeInTheDocument();
        expect(role).toBeEnabled();
        expect(status).toBeInTheDocument();
    });

    it('redirect to preview page', async () => {
        const { user } = renderSettingsUsersView({
            users: [stubBianchino],
            usersCounts: { total: 1, active: 1 },
        });

        const row = await screen.findByRole('row', {
            name: new RegExp('Francesco Bianchino'),
        });

        const cells = within(row).getAllByRole('cell');
        const lastCell = cells[cells.length - 1];
        const [preview] = within(lastCell).getAllByRole('button');

        await user.click(preview);

        expect(mocks.goTo).toBeCalled();
    });

    it('redirect to the user edit page when clicking on the edit button', async () => {
        const { user } = renderSettingsUsersView({
            users: [
                {
                    ...stubBianchino,
                    titolare: false,
                    idAgente: 1,
                },
            ],
            usersCounts: { total: 1, active: 1 },
        });

        const row = await screen.findByRole('row', {
            name: new RegExp('Francesco Bianchino'),
        });

        const cells = within(row).getAllByRole('cell');
        const lastCell = cells[cells.length - 1];
        const [, actions] = within(lastCell).getAllByRole('button');

        await user.click(actions);
        const editUser = await screen.findByText('label.edit_user');

        await user.click(editUser);

        expect(mocks.goTo).toBeCalledWith('/impostazioni/utenti/1/edit');
    });

    it('redirect to the user activation page when clicking on the edit button if the user is in pending of activation', async () => {
        const { user } = renderSettingsUsersView({
            users: [
                {
                    ...stubBianchino,
                    titolare: false,
                    idAgente: 1,
                    status: 0,
                },
            ],
            usersCounts: { total: 1, active: 0 },
        });

        const row = await screen.findByRole('row', {
            name: new RegExp('Francesco Bianchino'),
        });

        const cells = within(row).getAllByRole('cell');
        const lastCell = cells[cells.length - 1];
        const [, actions] = within(lastCell).getAllByRole('button');

        await user.click(actions!);
        const editUser = await screen.findByText('label.edit_user');

        await user.click(editUser);

        expect(mocks.goTo).toBeCalledWith('/impostazioni/utenti/1/activation');
    });
});
