parameters:
    data_provider_property: Tests\DataProvider\Property

services:
    _defaults:
        autowire: true
        autoconfigure: true

    app.session_handler:
        class: Tests\Service\Base\Session\Getrix

    App\DataMapper\DTOToResponse\Counters\AdsCountersResponseDataMapper:
        public: true
    App\DataMapper\DTOToResponse\Suggestions\SuggestionsDataMapper:
        public: true
    App\DataMapper\EntityToResponse\Property\SearchFiltersDataMapper:
        public: true
        arguments:
            $visibilities:
                premiumId: '%app.visibility.premium%'
                topId: '%app.visibility.top%'
                starId: '%app.visibility.star%'
                showcaseId: '%app.visibility.showcase%'

    App\DataProvider\Property\PropertyInterface:
        class: Tests\DataProvider\Property\Property
    App\DataProvider\Property\AuthenticatedPropertyInterface:
        class: Tests\DataProvider\Property\ApiGtx\AuthenticatedProperty

    App\Handler\Property\AdHandler:
        public: true
        arguments:
            $visibilities:
                premiumId: '%app.visibility.premium%'
                topId: '%app.visibility.top%'
                starId: '%app.visibility.star%'
                showcaseId: '%app.visibility.showcase%'

    App\Helper\AuctionsApiClientHelperInterface:
        class: Tests\Helper\AuctionsApiClientHelper

    App\Service\Base\Session\GetrixInterface:
        class: Tests\Service\Base\Session\Getrix

    App\Twig\Webpack\WebpackIncludeChunksExtension:
        class: Tests\Twig\Webpack\MockWebpackIncludeChunksExtension
