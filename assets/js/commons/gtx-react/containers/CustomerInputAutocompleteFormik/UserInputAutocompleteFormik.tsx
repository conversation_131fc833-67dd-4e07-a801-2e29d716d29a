import * as React from 'react';
import AutocompleteFormik from 'gtx-react/containers/AutocompleteFormik';
import { trans } from '@pepita-i18n/babelfish';
import { getCustomerApi, getSearchCustomerApi } from './web-api/api/customer';
import { useFormikContext } from 'formik';
import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { CustomerModal } from 'gtx-react/containers/CustomerModal';
import { MediaQuery } from 'gtx-react/components/MediaQuery';
import { VIEWPORT_MEDIA_QUERIES } from 'gtx-react/constants';
import { useCustomerModal } from './useCustomerModal';
import { endpoints } from './web-api/endpoints';

interface ICustomerInputAutocompleteProps {
    id?: string;
    hideEditBtn?: boolean;
    required?: boolean;
    queryParams?: { [key: string]: string | boolean };
}

type CustomerFormikStore = unknown & {
    customerId: string;
};
export const UserInputAutocompleteFormik: React.FC<
    ICustomerInputAutocompleteProps
> = ({ id: customerId, hideEditBtn, required, queryParams }) => {
    const { setFieldError, setFieldTouched, values } =
        useFormikContext<CustomerFormikStore>();

    const autocompleteOnChangeRef = React.useRef(
        (value: { name: string; id: any; email: string }) => {}
    );

    const {
        customerModalOpen,
        setCustomerModalOpen,
        handleCreateCustomer,
        handleEditCustomer,
        isLoadingCustomerForm,
    } = useCustomerModal();

    const { data: defaultUser, isLoading } = useQuery({
        queryKey: ['user-input-autocomplete', customerId],
        placeholderData: keepPreviousData,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        queryFn: () => {
            if (customerId && getCustomerApi) {
                return getCustomerApi(queryParams)({ id: customerId })
                    .then((res) => res.json())
                    .then((res) => res.customer)
                    .then((item) => {
                        return {
                            name: item.firstName,
                            email: item.email,
                            id: customerId,
                        };
                    });
            } else {
                return Promise.resolve(null);
            }
        },
    });

    return (
        <>
            <div>
                <div className="gx-form-group">
                    <AutocompleteFormik
                        name={'customerId'}
                        key={'customerId'}
                        label={trans('label.customer')}
                        placeholder={trans('label.insert_customer_name')}
                        defaultValue={defaultUser}
                        getAutocompleteApi={getSearchCustomerApi(queryParams)}
                        isMobile={false}
                        itemKey="id"
                        selectedLabelKey="name"
                        onFocus={() => setFieldError('customerId', undefined)}
                        onBlur={() => setFieldTouched('customerId', true)}
                        onChange={(_, data) => {}}
                        showRequiredSymbol={required}
                        formatFn={(item) =>
                            `${item.name} ${
                                item.email ? `(${item.email})` : ''
                            }`
                        }
                        onChangeRef={(setSelectedNameIn) =>
                            (autocompleteOnChangeRef.current =
                                setSelectedNameIn)
                        }
                        disabled={isLoading}
                    />
                    {!hideEditBtn && (
                        <MediaQuery queries={VIEWPORT_MEDIA_QUERIES}>
                            {(match) => (
                                <Button
                                    variant="accent"
                                    onClick={() => setCustomerModalOpen(true)}
                                    iconOnly={match.isMobile}
                                    type="button"
                                >
                                    {values.customerId &&
                                    values.customerId != '' ? (
                                        <Icon name="pencil" />
                                    ) : (
                                        <Icon name="plus" />
                                    )}
                                    {!match.isMobile && (
                                        <span>
                                            {values.customerId
                                                ? trans('label.edit_customer')
                                                : trans('label.add_customer')}
                                        </span>
                                    )}
                                </Button>
                            )}
                        </MediaQuery>
                    )}
                </div>
            </div>
            {!hideEditBtn && (
                <CustomerModal
                    id={values.customerId}
                    isOpen={customerModalOpen}
                    onCloseHandler={() => setCustomerModalOpen(false)}
                    countries={[]}
                    getCustomerApi={getCustomerApi(queryParams)}
                    lookupEndpoints={endpoints}
                    onSubmitHandler={
                        values.customerId
                            ? handleEditCustomer
                            : (data) =>
                                  handleCreateCustomer(data, {
                                      successCallback: (resp) => {
                                          const stringValue = `${resp.firstName} ${resp.lastName}`;
                                          autocompleteOnChangeRef.current({
                                              name: stringValue,
                                              id: resp.id,
                                              email: resp.email,
                                          });
                                      },
                                  })
                    }
                    loading={isLoadingCustomerForm}
                />
            )}
        </>
    );
};
