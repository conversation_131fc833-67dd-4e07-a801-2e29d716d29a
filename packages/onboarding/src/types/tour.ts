export interface IAppTour {
    id: number
    order: number
    name: string
    steps: IAppTourStep[]
}

export interface IAppTourStep {
    id: number
    el: OnBoardHTMLElement
    title: string | null
    body: string
    order: number
    imageUrl: string | null
    position: IAppOnboardingPosition
    indicatorStyle: 'light' | 'dark' | 'blue' | 'transparent'
}

export type IAppOnboardingPosition = "top" | "topLeft" | "topRight" | "bottom" | "left" | "right" | "bottomLeft" | "bottomRight" | "rightBottom" | "rightTop" | "leftBottom" | "leftTop"

export type OnBoardHTMLElement =  HTMLElement & {
    dataset: {
        onboarding: string // OnBoardDataAttrItem[]
        // onboardingStepOrder: string
        // onboardingPosition: IAppOnboardingPosition
    }
}

export type OnBoardDataAttrItem = {
    tour: string,
    step: number,
    position: IAppOnboardingPosition
    indicatorStyle: 'light' | 'dark' | 'blue' | 'transparent'
}
