# Front-end Scaffolding Tool

It will create a new section in `mls/site/assets/js/base/pages/{section_name}` with all you need to start develop. 

- It will includes GtxApp and GtxPages declaration
- File names derived from our section name following file-names and [folder structure draft](https://gitlab.pepita.io/pepita/frontend-docs/blob/7850d5ea8f8ee1836856a5aa7b81194e1ec13c5c/analysis/0004-naming-and-folder-structure-proposal.md)
- Redux Toolkit stores, slices and hooks declarations with examples (reducers, selector, thunks)
- Types and Interfaces divided by pages
- Views
- web-api folder with endpoints file, real and mocks folder with examples
- a main.js file with a GtxMainRender call
- added a new template `single-no-redux` to quick start a single page without redux store
- feel free to add new templates into `tools/gtx-app-scaffold/templates`


## Usage

### With makefile
`make scaffold`

- It will prompt user for section name

`MLS Section name (camelCase): {yourSectionName}`

- Then, the script will launch `yarn scaffold --showTemplates` to show available templates, prompting user to choose one of them

```
Available templates:  [ 'default', 'single-no-redux' ]
Done in 0.61s.
Choose a template (empty for 'default'):
```

Finally, the script will create the section in `assets/js/base/pages/your-section-name` using the chosen template.

### With yarn
You can also use the script directly from its folder `tools/gtx-app-scaffold` with yarn:

`yarn scaffold --name section_name --template template_name`

See [gtx-app-scaffold README](../tools/gtx-app-scaffold/README.md)
