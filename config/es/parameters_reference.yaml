parameters:
  app.logs_dir: '%kernel.logs_dir%'
  thrifter_rpc_services: 'config/%env(COUNTRY_TAG)%/sdk/rpc_services_dev.yml'
  app.a2f.api_key: 'getrix'
  maps.consumerKey: 'immobiliarepro'
  sapi.config:
    dev:
      services.getrix.getrix.annunci.immagini:
        baseUrl: "http://sapi.ns.imm.re:9977/"
        rootPath: "/storage/indomio-dev/images/"
        auth: "indomio-dev"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.getrix.annunci.planimetrie:
        baseUrl: "http://sapi.ns.imm.re:9977/"
        rootPath: "/storage/indomio-dev/plans/"
        auth: "indomio-dev"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.agenzie:
        baseUrl: "http://sapi.ns.imm.re:9977/"
        rootPath: "/storage/indomio-dev/images"
        auth: "indomio-dev"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.agenti:
        baseUrl: "http://sapi.ns.imm.re:9977/"
        rootPath: "/storage/indomio-dev/profiles/agents/images"
        auth: "indomio-dev"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.immobiliare.media:
        baseUrl: "http://sapi.ns.imm.re:9977/"
        rootPath: "/storage/indomio-dev/documents"
        auth: "indomio-dev"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
