{% extends 'base.html.twig' %}

{% block rawContent %}
<div id="content"></div>
<div id="root"></div>
{% endblock %}

{% block javascripts %}
	{{ parent() }}
	<script id="tos-title" type="application/json">{{ tosTitle }}</script>
	<script id="tos-description" type="application/json">{{ tosDescription|raw }}</script>
	<script id="tos-text" type="application/json">{{ tosText|raw }}</script>
	<script id="agency-email" type="application/json">{{ logged_agency.email|raw }}</script>
	<script id="agent-image-thumb" type="application/json">
		{% if logged_agent and logged_agent.urlImmagineAgenteThumb %}
			{{ logged_agent.urlImmagineAgenteThumb|raw }}
		{% endif %}
	</script>
	{{ webpack_asset('base/check-status') }}
{% endblock %}
