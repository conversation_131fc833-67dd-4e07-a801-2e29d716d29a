import { useCallback, useState, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
//@ts-ignore
//@ts-ignore
import {
    AttachmentTag,
    PHOTO_TYPE,
    VIDEO_TYPE,
    FILE_TYPE,
} from 'gtx-react/components/AttachmentTag/AttachmentTag';
import { trans } from '@pepita-i18n/babelfish';
import { ExpandableTextField } from '../../components/ExpandableTextField';
import { postThreadMessage } from '../../actions/thread';
import {
    getThreadAuthorUuid,
    getThreadId,
    getThreadParticipants,
    threadSelector,
} from '../../selectors/thread';
import {
    Participant,
    ThreadMessageSendPayload,
    IPostMessageResponse,
} from '../../types';
import { isImageAttachment, isVideoAttachment } from '../../utils';
import {
    ATTACHMENTS_ALLOWED_FORMATS,
    ATTACHMENTS_MAX_BYTE_SIZE,
    ATTACHMENTS_MAX_ITEMS,
    ATTACHMENTS_MAX_MB_SIZE,
    MIXPANEL,
} from '../../constants';
import { getThreadStatsApi } from '../../web-api';
import { useThreadContext } from '../../hooks/useThreadContext';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { Modal } from '@gx-design/modal';

function validateFilesMaxItems(files: File[]) {
    if (files && files.length > ATTACHMENTS_MAX_ITEMS) {
        return false;
    }

    return true;
}

function validateFilesMaxSize(files: File[]) {
    const totalSize = files.reduce((previousValue, currentValue) => {
        return previousValue + currentValue.size;
    }, 0);

    if (totalSize > ATTACHMENTS_MAX_BYTE_SIZE) {
        // Max size 30MB
        return false;
    }

    return true;
}

function countAttachmentsByType(attachments: File[]) {
    const photo = attachments.filter(
        (a: File) => isImageAttachment(a.type)?.length > 0
    ).length;
    const video = attachments.filter(
        (a: File) => isVideoAttachment(a.type)?.length > 0
    ).length;

    return [
        { type: PHOTO_TYPE, value: photo },
        { type: VIDEO_TYPE, value: video },
        { type: FILE_TYPE, value: attachments.length - photo - video },
    ];
}

export const ThreadInput = () => {
    const dispatch = useDispatch();
    const thread = useSelector(threadSelector);
    const authorUuid = getThreadAuthorUuid(thread);
    const participants = getThreadParticipants(thread);

    const [messageText, setMessageText] = useState<string>('');
    const [attachments, setAttachments] = useState<File[]>([]);
    const [attachmentsError, setAttachmentsError] = useState<string | null>(
        null
    );
    const fileInput = useRef<HTMLInputElement>(null);
    const { trackEvent, channel } = useThreadContext();

    const agentUuid = participants.find(
        (p: Participant) => p.uuid !== authorUuid
    )?.uuid;

    const attachmentsCount = countAttachmentsByType(attachments);

    const handleOnTextInput = useCallback((event) => {
        setMessageText(event.target.value);
    }, []);

    function trackMessageSent(messageId: number) {
        if (!MIXPANEL.TRACK_EVENTS) {
            return;
        }

        getThreadStatsApi(getThreadId(thread)).then((res) => {
            const { lastReceivedMessageDate } = res;

            trackEvent('messages_message_sent', {
                type: MIXPANEL.TYPE,
                channel: channel,
                area: MIXPANEL.AREA,
                section: MIXPANEL.SECTION,
                experimental: MIXPANEL.EXPERIMENTAL,
                ['thread_id']: getThreadId(thread),
                ['message_id']: messageId,
                ['answer_method']: 'chat',
                ['last_received_message_date']: lastReceivedMessageDate,
                ['message_attachments_count']: attachments
                    ? attachments.length
                    : 0,
                ['message_documents_count']: attachments
                    ? attachmentsCount.find((a) => a.type === FILE_TYPE).value
                    : 0,
                ['message_videos_count']: attachments
                    ? attachmentsCount.find((a) => a.type === VIDEO_TYPE).value
                    : 0,
                ['message_images_count']: attachments
                    ? attachmentsCount.find((a) => a.type === PHOTO_TYPE).value
                    : 0,
                ['message_length']: messageText ? messageText.length : 0,
            });
        });
    }

    function handleSendMessage() {
        if (!messageText && !attachments?.length) {
            return;
        }

        const payload: ThreadMessageSendPayload = {
            id: getThreadId(thread),
            messageId: new Date().getTime(),
            textPlain: messageText,
            authorUuid: agentUuid,
            attachments,
        };

        //@ts-ignore
        dispatch(postThreadMessage(payload)).then(
            (res: IPostMessageResponse) => {
                trackMessageSent(res.data.message.id);
            }
        );

        setMessageText('');
        setAttachments([]);
    }

    function removeAttachments(type: string) {
        let result: File[] = [];

        switch (type) {
            case PHOTO_TYPE:
                result = attachments.filter(
                    (a: File) => !isImageAttachment(a.type)?.length
                );
                break;
            case VIDEO_TYPE:
                result = attachments.filter(
                    (a: File) => !isVideoAttachment(a.type)?.length
                );
                break;
            case FILE_TYPE:
            default:
                result = attachments.filter(
                    (a: File) =>
                        isImageAttachment(a.type)?.length ||
                        isVideoAttachment(a.type)?.length
                );
                break;
        }

        setAttachments([...result]);
    }

    function handleFileInput(e: any) {
        const files = Array.from(e.target.files) as File[];

        if (!files || !files.length) {
            return;
        }

        if (!validateFilesMaxItems(attachments.concat(files))) {
            setAttachmentsError(
                trans('label.messaging.max_upload_attachments_error', {
                    MAX_ITEMS: ATTACHMENTS_MAX_ITEMS,
                })
            );
            return;
        }

        if (!validateFilesMaxSize(attachments.concat(files))) {
            setAttachmentsError(
                trans('label.messaging.max_upload_attachments_size_error', {
                    MAX_SIZE: ATTACHMENTS_MAX_MB_SIZE,
                })
            );
            return;
        }

        setAttachments((attachments) => attachments.concat(files));
        e.target.value = null;
    }

    return (
        <>
            <Modal
                size="small"
                isOpen={attachmentsError ? true : false}
                title={trans('label.messaging.failed_selection')}
                onClose={() => setAttachmentsError(null)}
                footer={
                    <Button
                        onClick={() => setAttachmentsError(null)}
                        variant="accent"
                    >
                        {trans('label.ok')}
                    </Button>
                }
            >
                {attachmentsError}
            </Modal>
            <div className="messaging-threadInput">
                <input
                    type="file"
                    ref={fileInput}
                    className="hidden"
                    multiple
                    accept={ATTACHMENTS_ALLOWED_FORMATS.join(',')}
                    onChange={handleFileInput}
                />
                <Button
                    className="no--border"
                    onClick={() =>
                        fileInput.current && fileInput.current.click()
                    }
                    iconOnly
                >
                    <Icon name="clip" />
                </Button>
                <div className="messaging-messageInputWrapper">
                    {attachments.length ? (
                        <div className="messaging-attachments">
                            <>
                                {attachmentsCount.map((a) =>
                                    a.value > 0 ? (
                                        <AttachmentTag
                                            key={a.type}
                                            type={a.type}
                                            count={a.value}
                                            onCloseClick={() =>
                                                removeAttachments(a.type)
                                            }
                                        />
                                    ) : null
                                )}
                            </>
                        </div>
                    ) : null}
                    <ExpandableTextField
                        text={messageText}
                        onInput={handleOnTextInput}
                    />
                </div>

                {(messageText || attachments.length > 0) && (
                    <Button
                        className="no--border"
                        onClick={handleSendMessage}
                        iconOnly
                    >
                        <Icon name="send" />
                    </Button>
                )}
            </div>
        </>
    );
};
