{"compilerOptions": {"pretty": true, "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "sourceMap": true, "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": false, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react", "downlevelIteration": true, "noUnusedLocals": true, "strictNullChecks": true, "noImplicitAny": true, "baseUrl": "."}, "exclude": ["dist", "node_modules"], "include": ["src/**/*.ts", "src/**/*.tsx"]}