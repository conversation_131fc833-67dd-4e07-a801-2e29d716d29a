{% extends 'base.html.twig' %}

{% block header %}
    <div id="header"></div>
{% endblock %}

{% block content %}
    <div id="content"></div>
{% endblock %}

{% block reactApp %}
    <div id="root"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script id="site-section-title" type="application/json">{{ title }}</script>
    {% include 'commons/core/js-scripts.html.twig' %}
    <script id="general-settings-data" type="application/json">{{ data|json_encode|raw }}</script>
    <script id="privacy-instrument" type="application/json">{{ globalConfigs.app.settings_general_privacy_instrument_appointment|json_encode|raw }}</script>
    {{ webpack_asset('base/settings-general') }}
{% endblock %}
