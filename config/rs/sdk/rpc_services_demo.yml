/* ex: set tabstop=2 softtabstop=2 expandtab shiftwidth=2 smarttab */
services:
  example:
    example:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  press:
    press:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  mls:
    search:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    contacts:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    bookmarks:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    collaborations:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  omi:
    lookup:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    evaluate:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  lookup:
    provider:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  immobiliare:
    autocomplete:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["immobiliare-autocomplete.srv.imm.mi.ns.farm", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  geography:
    geography:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  login:
    login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    getrix_login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  immobiliarepro:
    agenzie:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["immopro-agenzie.srv.imm.mi.ns.farm", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 80
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  fatture:
    getrix:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-agenzie-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    immobiliarepro:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["immopro-agenzie.srv.imm.mi.ns.farm", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 80
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  getrix:
    agenti:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-agenzie-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    agenzie:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-agenzie-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    whitelabel_login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-whitelabel-login.gtxpfbe.rete.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-login-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    estimates:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gestionale-valutazioni.dev.loc", 80, "/server_dev.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  survey:
    agent:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gestione.mutui.rete.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    lead:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gestione.mutui.rete.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gestione.mutui.rete.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    report:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["report-be.mutui.rete.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  aste:
    search:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["astebe.rete.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    aste:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["astebe.rete.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  documenti:
    documenti:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  media:
    immagini:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["media-immagini-demo.srv.imm.mi.ns.farm", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  trace:
    trace:
      socket:
        className: \Thrift\Transport\TSocket
        args: ["gtxpfbe-demo.srv.imm.mi.ns.farm", 9090]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
        mux: false
