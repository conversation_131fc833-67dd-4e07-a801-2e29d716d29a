const path = require('path');

const RemoveLicenseFilePlugin= require("./webpack-plugins/remove-license-file-plugin");

module.exports = {
  entry: './src/OnboardingApp',
  module: {
    rules: [
      {
        test: /(\.ts|\.tsx)$/, 
        exclude: /node_modules/,
        include: [path.join(__dirname, "src")],
        use: {
          loader: "ts-loader"
        }
      }
    ],
  },
  resolve: {
    extensions: [ '.tsx', '.ts', '.js' ],
    alias: {
      src: path.join(__dirname, "src")
    }
  },
  output: {
    filename: './onboarding.js',
    path: path.resolve(__dirname, '../../public/bundles/onboarding')
  },
  plugins: [new RemoveLicenseFilePlugin()]
};
