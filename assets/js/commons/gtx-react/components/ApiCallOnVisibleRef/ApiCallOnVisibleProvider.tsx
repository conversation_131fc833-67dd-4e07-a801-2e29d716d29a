import { createContext, useRef } from 'react';

export type ApiAbortObject = {
    id: string;
    abort: () => void;
};
export interface ApiCallOnVisibleContextValues {
    // abortArray: React.MutableRefObject<MatchesAbortObject[]>
    addAbortObj: (obj: ApiAbortObject) => void;
    removeAbortObj: (obj: ApiAbortObject) => void;
    abortAll: () => void;
    isLocked: React.MutableRefObject<boolean>;
}

export const ApiCallOnVisibleRefContext =
    createContext<ApiCallOnVisibleContextValues>(null!);

/**
 *
 * Questo provider espone il context ApiCallOnVisibleContextValues
 * wrappando l'applicazione con questo è possibile utilizzare l'hook
 * useApiCallOnVisibileRef il quale permette di eseguire una async callback
 * presumibilmente una chiamata api quando appare nel viewport un elemento html
 * Il context tiene traccia di tutte le chiamate pendinf e con
 * l'hook useApiCallOnVisibleContext è possibile effettuare l'abort di tutte
 * le chiamate ed impostare il flag isLocked.current = true|false che se true
 * impedisce che le chiamate vengano effettuate.
 */
export const ApiCallOnVisibleProvider: React.FC<React.PropsWithChildren> = ({
    children,
}) => {
    const abortArray = useRef<ApiAbortObject[]>([]);

    //TODO: spostare la generazione dell'id interna tornando l'oggetto creato
    const addAbortObj = (obj: ApiAbortObject) => {
        abortArray.current.push(obj);
    };

    const removeAbortObj = (obj: ApiAbortObject) => {
        abortArray.current = abortArray.current.filter(
            (item) => item.id != obj.id
        );
    };

    const abortAll = () => {
        for (let abortObj of abortArray.current) {
            removeAbortObj(abortObj);
            abortObj.abort();
        }
    };

    const isLocked = useRef<boolean>(false);

    return (
        <ApiCallOnVisibleRefContext.Provider
            value={{ addAbortObj, removeAbortObj, abortAll, isLocked }}
        >
            {children}
        </ApiCallOnVisibleRefContext.Provider>
    );
};
