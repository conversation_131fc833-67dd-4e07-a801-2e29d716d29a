{"extends": "./include-only.tsconfig", "compilerOptions": {"sourceMap": true, "strict": true, "noImplicitAny": false, "strictNullChecks": true, "inlineSources": true, "sourceRoot": "/", "esModuleInterop": true, "jsx": "react-jsx", "moduleResolution": "node", "baseUrl": "assets/js/commons", "resolveJsonModule": true, "paths": {"gtx-react": ["gtx-react"], "lib": ["lib"], "gtx-constants": ["../../../node_modules/@getrix/common/js/gtx-constants.js"]}}, "exclude": ["node_modules", "vendor", "tools", "packages"]}