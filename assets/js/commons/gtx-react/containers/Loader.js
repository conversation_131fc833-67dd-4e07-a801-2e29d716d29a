import React from 'react';
import { Loader as ListLoader } from '../components';
import { connect } from 'react-redux';
import { isLoadingSelector } from '../selectors/loader';

let mapStateToProps = (state) => ({
    isLoading: isLoadingSelector(state),
});

// is it replaceable with '@gx-design/loader';
export let Loader = connect(mapStateToProps)(({ isLoading }) => <ListLoader loading={isLoading} />);
