import { getSmartCounterString } from '../../../shared/utils/getSmartCounterString';
import { BaseMenuConfig } from '../../../types';
import { AdapterFC, AdapterHook } from '../types';
import { useCountersContext } from './CountersContext';

export const useMatchesCounterAdapter: AdapterHook = (menu: BaseMenuConfig) => {
  const counters = useCountersContext();

  const counter = counters.newMatches || 0;
  const shouldShowCounter = counter > 0;

  if (!shouldShowCounter) {
    return menu;
  }

  const number = getSmartCounterString(counter);

  return {
    ...menu,
    item: {
      ...menu.item,
      notificationBadge: {
        number,
      },
    },
  };
};

export const MatchesCounterAdapter: AdapterFC = (props) => {
  const remappedMenu = useMatchesCounterAdapter(props.menu, props.level);

  return props.children(remappedMenu);
};
