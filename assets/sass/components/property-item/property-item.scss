// ==========================================================================
// Property item - Components/Propert-item
// ==========================================================================
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.gx-property-item {
  display: flex;
  align-items: center;

  &--clickable {
    cursor: pointer;
  }

  &__pic {
    position: relative;
    flex-shrink: 0;
    width: ($gx-unit-size * 10);
    height: ($gx-unit-size * 7.5);
    overflow: hidden;
    border: 0.1rem solid color(border-main);

    img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      max-width: 102%;
    }
  }

  &__desc {
    overflow: hidden;
    margin-left: space(md);
  }
}
