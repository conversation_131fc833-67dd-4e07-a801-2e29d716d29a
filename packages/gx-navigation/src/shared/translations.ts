// eslint-disable-next-line no-restricted-imports -- this is the single point to import from @pepita-i18n/babelfish here
import { add, trans, transChoice, load } from '@pepita-i18n/babelfish';

/**
 * Loading trunk translations:
 * In trunk translations are still hydrated in page
 * So there will be "js-translations-hydration" with the full json
 *
 * In MLS we prefer to preload a JSON caching it and consuming from the various apps.
 * The JSON fetch are injected by the BabelfishLoaderWrapperPlugin
 */
if (!document.getElementById('js-translations-source')) {
  load(JSON.parse(document.getElementById('js-translations-hydration')?.innerHTML || '{}'));
}

export { add, trans, transChoice };
