// ==========================================================================
// Generic list - Components
// ==========================================================================
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$root-generic-list: gx-generic-list;

.#{$root-generic-list} {
  margin-bottom: space(sm);

  ul {
    list-style: none;
    padding: 0;
  }

  &__title {
    width: 100%;
    font-weight: 600;
    margin-bottom: space(md);
  }

  &__item {
    margin-bottom: space(md);

    label {
      display: flex;
    }

    .styled-checkbox {
      //TO-DO: rivedere quando sarà cambiato il componente check

      label {
        display: flex;
        align-items: center;
      }

      .styled-check {
        display: inline-flex;
        flex-shrink: 0;

        & + span {
          margin-left: space(xs);
        }
      }
    }
  }

  &--horizontal {
    ul {
      display: flex;
      flex-wrap: wrap;
    }

    .#{$root-generic-list}__item {
      & + .#{$root-generic-list}__item {
        margin-left: space(md);
      }
    }
  }
}
