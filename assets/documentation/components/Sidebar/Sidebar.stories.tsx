import {
    SidebarLeft,
    SidebarRight,
} from '../../../js/commons/gtx-react/components/Sidebar/Sidebar';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';

/**
 * Sidebar is a composite component for creating collapsible side navigation and content panels.
 * It supports both left and right positioning with persistent state and responsive behavior.
 */
const meta: Meta<typeof SidebarLeft> = {
    title: 'CRM/Sidebar',
    component: SidebarLeft,
    parameters: {
        componentSubtitle: 'Layout Component',
        componentTitle: 'Sidebar',
        componentSlug: 'Sidebar',
        layout: 'fullscreen',
        componentImport: 'SidebarLeft, SidebarRight',
        packageName: 'gtx-react/components/Sidebar/Sidebar',
        docs: {
            description: {
                component: `
The Sidebar component provides collapsible side panels for navigation and content display. It includes:

## Key Features:
- **Left and Right positioning**: Support for both left and right-side panels
- **Persistent state**: Automatically saves open/closed state to localStorage
- **Responsive behavior**: Only shows on large desktop screens
- **Section-based**: Different sections can have independent sidebar states
- **Compound components**: Built with sub-components for flexibility

## Sub-components:
- **SidebarLeft**: Main wrapper for left-side panels
- **SidebarLeft.Bar**: Content container for left sidebar
- **SidebarLeft.OpenButton**: Button to open the left sidebar
- **SidebarRight**: Main wrapper for right-side panels  
- **SidebarRight.Bar**: Content container for right sidebar
- **SidebarRight.OpenButton**: Button to open the right sidebar

## Sections:
The sidebar supports different sections for independent state management:
- 'property-list'
- 'property-performance' 
- 'inbox'
- 'inbox-detail'
- 'customers'
- 'customer-detail'

## Usage Pattern:
\`\`\`tsx
<SidebarLeft section="property-list" initialValue={false}>
    <SidebarLeft.Bar>
        <div className="crm-sidebar__header">
            <h3>Navigation</h3>
        </div>
        <div className="crm-sidebar__content">
            {/* Sidebar content */}
        </div>
    </SidebarLeft.Bar>
    
    <div className="main-content">
        <SidebarLeft.OpenButton />
        {/* Main content */}
    </div>
</SidebarLeft>
\`\`\`
                `,
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        section: {
            description:
                'The section identifier for persistent state management',
            control: { type: 'select' },
            options: [
                'property-list',
                'property-performance',
                'inbox',
                'inbox-detail',
                'customers',
                'customer-detail',
            ],
        },
        initialValue: {
            description: 'Initial open state when no persisted state exists',
            control: { type: 'boolean' },
        },
        children: {
            description:
                'Child components (typically SidebarLeft.Bar and main content)',
            control: { type: 'text' },
        },
    },
};

export default meta;

type Story = StoryObj<typeof SidebarLeft>;

// Mock navigation items
const mockNavItems = [
    {
        id: 1,
        label: 'All Properties',
        icon: 'home' as const,
        path: '/properties',
        active: true,
    },
    {
        id: 2,
        label: 'Active Listings',
        icon: 'eye' as const,
        path: '/properties/active',
        active: false,
    },
    {
        id: 3,
        label: 'Auctions',
        icon: 'clock' as const,
        path: '/properties/auctions',
        active: false,
    },
    {
        id: 4,
        label: 'Sold Properties',
        icon: 'check-circle' as const,
        path: '/properties/sold',
        active: false,
    },
    {
        id: 5,
        label: 'Archives',
        icon: 'inbox' as const,
        path: '/properties/archived',
        active: false,
    },
];

// Mock sidebar content components
const SidebarNavigation = () => (
    <>
        <div className="crm-sidebar__header">
            <h3 className="gx-title-2">Properties</h3>
            <Button size="small" variant="accent" iconOnly>
                <Icon name="plus" />
            </Button>
        </div>
        <div className="crm-sidebar__content">
            <div className="crm-sidebar-nav">
                {mockNavItems.map((item) => (
                    <a
                        key={item.id}
                        href={item.path}
                        className={`crm-sidebar-nav__item ${
                            item.active ? 'is-active' : ''
                        }`}
                        onClick={(e) => e.preventDefault()}
                    >
                        <Icon name={item.icon} />
                        <span>{item.label}</span>
                    </a>
                ))}
            </div>
        </div>
    </>
);

const SidebarDetails = () => (
    <>
        <div className="crm-sidebar__header">
            <h3 className="gx-title-2">Property Details</h3>
            <Button size="small" variant="ghost" iconOnly>
                <Icon name="pencil" />
            </Button>
        </div>
        <div className="crm-sidebar__content">
            <div style={{ padding: '16px 0' }}>
                <div style={{ marginBottom: '16px' }}>
                    <strong>Property ID:</strong> #PROP-2024-001
                </div>
                <div style={{ marginBottom: '16px' }}>
                    <strong>Address:</strong>
                    <br />
                    Via Roma 123
                    <br />
                    20121 Milano, MI
                </div>
                <div style={{ marginBottom: '16px' }}>
                    <strong>Price:</strong> €450,000
                </div>
                <div style={{ marginBottom: '16px' }}>
                    <strong>Status:</strong> Active
                </div>
                <div style={{ marginBottom: '16px' }}>
                    <strong>Agent:</strong> Mario Rossi
                </div>
                <Button variant="accent" style={{ width: '100%' }}>
                    View Full Details
                </Button>
            </div>
        </div>
    </>
);

const MainContent = ({
    title = 'Main Content Area',
    showOpenButton = true,
}) => (
    <div className="crm-section__content">
        <div className="crm-section__contentHeader">
            {showOpenButton && (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '12px',
                    }}
                >
                    <SidebarLeft.OpenButton />
                    <h2>{title}</h2>
                </div>
            )}
            {!showOpenButton && title && <h2>{title}</h2>}
        </div>
        <div
            style={{
                padding: '24px',
                backgroundColor: '#f8f9fa',
                minHeight: '400px',
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
            }}
        >
            <p>
                This is the main content area. The sidebar can be toggled to
                show/hide additional navigation or details.
            </p>
            <div
                style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '16px',
                }}
            >
                {[1, 2, 3].map((i) => (
                    <div
                        key={i}
                        style={{
                            padding: '16px',
                            backgroundColor: 'white',
                            borderRadius: '8px',
                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        }}
                    >
                        <h4>Content Block {i}</h4>
                        <p>Some content here...</p>
                    </div>
                ))}
            </div>
        </div>
    </div>
);

export const LeftSidebarNavigation: Story = {
    render: (args) => (
        <div
            style={{
                height: '500px',
                border: '1px solid #ddd',
            }}
        >
            <SidebarLeft section="property-list" initialValue={true}>
                <div className="crm-section">
                    <SidebarLeft.Bar>
                        <SidebarNavigation />
                    </SidebarLeft.Bar>
                    <MainContent title="Property Management" />
                </div>
            </SidebarLeft>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'A left sidebar with navigation items for property management. The sidebar opens by default and includes navigation links with icons.',
            },
        },
    },
};

export const RightSidebarDetails: Story = {
    render: (args) => (
        <div
            style={{
                height: '500px',
                border: '1px solid #ddd',
            }}
        >
            <SidebarRight section="property-list" initialValue={true}>
                <div className="crm-section">
                    <div className="crm-section__content">
                        <div className="crm-section__contentHeader">
                            <h2>Property Details View</h2>
                            <SidebarRight.OpenButton />
                        </div>
                        <div
                            style={{
                                padding: '24px',
                                backgroundColor: '#f8f9fa',
                                minHeight: '400px',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '16px',
                            }}
                        >
                            <p>
                                This is the main content area. The right sidebar
                                can be toggled to show/hide additional details.
                                Notice how this content area automatically
                                adjusts its width when the sidebar is
                                opened/closed.
                            </p>
                            <div
                                style={{
                                    display: 'grid',
                                    gridTemplateColumns:
                                        'repeat(auto-fit, minmax(200px, 1fr))',
                                    gap: '16px',
                                }}
                            >
                                {[1, 2, 3].map((i) => (
                                    <div
                                        key={i}
                                        style={{
                                            padding: '16px',
                                            backgroundColor: 'white',
                                            borderRadius: '8px',
                                            boxShadow:
                                                '0 2px 4px rgba(0,0,0,0.1)',
                                        }}
                                    >
                                        <h4>Content Block {i}</h4>
                                        <p>Some content here...</p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                    <SidebarRight.Bar>
                        <SidebarDetails />
                    </SidebarRight.Bar>
                </div>
            </SidebarRight>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'A right sidebar showing property details. This pattern is useful for showing detailed information about a selected item. The main content area automatically resizes when the sidebar is opened/closed.',
            },
        },
    },
};

export const BothSidebars: Story = {
    render: (args) => (
        <div
            style={{
                height: '570px',
                border: '1px solid #ddd',
            }}
        >
            <SidebarLeft section="property-list" initialValue={true}>
                <SidebarRight section="property-list" initialValue={false}>
                    <div className="crm-section">
                        <SidebarLeft.Bar>
                            <SidebarNavigation />
                        </SidebarLeft.Bar>
                        <div className="crm-section__content">
                            <div className="crm-section__contentHeader">
                                <div
                                    style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '12px',
                                    }}
                                >
                                    <SidebarLeft.OpenButton />
                                    <h2>Properties with Both Sidebars</h2>
                                </div>
                                <SidebarRight.OpenButton />
                            </div>
                            <div
                                style={{
                                    padding: '24px',
                                    backgroundColor: '#f8f9fa',
                                    minHeight: '400px',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '16px',
                                }}
                            >
                                <p>
                                    This demonstrates both left and right
                                    sidebars working together. The content area
                                    automatically adjusts its width when either
                                    sidebar is opened/closed. Try toggling both
                                    sidebars to see the responsive behavior.
                                </p>
                                <div
                                    style={{
                                        display: 'grid',
                                        gridTemplateColumns:
                                            'repeat(auto-fit, minmax(200px, 1fr))',
                                        gap: '16px',
                                    }}
                                >
                                    {[1, 2, 3].map((i) => (
                                        <div
                                            key={i}
                                            style={{
                                                padding: '16px',
                                                backgroundColor: 'white',
                                                borderRadius: '8px',
                                                boxShadow:
                                                    '0 2px 4px rgba(0,0,0,0.1)',
                                            }}
                                        >
                                            <h4>Content Block {i}</h4>
                                            <p>Some content here...</p>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                        <SidebarRight.Bar>
                            <SidebarDetails />
                        </SidebarRight.Bar>
                    </div>
                </SidebarRight>
            </SidebarLeft>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'An example with both left and right sidebars. The left sidebar contains navigation while the right sidebar shows details. The central content area dynamically adjusts its width based on which sidebars are open.',
            },
        },
    },
};

const InteractiveComponent = (args: any) => {
    const [selectedProperty, setSelectedProperty] = useState<
        (typeof mockNavItems)[0] | null
    >(mockNavItems[0] || null);

    const handleNavClick = (item: (typeof mockNavItems)[0]) => {
        setSelectedProperty(item);
    };

    return (
        <div
            style={{
                height: '600px',
                border: '1px solid #ddd',
            }}
        >
            <SidebarLeft section="property-list" initialValue={true} {...args}>
                <SidebarRight
                    section="property-list"
                    initialValue={!!selectedProperty}
                >
                    <div className="crm-section">
                        <SidebarLeft.Bar>
                            <div className="crm-sidebar__header">
                                <h3 className="gx-title-2">Properties</h3>
                                <Button size="small" variant="accent" iconOnly>
                                    <Icon name="plus" />
                                </Button>
                            </div>
                            <div className="crm-sidebar__content">
                                <div className="crm-sidebar-nav">
                                    {mockNavItems.map((item) => (
                                        <button
                                            key={item.id}
                                            className={`crm-sidebar-nav__item ${
                                                selectedProperty?.id === item.id
                                                    ? 'is-active'
                                                    : ''
                                            }`}
                                            onClick={() => handleNavClick(item)}
                                            style={{
                                                border: 'none',
                                                background: 'transparent',
                                                width: '100%',
                                                textAlign: 'left',
                                                cursor: 'pointer',
                                            }}
                                        >
                                            <Icon name={item.icon} />
                                            <span>{item.label}</span>
                                        </button>
                                    ))}
                                </div>
                            </div>
                        </SidebarLeft.Bar>

                        <div className="crm-section__content">
                            <div className="crm-section__contentHeader">
                                <div
                                    style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: '12px',
                                    }}
                                >
                                    <SidebarLeft.OpenButton />
                                    <h2>
                                        {selectedProperty?.label ||
                                            'Select a Property Type'}
                                    </h2>
                                </div>
                                <SidebarRight.OpenButton />
                            </div>
                            <div
                                style={{
                                    padding: '24px',
                                    backgroundColor: '#f8f9fa',
                                    minHeight: '400px',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    gap: '16px',
                                }}
                            >
                                {selectedProperty ? (
                                    <>
                                        <p>
                                            Showing properties for:{' '}
                                            <strong>
                                                {selectedProperty.label}
                                            </strong>
                                        </p>
                                        <div
                                            style={{
                                                display: 'grid',
                                                gridTemplateColumns:
                                                    'repeat(auto-fit, minmax(250px, 1fr))',
                                                gap: '16px',
                                            }}
                                        >
                                            {[1, 2, 3].map((i) => (
                                                <div
                                                    key={i}
                                                    style={{
                                                        padding: '16px',
                                                        backgroundColor:
                                                            'white',
                                                        borderRadius: '8px',
                                                        boxShadow:
                                                            '0 2px 4px rgba(0,0,0,0.1)',
                                                        cursor: 'pointer',
                                                        transition:
                                                            'transform 0.2s ease',
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.currentTarget.style.transform =
                                                            'translateY(-2px)';
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.currentTarget.style.transform =
                                                            'translateY(0)';
                                                    }}
                                                >
                                                    <div
                                                        style={{
                                                            display: 'flex',
                                                            alignItems:
                                                                'center',
                                                            gap: '8px',
                                                            marginBottom: '8px',
                                                        }}
                                                    >
                                                        <Icon
                                                            name={
                                                                selectedProperty.icon
                                                            }
                                                        />
                                                        <h4>Property {i}</h4>
                                                    </div>
                                                    <p
                                                        style={{
                                                            margin: 0,
                                                            color: '#666',
                                                        }}
                                                    >
                                                        Via Example {i}, Milano
                                                    </p>
                                                    <p
                                                        style={{
                                                            margin: '8px 0 0',
                                                            fontWeight: 'bold',
                                                        }}
                                                    >
                                                        €
                                                        {(
                                                            300000 +
                                                            i * 50000
                                                        ).toLocaleString()}
                                                    </p>
                                                </div>
                                            ))}
                                        </div>
                                    </>
                                ) : (
                                    <p>
                                        Select a property type from the left
                                        sidebar to view listings.
                                    </p>
                                )}
                            </div>
                        </div>

                        {selectedProperty && (
                            <SidebarRight.Bar>
                                <div className="crm-sidebar__header">
                                    <h3 className="gx-title-2">
                                        Quick Actions
                                    </h3>
                                </div>
                                <div className="crm-sidebar__content">
                                    <div
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            gap: '12px',
                                        }}
                                    >
                                        <Button
                                            variant="accent"
                                            style={{ width: '100%' }}
                                        >
                                            <Icon name="plus" />
                                            Add New Property
                                        </Button>
                                        <Button
                                            variant="default"
                                            style={{ width: '100%' }}
                                        >
                                            <Icon name="download" />
                                            Export List
                                        </Button>
                                        <Button
                                            variant="default"
                                            style={{ width: '100%' }}
                                        >
                                            <Icon name="search" />
                                            Advanced Filter
                                        </Button>
                                        <Button
                                            variant="default"
                                            style={{ width: '100%' }}
                                        >
                                            <Icon name="gear" />
                                            View Settings
                                        </Button>
                                    </div>

                                    <div
                                        style={{
                                            marginTop: '24px',
                                            padding: '16px',
                                            backgroundColor: '#f8f9fa',
                                            borderRadius: '8px',
                                        }}
                                    >
                                        <h4 style={{ margin: '0 0 12px' }}>
                                            Quick Stats
                                        </h4>
                                        <div
                                            style={{
                                                display: 'flex',
                                                flexDirection: 'column',
                                                gap: '8px',
                                            }}
                                        >
                                            <div
                                                style={{
                                                    display: 'flex',
                                                    justifyContent:
                                                        'space-between',
                                                }}
                                            >
                                                <span>Total Properties:</span>
                                                <strong>1,234</strong>
                                            </div>
                                            <div
                                                style={{
                                                    display: 'flex',
                                                    justifyContent:
                                                        'space-between',
                                                }}
                                            >
                                                <span>Active Listings:</span>
                                                <strong>856</strong>
                                            </div>
                                            <div
                                                style={{
                                                    display: 'flex',
                                                    justifyContent:
                                                        'space-between',
                                                }}
                                            >
                                                <span>This Month:</span>
                                                <strong>45</strong>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </SidebarRight.Bar>
                        )}
                    </div>
                </SidebarRight>
            </SidebarLeft>
        </div>
    );
};

export const InteractiveDemo: Story = {
    render: (args) => <InteractiveComponent {...args} />,
    parameters: {
        docs: {
            description: {
                story: 'A fully interactive demo showing both sidebars working together. Click on navigation items in the left sidebar to see dynamic content updates. The right sidebar shows contextual actions and statistics.',
            },
        },
    },
};

export const ClosedStates: Story = {
    render: (args) => (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            <div>
                <h3>Left Sidebar - Closed State</h3>
                <div
                    style={{
                        height: '400px',
                        border: '1px solid #ddd',
                    }}
                >
                    <SidebarLeft section="property-list" initialValue={false}>
                        <div className="crm-section">
                            <SidebarLeft.Bar>
                                <SidebarNavigation />
                            </SidebarLeft.Bar>
                            <MainContent title="Left Sidebar Demo (Closed)" />
                        </div>
                    </SidebarLeft>
                </div>
            </div>

            <div>
                <h3>Right Sidebar - Closed State</h3>
                <div
                    style={{
                        height: '400px',
                        border: '1px solid #ddd',
                    }}
                >
                    <SidebarRight
                        section="property-performance"
                        initialValue={false}
                    >
                        <div className="crm-section">
                            <div className="crm-section__content">
                                <div className="crm-section__contentHeader">
                                    <h2>Right Sidebar Demo (Closed)</h2>
                                    <SidebarRight.OpenButton />
                                </div>
                                <div
                                    style={{
                                        padding: '24px',
                                        backgroundColor: '#f8f9fa',
                                        minHeight: '300px',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: '16px',
                                    }}
                                >
                                    <p>
                                        This is the main content area with the
                                        right sidebar closed. Notice how the
                                        content area takes the full width when
                                        the sidebar is closed.
                                    </p>
                                    <div
                                        style={{
                                            display: 'grid',
                                            gridTemplateColumns:
                                                'repeat(auto-fit, minmax(200px, 1fr))',
                                            gap: '16px',
                                        }}
                                    >
                                        {[1, 2, 3, 4].map((i) => (
                                            <div
                                                key={i}
                                                style={{
                                                    padding: '16px',
                                                    backgroundColor: 'white',
                                                    borderRadius: '8px',
                                                    boxShadow:
                                                        '0 2px 4px rgba(0,0,0,0.1)',
                                                }}
                                            >
                                                <h4>Content Block {i}</h4>
                                                <p>Full width content!</p>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                            <SidebarRight.Bar>
                                <SidebarDetails />
                            </SidebarRight.Bar>
                        </div>
                    </SidebarRight>
                </div>
            </div>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'Examples of sidebars in their closed state. Notice how the open buttons are positioned to allow users to expand the sidebars, and how the content area takes the full available width when sidebars are closed.',
            },
        },
    },
};

export const DifferentSections: Story = {
    render: (args) => (
        <div
            style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '20px',
                height: '100%',
            }}
        >
            <div>
                <h3>Property List Section</h3>
                <div
                    style={{
                        height: '350px',
                        border: '1px solid #ddd',
                    }}
                >
                    <SidebarLeft section="property-list" initialValue={true}>
                        <div className="crm-section">
                            <SidebarLeft.Bar>
                                <div className="crm-sidebar__header">
                                    <h3 className="gx-title-2">
                                        Property List
                                    </h3>
                                </div>
                                <div className="crm-sidebar__content">
                                    <p style={{ padding: '0 0 16px' }}>
                                        Section: property-list
                                    </p>
                                    <div className="crm-sidebar-nav">
                                        <a
                                            href="#"
                                            className="crm-sidebar-nav__item is-active"
                                            onClick={(e) => e.preventDefault()}
                                        >
                                            <Icon name="home" />
                                            <span>All Properties</span>
                                        </a>
                                    </div>
                                </div>
                            </SidebarLeft.Bar>
                            <MainContent title="Property List Management" />
                        </div>
                    </SidebarLeft>
                </div>
            </div>

            <div>
                <h3>Customer Detail Section</h3>
                <div
                    style={{
                        height: '350px',
                        border: '1px solid #ddd',
                    }}
                >
                    <SidebarLeft section="customer-detail" initialValue={true}>
                        <div className="crm-section">
                            <SidebarLeft.Bar>
                                <div className="crm-sidebar__header">
                                    <h3 className="gx-title-2">
                                        Customer Detail
                                    </h3>
                                </div>
                                <div className="crm-sidebar__content">
                                    <p style={{ padding: '0 0 16px' }}>
                                        Section: customer-detail
                                    </p>
                                    <div className="crm-sidebar-nav">
                                        <a
                                            href="#"
                                            className="crm-sidebar-nav__item is-active"
                                            onClick={(e) => e.preventDefault()}
                                        >
                                            <Icon name="user-round" />
                                            <span>Customer Info</span>
                                        </a>
                                    </div>
                                </div>
                            </SidebarLeft.Bar>
                            <MainContent title="Customer Detail View" />
                        </div>
                    </SidebarLeft>
                </div>
            </div>

            <div>
                <h3>Inbox Section</h3>
                <div
                    style={{
                        height: '350px',
                        border: '1px solid #ddd',
                    }}
                >
                    <SidebarLeft section="inbox" initialValue={false}>
                        <div className="crm-section">
                            <SidebarLeft.Bar>
                                <div className="crm-sidebar__header">
                                    <h3 className="gx-title-2">Inbox</h3>
                                </div>
                                <div className="crm-sidebar__content">
                                    <p style={{ padding: '0 0 16px' }}>
                                        Section: inbox
                                    </p>
                                    <div className="crm-sidebar-nav">
                                        <a
                                            href="#"
                                            className="crm-sidebar-nav__item"
                                            onClick={(e) => e.preventDefault()}
                                        >
                                            <Icon name="mail" />
                                            <span>Messages</span>
                                        </a>
                                    </div>
                                </div>
                            </SidebarLeft.Bar>
                            <MainContent title="Inbox Management" />
                        </div>
                    </SidebarLeft>
                </div>
            </div>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'Different sections maintain independent sidebar states. Each section persists its own open/closed state in localStorage, allowing users to have different sidebar preferences for different parts of the application.',
            },
        },
    },
};

// Set story names
LeftSidebarNavigation.storyName = 'Left Sidebar Navigation';
RightSidebarDetails.storyName = 'Right Sidebar Details';
BothSidebars.storyName = 'Both Sidebars';
InteractiveDemo.storyName = 'Interactive Demo';
ClosedStates.storyName = 'Closed States';
DifferentSections.storyName = 'Different Sections';
