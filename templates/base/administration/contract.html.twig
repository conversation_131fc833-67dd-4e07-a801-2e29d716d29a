{% extends 'base.html.twig' %}

{% block header %}
    <div id="header"></div>
{% endblock %}

{% block content %}
    <div id="content"></div>
{% endblock %}

{% block reactApp %}
    <div id="root"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {% include 'base/Vendor/getrix/common/snippets/feature-toggle-configs.html.twig' %}
    <script id="site-section-title" type="application/json">{{ title }}</script>
    <script id="data" type="application/json">{{ data|json_encode|raw }}</script>
    {% include 'commons/core/js-scripts.html.twig' %}
    {{ webpack_asset('base/administration-contract') }}
{% endblock %}
