import type { SetupWorker, StartOptions } from 'msw/browser';
import { PropsWithChildren, useEffect, useState } from 'react';

type MswDeveloperBoundaryProps = {
    isDisabled?: boolean;
    worker?: Promise<{ worker: SetupWorker }> | null;
};

type MswState = 'disabled' | 'pending' | 'started' | 'error';

const isDevelopment = process.env.NODE_ENV === 'development';

const mswStartOptions: StartOptions = {};

const useMswState = (props: MswDeveloperBoundaryProps) => {
    const [mswState, setMswState] = useState<MswState>(
        props.isDisabled ? 'disabled' : 'pending'
    );

    useEffect(() => {
        if (props.isDisabled) {
            // eslint-disable-next-line no-console -- we want to log to notify the developer
            console.info('MSW is disabled via props!');
            return setMswState('disabled');
        }

        const onError = (e: unknown) => {
            // eslint-disable-next-line no-console -- we want to log the error just in case
            console.warn(
                'Error starting MSW, you could check the stacktrace, application will continue without MSW.',
                e
            );
            setMswState('error');
        };

        const onSuccess = ({ worker }: { worker: SetupWorker }) =>
            worker.start(mswStartOptions).then(() => {
                setTimeout(() => {
                    setMswState('started');
                }, 1000);
            });

        if (isDevelopment) {
            if (props.worker) {
                props.worker.then(onSuccess).catch(onError);
            } else {
                import('./setup')
                    .then(({ worker }) => {
                        worker.start().then(() => {
                            setMswState('started');
                        });
                    })
                    .catch((e) => {
                        setMswState('error');
                    });
            }
        }
    }, [props.isDisabled, props.worker]);

    return mswState;
};

/**
 * This component is used to enable MSW in development mode.
 * You can use it to wrap your app in development mode, so that you can use MSW to mock your API.
 */
const MswDeveloperBoundaryDevelopment = (
    props: PropsWithChildren<MswDeveloperBoundaryProps>
) => {
    const mswState = useMswState(props);

    switch (mswState) {
        case 'pending':
            return (
                <div
                    style={{
                        position: 'fixed',
                        width: '100%',
                        height: '100vh',
                        textAlign: 'center',
                        paddingTop: '200px',
                        fontSize: 'x-large',
                        fontWeight: 'bold',
                    }}
                >
                    🚧 Loading MSW...
                </div>
            );
        case 'started':
            return <>{props.children}</>;

        case 'disabled':
            return <>{props.children}</>;
        case 'error':
            return <>{props.children}</>;
        default:
            return <>{props.children}</>;
    }
};

const MswDeveloperBoundaryProduction = (
    props: PropsWithChildren<MswDeveloperBoundaryProps>
) => <>{props.children}</>;

/**
 * This component is used to enable MSW in development mode and disable it in production.
 * You can use it to wrap your app in development mode, so that you can use MSW to mock your API.
 * In production it will just render the children.
 *
 * If you decide to use a custom worker, you can pass it as a prop but it is important to note that should be passed useing the `onlyInDevelopment` function.
 * @example
 * const worker = onlyInDevelopment(() => import('../utils/worker'));
 * <MswDeveloperBoundary worker={worker}>{children}</MswDeveloperBoundary>
 */
export const MswDeveloperBoundary = isDevelopment
    ? MswDeveloperBoundaryDevelopment
    : MswDeveloperBoundaryProduction;
