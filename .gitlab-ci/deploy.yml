# yaml-language-server: $schema=https://gitlab.com/gitlab-org/gitlab/-/raw/master/app/assets/javascripts/editor/schema/ci.json

variables:

    # deploy-it
    DEPLOY_IT_REFERENCE: mls01.gestionale-it-dev.it3.ns.farm
    DEPLOY_IT_STAGING: mls01.gestionale-it-stg.it3.ns.farm
    DEPLOY_IT_PRODUCTION: mls01.gestionale-it.it3.ns.farm mls02.gestionale-it.it3.ns.farm mls03.gestionale-it.it3.ns.farm mls04.gestionale-it.it3.ns.farm
    DEPLOY_IT_ENV_DEST: .env.it

    # deploy-es
    DEPLOY_ES_REFERENCE: mls01.gestionale-es-dev.it3.ns.farm
    DEPLOY_ES_STAGING: mls01.gestionale-es-stg.it3.ns.farm
    DEPLOY_ES_PRODUCTION: mls01.gestionale-es.it3.ns.farm mls02.gestionale-es.it3.ns.farm
    DEPLOY_ES_ENV_DEST: .env.es

    # deploy-lu
    DEPLOY_LU_REFERENCE: mls01.gestionale-lu-dev.it3.ns.farm
    DEPLOY_LU_STAGING: mls01.gestionale-lu-stg.it3.ns.farm
    DEPLOY_LU_PRODUCTION: mls01.gestionale-lu.it3.ns.farm mls02.gestionale-lu.it3.ns.farm
    DEPLOY_LU_ENV_DEST: .env.lu

    # deploy-hr
    DEPLOY_HR_REFERENCE: mls01.gestionale-hr-dev.it3.ns.farm
    DEPLOY_HR_STAGING: mls01.gestionale-hr-stg.it3.ns.farm
    DEPLOY_HR_PRODUCTION: mls01.gestionale-hr.it3.ns.farm mls02.gestionale-hr.it3.ns.farm
    DEPLOY_HR_ENV_DEST: .env.hr

    # deploy-rs
    DEPLOY_RS_REFERENCE: mls01.gestionale-rs-dev.it3.ns.farm
    DEPLOY_RS_STAGING: mls01.gestionale-rs-stg.it3.ns.farm
    DEPLOY_RS_PRODUCTION: mls01.gestionale-rs.it3.ns.farm mls02.gestionale-rs.it3.ns.farm
    DEPLOY_RS_ENV_DEST: .env.rs
