import { useEffect, useMemo, useState } from 'react';
import { createConfig as baseHeaderConfig } from './config/header';
import { BaseNavigationContextType } from './config/types';
import { CustomComponents } from './custom';
import { URL_HISTORY_CHANGE } from './events/inputTypes';
import { on } from './events/utils';
import { Header } from './header';
import {
  AgencyLogo,
  AgencyName,
  HeaderContent,
  Logo as LogoMobile,
  NotificationButton,
} from './header/HeaderContent';
import { HamburgerButton, HeaderHeading, Logo, SectionBox } from './header/HeaderHeading';
import { Menu } from './menu/Menu';
import { AgencyConsumer, AgencyProvider } from './shared/components/AgencyContext';
import { GxNavigationProvider } from './shared/components/GxNavigationContext';
import { GxNavigationMenuProvider } from './shared/components/GxNavigationMenuContext';
import { getHeaderConfiguration, getSectionProps } from './shared/utils';
import { BaseHeaderConfig } from './types';
import { MenuConfigSegmentProvider } from './menu/MenuProvider';
import { CustomerServiceButton } from './header/HeaderContent/CustomerService';
import { GxNavigationFeatureFlagsProvider } from './shared/components/GxNavigationFeatureFlagsContext';

export type GxNavigationProps = {
  config: BaseHeaderConfig;
  context: BaseNavigationContextType;
};

export const GxNavigation = (props: GxNavigationProps) => {
  const {
    context: {
      agencyImage,
      agencyName,
      agentAvatar,
      agentEmail,
      agentName,
      agencyId,
      adPortal,
      isMasterAccount,
      hasTwoFactorAuth,
      profileUrl,
      serviceRemoteVisit,
      isAdmin,
      featureFlags,
      ...context
    },
  } = props;
  const [currentConfig, setCurrentConfig] = useState(props.config);

  const config = baseHeaderConfig({ adPortal, serviceRemoteVisit, featureFlags });

  useEffect(() => {
    const unsubscribe = on(URL_HISTORY_CHANGE, (data: string) => {
      const headerConfig = getHeaderConfiguration(config, new URL(data, window.location.origin));
      if (headerConfig) {
        setCurrentConfig(headerConfig);
      }
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const mergedMenuConfig = useMemo(() => {
    return { ...context.primaryMenu, ...context.secondaryMenu };
  }, [context.primaryMenu, context.secondaryMenu]);

  return (
    <GxNavigationProvider value={context}>
      <GxNavigationFeatureFlagsProvider value={featureFlags}>
        <GxNavigationMenuProvider>
          <AgencyProvider
            value={{
              serviceRemoteVisit,
              isAdmin,
              profileUrl,
              hasTwoFactorAuth,
              isMasterAccount,
              agencyId,
              agencyImage,
              agencyName,
              agentAvatar,
              agentEmail,
              agentName,
              adPortal,
            }}
          >
            <>
              <Header>
                <HeaderHeading>
                  <HamburgerButton />
                  {currentConfig.section && (
                    <SectionBox {...getSectionProps(currentConfig.section)} />
                  )}
                  <Logo
                    imageUrl={context.logo}
                    url={context.homeUrl}
                    alt={context.siteName}
                    isVisible={!currentConfig.section}
                  />
                </HeaderHeading>

                <div className='gx-navigation-header__content'>
                  {currentConfig.actions && <CustomComponents actions={currentConfig.actions} />}
                  <div id='gx-navigation-actions-portal'></div>
                  <HeaderContent>
                    <LogoMobile
                      imageUrl={context.logo}
                      url={context.homeUrl}
                      alt={context.siteName}
                    />
                    <AgencyConsumer>
                      {({ agencyName, agencyImage }) => (
                        <>
                          {agencyImage ? (
                            <AgencyLogo imageUrl={agencyImage} alt={agencyName} />
                          ) : (
                            <AgencyName name={agencyName} />
                          )}
                          {featureFlags.saleforceLiveChatEnabled && (
                            <CustomerServiceButton position='bottom-right' />
                          )}
                          {featureFlags.beamerNewsEnabled ? <NotificationButton /> : null}
                        </>
                      )}
                    </AgencyConsumer>
                  </HeaderContent>
                </div>
              </Header>
              {Object.keys(mergedMenuConfig).length > 0 && (
                <MenuConfigSegmentProvider config={mergedMenuConfig}>
                  <Menu />
                </MenuConfigSegmentProvider>
              )}
            </>
          </AgencyProvider>
        </GxNavigationMenuProvider>
      </GxNavigationFeatureFlagsProvider>
    </GxNavigationProvider>
  );
};
