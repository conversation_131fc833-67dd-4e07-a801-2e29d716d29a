import inquirer from 'inquirer';
import {all_entries} from './webpack.common.mjs'


export const getEntriesCli = async (env) => {
  let entries = all_entries;

    if(env.LIST){
      console.log(Object.keys(entries))
      process.exit(0)
    }
    

    if (!env.ENTRIES) {
        const selectedEntryPoints = await inquirer.prompt([
          {
            type: 'checkbox',
            name: 'entrypoints',
            message: 'Seleziona gli entrypoints da includere:',
            choices: Object.keys(entries),
          }
        ]);
    
        env.ENTRIES = selectedEntryPoints.entrypoints;
      }else{
        env.ENTRIES = env.ENTRIES.split(',')
      }


    const entrypointFilterArray = env.ENTRIES

    if(entrypointFilterArray && entrypointFilterArray.length > 0){
      entries = Object.keys(entries)
          .filter((entry) => entrypointFilterArray.includes(entry))
          .reduce((filtered, entry) => {
              filtered[entry] = entries[entry];
              return filtered;
          }, {});
    }

    const coloredConsole = {
      yellow: message => console.log(`\x1b[93m${message}\x1b[0m`),
    }
    const restartInfo = () => {
      console.log()
      console.log()
      console.log('----------------------------------------------------------------------')
      console.log(`You can restart the process with the same selection using:` )
      coloredConsole.yellow(`yarn start-selective --env ENTRIES=${env.ENTRIES.join(',')}`)
      console.log('or')
      coloredConsole.yellow(`yarn build-selective --env ENTRIES=${env.ENTRIES.join(',')}`)
      console.log('----------------------------------------------------------------------')
      console.log()
      console.log()
    }
    
    restartInfo()

    process.on('SIGINT', () => {
      restartInfo();
      process.exit(0);
    });

    return entries
}
