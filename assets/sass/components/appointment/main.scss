@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

[class*='apt-type-'] td:first-child {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    z-index: 1;
    margin-left: -0.1rem;
    width: 0.5rem;
    height: 100%;
    left: 0;
    top: 0;
  }
}

$appointment-color-map: (
  '1': $appointment-type-1,
  '2': $appointment-type-2,
  '3': $appointment-type-3,
  '4': $appointment-type-4,
  '5': $appointment-type-5,
  '6': $appointment-type-6,
  '7': $appointment-type-7,
  '8': $appointment-type-8,
  '9': $appointment-type-9,
  '10': $appointment-type-10,
  '11': $appointment-type-11,
  '12': $appointment-type-12,
);

@each $type, $color in $appointment-color-map {
  .apt-type-#{$type} {
    td:first-child::before {
      background-color: $color;
    }
  }
}

.apt-type-circle {
  display: flex;
  align-items: center;
  font-weight: bold;

  &::before {
    content: '';
    width: space(sm);
    height: space(sm);
    display: inline-flex;
    border-radius: radius(rounded);
    margin-right: space(xs);
  }

  @each $type, $color in $appointment-color-map {
    &.apt-type-#{$type} {
      &::before {
        background-color: $color;
      }
    }
  }
}
