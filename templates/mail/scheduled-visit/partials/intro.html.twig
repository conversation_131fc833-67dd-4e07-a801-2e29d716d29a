<table width="100%" cellpadding="0" align="left" cellspacing="0" border="0" style="margin-top:8px;border-collapse:collapse;font-family:Arial;font-size:13px;line-height:1.6">
  <tbody>
    <tr>
      <td colspan="2" height="16" style="border-collapse:collapse"></td>
    </tr>
    <tr>
      <td colspan="2" valign="top"
        style="border-collapse:collapse;line-height:20px">
        <p style="font-size:14px;color:#666666;line-height:20px; margin: 0 0 16px;">
          {{ trans('label.hello', {}, gtxConstants.DOMAIN_CMS) }}
        </p>
        <p style="font-size:14px;color:#666666;margin:0">
            {% if room_event_type == 'created' %}
                {% if user_type == 'guest' %}
                    {% if visit_type == 'distance' %}
                      {{ trans('label.scheduled_visits_invite', {}, gtxConstants.DOMAIN_CMS) | raw }}:
                    {% else %}
                      {{ trans('label.scheduled_visits_invite_in_person', {}, gtxConstants.DOMAIN_CMS) | raw }}:
                    {% endif %}
                {% else %}
                    {% if visit_type == 'distance' %}
                      {{ trans('label.scheduled_visits_planning', {}, gtxConstants.DOMAIN_CMS) }} <b>rif. {{ room.ad.id }}</b>:
                    {% else %}
                      {{ trans('label.scheduled_visits_planning_in_person', {}, gtxConstants.DOMAIN_CMS) }} <b>rif. {{ room.ad.id }}</b>:
                    {% endif %}
                {% endif %}
            {% elseif room_event_type == 'updated' %}
                {% if user_type == 'guest' %}
                    {% if visit_type == 'distance' %}
                      {{ trans('label.scheduled_visits_updated', {}, gtxConstants.DOMAIN_CMS) | raw }}
                    {% else %}
                      {{ trans('label.scheduled_visits_updated_in_person', {}, gtxConstants.DOMAIN_CMS) | raw }}
                    {% endif %}
                {% else %}
                    {% if visit_type == 'distance' %}
                      {{ trans('label.scheduled_visits_updated_agent', {}, gtxConstants.DOMAIN_CMS) | raw }} <b>rif. {{ room.ad.id }}</b>:
                    {% else %}
                      {{ trans('label.scheduled_visits_updated_agent_in_person', {}, gtxConstants.DOMAIN_CMS) | raw }} <b>rif. {{ room.ad.id }}</b>:
                    {% endif %} 
                {% endif %}
            {% elseif room_event_type == 'deleted' %}
                {% if user_type == 'guest' %}
                    {% if visit_type == 'distance' %}
                      {{ trans('label.scheduled_visits_deleted', {}, gtxConstants.DOMAIN_CMS ) | raw }}
                    {% else %}
                      {{ trans('label.scheduled_visits_deleted_in_person', {}, gtxConstants.DOMAIN_CMS ) | raw }}
                    {% endif %} 
                {% else %}
                    {% if visit_type == 'distance' %}
                      {{ trans('label.scheduled_visits_deleted_agent', {}, gtxConstants.DOMAIN_CMS) | raw }} <b>rif. {{ room.ad.id }}</b>:
                    {% else %}
                      {{ trans('label.scheduled_visits_deleted_agent_in_person', {}, gtxConstants.DOMAIN_CMS) | raw }} <b>rif. {{ room.ad.id }}</b>:
                    {% endif %} 
                {% endif %}
            {% endif %}
        </p>
      </td>
    </tr>
    <tr>
      <td colspan="2" height="32" style="border-collapse:collapse"></td>
    </tr>
  </tbody>
</table>
