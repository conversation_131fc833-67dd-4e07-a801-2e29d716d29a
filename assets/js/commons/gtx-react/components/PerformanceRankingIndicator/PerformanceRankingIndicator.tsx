import gtxConstants from '@getrix/common/js/gtx-constants';
import { Icon } from '@gx-design/icon';
import { Popover } from '@gx-design/popover';
import { Tooltip } from '@gx-design/tooltip';
import { trans } from '@pepita-i18n/babelfish';
import clsx from 'clsx';

import { PERFORMANCE_TAGS } from 'constants/propertyPerformance';
import { PerformanceBadge } from 'gtx-react/components/PerformanceBadge';
import { getPropertyFeatures } from 'lib/property';
import {
    CanSeePerformanceOutput,
    NoPerformanceReason,
} from 'lib/propertyPerformances';
import { IPortalPropertiesListItem } from 'types/api/property';
import { RankingVariation, RankingVariationType } from './types';

export const RANKING_VARIATIONS: RankingVariation = {
    equal: {
        icon: 'equal',
    },
    positive: {
        icon: 'triangle-top',
        sign: '+',
        class: 'gx-text-success',
    },
    negative: {
        icon: 'triangle-down',
        sign: '-',
        class: 'gx-text-error',
    },
};

type Props = CanSeePerformanceOutput & { data: IPortalPropertiesListItem };

const FewSimilarPropertiesPopover: React.FC<{
    data: IPortalPropertiesListItem;
}> = ({ data }) => (
    <div className="performance-text-maxWidth" style={{ whiteSpace: 'normal' }}>
        <span>{trans('label.property_performance.unavailable')}</span>{' '}
        <Popover
            title={''}
            onEdge={false}
            large
            content={
                <>
                    <span>
                        {trans(
                            'label.performances.unique_property_with_following_features',
                            {
                                PORTAL: gtxConstants('AD_PORTAL'),
                            }
                        )}
                    </span>
                    <br />
                    <strong>{getPropertyFeatures(data)}</strong>
                </>
            }
        >
            <Icon className="gx-icon--info" name="info-circle--active" />
        </Popover>
    </div>
);

/**
 * React component to show info about performance ranking, along with an indication about previous week's ranking.
 */
export const PerformanceRankingIndicator: React.FC<Props> = ({
    response,
    reason,
    extra,
    data,
}) => {
    const DAYS = extra?.days;

    if (!response) {
        switch (reason) {
            case NoPerformanceReason.NotResidential:
                return (
                    <div
                        className="performance-text-maxWidth"
                        style={{ whiteSpace: 'normal' }}
                    >
                        {trans('label.performances.no_data_for_this_typology')}
                    </div>
                );

            case NoPerformanceReason.AvailableInXDays:
                return (
                    <div style={{ whiteSpace: 'normal' }}>
                        {DAYS == 1
                            ? trans(
                                  'label.performances.data_available_in_one_day'
                              )
                            : trans(
                                  'label.performances.data_available_in_x_days',
                                  { DAYS }
                              )}
                    </div>
                );

            case NoPerformanceReason.FewSimilarAds:
                return <FewSimilarPropertiesPopover data={data} />;

            case NoPerformanceReason.InvalidData:
            case NoPerformanceReason.IsTourist:
            case NoPerformanceReason.NotActive:
            case NoPerformanceReason.Unavailable:
            default:
                return trans('label.property_performance.unavailable');
        }
    }

    if (!data?.zoneSimilarPropertiesPosition) {
        return trans('label.property_performance.unavailable');
    }

    const { score }: any = PERFORMANCE_TAGS.find(
        (tag) => tag.value === data.performanceRelativeIndex?.id
    );

    let variationType: RankingVariationType | null = null;

    if (typeof data?.positionDiff === 'number') {
        if (data.positionDiff > 0) {
            variationType = 'positive';
        } else if (data.positionDiff < 0) {
            variationType = 'negative';
        } else {
            variationType = 'equal';
        }
    }

    const variation = variationType ? RANKING_VARIATIONS[variationType] : null;

    return (
        <div className="gx-table-new__contentFixedHeight">
            <PerformanceBadge performance={score} large={false}>
                <span>
                    <b>{`${data.zoneSimilarPropertiesPosition}°`}</b>
                    {` ${trans('label.out_of')} ${data.zoneSimilarProperties}`}
                </span>
            </PerformanceBadge>
            {variation && (
                <Tooltip
                    text={trans(
                        'label.ranking_variation_over_the_last_n_days',
                        { N: 7 }
                    )}
                    position="top"
                >
                    <span
                        className={clsx(['variation-index', variation?.class])}
                    >
                        {variation?.icon && <Icon name={variation.icon} />}
                        <span>
                            {variation?.sign}
                            {typeof data?.positionDiff === 'number' &&
                            data.positionDiff !== 0
                                ? Math.abs(data.positionDiff)
                                : ''}
                        </span>
                    </span>
                </Tooltip>
            )}
        </div>
    );
};
