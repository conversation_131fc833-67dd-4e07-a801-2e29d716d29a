import { render, screen } from '#tests/react/testing-library-enhanced';
import { describe, expect, it } from 'vitest';
import { ContractVisibilitySpaces } from './ContractVisibilitySpaces';

describe('ContractVisibilitySpaces', () => {
    it('should render correctly', () => {
        render(
            <ContractVisibilitySpaces
                visibility={
                    {
                        "name": "Vetrina",
                        "service": "showcase",
                        "total": 15,
                        "used": 2,
                        "active": true,
                        "expiration": "30\/11\/2023"
                    }}
            />
        );

        expect(screen.getByText('Vetrina')).toBeInTheDocument();
        expect(screen.getByText('2 label.out_of 15')).toBeInTheDocument();
        expect(screen.queryAllByRole('generic')).toHaveLength(4);
    });

    it('should not render visibility name', () => {
        render(
            <ContractVisibilitySpaces
                visibility={
                    {
                        "name": "Top",
                        "service": "unknown",
                        "total": 15,
                        "used": 2,
                        "active": true,
                        "expiration": "30\/11\/2023"
                    }}
            />
        );

        expect(screen.queryAllByRole('generic')).toHaveLength(2);
    });
});
