@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$data-variation: 'data-variation';

.#{$data-variation} {
  &-wrapper {
    display: flex;
    flex-direction: column;
    gap: space(md);
  }

  &-day {
    display: flex;
    flex-direction: column;
    padding: space(md) 0;

    & + & {
      border-top: 1px solid color(border-main);
    }

    &__date {
      display: flex;
      align-items: center;
      gap: space(sm);
      width: 14rem;
      color: color(content-medium);

      svg {
        @include icon-size(sm);
      }
    }

    @include media('>=#{breakpoint(sm)}') {
      flex-direction: row;
      align-items: flex-start;
      gap: space(xl);
    }
  }

  &-list {
    padding: 0;
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: space(md);

    &__time {
      display: flex;
      align-items: center;
      gap: space(sm);
      height: 2.4rem;
      margin-right: space(xl);
      color: color(content-low);

      svg {
        @include icon-size(xs);
      }
    }

    &__item {
      display: grid;
      grid-template-columns: 1fr auto 2fr;
    }

    &__content {
      display: flex;
      flex-direction: column;
      gap: space(xs);
      margin-left: space(sm);

      &Action {
        font-weight: bold;
      }

      &Author {
        color: color(content-medium);
      }

      &PriceVariation {
        &::before {
          content: '';
          display: inline-block;
          width: 0;
          height: 0;
          margin-right: space(xs);
          border-style: solid;
          vertical-align: middle;
        }

        &.up::before {
          border-width: 0 5px 6px 5px;
          border-color: transparent transparent color(content-success) transparent;
        }

        &.down::before {
          border-width: 6px 5px 0 5px;
          border-color: color(content-error) transparent transparent transparent;
        }

        .oldPrice {
          color: color(content-low);
          text-decoration: line-through;
        }
      }
    }
  }
}
