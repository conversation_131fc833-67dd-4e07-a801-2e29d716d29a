{% macro search_tags(city = "", province = "", zones = [], drawn) %}
    {% if city != "" %}
        {{ city }}{{ _self.print_zones(zones)|trim }}
    {% elseif provice != "" %}
        {{ province }}{{ _self.print_zones(zones)|trim }}
    {% elseif drawn != "" %}
        {{'map.drawn_area'|trans({}, gtxConstants.DOMAIN_CMS) }}
    {% endif %}
{% endmacro %}

{% macro print_zones(zones) %}
    {% if zones|length > 0 %}, {{ zones|length }} {{ (zones|length >1 ? 'zone' : 'zona') }}
    {% endif %}
{% endmacro %}
