import gtxConstants from '@getrix/common/js/gtx-constants';
import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { Loader } from '@gx-design/loader';
import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { useQuery } from '@tanstack/react-query';
import clsx from 'clsx';

import { createGetPropertyDetailOptions } from 'lib/REST/requests/api/properties/query-factory';
import { DetailPropertyType } from 'lib/REST/types/detail-property';
import { RealEstateCategory } from 'types/portalModal';
import { DetailContent } from './DetailContent';
import { AD_MEDIA_FIELDS, NEW_CONSTRUCTION_MEDIA_FIELDS } from './MediaSection';
import { RealEstateDetailModalType, RealEstateModalTab } from './types';
import { getValueByPath } from './utils';
import { SectionData } from 'constants/realEstateSectionsData';

const QUERY_CONFIG = {
    refetchIntervalInBackground: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: 600 * 1000,
};

export type RealEstateDetailProps = {
    id: string;
    category: RealEstateCategory;
    onError: () => void;
    onTabChange: (
        tab: RealEstateModalTab
    ) => React.MouseEventHandler<HTMLButtonElement>;
    type: RealEstateDetailModalType;
    onEditClick: (section: SectionData) => void;
};

export const RealEstateDetail: React.FC<RealEstateDetailProps> = ({
    id,
    category,
    onError,
    onTabChange,
    type,
    onEditClick,
}) => {
    const { showNotification } = useNotifyContext();

    const tab = type.split('-')?.[1] as RealEstateModalTab;

    const onDetailError = () => {
        onError();
        showNotification({
            type: 'error',
            message: trans('label.error_durin_operation'),
        });
    };

    const { isLoading, data } = useQuery({
        ...createGetPropertyDetailOptions({ params: { id } }),
        ...QUERY_CONFIG,
        meta: {
            errorCallback: onDetailError,
        },
    });

    if (isLoading || !data) {
        return (
            <div className="gx-modal__bodyLoading">
                <Loader />
            </div>
        );
    }

    switch (tab) {
        case 'detail':
            return (
                <DetailContent
                    category={category}
                    propertyData={data}
                    onTabChange={onTabChange}
                    onEditClick={onEditClick}
                />
            );
        case 'photos':
        case 'plans':
            return (
                <GallerySection
                    data={data}
                    media={tab}
                    twoColumns={tab === 'photos'}
                    category={category}
                />
            );
        case 'video':
        case 'virtualtour':
        case 'photoplan':
            return (
                <IFrameSection data={data} media={tab} category={category} />
            );
        default:
            return null;
    }
};

const GallerySection = ({ data, media, twoColumns, category }) => {
    const path = (
        category === 'newConstruction'
            ? [...NEW_CONSTRUCTION_MEDIA_FIELDS]
            : [...AD_MEDIA_FIELDS]
    ).find((elem) => elem.name === media)?.path;

    const list = getValueByPath(data, path)?.map((elem) => {
        if (category !== 'newConstruction') {
            return elem;
        } else {
            // TODO: create a reusable util to do this
            return {
                url: `${gtxConstants('GETRIX_MEDIA_URL')}/${gtxConstants(
                    'AD_IMAGE_ENDPOINT'
                )}/${elem.id}/print.jpg`,
            };
        }
    });

    return (
        <div
            className={clsx([
                'propertyDetail__gallery',
                { ' propertyDetail__gallery--twoColumns': twoColumns },
            ])}
        >
            {list.map((img, idx) => (
                <div className="propertyDetail__galleryItem" key={`img-${idx}`}>
                    <img src={img.url} />
                </div>
            ))}
        </div>
    );
};

const generateIFrameUrl = (
    media: RealEstateModalTab,
    value: any,
    data: DetailPropertyType,
    category: RealEstateCategory
) => {
    switch (media) {
        case 'video':
            return `https://www.youtube.com/embed/${value}`;
        case 'virtualtour':
            return value;
        case 'photoplan':
            if (category === 'auction') {
                if (!data.properties?.length) {
                    // this should hypothetically never happen
                    return null;
                }
                const mainPropertyIdx = data.properties.findIndex(
                    (elem) => elem.isMain === true
                );
                const idx = mainPropertyIdx > -1 ? mainPropertyIdx : 0;
                return `${gtxConstants('FOTOPLAN_BASEURL')}/${data.id}/${data
                    ?.properties?.[idx]?.id}/viewer`;
            }
            return `${gtxConstants('FOTOPLAN_BASEURL')}/${data.id}/viewer`;
        default:
            return null;
    }
};

const IFrameSection = ({ data, media, category }) => {
    const path = AD_MEDIA_FIELDS.find((elem) => elem.name === media)?.path;
    const value = getValueByPath(data, path);

    return (
        <div className="propertyDetail__iframeWrapper">
            <iframe
                className={`propertyDetail__iframe-${media}`}
                src={generateIFrameUrl(media, value, data, category)}
            />
        </div>
    );
};

type DetailModalTitleProps = {
    onGoBack: React.MouseEventHandler<HTMLButtonElement>;
    title: string;
};
export const DetailModalTitle: React.FC<DetailModalTitleProps> = (props) => (
    <>
        <Button
            className="gx-modal__back"
            onClick={props.onGoBack}
            iconOnly
            variant="ghost"
        >
            <Icon name="arrow-left" />
        </Button>
        <span>{` ${props.title}`}</span>
    </>
);
