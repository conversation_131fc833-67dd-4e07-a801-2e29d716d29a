import fs from 'node:fs/promises';
import openapiTS, { astToString } from 'openapi-typescript';

const docs = 'http://gestionale.it.localhost/fapi-docs/json';
const fapiBaseDir = './assets/js/commons/lib/fapi';

async function fetchAndSaveOas() {
    try {
        const oasContent = await fetch(docs).then((res) => res.json());

        const fileContent = `import { fromOpenApi } from '@mswjs/source/open-api';

export const fapiOASHandlers = await fromOpenApi(${JSON.stringify(
            oasContent,
            null,
            4
        )});`;

        const ast = await openapiTS(oasContent);
        const contents = astToString(ast);

        await Promise.all([
            fs.writeFile(`${fapiBaseDir}/fapi.d.ts`, contents),
            fs.writeFile(`${fapiBaseDir}/spec.ts`, fileContent),
        ]).then(() => {
            console.log(
                '🪄  OAS declaration types for fapi client are been generated!'
            );
            console.log(
                '🧪 OAS handler file has been saved successfully (for @mswjs/source).'
            );
        });
    } catch (error) {
        console.error('Error fetching or saving OAS:', error);
    }
}

fetchAndSaveOas();
