# OnBoarding App 
## A micro frontend, framework agnostic, solution to show onboarding tours  

### The problem 

Our need was to develop an onboarding flow to be shown on our SideMenu and in future also in other parts of our app, 
in our Getrix app we have different combination of micro frontend depending on which page user request, combinations can be
- Immobiliare Flux (Trunk) + Plain Js (Side-Menu)
- React.js (Mls) + Plain Js (Side-Menu)

These situation led us to the need of a framework agnostic solution, which can be used also in other contexts and apps.

The key concept is that the *OnBoardingApp* will look in DOM for Elements having the following data attribute:
- **data-onboarding**


## DOM Scan

On mount the *OnBoardingApp* will register a listener for *DOMContentLoaded* which trigger the first DOM scan.
The scan will be also triggered by dispatching the custom event *onboardingRescanRequest* (see below in [Events](#events) section)

## Tours Validation Logic

To use effectively the *OnBoardingApp* a "Tour Validation Logic" insight is needed.

At startup the *OnBoardingApp* will retrieve the list of onboarding tours to show from its API.

A tour will be shown if in the document, at the time of the last scan, there was elements for
all the steps defined in the Tour object coming from API.

So for example if the api tour object *searches-tour* has 3 steps and in DOM the App find only elements for 2 steps the Tour will not start.

This logic helps to wait the complete list of elements in an asyncronous scenario where html elements are rendered
from different App (e.g. React.js (Mls) + Plain Js (Side-Menu)), possibly as consequence of different api call.

## Tours Presentation Logic

The tours objects from API will come with an **order** field, if multiple tours are to be shown (so the app found multiple valid tours) they will be shown in ordered sequence.

Once a *tour* end or it is closed by the user (clicking on X) the app will call the **/onboarding-tour/{tour-id}/read** api endpoint in order to not show anymore the tour for that user, if there is another valid tour in the array it will be shown immediatelly, if not, the app will call the **/onboarding-tour** list api after the /read responde and
if there will be new tours to show the app will show them ordered as above restarting the flow.

## Added css classes

The *OnBoardingApp* once a Tour is shown will add to the body the class *onboarding-open* and will add to the active element the class *onboarding-item-active*

## Data Attributes Syntax

The **data-onboarding** need a json array of objects with the following form:

- **tour**: just a string with the tour name (has to match the one coming from API) *"searches-tour"*
- **step**: number
- **position**: will inform the *OnBoardingApp* where to mount the tour Box for that step the following values are allowed "top" | "topLeft" | "topRight" | "bottom" | "left" | "right" | "bottomLeft" | "bottomRight" | "rightBottom" | "rightTop" | "leftBottom" | "leftTop"

```ts
type OnBoardDataAttrItem = {
    tour: string, 
    step: number, 
    position: "top" | "topLeft" | "topRight" | "bottom" | "left" | "right" | "bottomLeft" | "bottomRight" | "rightBottom" | "rightTop" | "leftBottom" | "leftTop"
}
```

This data structure allow to have the same element as a subject of different steps of the same tour or even different tours.

A json declaration:
```json
[ 
  { "tour": "searches-tour", "step": 1, "position": "right" }, 
  { "tour": "searches-tour", "step": 2, "position": "right" }, 
  { "tour": "searches-tour-2", "step": 1, "position": "right" },
  { "tour": "searches-tour-2", "step": 2, "position": "right" } 
]
``` 

The html result:
```html
<div id="menu-messages" class="gx-side-menu__item panel onboarding-item-active" data-onboarding="[{&quot;tour&quot;:&quot;searches-tour&quot;,&quot;step&quot;:1,&quot;position&quot;:&quot;right&quot;},{&quot;tour&quot;:&quot;searches-tour&quot;,&quot;step&quot;:2,&quot;position&quot;:&quot;right&quot;},{&quot;tour&quot;:&quot;searches-tour-2&quot;,&quot;step&quot;:1,&quot;position&quot;:&quot;right&quot;},{&quot;tour&quot;:&quot;searches-tour-2&quot;,&quot;step&quot;:2,&quot;position&quot;:&quot;right&quot;}]">
</div>
```

## Events

Listen:
- onboardingRescanRequest
- onboradingUpdateBoxPositionReq

Emit:
- onboardingStepChange
- onboardingMount

### Events the app will listen to:

**onboardingRescanRequest**

A *Custom event* to handle the async scenario described in [Tours Validation Logic](#tours-validation-logic) section.

The app which render asyncronously elements that as to be used by the OnboardingTourApp,
as to actively dispatch this custom event to notify the *OnBoardingApp* that has to rescan.

Example:
```tsx
    useEffect(()=> {
        //...your logic
        window.dispatchEvent(new CustomEvent('onboardingRescanRequest'));
    }, [yourDependency])
```


**onboradingUpdateBoxPositionReq** 

A *Custom event* to trigger a recalculation of the box position in relation to its anchor div.

Below the event emitted from a JQuery app:
```js
$menu.on('transitionend', function () {
    window.dispatchEvent(new CustomEvent('onboradingUpdateBoxPositionReq'));
})
```

### Events the app will emit:

**onboardingStepChange** 

A *Custom event* which will contain *stepIndex* and *tourName*

Below an example on how to handle the event emitted
```tsx
    useEffect(()=> {
        const onBoardingStepChange = (ev: { tourName: string, stepIndex: number }) => {
            //...your logic
            if(ev.tourName === 'myComponent-tour' && stepIndex === 2){
                /**
                 * logic of your component for step 2 such as open a dropdown
                 * or launch a rocket, your choice.
                 **/
            }
        }
        
        window.addEventListener('onboardingStepChange', onBoardingStepChange)

        return () => {
            window.removeEventListener('onboardingStepChange', onBoardingStepChange)
        }
    }, [])
```

**onboardingMount** 

A *Custom event* dispatched by the Onboarding app on UI box mount. The event will have *stepIndex* and *tourName* as data.

The event emitted:
```ts
const onboardingMount = () => new CustomEvent(
        'onboardingMount',
        {
            detail: {
                stepIndex: stepIndex,
                tourName: currentTour?.name
            }
        })
```
Below the event handled with JQuery:

```js
$(window).on('onboardingMount', function (event) {
            const tourName = event.originalEvent.detail.tourName;
            if (tourName === 'searches-tour') {
                openMenu()
            }
        })
```

## MLS Menu - How to

To declare a menu element as a subject of a Tour Step you can add in @getrix/common > *config/side-menu.json*  the keyword **onboarding** in a section or subsection with a JSON value with the same data structure defined in  [Data Attribute Syntax Chapter](#data-attributes-syntax)

Example:
```json
  "messages": {
    "name": "messages",
    "label": "label.messages",
    "href": "messaggi",
    "icon": "envelope_icon",
    "featureToggleGroup": "customer_requests",
    "onboarding": [ 
      { "tour": "searches-tour", "step": 1, "position": "right" }, 
      { "tour": "searches-tour", "step": 2, "position": "right" }, 
      { "tour": "searches-tour-2", "step": 1, "position": "right" },
      { "tour": "searches-tour-2", "step": 2, "position": "right" } 
    ],
    "subsections": {
      "request-messages": {
        "name": "request-messages",
        "label": "label.messages",
        "href": "/messaggi/lista",
        "featureToggleFeature": "requests_messages",
        "template": "messaging",
        "tag": "label.brand_new"
      },
      //etc ....
```
In the case above the menu section "messages" will be used by the OboardingApp to show 2 step of the tour "searches-tour" and 2 steps of the tour "searches-tour-2".
