import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { useMutation } from '@tanstack/react-query';
import { getRankingDetailComparison } from 'lib/REST/requests/v2/lista-annunci';

type ComparisonParams = {
    propertyId: string;
    comparisonId: string;
};

type ComparisonResult = Awaited<ReturnType<typeof getRankingDetailComparison>>;

export const useRankingDetailComparisonMutation = () => {
    const { showNotification } = useNotifyContext();

    return useMutation<ComparisonResult, Error, ComparisonParams>({
        mutationFn: async ({ propertyId, comparisonId }) =>
            getRankingDetailComparison({
                query: { adId: propertyId, adIdToCompare: comparisonId },
            }),

        onSettled: (data, error) => {
            if (error || !data?.success || data?.data?.length !== 2) {
                {
                    showNotification({
                        type: 'error',
                        message: trans('label.error_durin_operation'),
                    });
                }
            }
        },
    });
};
