# you can read more about security in the related section of the documentation
# http://symfony.com/doc/current/book/security.html
security:

    # http://symfony.com/doc/current/book/security.html#where-do-users-come-from-user-providers
    providers:
        user_provider:
            id: getrix_app.user.provider
        basic_auth_users:
            memory:
                users:
                    data-api:
                        password: 8be8de9c-f031-4b2a-9873-f3d3c4337fdb

    firewalls:
        # disables firewall for assets and the profiler
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js|build|assets)/
            security: false

        searches_internal:
            pattern: ^/internal/api/searches
            stateless: true
            http_basic:
                provider: basic_auth_users

        secured_area:
            anonymous: ~
            access_denied_url: /signin/
            provider: user_provider
            logout_on_user_change: true

            remember_me:
                path: /
                secret: '%secret%'
                lifetime: 86400 # This value is overridden in TokenBasedRememberMeServices::onLoginSuccess
                always_remember_me: true
                name: '%getrix.cookie_session_persist_name%'
                domain: '%getrix.cookie_session_domain%'
                secure: true
                httponly: true

            form_login:
                login_path: /signin/
                check_path: /login_check
                default_target_path: '%app.default_target_path%'
                #csrf_token_generator: security.csrf.token_manager
                failure_path: /signin/
                username_parameter: username # Per mantenere l'autocomplete delle credenziali
                password_parameter: password # Per mantenere l'autocomplete delle credenziali
                remember_me: true

            logout:
                path:   /logout
                target: "%app.logout_target%"
                success_handler: App\Security\LogoutSuccessHandler
                delete_cookies:
                    '%getrix.cookie_session_name%': { path: /, domain: '%getrix.cookie_session_domain%' }

    encoders:
        Symfony\Component\Security\Core\User\User: plaintext
        App\Security\User\GetrixUser:
            algorithm: bcrypt
            cost: 12

    role_hierarchy:
        ROLE_GETRIX_USER:           [ROLE_USER]
        ROLE_AMMINISTRATORE:        [ROLE_USER, ROLE_GETRIX_USER]
        ROLE_SUPER_AMMINISTRATORE:  [ROLE_USER, ROLE_GETRIX_USER, ROLE_AMMINISTRATORE]
