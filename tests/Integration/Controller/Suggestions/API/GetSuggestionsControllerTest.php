<?php

declare(strict_types=1);

namespace Tests\Integration\Controller\Suggestions\API;

use App\Constants\Property\TypeConstants;
use App\Controller\Suggestions\API\GetSuggestionsController;
use App\DataMapper\DTOToResponse\Suggestions\SuggestionsDataMapper;
use App\Handler\Suggestions\SuggestionsHandler;
use App\Model\Request\Suggestions\SuggestionsRequest;
use App\Model\Suggestions\Suggestion;
use App\Model\Suggestions\Suggestions;
use Symfony\Component\HttpFoundation\Response;
use Tests\KernelTestCase;

class GetSuggestionsControllerTest extends KernelTestCase
{
    private GetSuggestionsController $controller;
    private SuggestionsHandler $suggestionsHandler;
    private SuggestionsDataMapper $dataMapper;

    protected function setUp(): void
    {
        parent::setUp();
        $kernel = static::bootKernel();

        /** @var SuggestionsDataMapper $suggestionsDataMapper */
        $suggestionsDataMapper = $kernel->getContainer()->get(SuggestionsDataMapper::class);
        $this->dataMapper = $suggestionsDataMapper;
        $this->suggestionsHandler = $this->createMock(SuggestionsHandler::class);

        $this->controller = new GetSuggestionsController(
            $this->dataMapper,
            $this->suggestionsHandler
        );
    }

    public function testLookupWithValidRequest(): void
    {
        $request = new SuggestionsRequest();
        $request->setTerm('test');
        $request->setType(TypeConstants::ACTIVE);

        $suggestions = new Suggestions();
        $suggestions->setTerm('test');

        $idSuggestion = new Suggestion();
        $idSuggestion->setValue('123');
        $suggestions->setIds([$idSuggestion]);

        $citySuggestion = new Suggestion();
        $citySuggestion->setValue('Milan');
        $suggestions->setCities([$citySuggestion]);

        $codeSuggestion = new Suggestion();
        $codeSuggestion->setValue('PROP123');
        $suggestions->setCodes([$codeSuggestion]);

        $addressSuggestion = new Suggestion();
        $addressSuggestion->setValue('Via Roma 1');
        $suggestions->setAddress([$addressSuggestion]);

        $zoneSuggestion = new Suggestion();
        $zoneSuggestion->setValue('Centro');
        $suggestions->setMacroZones([$zoneSuggestion]);

        $this->suggestionsHandler
            ->expects($this->once())
            ->method('getSuggestions')
            ->with($request)
            ->willReturn($suggestions);

        $response = $this->controller->lookup($request);

        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $content = \json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $this->assertArrayHasKey('status', $content);
        $this->assertEquals('success', $content['status']);

        $data = $content['data'];
        $this->assertArrayHasKey('suggestions', $data);
        $suggestions = $data['suggestions'];
        $this->assertIsArray($suggestions);

        $types = \array_column($suggestions, 'type');
        $this->assertContains('ref', $types);
        $this->assertContains('city', $types);
        $this->assertContains('code', $types);
        $this->assertContains('address', $types);
        $this->assertContains('zone', $types);
    }

    public function testLookupWithEmptyResults(): void
    {
        $request = new SuggestionsRequest();
        $request->setTerm('nonexistent');
        $request->setType(TypeConstants::ACTIVE);

        $suggestions = new Suggestions();
        $suggestions->setTerm('nonexistent');

        $this->suggestionsHandler
            ->expects($this->once())
            ->method('getSuggestions')
            ->with($request)
            ->willReturn($suggestions);

        $response = $this->controller->lookup($request);

        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(200, $response->getStatusCode());

        $content = \json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $this->assertArrayHasKey('status', $content);
        $this->assertEquals('success', $content['status']);

        $data = $content['data'];
        $this->assertArrayHasKey('suggestions', $data);
        $this->assertEmpty($data['suggestions']);
    }
}
