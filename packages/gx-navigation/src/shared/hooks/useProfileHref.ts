import { useEnsureGxNavigationContext } from '../components/GxNavigationContext';

export const useProfileHref = (urlFromParam: string) => {
  const { defaultLanguage, agencyLanguage } = useEnsureGxNavigationContext();

  if (!urlFromParam.includes('%LANG%')) {
    return urlFromParam;
  }

  let lang = defaultLanguage;

  if (agencyLanguage && typeof agencyLanguage === 'string') {
    lang = agencyLanguage;
  }

  lang = lang.substring(0, 2).toLowerCase();

  return urlFromParam.replace('%LANG%', lang);
};
