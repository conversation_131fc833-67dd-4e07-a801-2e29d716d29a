import { PortalModalState } from 'types/portalModal';
import { REAL_ESTATE_DETAIL_TITLE } from './constants';
import { DetailModalTitle, RealEstateDetailProps } from './RealEstateDetail';
import { RealEstateModalTab } from './types';

type ReturnType = {
    onTabChange: RealEstateDetailProps['onTabChange'];
};

type Input<T extends {}> = {
    showModal: React.Dispatch<React.SetStateAction<PortalModalState & T>>;
};
/** Useful hook to integrate the new detail modal into the "old" mechanism of single portal lists */
export const useIntegrateRealEstateModal = <T extends {}>({
    showModal,
}: Input<T>): ReturnType => {
    const onGoBack = () =>
        showModal((prev) => ({
            ...prev,
            type: 'realEstate-detail',
            title: REAL_ESTATE_DETAIL_TITLE['detail'],
        }));

    const onTabChange = (tab: RealEstateModalTab) => () =>
        showModal((prev) => ({
            ...prev,
            type: `realEstate-${tab}`,
            title: (
                <DetailModalTitle
                    onGoBack={onGoBack}
                    title={REAL_ESTATE_DETAIL_TITLE[tab]}
                />
            ),
        }));

    return { onTabChange };
};
