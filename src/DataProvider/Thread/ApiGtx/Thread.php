<?php

declare(strict_types=1);

namespace App\DataProvider\Thread\ApiGtx;

use App\Builder\Model\Messaging\AdditionalInfoBuilder;
use App\Constants\Base\MessagingConstants;
use App\Constants\Base\PropertyConstants;
use App\Constants\PerformanceProfiler;
use App\DataProvider\AbstractApiGtxDataProvider;
use App\DataProvider\Thread\ApiGtx\DataMapper\InboxDataMapper;
use App\DataProvider\Thread\ApiGtx\DataMapper\StatsDataMapper;
use App\DataProvider\Thread\ApiGtx\DataMapper\ThreadDataMapper;
use App\DataProvider\Thread\ThreadInterface;
use App\DTO\Messaging\MarkThreadAsAnsweredByPhone as MarkThreadAsAnsweredByPhoneDTO;
use App\DTO\Messaging\MarkThreadsAsRead as MarkThreadsAsReadDTO;
use App\Exception\ApiException;
use App\Helper\ApiWsseHeaderGenerator;
use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;
use App\Model\Messaging\AdditionalInfo;
use App\Model\Messaging\Inbox;
use App\Model\Messaging\ParametricStatsFilters;
use App\Model\Messaging\Stats;
use App\Performance\ProfilerInterface;
use App\Utils\SessionUtils;
use Symfony\Component\HttpFoundation\Response;

class Thread extends AbstractApiGtxDataProvider implements ThreadInterface
{
    const RESOURCE_SOCIALS = 'socials';
    const RESOURCE_AGENCY = 'agency';
    const RESOURCE_AGENTS = 'agents';
    const RESOURCE_LEADS = 'leads';

    private float $countersTimeout;
    private InboxDataMapper $inboxDataMapper;
    private StatsDataMapper $statsDataMapper;
    private ThreadDataMapper $threadDataMapper;

    public function __construct(
        ApiWsseHeaderGenerator $apiWsseHeaderGenerator,
        HttpClientHelper $client,
        InboxDataMapper $inboxDataMapper,
        ParametersBag $parametersBag,
        ProfilerInterface $performanceProfiler,
        SessionUtils $sessionUtils,
        StatsDataMapper $statsDataMapper,
        ThreadDataMapper $threadDataMapper
    ) {
        parent::__construct($apiWsseHeaderGenerator, $client, $parametersBag, $performanceProfiler, $sessionUtils);

        $this->inboxDataMapper = $inboxDataMapper;
        $this->statsDataMapper = $statsDataMapper;
        $this->threadDataMapper = $threadDataMapper;

        $this->countersTimeout = (float) $parametersBag->get('getrix.api_counters_timeout');
    }

    /**
     * @throws ApiException
     */
    public function getThread(
        int $agencyId,
        int $agentId,
        string $id
    ): \App\Model\Messaging\Thread {
        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s',
            $this->getBaseUri(),
            self::RESOURCE_AGENCY,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::RESOURCE_LEADS,
            self::RESOURCE_SOCIALS,
            $id
        );

        $response = $this->client->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (null === $response || Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        return $this->threadDataMapper->map($decoded['data']);
    }

    /**
     * @throws ApiException
     */
    public function setThreadAsFavorite(
        int $agencyId,
        int $agentId,
        string $id
    ) {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/favorite',
            $this->getBaseUri(),
            self::RESOURCE_AGENCY,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::RESOURCE_LEADS,
            self::RESOURCE_SOCIALS,
            $id
        );

        $response = $this->client->execRequestWithResponse(
            'PATCH',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (null === $response || Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $this->performanceProfiler->stop(__METHOD__);
    }

    /**
     * @throws ApiException
     */
    public function setThreadAsNotFavorite(
        int $agencyId,
        int $agentId,
        string $id
    ) {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/not-favorite',
            $this->getBaseUri(),
            self::RESOURCE_AGENCY,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::RESOURCE_LEADS,
            self::RESOURCE_SOCIALS,
            $id
        );

        $response = $this->client->execRequestWithResponse(
            'PATCH',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $this->performanceProfiler->stop(__METHOD__);
    }

    /**
     * @throws ApiException
     */
    public function setThreadArchived(
        int $agencyId,
        int $agentId,
        string $id
    ) {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/archive',
            $this->getBaseUri(),
            self::RESOURCE_AGENCY,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::RESOURCE_LEADS,
            self::RESOURCE_SOCIALS,
            $id
        );

        $response = $this->client->execRequestWithResponse(
            'PATCH',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (null === $response || Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $this->performanceProfiler->stop(__METHOD__);
    }

    /**
     * @throws ApiException
     */
    public function markAllThreadsAsRead(
        int $agencyId,
        int $agentId
    ) {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/read-all',
            $this->getBaseUri(),
            self::RESOURCE_AGENCY,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::RESOURCE_LEADS,
            self::RESOURCE_SOCIALS
        );

        $response = $this->client->execRequestWithResponse(
            'PATCH',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (null === $response || Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $this->performanceProfiler->stop(__METHOD__);
    }

    /**
     * @throws ApiException
     */
    public function markThreadsAsRead(
        int $agencyId,
        int $agentId,
        MarkThreadsAsReadDTO $markThreadsAsRead
    ): bool {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/read',
            $this->getBaseUri(),
            self::RESOURCE_AGENCY,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::RESOURCE_LEADS,
            self::RESOURCE_SOCIALS
        );

        $response = $this->client->execRequestWithResponse(
            'PATCH',
            $url,
            ['threadIds' => $markThreadsAsRead->getThreadIds()],
            $this->getDefaultJsonHeaders()
        );

        if (null === $response) {
            throw new ApiException(PropertyConstants::EXCEPTION_MESSAGES['generic'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return $decoded['data'];
    }

    public function markThreadAsAnsweredByPhone(
        int $agencyId,
        int $agentId,
        MarkThreadAsAnsweredByPhoneDTO $markThreadAsAnsweredByPhone
    ): AdditionalInfo {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/additional-info',
            $this->getBaseUri(),
            self::RESOURCE_AGENCY,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::RESOURCE_LEADS,
            self::RESOURCE_SOCIALS,
            $markThreadAsAnsweredByPhone->getThreadId()
        );

        $response = $this->client->execRequestWithResponse(
            'POST',
            $url,
            ['answeredByPhone' => $markThreadAsAnsweredByPhone->getAnsweredByPhone()->toIso8601String()],
            $this->getDefaultJsonHeaders()
        );

        if (null === $response) {
            throw new ApiException(PropertyConstants::EXCEPTION_MESSAGES['generic'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        if (Response::HTTP_CREATED !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return AdditionalInfoBuilder::newBuilder()->fromApiResponse($decoded['data'])->build();
    }

    /**
     * @throws ApiException
     */
    public function getParametricStats(
        int $agencyId,
        int $agentId,
        ParametricStatsFilters $filters
    ): Stats {
        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/parametric-stats',
            $this->getBaseUri(),
            self::RESOURCE_AGENCY,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::RESOURCE_LEADS,
            self::RESOURCE_SOCIALS
        );

        $response = $this->client->execRequestWithResponse(
            'GET',
            $url,
            $this->buildGetSocialsParametricStatsFilters($filters),
            $this->getDefaultHeaders()
        );

        if (null === $response) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        return $this->statsDataMapper->map($decoded['data']);
    }

    /**
     * @throws ApiException
     */
    public function getInbox(
        int $agencyId,
        int $agentId
    ): Inbox {
        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/inbox',
            $this->getBaseUri(),
            self::RESOURCE_AGENCY,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::RESOURCE_LEADS,
            self::RESOURCE_SOCIALS
        );

        $this->client->setTimeout($this->countersTimeout);
        $response = $this->client->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultHeaders()
        );

        if (null === $response) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(
                MessagingConstants::EXCEPTION_MESSAGES['generic'],
                $response->getStatusCode(),
                null,
                $response
            );
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        return $this->inboxDataMapper->map($decoded['data']);
    }

    private function buildGetSocialsParametricStatsFilters(
        ParametricStatsFilters $filters
    ): array {
        $parameters = [];

        if (null !== $filters->getDeleted()) {
            $parameters['deleted'] = $filters->getDeleted();
        }

        if (null !== $filters->getFavorite()) {
            $parameters['favorite'] = $filters->getFavorite();
        }

        if (null !== $filters->getArchived()) {
            $parameters['archived'] = $filters->getArchived();
        }

        if (null !== $filters->getThread()) {
            $parameters['thread'] = $filters->getThread();
        }

        if (null !== $filters->getMessage()) {
            $parameters['message'] = $filters->getMessage();
        }

        if (null !== $filters->getRelation()) {
            $parameters['relation'] = $filters->getRelation();
        }

        return $parameters;
    }
}
