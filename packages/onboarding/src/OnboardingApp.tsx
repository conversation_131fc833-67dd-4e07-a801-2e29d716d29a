import * as React from 'react';
import { createRoot } from 'react-dom/client';
import { ToursProvider } from './components/ToursProvider';
import { TourManager } from './views/TourManager';

const container = document?.getElementById('root-onboarding');

const OnBoardingApp = () => {
    return (
        <ToursProvider>
            <TourManager />
        </ToursProvider>
    );
};

if (container) {
    const root = createRoot(container);

    root.render(<OnBoardingApp />);
} else {
    console.warn(
        'OnBoardingApp: Cannot mount Onboarding app, div with id root-onboarding missing in DOM'
    );
}
