{% extends 'mail/base-restyled.html.twig' %}

{% block content %}

    <h1 style="width: 100%; max-width: 398px; font-size: 18px; font-weight: normal; color: #333333; font-family: Arial, sans-serif, 'Open Sans'; margin: 0 auto 20px;">
        <b style="width: 100%; max-width: 398px; margin-left: auto; margin-right: auto; font-weight: bold;">{{ user.nome|title }}</b>,
        {{ 'mail.settings.user_active.text_1'|trans({'%APP_NAME%':gtxConstants.APP_NAME}, gtxConstants.DOMAIN_CMS) }} <b class="green" style="color: #4ab04a; width: 100%; max-width: 398px; margin-left: auto; margin-right: auto; font-weight: bold;">{{ 'label.active'|trans({}, gtxConstants.DOMAIN_CMS)|lower }}.</b>
    </h1>

    <p style="line-height: 1.5; font-size: 14px; color: #333333; font-family: Aria<PERSON>, sans-serif, 'Open Sans'; width: 100%; max-width: 520px; margin: 0 auto 30px;">
        {{ 'mail.settings.user_active.text_2'|trans({}, gtxConstants.DOMAIN_CMS) }} <a href="{{ globalConfigs.getrix.baseurl }}" style="color: #408cc6; text-decoration: none; width: 100%; max-width: 398px; margin-left: auto; margin-right: auto;">{{ globalConfigs.getrix.baseurl }}</a>,
        <br style="width: 100%; max-width: 398px; margin-left: auto; margin-right: auto;" />
        {{ 'mail.settings.user_active.text_3'|trans({}, gtxConstants.DOMAIN_CMS) }}
    </p>

    <a href="{{ globalConfigs.getrix.baseurl }}{{ path('profile_me') }}" target="_blank" style="border-style: none;">
        <img class="full-img" src="{{ globalConfigs.getrix.baseurl }}{{ asset('base/getrix/common/img/country/' ~ gtxConstants.COUNTRY_TAG ~ '/email/complete-profile.png') }}" alt="completa profilo" style="display: block; width: 398px; margin: 0 auto 30px;" />
    </a>

    <p style="line-height: 1.5; font-size: 14px; color: #333333; font-family: Arial, sans-serif, 'Open Sans'; width: 100%; max-width: 520px; margin: 0 auto 30px;">
        {{ 'mail.settings.user_active.text_4'|trans({}, gtxConstants.DOMAIN_CMS) }} {{ gtxConstants.APP_FIRMA_EMAIL }}
    </p>

{% endblock %}

{% block footer %}
    {% include 'mail/settings/footer.html.twig' %}
{% endblock %}
