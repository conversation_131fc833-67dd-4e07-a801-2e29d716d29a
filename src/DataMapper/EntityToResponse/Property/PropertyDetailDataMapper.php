<?php

declare(strict_types=1);

namespace App\DataMapper\EntityToResponse\Property;

use App\Constants\Base\GtxConstants;
use App\DataMapper\AbstractDataMapper;
use App\Model\Property\CadastralData;
use App\Model\Property\Image;
use App\Model\Property\Plan;
use App\Model\Response\Property\PropertyResponse;
use App\Utils\TranslatorTrait;
use App\Utils\ValuteTrait;
use ObjectMapper\Configuration\ObjectMapperConfig;
use Symfony\Component\Translation\TranslatorInterface;

class PropertyDetailDataMapper extends AbstractDataMapper
{
    use ValuteTrait;
    use TranslatorTrait;

    private CadastralDataDataMapper $cadastralDataDataMapper;
    private CompositionDataMapper $compositionDataMapper;
    private ConsistenceDataMapper $consistenceDataMapper;
    private CoWorkingTimetableDataMapper $coWorkingTimetableDataMapper;
    private EnergyClassDataMapper $energyClassDataMapper;
    private FeaturesDataMapper $featuresDataMapper;
    private GarageDataMapper $garageDataMapper;
    private IndustrialDataMapper $industrialDataMapper;
    private LandDataMapper $landDataMapper;
    private PortionDataMapper $portionDataMapper;
    private PropertyImageDataMapper $propertyImageDataMapper;
    private PropertyPlanDataMapper $propertyPlanDataMapper;
    private SharedApartmentInformationDataMapper $sharedApartmentInformationDataMapper;
    private ShopDataMapper $shopDataMapper;
    private TranslatorInterface $translator;

    public function __construct(
        CadastralDataDataMapper $cadastralDataDataMapper,
        CompositionDataMapper $compositionDataMapper,
        ConsistenceDataMapper $consistenceDataMapper,
        CoWorkingTimetableDataMapper $coWorkingTimetableDataMapper,
        EnergyClassDataMapper $energyClassDataMapper,
        FeaturesDataMapper $featuresDataMapper,
        GarageDataMapper $garageDataMapper,
        IndustrialDataMapper $industrialDataMapper,
        LandDataMapper $landDataMapper,
        PortionDataMapper $portionDataMapper,
        PropertyImageDataMapper $propertyImageDataMapper,
        PropertyPlanDataMapper $propertyPlanDataMapper,
        SharedApartmentInformationDataMapper $sharedApartmentInformationDataMapper,
        ShopDataMapper $shopDataMapper,
        TranslatorInterface $translator
    ) {
        $this->translator = $translator;
        $this->cadastralDataDataMapper = $cadastralDataDataMapper;
        $this->propertyImageDataMapper = $propertyImageDataMapper;
        $this->consistenceDataMapper = $consistenceDataMapper;
        $this->compositionDataMapper = $compositionDataMapper;
        $this->featuresDataMapper = $featuresDataMapper;
        $this->energyClassDataMapper = $energyClassDataMapper;
        $this->garageDataMapper = $garageDataMapper;
        $this->industrialDataMapper = $industrialDataMapper;
        $this->landDataMapper = $landDataMapper;
        $this->shopDataMapper = $shopDataMapper;
        $this->sharedApartmentInformationDataMapper = $sharedApartmentInformationDataMapper;
        $this->propertyPlanDataMapper = $propertyPlanDataMapper;
        $this->portionDataMapper = $portionDataMapper;
        $this->coWorkingTimetableDataMapper = $coWorkingTimetableDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return PropertyResponse::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var \App\Model\Property\Property $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(\get_class($data), $this->getDestinationClass())
            ->forMember('category', function () use ($data) {
                $typologyV2 = $data->getTypologyV2();
                $categoryId = !empty($typologyV2['parent']['parent']['id']) ? $typologyV2['parent']['parent']['id'] : null;

                return !empty($categoryId) ? $this->translateContent('db_typologyV2.id_', (string) $categoryId) : null;
            })
            ->forMember('typology', function () use ($data) {
                $typologyV2 = $data->getTypologyV2();
                $typologyId = !empty($typologyV2['parent']['id']) ? $typologyV2['parent']['id'] : null;

                return !empty($typologyId) ? $this->translateContent('db_typologyV2.id_', (string) $typologyId) : null;
            })
            ->forMember('subTypology', function () use ($data) {
                $typologyV2 = $data->getTypologyV2();
                $subTypologyId = !empty($typologyV2['id']) ? $typologyV2['id'] : null;

                return !empty($subTypologyId) ? $this->translateContent('db_typologyV2.id_', (string) $subTypologyId) : null;
            })
            ->forMember('categoryId', function () use ($data) {
                $typologyV2 = $data->getTypologyV2();

                return !empty($typologyV2['parent']['parent']['id']) ? $typologyV2['parent']['parent']['id'] : null;
            })
            ->forMember('typologyId', function () use ($data) {
                $typologyV2 = $data->getTypologyV2();

                return !empty($typologyV2['parent']['id']) ? $typologyV2['parent']['id'] : null;
            })
            ->forMember('subTypologyId', function () use ($data) {
                $typologyV2 = $data->getTypologyV2();

                return !empty($typologyV2['id']) ? $typologyV2['id'] : null;
            })
            ->forMember('city', static function () use ($data) {
                $geographyInformation = $data->getGeographyInformation();

                return null !== $geographyInformation && null !== $geographyInformation->getCity() ?
                    $geographyInformation->getCity()->getName() :
                    null;
            })
            ->forMember('province', static function () use ($data) {
                $geographyInformation = $data->getGeographyInformation();
                if (null === $geographyInformation) {
                    return null;
                }
                $countryId = null !== $geographyInformation->getCity()
                    && null !== $geographyInformation->getCity()->getProvince()
                    && null !== $geographyInformation->getCity()->getProvince()->getRegion()
                    && null !== $geographyInformation->getCity()->getProvince()->getRegion()->getCountry() ?
                    $geographyInformation->getCity()->getProvince()->getRegion()->getCountry()->getId() :
                    null;

                if (GtxConstants::NATION_ITALY !== $countryId) {
                    return null !== $geographyInformation->getCity() && null !== $geographyInformation->getCity()->getProvince() ?
                        $geographyInformation->getCity()->getProvince()->getName() :
                        null;
                }

                return null !== $geographyInformation->getCity() && null !== $geographyInformation->getCity()->getProvince() ?
                    $geographyInformation->getCity()->getProvince()->getId() :
                    null;
            })
            ->forMember('country', static function () use ($data) {
                $geographyInformation = $data->getGeographyInformation();

                return null !== $geographyInformation
                    && null !== $geographyInformation->getCity()
                    && null !== $geographyInformation->getCity()->getProvince()
                    && null !== $geographyInformation->getCity()->getProvince()->getRegion()
                    && null !== $geographyInformation->getCity()->getProvince()->getRegion()->getCountry() ?
                    $geographyInformation->getCity()->getProvince()->getRegion()->getCountry()->getId() :
                    null;
            })
            ->forMember('zone', static function () use ($data) {
                $geographyInformation = $data->getGeographyInformation();

                return null !== $geographyInformation && null !== $geographyInformation->getMacroZone() ?
                    $geographyInformation->getMacroZone()->getName() :
                    null;
            })
            ->forMember('consistences', function () use ($data) {
                return !empty($data->getConsistences())
                    ? \array_map(function ($consistence) {
                        return $this->consistenceDataMapper->map($consistence);
                    }, $data->getConsistences())
                    : null;
            })
            ->forMember('address', function () use ($data) {
                $geographyInformation = $data->getGeographyInformation();

                $address = null !== $geographyInformation && null !== $geographyInformation->getAddress() ?
                    $geographyInformation->getAddress()->getStreet() :
                    null;

                $addressNumber = null !== $geographyInformation->getAddress() ?
                    $geographyInformation->getAddress()->getNumber() :
                    null;

                return null !== $address ? \sprintf('%s %s', $address, null !== $addressNumber ? $addressNumber : null) : null;
            })
            ->forMember('surface', function () use ($data) {
                return !empty($data->getSurface()) ? \sprintf('%s %s', \number_format($data->getSurface(), 0, null, '.'), GtxConstants::SURFACE_UNIT_MEASUREMENT) : null;
            })
            ->forMember('images', function () use ($data) {
                return !empty($data->getImagesList()) ?
                    \array_map(function (Image $image) {
                        return $this->propertyImageDataMapper->map($image);
                    }, $data->getImagesList()) :
                    null;
            })
            ->forMember('condominiumCosts', function () use ($data) {
                $condominiumInfo = $data->getCondominiumInformation();

                return !empty($condominiumInfo['monthlyCosts']) ? $this->formatValute($condominiumInfo['monthlyCosts'], 0) : null;
            })
            ->forMember('propertyOwnership', function () use ($data) {
                $contractInfo = $data->getContractInformation();

                return !empty($contractInfo['propertyOwnership']['id']) ? $this->translateContent('db_property_type.id_', (string) $contractInfo['propertyOwnership']['id']) : null;
            })
            ->forMember('rentToBuy', function () use ($data) {
                $contractInfo = $data->getContractInformation();

                if (!isset($contractInfo['rentToBuy'])) {
                    return null;
                }

                return $contractInfo['rentToBuy'] ? $this->translateCms('label.yes') : $this->translateCms('label.no');
            })
            ->forMember('rentContractType', function () use ($data) {
                return !empty($data->getContractInformation()['rentContractType']) ? $this->translateContent('db_contract_rent.id_', (string) $data->getContractInformation()['rentContractType']['id']) : null;
            })
            ->forMember('freeNow', function () use ($data) {
                $contractInfo = $data->getContractInformation();

                if (!isset($contractInfo['freeNow'])) {
                    return null;
                }

                return $contractInfo['freeNow'] ? $this->translateCms('label.yes') : $this->translateCms('label.no');
            })
            ->forMember('incomeProducingProperty', function () use ($data) {
                $contractInfo = $data->getContractInformation();

                if (!isset($contractInfo['incomeProducingProperty'])) {
                    return null;
                }

                return $contractInfo['incomeProducingProperty'] ? $this->translateCms('label.yes') : $this->translateCms('label.no');
            })
            ->forMember('deposit', function () use ($data) {
                $contractInfo = $data->getContractInformation();

                if (!isset($contractInfo['deposit'])) {
                    return null;
                }

                return $contractInfo['deposit'] ? $this->formatValute($contractInfo['deposit'], 0) : null;
            })
            ->forMember('cadastralData', function () use ($data) {
                /** @var CadastralData[] $cadastralData */
                $cadastralData = $data->getCadastralData();

                return !empty($cadastralData) ?
                    $this->cadastralDataDataMapper->map($cadastralData[0]) :
                    null;
            })
            ->forMember('composition', function () use ($data) {
                if (null === $data->getComposition() && null === $data->getOffice()) {
                    return null;
                }
                if (null === $data->getComposition()) {
                    return $this->compositionDataMapper->map($data->getOffice());
                }
                if (null === $data->getOffice()) {
                    return $this->compositionDataMapper->map($data->getComposition());
                }

                return $this->compositionDataMapper->map(\array_merge($data->getComposition(), $data->getOffice()));
            })
            ->forMember('features', function () use ($data) {
                return !empty($data->getFeatures()) ? $this->featuresDataMapper->map($data->getFeatures()) : null;
            })
            ->forMember('energyClass', function () use ($data) {
                return !empty($data->getEnergyClass()) ? $this->energyClassDataMapper->map($data->getEnergyClass()) : null;
            })
            ->forMember('garage', function () use ($data) {
                return !empty($data->getGarage()) ? $this->garageDataMapper->map($data->getGarage()) : null;
            })
            ->forMember('industrial', function () use ($data) {
                return !empty($data->getIndustrial()) ? $this->industrialDataMapper->map($data->getIndustrial()) : null;
            })
            ->forMember('land', function () use ($data) {
                return !empty($data->getLand()) ? $this->landDataMapper->map($data->getLand()) : null;
            })
            ->forMember('shop', function () use ($data) {
                return !empty($data->getShop()) ? $this->shopDataMapper->map($data->getShop()) : null;
            })
            ->forMember('sharedApartmentInformation', function () use ($data) {
                return !empty($data->getSharedApartmentInformation()) ? $this->sharedApartmentInformationDataMapper->map($data->getSharedApartmentInformation()) : null;
            })
            ->forMember('plans', function () use ($data) {
                return !empty($data->getPlansList()) ? \array_map(function (Plan $plan) {
                    return $this->propertyPlanDataMapper->map($plan);
                }, $data->getPlansList()) :
                    null;
            })
            ->forMember('availability', function () use ($data) {
                if (empty($data->getContractInformation()['availability'])) {
                    return null;
                }

                return $data->getContractInformation()['availability'] ?? null;
            })
            ->forMember('availableForStudents', function () use ($data) {
                if (empty($data->getContractInformation()['availableForStudents'])) {
                    return null;
                }

                return $data->getContractInformation()['availableForStudents'] ? $this->translateCms('label.yes') : $this->translateCms('label.no');
            })
            ->forMember('price', function () use ($data) {
                if (empty($data->getBatch()['price']['price'])) {
                    return null;
                }

                return $this->formatValute($data->getBatch()['price']['price']);
            })
            ->forMember('portions', function () use ($data) {
                return !empty($data->getPortions())
                    ? \array_values(\array_map(
                        function ($portion) { return $this->portionDataMapper->map($portion); },
                        $data->getPortions()
                    ))
                    : null;
            })
            ->forMember('coWorkingTimetable', function () use ($data) {
                return !empty($data->getCoWorkingTimetable())
                    ? \array_values(\array_map(
                        function ($item) { return $this->coWorkingTimetableDataMapper->map($item); },
                        $data->getCoWorkingTimetable()
                    ))
                    : null;
            })
        ;

        return $config;
    }
}
