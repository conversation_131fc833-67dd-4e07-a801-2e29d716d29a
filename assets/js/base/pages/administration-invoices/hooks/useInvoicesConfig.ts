import { useMemo } from 'react';
import { CardListConfigs } from 'gtx-react/components/CardList/types';
import { TableConfigs } from 'gtx-react/components/GxTable/types';

type UseInvoicesConfigInput = {
    invoices: any[];
    rawConfig: TableConfigs | CardListConfigs;
};

const useInvoicesConfig = ({
    invoices,
    rawConfig,
}: UseInvoicesConfigInput): TableConfigs | CardListConfigs => {
    const config = useMemo(
        () => ({
            ...rawConfig,
            itemActionsHelper: (...[id, actionsData, actionType]) => {
                const invoiceData = invoices.find(invoice => invoice.id === id);

                if (!invoiceData) {
                    return null;
                }

                if (actionType === 'quick') {
                    const validForEntries = Object.entries(
                        actionsData[actionType][0].validFor
                    );

                    // this condition is valid only for 'attachment' field
                    const shouldRenderQuickAction = validForEntries.every(
                        ([key, value]) =>
                            key === 'attachment' &&
                            invoiceData[key] !== null &&
                            invoiceData[key] !== undefined
                    );

                    if (shouldRenderQuickAction) {
                        return actionsData[actionType];
                    }
                }

                return null;
            },
        }),
        [invoices]
    );

    return config;
};

export default useInvoicesConfig;
