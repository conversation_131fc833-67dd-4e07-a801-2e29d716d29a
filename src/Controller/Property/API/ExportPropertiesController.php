<?php

declare(strict_types=1);

namespace App\Controller\Property\API;

use App\Formatter\Property\AdExportFormatter;
use App\Handler\Agency\AgencyHandler;
use App\Handler\Property\ExportHandler;
use App\Model\Property\AdExport;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Route("/properties")
 */
class ExportPropertiesController extends Controller
{
    private const CHUNK = 1000;

    private ExportHandler $handler;
    private AdExportFormatter $formatter;
    private AgencyHandler $agencyHandler;

    public function __construct(
        AdExportFormatter $formatter,
        AgencyHandler $agencyHandler,
        ExportHandler $handler
    ) {
        $this->agencyHandler = $agencyHandler;
        $this->handler = $handler;
        $this->formatter = $formatter;
    }

    /**
     * @Route("/export", name="app_api_properties_export", methods="GET")
     */
    public function index(): Response
    {
        $tempPath = \tempnam(\sys_get_temp_dir(), 'export_' . $this->agencyHandler->getAgencyId());
        $file = \fopen($tempPath, 'w');
        $isPro = $this->agencyHandler->isPro();
        \fputcsv($file, \array_values($this->handler->exportAdsHeader($isPro)), ';');

        $offset = 0;
        $limit = self::CHUNK;
        while (true) {
            $data = \array_map(
                function (AdExport $ad) use ($isPro) { return $this->formatter->format($ad, $isPro); },
                $this->handler->exportAds($offset, $limit)
            );
            foreach ($data as $row) {
                \fputcsv($file, \array_values($row), ';');
            }
            if (\count($data) < self::CHUNK) {
                break;
            }
            $offset += self::CHUNK;
            $limit += self::CHUNK;
        }

        \fclose($file);

        return (new BinaryFileResponse($tempPath))
            ->setContentDisposition(
                ResponseHeaderBag::DISPOSITION_ATTACHMENT,
                \sprintf('annunci-%s.csv', \date('Y-m-d')) // name hard-coded for retro compatibility
            )
            ->deleteFileAfterSend(true);
    }
}
