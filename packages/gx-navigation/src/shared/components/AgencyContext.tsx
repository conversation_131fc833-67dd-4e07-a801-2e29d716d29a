import { ReactNode, createContext, useContext, useEffect, useState, type JSX } from 'react';
import { on } from '../../events/utils';
import { UPDATE_AGENCY, UPDATE_AGENCY_AVATAR } from '../../events/inputTypes';
import { BaseNavigationContextType } from '../../config/types';

type AgencyContextProps = Pick<
  BaseNavigationContextType,
  | 'agencyImage'
  | 'agencyName'
  | 'agentName'
  | 'agentEmail'
  | 'agentAvatar'
  | 'agencyId'
  | 'adPortal'
  | 'isMasterAccount'
  | 'hasTwoFactorAuth'
  | 'profileUrl'
  | 'serviceRemoteVisit'
  | 'isAdmin'
>;

export const AgencyContext = createContext<AgencyContextProps | undefined>(undefined);

export const AgencyProvider = (props: { value: AgencyContextProps; children: ReactNode }) => {
  const [agency, setAgency] = useState(props.value);

  useEffect(() => {
    const unsubscribe = on(UPDATE_AGENCY_AVATAR, (data: string) => {
      setAgency((prev) => ({ ...prev, agencyImage: data }));
    });

    return () => {
      unsubscribe();
    };
  }, []);

  useEffect(() => {
    const unsubscribe = on(UPDATE_AGENCY, (data: AgencyContextProps) => {
      setAgency((prev) => ({ ...prev, ...data }));
    });

    return () => {
      unsubscribe();
    };
  }, []);

  return <AgencyContext.Provider value={agency}>{props.children}</AgencyContext.Provider>;
};

export const useAgencyContext = () => {
  const context = useContext(AgencyContext);

  if (!context) {
    throw new Error('useAgencyContext must be used within a AgencyProvider');
  }

  return context;
};

export const AgencyConsumer = (props: {
  children: (agencyContext: AgencyContextProps) => JSX.Element | null;
}) => {
  const context = useAgencyContext();

  return props.children(context);
};
