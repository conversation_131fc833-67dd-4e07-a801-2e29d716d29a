{% extends 'base.html.twig' %}

{% block headerscripts %}
    {{ parent() }}
{% endblock %}


{% block content %}
    <div id="content" class="content-acquisition"></div>
{% endblock %}

{% block reactApp %}
<div id="root"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
     <script id="site-section-title" type="application/json">{{ trans("label.private_properties", {}, gtxConstants.DOMAIN_CMS) }}</script>
    {{ webpack_asset('base/acquisition-privates') }}
{% endblock %}
