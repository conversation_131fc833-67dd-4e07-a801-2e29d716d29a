@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

/* LOGIN */
$side-login-width: 46rem;

.register-text {
  p {
    @include typography(body);
  }
}

.form-account-message {
  padding: 0 space(md);
  width: 100%;

  h1 {
    margin-bottom: space(sm);
    @include typography(title-1);
    color: color(content-high);
  }
}

.login-wrapper {
  display: flex;
  min-height: 100vh;
  align-items: stretch;
}

.login-header {
  width: 100%;
  padding: space(md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  @include z-index(base, 9);

  &__logo {
    position: relative;
    display: block;
    height: 2.8rem;
    background-size: contain;
    background-repeat: no-repeat;
    @include z-index(base, 2);

    img {
      width: 18.7rem;
      height: 2.8rem;
      object-fit: contain;
      object-position: left top;
    }

    &-reversed {
      display: none;
    }
  }

  &__action {
    display: flex;
    align-items: center;

    span {
      color: color(content-medium);
      display: none;
    }
  }
}

/* Blocco descrizione laterale */
.login-desc {
  background-color: color(background-brand);
  flex: 0 0 $side-login-width;
  min-height: 64rem;
  display: none;
  position: relative;

  &__content {
    width: 38rem;
    transform: translate(-50%, -50%);
    color: color(content-accent);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;

    a:not(.gx-button) {
      color: color(content-accent);
      display: inline-block;

      &:hover {
        color: color(content-accent);
      }
    }

    img {
      max-width: 100%;
    }

    h2 {
      margin-bottom: space(md);
      @include typography(title-2);
      text-transform: uppercase;
      text-transform: uppercase;
    }

    h1 {
      margin-bottom: space(lg);
      margin-top: space(md);
    }

    p {
      margin-bottom: space(2xl);
      padding: 0 space(md);
    }

    .gx-button {
      border: 0;
    }
  }

  &__footer {
    display: none;
    margin-bottom: space(xl);
    width: 100%;

    ul {
      width: 100%;
      margin: 0;
      padding: 0;
      list-style: none;
      display: flex;
      justify-content: center;

      li {
        &:last-child {
          border-right: none;
        }

        a {
          margin: 0 space(md);
        }
      }
    }
  }
}

.login-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 45rem;

  &--register {
    margin: space(4xl) 0;

    h1 {
      margin-bottom: space(sm);
      color: color(content-medium);
    }
  }

  &-wrap {
    margin-left: 0;
    display: flex;
    flex-direction: column;
    width: 100%;

    .login-desc__footer {
      display: block;
    }
  }

  .form-account {
    padding: 0 space(md);
    max-width: 45rem;
    width: 100%;

    h1 {
      margin-bottom: space(sm);
      color: color(content-high);
    }

    p {
      color: color(content-medium);
      margin-bottom: space(xl);
    }

    &--register {
      .gx-radio-wrapper {
        display: block;
        margin-bottom: space(md);
      }

      .gx-radio {
        display: flex;
        margin-bottom: space(md);

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    &__footer {
      margin-top: space(md);
    }

    .gx-input-wrapper {
      margin-bottom: space(md);
    }

    .set-user-phone-number-row {
      .gx-input-wrapper {
        margin-bottom: 0;
      }

      [data-role='error-message'] {
        margin-bottom: 0;
      }
    }
  }
}

.back-to-link {
  margin-top: space(md);
  display: inline-block;
}

@include media('screen', '>=#{emConverter(600)}') {
  .login-header {
    &__action {
      span {
        display: flex;
      }

      .gx-button {
        margin-left: space(md);
      }
    }
  }

  .login-content {
    .form-account-message {
      padding: 0;
      width: 40rem;
    }

    &--register {
      .form-account-message {
        padding: 0;
        width: 42rem;
      }
    }

    .form-account {
      &--set-password {
        // To prevent strength meter increase the width of the form
        width: 40rem;
      }
    }
  }
}

@include media('screen', '>=#{breakpoint(sm)}') {
  .login-content {
    .form-account {
      &--register {
        padding: 0;

        .gx-radio-wrapper {
          display: flex;
        }

        .gx-radio {
          display: inline-flex;
          margin-bottom: 0;
        }
      }
    }
  }
}

@include media('screen', '>=#{breakpoint(md)}') {
  .login-header {
    padding: space(xl);

    &__logo {
      height: 3.6rem;

      img {
        width: 24rem;
        height: 3.6rem;
      }

      &-default {
        display: none;
      }

      &-reversed {
        display: block;
      }
    }
  }

  .login-desc {
    display: flex;

    .login-desc__footer {
      display: block;
      position: absolute;
      bottom: space(xl);
      left: 0;
      width: 100%;
      margin: 0;

      ul li {
        border-color: color(content-accent);
      }

      a {
        color: color(content-accent);
      }
    }
  }

  .login-content {
    .form-account {
      min-width: 40rem;
      padding: 0;
    }

    &--register {
      margin: 9.6rem 0;
    }

    &-wrap {
      .login-desc__footer {
        display: none;
      }
    }
  }
}

.loader-login-button {
  width: 2.4rem;
}
