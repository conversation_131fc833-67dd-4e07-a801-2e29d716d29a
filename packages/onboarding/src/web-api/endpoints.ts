const DEV_BASE_PATH = ''
const PROD_BASE_PATH = ''

export let endpoints = {
    TOURS: '/api/onboarding-tours/',
    TOUR_READ: (tourId: string) => `/api/onboarding-tours/${tourId}/read`
}

switch (process.env.NODE_ENV) {
    case 'development': {
        endpoints = {
            TOURS: DEV_BASE_PATH + '/api/onboarding-tours/',
            TOUR_READ: (tourId: string) => DEV_BASE_PATH + `/api/onboarding-tours/${tourId}/read`
        }
        break;
    }
    case 'production': {
        endpoints = {
            TOURS: PROD_BASE_PATH + '/api/onboarding-tours/',
            TOUR_READ: (tourId: string) => PROD_BASE_PATH + `/api/onboarding-tours/${tourId}/read`
        }
        break;
    }
}


export default endpoints
