import { http } from '@pepita/http';
import { lookupEndpoints } from '../../base/pages/customer/web-api/endpoints';

export const lookupApi = (endpoint, value) => {
    if (value) {
        return http.get(endpoint, { searchParams: { value } }).json();
    }
};

export const getLookupTypesApi = () =>
    http.get(lookupEndpoints.GET_TYPES_LOOKUP).json();

export const getLookupDocumentTypesApi = () =>
    http.get(lookupEndpoints.GET_DOCUMENT_TYPES_LOOKUP).json();

export const getLookupSourcesApi = () =>
    http.get(lookupEndpoints.GET_SOURCES_LOOKUP).json();

export const lookupApiWithQs = (endpoint, params) => {
    if (params) {
        return http.get(endpoint, { searchParams: params }).json();
    }
};

export const getLookupNationsApi = () =>
    http.get(lookupEndpoints.GET_CUSTOMER_GEO_NATION_LOOKUP).json();
