@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.messaging-landing {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: 'Open Sans', sans-serif;
  text-align: center;

  &__header {
    padding: space(3xl) space(md);

    .logo {
      max-width: 24rem;
    }

    h2 {
      margin-top: space(2xl);
      color: #333333;
      font-family: 'Raleway', sans-serif;
      font-size: 2.8rem;
      line-height: 3.2rem;
    }

    @include media('screen', '>=#{breakpoint(sm)}') {
      .logo {
        max-width: 32.5rem;
      }
    }
  }

  &__body {
    max-width: 60rem;
    margin: 0 auto;
    padding: 0 space(md);

    img {
      max-width: 100%;
      height: auto;
    }

    p {
      margin-top: 2.4rem;
      color: #5e5e5e;
      line-height: 2rem;
    }

    .gx-button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 20rem;
      height: ($gx-unit-size * 7);
      margin-top: space(lg);
      margin-bottom: space(2xl);
      padding: space(md) space(xl);
      border-radius: radius(sm);
      background: #e50013;
      color: #fff;
      text-decoration: none;
      text-transform: uppercase;

      @include media('screen', '>=#{breakpoint(md)}') {
        margin-bottom: 12rem;
      }
    }
  }
}

.landing-page__contact__title.landing-page__contact__title--messaging {
  font-family: 'Raleway', sans-serif;
  font-size: 2rem;
  font-weight: 500;
}

.landing-page__contact.landing-page__contact--messaging {
  margin-top: auto;
}
