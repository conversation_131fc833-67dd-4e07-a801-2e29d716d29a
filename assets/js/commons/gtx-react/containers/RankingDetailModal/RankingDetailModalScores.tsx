import React from 'react';
import { Alert } from '@gx-design/alert';
import { Badge } from '@gx-design/badge';
import { trans } from '@pepita-i18n/babelfish';
import { RankingDetailsDataApiResponse } from 'lib/REST/types/rankingDetailModal';

type RankingDetailModalScoresType = {
    scores: RankingDetailsDataApiResponse['detail'];
};

export const RankingDetailModalScores: React.FC<
    RankingDetailModalScoresType
> = ({ scores }) => {
    const ScoreType = {
        Insufficient: 0,
        Improvable: 1,
        Sufficient: 2,
    } as const;

    const getBadgeStyle = (
        scoreSection: (typeof ScoreType)[keyof typeof ScoreType]
    ) => {
        switch (scoreSection) {
            case 0:
                return 'error';
            case 1:
                return 'warning';
            case 2:
                return 'success';
            default:
                return null;
        }
    };

    const generateSectionTitle = (
        scoreSection: (typeof ScoreType)[keyof typeof ScoreType]
    ) => {
        switch (scoreSection) {
            case 0:
                return trans('label.info_insufficient');
            case 1:
                return trans('label.info_improvable');
            case 2:
                return trans('label.info_sufficient');
            default:
                return null;
        }
    };

    return (
        <>
            {scores.length === 3 ? (
                <>
                    {Object.values(ScoreType).map((type, index) => {
                        const detailScores = scores[type]?.details;
                        return (
                            <div
                                key={index}
                                className="property-ranking-details-modal__score"
                            >
                                <div className="property-ranking-details-modal__scoreHeader">
                                    <h4>{generateSectionTitle(type)}</h4>
                                    <div>
                                        <Badge
                                            style={
                                                getBadgeStyle(type) || undefined
                                            }
                                            text={`${scores[type]?.score}/${scores[type]?.maxScore}`}
                                        />
                                    </div>
                                </div>
                                {detailScores?.map(
                                    (score: any, idx: number) => {
                                        return (
                                            <div
                                                key={`${score}-${idx}`}
                                                className="gx-row property-ranking-details-modal__scores"
                                            >
                                                <div className="gx-col-md-4">
                                                    <span>{score?.Type}</span>
                                                </div>
                                                <div className="gx-col-md-2">
                                                    <Badge
                                                        style={
                                                            getBadgeStyle(
                                                                type
                                                            ) || undefined
                                                        }
                                                        text={`${score?.Score}/${score?.MaxScore}`}
                                                    />
                                                </div>
                                                <div className="gx-col-md-6">
                                                    {score?.Description?.map(
                                                        (perk: string) => {
                                                            return (
                                                                <span
                                                                    key={perk}
                                                                >
                                                                    {perk}
                                                                    <br />
                                                                </span>
                                                            );
                                                        }
                                                    )}
                                                </div>
                                            </div>
                                        );
                                    }
                                )}
                            </div>
                        );
                    })}
                </>
            ) : (
                <Alert style="warning">{trans('ranking.no_details')}</Alert>
            )}
        </>
    );
};
