# TanStack Table Storybook

This directory contains the Storybook story for the TanStack Table component used in the CRM application.

## Files

- `TanstackTable.stories.tsx` - Complete Storybook story with multiple variations

## Features Demonstrated

The story showcases a comprehensive TanStack Table implementation with:

### Core Features
- **Row Selection**: Checkbox-based multi-row selection with bulk actions
- **Sorting**: Multi-column sorting with visual indicators
- **Pagination**: Configurable page sizes and navigation
- **Column Management**: Show/hide, resize, pin, and reorder columns

### Advanced Features  
- **Drag & Drop**: Column reordering via drag and drop
- **Column Pinning**: Pin columns to left or right
- **Column Resizing**: Drag handles to resize column widths
- **Responsive Design**: Adapts to different screen sizes

### Data Types
- **Mixed Content**: Text, images, badges, and action buttons
- **Status Indicators**: Colored badges for different states
- **Action Buttons**: Icon-only buttons with tooltips
- **Property Display**: Complex cells with images and metadata

## Story Variations

1. **DefaultTable**: Full-featured table with all capabilities enabled
2. **WithoutRowSelection**: Table without row selection functionality
3. **WithoutPagination**: Table showing all rows without pagination
4. **SimpleTable**: Basic table with minimal features
5. **LargeDataset**: Table with 100 rows to test pagination and performance
6. **EmptyState**: Table with no data to show empty state handling

## Data Structure

The stories use a `PropertyData` type representing real estate properties with:
- Basic info (id, code, title, price, location)
- Media (image)
- Status (active, pending, sold)
- Property details (bedrooms, bathrooms, square meters, type)
- Metadata (creation date)

## Usage

To view the stories:
1. Start Storybook: `npm run storybook`
2. Navigate to "CRM/TanstackTable" in the sidebar
3. Explore the different variations and interact with the table features

## Integration

This story demonstrates the real patterns used in the application's property listings, customer management, and other data-heavy interfaces. It serves as both documentation and a testing ground for table functionality.
