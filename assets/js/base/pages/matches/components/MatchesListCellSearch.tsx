import { MatchItem } from '../types/matches';
import { FC } from 'react';
import { trans } from '@pepita-i18n/babelfish';
import { List, ListItem } from '@gx-design/list';
import { currencyFormatter } from '../../../../commons/lib/currencyFormatter';
import { cityZoneToMap, polygonsToMap } from 'lib/staticMapUtils';
import { Popover } from '@gx-design/popover';
import { MatchesListCellSearchTypologies } from './MatchesListCellSearchTypologies';
import { MatchesListCellSearchFilters } from './MatchesListCellSearchFilters';

type MatchesListCellSearchProps = {
    data: MatchItem;
};

export const MatchesListCellSearch: FC<MatchesListCellSearchProps> = ({
    data,
}) => {
    const geo = data.search.geographyInformation;
    const typologyItem =
        data.search.typologies && data.search.typologies.length > 0
            ? data.search.typologies[0]
            : null;

    const categoryFromRoot = data.search.category
        ? trans(`db_category.id_${data.search.category.id}`)
        : null;

    const category = typologyItem?.category
        ? trans(`db_category.id_${typologyItem?.category.id}`)
        : categoryFromRoot;

    const contract = data.search?.contract
        ? trans(`db_contract.id_${data.search?.contract?.id}`)
        : null;

    const polygonPoints =
        geo?.areaPolygon?.points.length && geo.areaPolygon.points.length > 0
            ? geo.areaPolygon.points
            : null;

    const macrozones =
        geo?.macroZones && geo.macroZones.length > 0 ? geo.macroZones : null;

    const polygonPointMapElement = polygonPoints ? (
        <img className="media-object" src={polygonsToMap(polygonPoints)}></img>
    ) : null;

    const cityAndZonesMapElement =
        geo?.city?.id && polygonPoints === null ? (
            <img
                className="media-object"
                src={cityZoneToMap({
                    cityId: geo?.city?.id.toString(),
                    zoneIds:
                        macrozones?.map((item) => item.id.toString()) || [],
                })}
            ></img>
        ) : null;

    const mapElement =
        polygonPointMapElement || cityAndZonesMapElement ? (
            <>
                {polygonPointMapElement}
                {cityAndZonesMapElement}
            </>
        ) : (
            <img
                className="media-object"
                src="/bundles/base/img/matches/savedsearch_placeholder_squared.png"
            ></img>
        );

    const cityWithProv = geo?.city
        ? `${geo?.city?.name || ''} ${
              geo?.city?.province?.name ? `(${geo?.city.province.id})` : ''
          }`
        : geo?.province
        ? `${geo?.province.name} - ${trans('label.province')}`
        : '';
    const getPopOverTitle = () => {
        const cleanZoneName = (zone: string) =>
            zone.replace(/^\s*\d+\s*-\s*/, '');

        if (polygonPoints) {
            return trans('map.drawn_area');
        }
        if (macrozones) {
            if (macrozones.length > 1) {
                return (
                    <ul>
                        {macrozones.map((itm) => (
                            <li key={`macrozones_${itm.id}`}>
                                {cleanZoneName(itm.name)}
                            </li>
                        ))}
                    </ul>
                );
            } else {
                return macrozones.map((itm) => (
                    <span key={`macrozones_${itm.id}`}>
                        {cleanZoneName(itm.name)}
                    </span>
                ));
            }
        }
        return cityWithProv;
    };

    const priceMin = data.search?.price?.min
        ? currencyFormatter(data.search.price?.min)
        : null;

    const priceMax = data.search?.price?.max
        ? (priceMin ? ' - ' : '') + currencyFormatter(data.search.price?.max)
        : null;

    const getPricePrefix = () => {
        let prefix = '';

        if (priceMin && !priceMax) {
            prefix = trans('label.from');
        }

        if (!priceMin && priceMax) {
            prefix = trans('label.until');
        }

        return prefix;
    };
    return (
        <div className="matches-search-field">
            <Popover
                large={false}
                onEdge={true}
                position="left"
                content={
                    <div className="matches-search-field-popover">
                        <div className="matches-search-field-popover__map">
                            {mapElement}
                        </div>
                        <div className="matches-search-field-popover__text">
                            {polygonPoints
                                ? trans('map.drawn_area')
                                : getPopOverTitle()}
                        </div>
                    </div>
                }
                //@ts-expect-error
                title={null}
                className="gx-popover-matches"
            >
                <div className="matches-search-field__map">{mapElement}</div>
            </Popover>
            <div className="matches-search-field__details">
                <List>
                    <strong>
                        {polygonPoints ? (
                            <ListItem content={trans('map.drawn_area')} />
                        ) : (
                            <ListItem
                                content={`${cityWithProv}${
                                    macrozones ? ' • ' : ''
                                }${macrozones ? macrozones.length : ''} ${
                                    macrozones
                                        ? macrozones.length > 1
                                            ? trans(
                                                  'label.zone_plural'
                                              ).toLowerCase()
                                            : trans('label.zone').toLowerCase()
                                        : ''
                                }`}
                            />
                        )}
                    </strong>
                    {!!contract && (
                        <ListItem content={contract} icon="certificate" />
                    )}
                    {!!category && (
                        <div className="matches-search-field__category">
                            <ListItem
                                content={
                                    <>
                                        {category} <br />
                                        <MatchesListCellSearchTypologies
                                            search={data.search}
                                        />
                                    </>
                                }
                                icon="palaces"
                            />
                        </div>
                    )}

                    <ListItem
                        content={`${getPricePrefix()}${priceMin || ''} ${
                            priceMax || ''
                        }`}
                        icon="money"
                    />
                    <MatchesListCellSearchFilters search={data.search} />
                </List>
            </div>
        </div>
    );
};
