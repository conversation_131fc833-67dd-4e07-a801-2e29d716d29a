import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { PropsWithChildren } from 'react';
import { ImageProps } from '../../types';
import { getLogo } from '../../shared/utils/getLogo';
import { HOLIDAY_DAYS_CHECK } from '../../shared/constants';
import { Tooltip } from '@gx-design/tooltip';
import { trans } from '@pepita-i18n/babelfish';

type LogoProps = {
  url: string;
} & ImageProps;

export const Logo = (props: LogoProps) => {
  const logoUrl = getLogo(props.imageUrl, HOLIDAY_DAYS_CHECK);
  return (
    <a href={props.url} className='gx-navigation-header__logo gx-is-hidden-md-up'>
      <img src={logoUrl} alt={props.alt} />
    </a>
  );
};

export const AgencyLogo = (props: ImageProps) => {
  return (
    <div className='gx-navigation-header__agencyLogo gx-is-hidden-sm-down'>
      <img src={props.imageUrl} alt={props.alt} />
    </div>
  );
};

type AgencyNameProps = {
  name: string;
};

export const AgencyName = (props: AgencyNameProps) => {
  return <div className='gx-navigation-header__agencyName gx-is-hidden-sm-down'>{props.name}</div>;
};

export const NotificationButton = () => {
  return (
    <div className='gx-navigation-header__bell gx-is-hidden-sm-down' id='header-link-news'>
      <Tooltip text={trans('label.brand_new')} position='bottom'>
        <Button variant='ghost' iconOnly>
          <Icon name='bell' />
        </Button>
      </Tooltip>
    </div>
  );
};

export const HeaderContent = (props: PropsWithChildren<unknown>) => {
  return <div className='gx-navigation-header__wrapMobile'>{props.children}</div>;
};
