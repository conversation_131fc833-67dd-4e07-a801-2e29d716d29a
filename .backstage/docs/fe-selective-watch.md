# MLS Frontend selective build script

To make (re)build time flow faster and minimize memory usage during development you can use the selective build or watch script running the command:

```bash
yarn start-selective
yarn build-selective
```

You will be prompted to multi-select the entry points you want to be watched and built.

![selective watch prompt](./imgs/selective-watch.png)

NOTE: There are a set of always included entry points, this to allow basic navigation and login (they are defined in the `basicEntries` constant [here](../../webpack.common.mjs) ):

- base/main-bootstrap
- base/login
- base/main-pro
- dashboard/main

## Selective watch/build without prompt

You can pass directly in CLI the entry points you want to be watched using this syntax:


```bash
 yarn start-selective --env ENTRIES=<comma separated entries list>
 yarn build-selective --env ENTRIES=<comma separated entries list>
```

Example:
```bash
 yarn start-selective --env ENTRIES=base/searches,base/youdomus
 yarn build-selective --env ENTRIES=base/searches
```

You can obtain a list of all selectable entries with the following command.

Example:
```bash
 yarn start-selective --env LIST
```
