<?php

    use App\Kernel;
    use Symfony\Component\Debug\Debug;
    use Symfony\Component\Dotenv\Dotenv;
    use Symfony\Component\HttpFoundation\Request;

    require __DIR__ . '/../vendor/autoload.php';
    if (!class_exists(Dotenv::class)) {
        throw new \RuntimeException('APP_ENV environment variable is not defined. You need to define environment variables for configuration or add "symfony/dotenv" as a Composer dependency to load variables from a .env file.');
    }

    $productLang            = $_SERVER['COUNTRY_TAG'] ?? 'it';
    $_SERVER['COUNTRY_TAG'] = $productLang;

    $envFileName = '.env.' . $productLang;

    (new Dotenv())->load(dirname(__DIR__) . '/' . $envFileName);

    $env   = $_SERVER['APP_ENV'] ?? 'dev';
    $debug = (bool) ($_SERVER['APP_DEBUG'] ?? ('prod' !== $env));

    if ($debug) {
        umask(0000);

        Debug::enable();
    }

    if (isset($_SERVER['REMOTE_ADDR']) ?? false) {
        Request::setTrustedProxies([$_SERVER['REMOTE_ADDR']]);
    }

    if ($trustedHosts = $_SERVER['TRUSTED_HOSTS'] ?? false) {
        Request::setTrustedHosts(explode(',', $trustedHosts));
    }

    $kernel = new Kernel($productLang, $env, $debug);

    $request  = Request::createFromGlobals();
    $response = $kernel->handle($request);
    $response->send();
    $kernel->terminate($request, $response);
