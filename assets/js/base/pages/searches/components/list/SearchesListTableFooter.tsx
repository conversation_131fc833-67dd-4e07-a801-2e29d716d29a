import * as React from 'react';
import { useDispatch } from 'react-redux';
import { useDispatchFetchListWithLock } from '../../hooks/useDispatchFetchListWithLock';
import { PaginationBar } from '@gx-design/pagination-bar';
import { selectFilters } from 'gtx-react/rtk/slices/lists/filtersSlice';
import { setSelections } from 'gtx-react/rtk/slices/lists/listSelectionsSlice';
import { selectPagination } from 'gtx-react/rtk/slices/lists/paginationSlice';
import { useSearchesListSelector } from '../../redux/list/hooks';
import { trans } from '@pepita-i18n/babelfish';

export const SearchesListTableFooter = () => {
    const pagination = useSearchesListSelector(selectPagination);
    const filters = useSearchesListSelector(selectFilters);
    const dispatch = useDispatch();
    const totPages =
        pagination.total && pagination.results
            ? Math.ceil(pagination.total / pagination.results)
            : 1;
    const { dispatchFetchListWithLock } = useDispatchFetchListWithLock();

    return (
        <PaginationBar
            currentResults={pagination.results}
            totalResults={pagination.total}
            separatorString={trans('label.out_of_2')}
            resultString={trans('label.results')}
        >
            <PaginationBar.DropDown
                resultString={trans('label.results')}
                onResultsChange={(results) => {
                    dispatchFetchListWithLock({
                        pagination: { ...pagination, results, page: 1 },
                        filters,
                    });
                    dispatch(setSelections([]));
                }}
                options={[10, 30, 50, 100]}
                value={pagination.results}
            />

            <PaginationBar.Pager
                activePage={pagination.page}
                maxPagesToShow={3}
                onPageClick={(pageNum) => {
                    dispatchFetchListWithLock({
                        pagination: { ...pagination, page: pageNum },
                        filters,
                    });
                }}
                totalPages={totPages}
            />
        </PaginationBar>
    );
};
