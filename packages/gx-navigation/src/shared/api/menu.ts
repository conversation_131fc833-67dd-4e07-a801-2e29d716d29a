import { MenuConfig } from '../../types';
import { captureException } from '@sentry/browser';

type MenuConfigResponse = {
  primary: MenuConfig;
  secondary: MenuConfig;
};

type APIResponse<T> = { status: 'success'; data: T } | { status: 'error'; data: string };

/**
 * Fetch menu config from API endpoint `/api/menu`
 */
export async function fetchMenuConfig(
  config: {
    cacheToken: string;
    lang: string;
  },
  retries: number,
  delay: number,
): Promise<MenuConfigResponse> {
  const url = new URL('/api/menu', window.location.origin);

  url.searchParams.set('cache-token', config.cacheToken);
  url.searchParams.set('lang', config.lang);

  const response = await window.fetch(url, {
    cache: 'force-cache',
  });

  try {
    if (response.ok) {
      const json = (await response.json()) as APIResponse<MenuConfigResponse>;
      if (json.status === 'success') {
        return json.data;
      }
    }

    throw new Error(`HTTP error, status = ${response.status}`);
  } catch (error) {
    if (retries > 0) {
      await new Promise((resolve) => setTimeout(() => resolve({}), delay));
      return await fetchMenuConfig(config, retries - 1, delay);
    } else {
      if (window.globalVars.SENTRY_ENABLED) {
        captureException(
          new Error(
            `Cannot fetch menu config after 3 retries - HTTP error, status = ${response.status}`,
          ),
        );
      }
      throw new Error('Cannot fetch menu config after 3 retries');
    }
  }
}
