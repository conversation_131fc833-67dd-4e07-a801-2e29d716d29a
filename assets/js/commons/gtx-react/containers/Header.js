import React, { Fragment } from 'react';
import classNames from 'classnames';
import gtxConstants from '@getrix/common/js/gtx-constants';
import { trans } from '@pepita-i18n/babelfish';
import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';

const headerConfig =
    document.getElementById('header-config') && JSON.parse(document.getElementById('header-config').innerHTML);
const siteSection = document.getElementById('site-section')?.innerHTML;
const siteSubsection = document.getElementById('site-subsection')?.innerHTML;
const agency = document.getElementById('agency-info') && JSON.parse(document.getElementById('agency-info')?.innerHTML);
const loggedAgentImageThumb = document.getElementById('agent-image-thumb')?.innerHTML;
const loggedUser = window.gtxLoggedUser;
const agencyLogo =
    agency && agency.fkLogoBig
        ? `${gtxConstants('VHOST_URL_MEDIA_IMAGE')}${gtxConstants('AD_IMAGENORESIZE_ENDPOINT')}/${agency.fkLogoBig}.jpg`
        : null;

const SectionLabel = () => {
    return (
        <Fragment>
            {headerConfig[siteSection] && headerConfig[siteSection]['label'] ? (
                <div className="gx-header__section gx-body-small">
                    {trans(headerConfig[siteSection]['label'])
                        ? trans(headerConfig[siteSection]['label'])
                        : headerConfig[siteSection]['label']}
                </div>
            ) : null}
        </Fragment>
    );
};

const SubsectionLabel = () => (
    <Fragment>
        {headerConfig[siteSection] &&
        headerConfig[siteSection]['subsections'] &&
        headerConfig[siteSection]['subsections'][siteSubsection] ? (
            <div className="gx-header__subSection gx-title-2">
                {trans(headerConfig[siteSection]['subsections'][siteSubsection]['label'])
                    ? trans(headerConfig[siteSection]['subsections'][siteSubsection]['label'])
                    : headerConfig[siteSection]['subsections'][siteSubsection]['label']}
            </div>
        ) : null}
    </Fragment>
);

const Labels = ({ isSectionLabelVisible, customTitle }) => (
    <Fragment>
        {headerConfig[siteSection]['showSectionLabels'] ? (
            <div className="gx-header__sectionBox gx-is-hidden-md-up">
                {customTitle ? customTitle : isSectionLabelVisible ? <SectionLabel /> : null}

                <SubsectionLabel />
            </div>
        ) : null}
    </Fragment>
);

const HamburgerMenu = () => (
    <button className="gx-header__hamburger gx-is-hidden-xlg-up" data-action="toggle-menu">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
    </button>
);

const AgencyLogo = () => (
    <Fragment>
        <div
            id="header-agency-logo"
            className={`gx-header__AgencyLogo gx-is-hidden-sm-down ${agencyLogo ? '' : 'hidden'}`}
        >
            <img src={agencyLogo} />
        </div>
        <div
            id="header-agency-name"
            className={`gx-header__AgencyName gx-is-hidden-sm-down ${!agencyLogo ? '' : 'hidden'}`}
        >
            {agency.nome}
        </div>
    </Fragment>
);

const AgentDropdown = () => (
    <Fragment>
        <Button data-toggle="dropdown" className="gx-header__profile dropdown-toggle">
            {loggedAgentImageThumb ? (
                <div id="header-agent-name" className="gx-header__profileImage">
                    <img src={loggedAgentImageThumb} />
                </div>
            ) : null}
            <Icon name="arrow-down" />
        </Button>
        <div className="gx-header__profileDropDownMenu">
            <div className="gx-header__profileBox">
                <div id="header-agent-name" className="gx-title-2">
                    {loggedUser.name} {loggedUser.surname}
                </div>
                <div className="gx-header__profileMail">{loggedUser.email}</div>
                <a href="/profile/me" className="gx-button gx-button--fullWidth">
                    <Icon name="user-round" />
                    <span>{trans('label.my_profile')}</span>
                </a>
            </div>
            <a className="gx-button gx-button--fullWidth gx-header__profileLogout" href="/logout">
                {trans('label.exit')}
            </a>
        </div>
    </Fragment>
);

const withLegacyHeader = (Component) => {
    if (window.GxNavigation && window.GxNavigation.isDisabled) {
        console.warn('⚠️ withLegacyHeader: this component will be replaced by GxNavigation');
        return (props) => <Component {...props} />;
    }

    return () => {
        return null;
    };
};

export const Header = withLegacyHeader(({ actions, isSectionLabelVisible = true, customTitle = '' }) => {
    return (
        <header className={classNames(['gx-header', headerConfig[siteSection]['showLogo'] && 'gx-header--withLogo'])}>
            <HamburgerMenu />
            <Labels customTitle={customTitle} isSectionLabelVisible={isSectionLabelVisible} />
            {actions ? <div className="gx-header__actions gx-is-hidden-md-up">{actions}</div> : null}
            <div className="gx-header__wrapMobile">
                <a className="gx-header__logo" href={gtxConstants('HOME_PATH')}>
                    <span className="gx-sr-only">Logo</span>
                </a>
                <div className="gx-header__userBox" data-component="header-agency-info">
                    <AgencyLogo />
                    <a
                        className="gx-header__bell gx-button gx-button--iconOnly gx-button--ghost gx-is-hidden-sm-down"
                        id="header-link-news"
                    >
                        <Icon name="bell" />
                    </a>
                    <AgentDropdown />
                </div>
            </div>
        </header>
    );
});
