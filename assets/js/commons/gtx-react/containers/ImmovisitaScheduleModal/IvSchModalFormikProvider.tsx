import * as Yup from 'yup';
import { Formik } from 'formik';
import { trans } from '@pepita-i18n/babelfish';
import { UTCDate } from '@date-fns/utc';
import { formatISO, parse, setHours, setMinutes } from 'date-fns';
import { ImmovisitaScheduleModalFormFields } from './types/form';
import { initialFormValues } from './utils/initialFormValues';
import {
    IApiRoom,
    IApiRoomResponse,
    IOnSubmitContextCallbackReturn,
} from './types';
import { updateRoom, createRoom } from './web-api';

//@ts-ignore
const FormSchema: Yup.SchemaOf<ImmovisitaScheduleModalFormFields> =
    Yup.object().shape({
        guests: Yup.array().of(
            Yup.object().shape({
                email: Yup.string()
                    .email(trans('label.insert_mail.error'))
                    .required(trans('label.insert_mail.error')),
            })
        ),
        ['scheduled_date']: Yup.string(),
        note: Yup.string(),
        ['scheduled_time']: Yup.string().matches(
            /(?:[01]\d|2[0-3]):(?:[0-5]\d)/,
            trans('label.immovisita_scheduled.form.errors.invalid_time')
        ),
        propertyId: Yup.number()
            .typeError(
                trans('label.immovisita_scheduled.form.errors.real_estate')
            )
            .required(
                trans('label.immovisita_scheduled.form.errors.real_estate')
            ),
        agent: Yup.number()
            .typeError(trans('label.immovisita_scheduled.form.errors.agent'))
            .required(trans('label.immovisita_scheduled.form.errors.agent')),
    });

export interface IImmovisitaScheduleFormikProviderProps
    extends React.PropsWithChildren {
    onCreate?: (
        roomObj: IApiRoom,
        onCreateSuccess: (roomObj: IOnSubmitContextCallbackReturn) => void
    ) => Promise<IApiRoomResponse | void>;
    onUpdate?: (
        roomObj: IApiRoom,
        onUpdateSuccess: (roomObj: IOnSubmitContextCallbackReturn) => void
    ) => Promise<IApiRoomResponse | void>;
    onCreateSuccess: (roomObj: IOnSubmitContextCallbackReturn) => void;
    onUpdateSuccess: (roomObj: IOnSubmitContextCallbackReturn) => void;
    onError: (error: Error) => void;
}

export const IvSchModalFormikProvider: React.FC<
    IImmovisitaScheduleFormikProviderProps
> = ({
    children,
    onCreateSuccess,
    onUpdateSuccess,
    onError,
    onCreate,
    onUpdate,
}) => {
    return (
        <Formik
            initialValues={initialFormValues()}
            onSubmit={(values, actions) => {
                actions.validateForm().then(() => {
                    //Aggiorno la data con l'orario settato nella drop down
                    //Alla form andrà passato solo values.scheduled_date.toISOString()

                    const hours = parseInt(values.scheduled_time.split(':')[0]);
                    const minutes = parseInt(
                        values.scheduled_time.split(':')[1]
                    );
                    const date = setMinutes(
                        setHours(
                            parse(
                                values.scheduled_date,
                                'dd/MM/yyyy',
                                new Date()
                            ),
                            hours
                        ),
                        minutes
                    );
                    const scheduledTime = formatISO(new UTCDate(date));

                    if (values.propertyId) {
                        const roomToPost: IApiRoom = {
                            agentId: parseInt(values.agent.toString()),
                            propertyId: values.propertyId,
                            // scheduledTime: values.scheduled_date
                            //     .utc()
                            //     .format('YYYY-MM-DD HH:mm:ss'), //2022-01-30 16:00:00
                            scheduledTime,
                            note: values.note,
                            guests: values.guests,
                        };

                        if (values.id) {
                            if (onUpdate) {
                                onUpdate(
                                    Object.assign(roomToPost, {
                                        id: values.id,
                                    }),
                                    onUpdateSuccess
                                )
                                    .catch((err) => onError(err))
                                    .finally(() => {
                                        actions.setSubmitting(false);
                                    });
                            } else {
                                updateRoom(
                                    Object.assign(roomToPost, {
                                        id: values.id,
                                    }),
                                    values.visitType,
                                    values.threadId
                                        ? {
                                              threadId: values.threadId,
                                          }
                                        : {}
                                )
                                    .json()
                                    .then((data) => {
                                        onUpdateSuccess(
                                            Object.assign({}, data, {
                                                visitType: values.visitType,
                                            })
                                        );
                                    })
                                    .catch((err) => onError(err))
                                    .finally(() => {
                                        actions.setSubmitting(false);
                                    });
                            }
                        } else {
                            if (onCreate) {
                                onCreate(roomToPost, onUpdateSuccess)
                                    .catch((err) => onError(err))
                                    .finally(() => {
                                        actions.setSubmitting(false);
                                    });
                            } else {
                                createRoom(
                                    roomToPost,
                                    values.visitType,
                                    values.threadId
                                        ? {
                                              threadId: values.threadId,
                                          }
                                        : {}
                                )
                                    .json()
                                    .then((data: IApiRoomResponse) => {
                                        onCreateSuccess(
                                            Object.assign({}, data, {
                                                visitType: values.visitType,
                                            })
                                        );
                                    })
                                    .catch((err) => onError(err))
                                    .finally(() => {
                                        actions.setSubmitting(false);
                                    });
                            }
                        }
                    } else {
                        return;
                    }
                });
            }}
            validationSchema={FormSchema}
            validateOnChange={false}
            validateOnBlur
        >
            {children}
        </Formik>
    );
};
