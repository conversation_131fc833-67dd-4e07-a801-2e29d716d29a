<?php

declare(strict_types=1);

namespace App\Model\Property;

use App\Model\Geography\Address;
use App\Model\Geography\City;

class CadastralDataAddress
{
    private ?City $city = null;
    private ?Address $address = null;

    public function getCity(): ?City
    {
        return $this->city;
    }

    public function setCity(?City $city): void
    {
        $this->city = $city;
    }

    public function getAddress(): ?Address
    {
        return $this->address;
    }

    public function setAddress(?Address $address): void
    {
        $this->address = $address;
    }
}
