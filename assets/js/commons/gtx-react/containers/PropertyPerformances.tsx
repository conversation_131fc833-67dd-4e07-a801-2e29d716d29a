import { FC, PropsWithChildren, createContext, useContext } from 'react';
import gtxConstants from '@getrix/common/js/gtx-constants';
import { Alert } from '@gx-design/alert';
import { Icon } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import { PERFORMANCE_TAGS } from 'constants/propertyPerformance';
import clsx from 'clsx';
import { getPropertyFeatures } from 'lib/property';
import { ucFirst } from 'lib/strings-formatter';
import { useProperty } from '../../../base/pages/property-performance/hooks/useProperty';
import {
    KpiPerformanceResponseTypes,
    PropertyPerformanceResponse,
} from 'types/performances';
import { Skeleton } from 'gtx-react/components/Skeleton';

type KpiPerformanceCardProps = {
    position?: number;
    title?: string;
    total?: number;
    ranking?: number;
};

type PropertyDetailsProps = {
    propertyId: string;
};

const KpiPerformanceCard = ({
    position,
    title,
    total,
    ranking,
}: KpiPerformanceCardProps) => {
    const foundPerformance = PERFORMANCE_TAGS.find(
        (tag) => tag.value === ranking
    );

    const { isLoadingData } = usePropertyPerformancesContext();

    return (
        <div
            className={clsx(
                'gx-card',
                foundPerformance && `performance-${foundPerformance.score}`
            )}
        >
            <div className="gx-card__content">
                <div className="increases-performance-box__title">
                    <h6 className="gx-overline">{title}</h6>
                </div>
                {isLoadingData ? (
                    <div className="increases-performance-box__skeleton">
                        <Skeleton height={20} width={180} />
                        <Skeleton height={24} width={100} />
                    </div>
                ) : position && foundPerformance ? (
                    <div className="performance-position">
                        <div>
                            <Icon
                                className="performance-icon-status"
                                name={foundPerformance.icon}
                            />
                            <span>{`${position}°`}</span>
                            {` ${trans('label.out_of')} ${total}`}
                        </div>
                    </div>
                ) : (
                    <div className="increases-performance-box__valueEmpty">
                        {ucFirst(trans('label.no_data_to_show'))}
                    </div>
                )}
            </div>
        </div>
    );
};

const GeneralPerformances: FC = () => {
    const { isLoadingData, data } = usePropertyPerformancesContext();

    const performance = data?.generals
        ? PERFORMANCE_TAGS.find(
              (tag) =>
                  tag.value ===
                  data[KpiPerformanceResponseTypes.Generals]?.rangeId
          )
        : null;

    return (
        <div className="increases-performance-box__head">
            <div className="increases-performance-box__headTitle">
                <Icon name="podium" />
                <h2 className="gx-title-2">
                    {trans('label.property_performance.zones_leaderboard')}
                </h2>
            </div>
            <p className="gx-body-small gx-text-light">
                {trans(
                    'label.property_performance.zones_leaderboard_description'
                )}
            </p>
            <div className="increases-performance-box__headCenter">
                <div className="increases-performance-box__headTitle">
                    <Icon name="podium" />
                    <h2 className="gx-title-2">
                        {trans('label.overall_performance_ranking')}
                    </h2>
                </div>
                {isLoadingData ? (
                    <Skeleton
                        height={24}
                        width={280}
                        marginTop={4}
                        marginBottom={4}
                    />
                ) : data && performance ? (
                    <div
                        className={`increases-performance-box__headSubTitle gx-display-subtitle performance-${performance.score}`}
                    >
                        <Icon name={performance.icon} />
                        <div>
                            <span>{`${data?.[
                                KpiPerformanceResponseTypes.Generals
                            ]?.position}°`}</span>
                            {` ${trans('label.out_of')} ${data?.total}`}
                        </div>
                    </div>
                ) : (
                    <div>{ucFirst(trans('label.no_data_to_show'))}</div>
                )}
            </div>
        </div>
    );
};

const KpiPerformances = ({ children }: PropsWithChildren) => {
    const { data } = usePropertyPerformancesContext();

    return (
        <div>
            <div className="increases-performance-box__details">
                <KpiPerformanceCard
                    title={trans('label.in_list_views_ranking')}
                    position={
                        data?.[KpiPerformanceResponseTypes.Views]?.position
                    }
                    total={data?.total}
                    ranking={data?.[KpiPerformanceResponseTypes.Views]?.rangeId}
                />
                <KpiPerformanceCard
                    title={trans('label.ad_clicks_ranking')}
                    position={
                        data?.[KpiPerformanceResponseTypes.Clicks]?.position
                    }
                    total={data?.total}
                    ranking={
                        data?.[KpiPerformanceResponseTypes.Clicks]?.rangeId
                    }
                />
                <KpiPerformanceCard
                    title={trans('label.received_contacts_ranking')}
                    position={
                        data?.[KpiPerformanceResponseTypes.Contacts]?.position
                    }
                    total={data?.total}
                    ranking={
                        data?.[KpiPerformanceResponseTypes.Contacts]?.rangeId
                    }
                />
            </div>
            {children}
        </div>
    );
};

export const PropertyDetailsInfo = ({ propertyId }: PropertyDetailsProps) => {
    const { data: propertyData, isLoading: isLoadingProperty } =
        useProperty(propertyId);

    if (isLoadingProperty) {
        return <Skeleton className="skeleton-alert" height={66} width="100%" />;
    }

    return (
        <Alert style="info" withMarginBottom>
            <>
                {trans('label.performances.calculated_on_similar_ads', {
                    PORTAL: gtxConstants('AD_PORTAL'),
                })}
                :<br />
                <strong>{getPropertyFeatures(propertyData)}</strong>
            </>
        </Alert>
    );
};

type PropertyPerformanceContextType = {
    isLoadingData: boolean;
    data: PropertyPerformanceResponse['data'] | null;
};

const PropertyPerformancesContext =
    createContext<PropertyPerformanceContextType>({
        isLoadingData: false,
        data: null,
    });

export const usePropertyPerformancesContext = () => {
    const context = useContext(PropertyPerformancesContext);

    if (!context) {
        throw new Error(
            'usePropertyPerformancesContext must be used within a PropertyPerformancesContext'
        );
    }

    return context;
};

export const PropertyPerformances = ({
    children,
    isLoadingData = false,
    data = null,
}) => {
    const context = {
        isLoadingData,
        data,
    };

    return (
        <PropertyPerformancesContext.Provider value={context}>
            {children}
        </PropertyPerformancesContext.Provider>
    );
};

PropertyPerformances.GeneralPerformances = GeneralPerformances;
PropertyPerformances.KpiPerformances = KpiPerformances;

export default PropertyPerformances;
