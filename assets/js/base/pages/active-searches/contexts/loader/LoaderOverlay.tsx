import { Loader } from '@gx-design/loader';
import { useIsFetching, useIsMutating } from '@tanstack/react-query';
import { queries, mutations } from '../../utils';

export function LoaderOverlay() {
    const isMutating = useIsMutating({ mutationKey: mutations.list });
    const isFetching = useIsFetching({ queryKey: queries.list });

    const isLoading = Boolean(isMutating) || Boolean(isFetching);
    return isLoading ? <Loader variant="fixed" /> : null;
}
