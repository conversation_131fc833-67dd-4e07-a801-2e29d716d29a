import { http } from '@pepita/http';
import { IApiRoom, VisitType } from '../types';
import { endpoints } from './endpoints';
import { validateLegacyToJson } from 'lib/REST/helpers/utils';
import { GetPropertiesFilters } from '../ChangeRealEstate/types';
// import gtxConstants from '@getrix/common/js/gtx-constants';
// import gtxLoggedUser from '@getrix/common/js/gtx-logged-user';

// const MESSAGING_V2_ENABLED = Boolean(gtxConstants('MESSAGING_V2_ENABLED'));
// const MESSAGING_V2_ENABLED_AGENCIES = JSON.parse(`[${gtxConstants('MESSAGING_V2_AGENCY_ENABLED')}]`);
// const AGENCY_ID = parseInt(gtxLoggedUser('agencyId'));
// const hasV2Messaging = MESSAGING_V2_ENABLED && MESSAGING_V2_ENABLED_AGENCIES.includes(AGENCY_ID);

/** Fixed number of results per page, for getProperties API */
const results = '4';

export const getPropertyById = (propertyId) => {
    return http.get(endpoints.GET_PROPERTY(propertyId));
};

export const createRoom = (roomObj: IApiRoom, type: VisitType, headers?: { [key: string]: string }) => {
    //default immoVisita endpoint
    let endpoint = endpoints.ROOMS;

    if (headers?.threadId) {
        // commented new endpoints for reverse double write
        // const version = hasV2Messaging ? 'V2' : 'V1';
        const version = 'V1';

        endpoint =
            type === VisitType.REAL
                ? endpoints[version].SCHEDULED_VISIT_REAL
                : endpoints[version].SCHEDULED_VISIT_VIRTUAL;
    }

    return http.post(endpoint, {
        json: roomObj,
        headers: {
            'X-Thread-Id': headers?.threadId,
        },
    });
};

export const updateRoom = (roomObj: IApiRoom, type: VisitType, headers?: { [key: string]: string }) => {
    return http(`${endpoints.ROOMS}/${roomObj.id}`, {
        method: 'PUT',
        json: roomObj,
        headers: {
            'X-Thread-Id': headers?.threadId,
        },
    });
};

export const getProperties = async (filters: GetPropertiesFilters) => {
    try {
        // @ts-ignore
        const params = new URLSearchParams({ ...filters, results });
        const response = await fetch(`${endpoints.GET_PROPERTY_LIST}?${params}`);
        const properties = await response.json();
        const total = parseInt(response.headers.get('x-total-count') ?? '0');
        return { total, properties };
    } catch (error) {
        throw new Error(
            error instanceof Error ? error?.message : 'Unable to get properties list (change real estate modal)'
        );
    }
};

export const getCategories = () => validateLegacyToJson(fetch(endpoints.LOOKUP_CATEGORIES));

export const getTypologies = (value: string) =>
    validateLegacyToJson(fetch(`${endpoints.LOOKUP_TYPOLOGIES}?${new URLSearchParams({ value })}`));

export const getContracts = (value: string) =>
    validateLegacyToJson(fetch(`${endpoints.LOOKUP_CONTRACTS}?${new URLSearchParams({ value })}`));
