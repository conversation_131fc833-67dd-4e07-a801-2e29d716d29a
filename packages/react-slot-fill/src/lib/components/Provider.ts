import * as React from 'react';
import mitt from 'mitt';
import Manager from '../Manager';
import Fill from './Fill';
export interface SlotFillContextValue {
    manager: Manager;
    bus: mitt.Emitter;
}
export const SlotFillContext = React.createContext<SlotFillContextValue | null>(null);

export default class Provider extends React.Component<any, any> {
    private _bus: mitt.Emitter;
    private _manager: Manager;

    constructor(props: any) {
        super(props);
        this._bus = new mitt();
        this._manager = new Manager(this._bus);
        this._manager.mount();
    }

    componentWillUnmount() {
        this._manager.unmount();
    }

    render(): any {
        return this.props.children;
    }

    /**
     * Returns instances of Fill react components
     */
    getFillsByName(name: string): Fill[] {
        return this._manager.getFillsByName(name);
    }

    /**
     * Return React elements that were inside Fills
     */
    getChildrenByName(name: string): (React.ReactElement | number | string)[] {
        return this._manager.getChildrenByName(name);
    }
}
