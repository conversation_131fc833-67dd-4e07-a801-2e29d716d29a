{"name": "onboarding", "version": "1.0.0", "license": "proprietary", "private": true, "source": "src/OnboardingApp.tsx", "main": "dist/onboarding.js", "scripts": {"build": "webpack --config webpack.prod.js", "watch": "webpack --watch"}, "resolutions": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "typings": "dist/Onboarding.d.ts", "volta": {"node": "18.14.2", "yarn": "3.4.1"}, "dependencies": {"@gx-design/button": "^2.0.9", "@gx-design/icon": "^2.0.5", "@gx-design/onboarding": "^2.1.6", "@pepita/babelfish": "^1.8.5", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^17.0.2", "@types/react": "^17.0.2", "@types/react-dom": "^17.0.2", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-merge": "^4.1.4"}, "packageManager": "yarn@3.4.1", "browserslist": ["last 2 versions", "safari >= 7"], "repository": {"type": "git", "url": "https://gitlab.pepita.io/getrix/mls-site.git", "directory": "packages/onboarding"}}