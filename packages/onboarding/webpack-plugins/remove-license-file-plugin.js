const webpack = require('webpack')
// Compilation object gives us reference to some useful constants. 
const { Compilation } = webpack;
class RemoveLicenseFilePlugin {
    apply(compiler) {
        compiler.hooks.compilation.tap("RemoveLicenseFilePlugin", (compilation) => {
            compilation.hooks.processAssets.tapAsync(
                {
                  name: 'RemoveLicenseFilePlugin',
                  stage: Compilation.PROCESS_ASSETS_STAGE_REPORT
                },
                (assets, callback) => {
                  // this function will be called once with assets added by plugins on prior stages
                  for (let name in assets) {
                        if (name.endsWith("LICENSE.txt")) {
                            console.log('Removing asset: ', name)
                            delete assets[name];
                        }
                    }
                    callback()
                }
              );
        });
    }
}

module.exports = RemoveLicenseFilePlugin;
