import { http } from '@pepita/http';
import { CITY_AUTOCOMPLETE_API } from './endpoints';
import { ApiResponse } from 'types/api-response';
import { LookupApiGeographyCitiesData } from '../types';

export type CityAutocompleteOption = {
    idComune: number;
    comune_nome: string;
    provincia_nome: string;
    nazione_nome: string;
};
export const getCitiesAutocompleteApi = async (s): Promise<CityAutocompleteOption[] | null> => {
    if (!s) {
        return Promise.resolve(null);
    }

    const searchParams = {
        query: s,
        sort: '+BestMatch',
        results: 5,
    };

    const response: ApiResponse<LookupApiGeographyCitiesData[]> = await http
        .get(CITY_AUTOCOMPLETE_API, { searchParams })
        .json();

    if (response.status === 'success') {
        const array = response?.data;
        const toReturn: CityAutocompleteOption[] = array.map((elem) => ({
            idComune: elem.id,
            comune_nome: elem.name, // eslint-disable-line
            provincia_nome: elem.province?.name, // eslint-disable-line
            nazione_nome: elem.province?.region?.country?.name,
        }));
        return toReturn;
    } else {
        return Promise.reject(response.data.error);
    }
};
