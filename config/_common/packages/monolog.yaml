monolog:
  channels: [ 'gtx_events', 'sync_scheduler', 'report_similar_properties', 'migration_verifier', 'report_low_number_searches', 'report_low_number_exposure_pois', 'report_property_change', 'immovisita_tos', 'mls_debugger' ]
  handlers:
    main:
      type:         fingers_crossed
      action_level: error
      handler:      nested
    nested:
      type: stream
      path: "%app.logs_dir%/%kernel.environment%.log"
      level: debug
    console:
      type:  console
    gtx_events:
      channels: [ gtx_events ]
      type: stream
      path: "%app.logs_dir%/%kernel.environment%_gtx_events.log"
      level: info
    sync_scheduler:
      channels: [ sync_scheduler ]
      type: stream
      path: "%app.logs_dir%/%kernel.environment%_sync_scheduler.log"
      level: debug
    report_similar_properties:
      channels: [ report_similar_properties ]
      type: stream
      path: "%app.logs_dir%/%kernel.environment%_report_similar_properties.log"
      level: debug
    report_low_number_searches:
      channels: [ report_low_number_searches ]
      type: stream
      path: "%app.logs_dir%/%kernel.environment%_report_low_number_searches.log"
      level: debug
    report_low_number_exposure_pois:
      channels: [ report_low_number_exposure_pois ]
      type: stream
      path: "%app.logs_dir%/%kernel.environment%_report_low_number_exposure_pois.log"
      level: debug
    report_property_change:
      channels: [ report_property_change ]
      type: stream
      path: "%app.logs_dir%/%kernel.environment%_report_property_change.log"
      level: debug
    migration_verifier:
      channels: [ migration_verifier ]
      type: stream
      path: "%app.logs_dir%/%kernel.environment%_migration_verifier.log"
      level: debug
    immovisita_tos:
      channels: [ immovisita_tos ]
      type: stream
      path: "%app.logs_dir%/%kernel.environment%_immovisita_tos_rejected.log"
      level: debug
    mls_debugger:
      channels: [ mls_debugger ]
      type: stream
      path: "%app.logs_dir%/%kernel.environment%_debugger.log"
      level: debug
