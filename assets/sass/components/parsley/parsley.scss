@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

input.parsley-error,
select.parsley-error,
textarea.parsley-error {
  color: color(content-error);
  border: 0.1rem solid color(border-error);
  box-shadow: none;
}

// TODO: input_error è una classe di errore dell'inserimento annuncio legacy. Rimuovere quando l'insermento annuncio sarà portato tutto sul nuovo come inserimento annuncio Aste
input,
textarea {
  &.input_error,
  &.parsley-error {
    &,
    &:focus {
      border-color: color(border-error) !important;
      background: color(background-error) !important;
    }
  }
}

.parsley-errors-list {
  color: color(content-error);
  margin: 0;
  padding: 0;
  list-style-type: none;
  font-size: 0.9em;
  line-height: 0.9em;
  opacity: 0;
  transition: all 0.3s ease-in;
  -o-transition: all 0.3s ease-in;
  -moz-transition: all 0.3s ease-in;
  -webkit-transition: all 0.3s ease-in;
}

.parsley-errors-list.filled {
  margin-top: 0.8rem;
  opacity: 1;
}

.parsley-errors-list.filled li,
.parsley-custom-error-message {
  text-transform: uppercase;
  font-weight: 600;
  font-size: 0.9em;
  margin-top: 0.8rem;
}

.styled-select {
  select.parsley-error + .dropdown-toggle {
    border: 0.1rem solid color(border-error);

    &:before {
      border: none;
    }
  }

  select.parsley-error + .input-group > .dropdown-toggle {
    border-top: 0.1rem solid color(border-error);
    border-bottom: 0.1rem solid color(border-error);
    border-left: 0.1rem solid color(border-error);
  }

  select.parsley-error + .input-group > .dropdown-toggle + .input-group-addon {
    border-top: 0.1rem solid color(border-error);
    border-bottom: 0.1rem solid color(border-error);
    border-right: 0.1rem solid color(border-error);
  }
}
