@use '@gx-design/layout/styles' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;
@use '../variables/variables' as *;

.gx-navigation-header {
  box-shadow: elevation(fixed-top);
  position: relative;
  @include z-index(navigation);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: $gx-header-size;
  background-color: color(background-main);
  padding: 0 0 0 space(xl);
  border-bottom: 0.1rem solid color(border-main);

  // It is used for the old modal
  .modal-open & {
    @include z-index(navigation, -1);
  }

  &__dropdown {
    @include z-index(navigation, 1);
    position: absolute;
    width: 34.4rem;
    border-color: color(border-main);
    border-radius: radius(md);
    background-color: color(background-main);
    box-shadow: elevation(raised);
  }

  &__buttonGroup {
    display: inline-flex;
    vertical-align: middle;
    flex-direction: column;
    gap: space(sm);
    width: 100%;
  }

  &__menuSwitchDropdown {
    @include z-index(navigation, 1);
  }

  .multi-agency--switch & {
    top: $gx-multi-agency-banner-height;
  }

  .gx-icon {
    flex-shrink: 0;
    @include icon-size(md);
  }

  @include media('screen', '<#{breakpoint(xl)}') {
    padding-left: 0;
  }

  @include media('screen', '<#{breakpoint(md)}') {
    padding-right: space(md);
  }

  &--newMenu {
    padding-left: space(xl);
    padding-right: space(lg);
    margin-left: $gx-new-menu-width;
    position: fixed;
    top: 0;
    left: 0;
    height: $gx-new-menu-header-height;
    width: calc(100% - #{$gx-new-menu-width});

    @include media('screen', '<#{breakpoint(md)}') {
      margin-left: 0;
      width: 100%;
      padding-left: space(md);

      .gx-navigation-header__wrapMobile {
        display: none;
      }
    }
  }

  &__sectionBox {
    @include media('screen', '<#{breakpoint(xs)}') {
      max-width: 18rem;
    }
  }

  &__section {
    color: color(content-low);
  }

  &__subSection {
    line-height: 1.6rem;
  }

  &__content {
    display: flex;
    align-items: center;
  }

  &__wrapMobile {
    display: flex;
    align-items: center;
    gap: 0 space(sm);
    background-color: color(background-main);
    border-bottom: 0;

    @include media('screen', '<#{breakpoint(md)}') {
      position: absolute;
      left: 0;
      top: 0;
      width: 32rem;
      transform: translateX(-32rem);
      justify-content: space-between;
      padding-left: space(4xl);
      border-right: 0.1rem solid color(border-main);
      transition: transform 0.2s ease-in-out;
      border-bottom: 0;
    }

    .st-menu-open & {
      transform: translateX(0);
    }
  }

  &__logo {
    flex-shrink: 0;

    img {
      max-width: 100%;
      height: 3rem;
    }

    &.is-christmas {
      img {
        height: 3.4rem;
      }
    }
  }

  &__heading {
    display: flex;
    align-items: center;
  }

  &__menu-hamburger {
    font-size: 2.4rem;
    color: color(content-selected);
    display: none;
    flex: 1;
    margin-right: space(md);

    @include media('screen', '<#{breakpoint(md)}') {
      display: flex;
    }
  }

  &__actions {
    display: flex;
    align-items: center;

    > * + * {
      margin-left: space(md);
    }
  }

  &__profile {
    position: relative;
    display: flex;
    align-items: center;
    height: 5.6rem;
    margin-left: space(md);
    border: none;
    border-left: 0.1rem solid color(border-main);
    background-color: color(background-main);
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    cursor: pointer;
    border-radius: 0;
    padding: 0 space(sm);

    .gx-icon {
      flex-shrink: 0;
      font-size: 2.4rem;
      color: color(content-selected);
    }

    &Box {
      padding: space(md) space(lg);
    }

    &Logout {
      border: none;
      border-top: 0.1rem solid color(border-main);
      border-radius: 0;
    }

    &Mail {
      margin-bottom: space(sm);
      color: color(content-low);
    }

    &Image {
      width: 4rem;
      height: 4rem;
      border-radius: radius(rounded);
      overflow: hidden;
      background-color: color(background-alt);
      margin-right: space(sm);
      flex-shrink: 0;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }

  &__addProperty {
    margin-right: space(md);
    flex-shrink: 0;
  }

  &__agencyName {
    max-width: 20rem;
  }

  &__agencyLogo {
    height: 3.2rem;
    max-width: 20rem;
    overflow: hidden;

    img {
      max-width: 100%;
      max-height: 100%;
      @include typography(body-small);
    }

    & + * {
      margin-left: space(md);
    }
  }

  &__menuSwitch {
    display: flex;
    border: 0.1rem solid color(border-action);
    background-color: color(background-action);
    border-radius: radius(md);
    margin-right: space(lg);
    height: 4rem;
    cursor: pointer;

    &Button {
      padding: 0 space(md);
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: transparent;
      border: 0;
      border-radius: 0;

      span {
        @include typography(button);
        color: color(content-action);
      }
    }

    &Arrow {
      width: 4rem;
      display: flex;
      align-items: center;
      justify-content: center;
      border-left: 0.1rem solid color(border-action);
      font-size: 2rem;
      color: color(content-action);

      .gx-button {
        border: 0;
        background-color: transparent;
        border-radius: 0;
      }
    }
  }
}

.multi-agency {
  &--switch {
    .gx-navigation-header {
      &--newMenu {
        top: $gx-multi-agency-banner-height;
      }
    }

    .multi-agency {
      &__alert {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        border-radius: 0;
        height: $gx-multi-agency-banner-height;
        @include z-index(navigation, -2);
        border-left: none;
        border-right: none;
        border-top: none;
        justify-content: center;

        .gx-alert {
          &__text {
            max-width: 100%;

            & > span {
              display: block;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          &__link {
            cursor: pointer;
            padding: 0;
            @include typography(body-small);

            &:hover {
              text-decoration: underline;
            }
          }
        }

        @include media('screen', '<#{breakpoint(md)}') {
          flex-direction: column;
          align-items: start;
          .gx-alert {
            &__text {
              margin-left: 0;
            }
          }

          svg {
            display: none;
          }
        }
      }
    }
  }
}

// Christmas

@keyframes light-animation {
  0% {
    fill: #f15461;
  }
  25% {
    fill: #facc5f;
  }
  50% {
    fill: #9ac47a;
  }
  75% {
    fill: #68addb;
  }
  100% {
    fill: #f15461;
  }
}

.christmas-lights {
  display: flex;
  position: relative;
  transform: translateY(-#{space(md)});
  height: 3.4rem;
  margin-left: space(3xl);
  margin-right: space(3xl);
  padding-right: 3.8rem;
  max-width: 23rem;

  @include media('screen', '>=#{breakpoint(lg)}') {
    max-width: 48rem;
  }

  @include media('screen', '>=#{breakpoint(xl)}') {
    max-width: 64rem;
  }

  &__wrapper {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    flex-wrap: wrap;
    overflow: hidden;
  }

  &__segment {
    width: 5.2rem;
    height: 3.4rem;

    & + & {
      margin-left: -(space(sm));
    }

    &--lastChild {
      position: absolute;
      top: 0;
      right: 0;
      width: 4.6rem;
    }
  }

  &__bulb {
    animation-name: light-animation;
    animation-duration: 4s;
    animation-iteration-count: infinite;
  }

  .christmas-lights__segment:nth-child(2n + 1) .christmas-lights__bulb {
    fill: #f15461;
    & + .christmas-lights__bulb {
      fill: #facc5f;
      animation-delay: -1s;
    }
  }

  .christmas-lights__segment:nth-child(2n + 2) .christmas-lights__bulb {
    fill: #9ac47a;
    animation-delay: -2s;
    & + .christmas-lights__bulb {
      fill: #68addb;
      animation-delay: -3s;
    }
  }
}
