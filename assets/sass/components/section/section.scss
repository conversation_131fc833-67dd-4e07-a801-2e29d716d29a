// ==========================================================================
// Section - Components
// ==========================================================================
@use '@gx-design/theme/styles' as *;

.gx-section {
  & + & {
    padding-top: space(xl);
    margin-top: space(sm);
    border-top: 0.1rem solid color(border-main);
  }

  .gx-title-1 {
    display: flex;
    align-items: center;
    margin-bottom: space(lg);

    > * + * {
      margin-left: space(sm);
    }
  }

  &--paddingBottom {
    padding-bottom: space(md);
  }
}
