import { CellContext } from '@tanstack/react-table';
import { Property } from '../../types';
import { useQuery } from '@tanstack/react-query';
import { createGetQualityDetailsOptions } from 'lib/REST/requests/api/properties/query-factory';
import { qualityDetailsMapper } from './helpers/quality-details-mapper';
import { memo, PropsWithChildren, useMemo, useRef } from 'react';
import { Icon } from '@gx-design/icon';
import clsx from 'clsx';
import { isTuristicProperty } from '../../../../utils';
import { Button } from '@gx-design/button';
import { trans } from '@pepita-i18n/babelfish';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { useLoggedUserContext } from '../../../LoggedUserProvider';
import { useOpenPropertyRankingDetailDialog } from '../../../dialogs/PropertyRankingDetailDialog/PropertyRankingDetailDialog';
import { useOpenImproveQualityDialog } from '../../../dialogs/ImproveQualityDialog/ImproveQualityDialog';
import useIsInViewPort from 'gtx-react/hooks/useIsInViewPort';
import { ucFirst } from 'lib/strings-formatter';
import { PropertyStatus } from '../../../../types';

function QualityDetail(
    props: PropsWithChildren<{
        status: PropertyStatus;
        propertyId: string;
        categoryId: number;
        shouldFetch: boolean;
        type: 'auction' | 'property' | 'new_construction';
    }>
) {
    const ref = useRef(null);
    const isInViewport = useIsInViewPort(ref, 0);
    const { trackEvent } = useMixpanelContext();
    const { isFromBackoffice } = useLoggedUserContext();
    const { open: openRankingDetailDialog } =
        useOpenPropertyRankingDetailDialog();
    const qualityQuery = useQuery({
        ...createGetQualityDetailsOptions({
            params: { propertyId: props.propertyId },
        }),
        enabled: props.shouldFetch && isInViewport,
        select: qualityDetailsMapper(props.categoryId),
    });
    const { open } = useOpenImproveQualityDialog();

    const content = useMemo(() => {
        switch (qualityQuery.status) {
            case 'pending':
                return (
                    <span className="gx-table-new__contentFixedHeight">
                        ...
                    </span>
                );

            case 'error':
                return (
                    <span className="gx-table-new__contentFixedHeight">
                        ---
                    </span>
                );

            case 'success':
                return (
                    <>
                        <span
                            onClick={
                                isFromBackoffice
                                    ? () =>
                                          openRankingDetailDialog(
                                              props.propertyId
                                          )
                                    : undefined
                            }
                            className="crm-cell-contentItem crm-cell-contentItem--xsGap"
                        >
                            <span>{props.children}</span>
                            <Icon
                                className={clsx('gx-icon', {
                                    'gx-text-positive': qualityQuery.data,
                                    'gx-text-warning': !qualityQuery.data,
                                })}
                                name={
                                    qualityQuery.data
                                        ? 'check-circle--active'
                                        : 'exclamation-mark-circle--active'
                                }
                            />
                        </span>
                        <Button
                            onClick={() => {
                                open({
                                    propertyId: Number(props.propertyId),
                                    type: props.type,
                                });
                                trackEvent({
                                    event: 'properties_improve_listing',
                                    extra: {
                                        ['property_id']: props.propertyId,
                                        ['list_view']: ucFirst(props.status),
                                    },
                                });
                            }}
                            size="small"
                        >
                            <Icon name="cockade" />
                            <span>{trans('label.improve')}</span>
                        </Button>
                    </>
                );
        }
    }, [
        isFromBackoffice,
        open,
        openRankingDetailDialog,
        props.children,
        props.propertyId,
        props.status,
        props.type,
        qualityQuery.data,
        qualityQuery.status,
        trackEvent,
    ]);

    return <div ref={ref}>{content}</div>;
}

export function QualityCell({
    cell,
    row,
}: CellContext<Property, Property['quality']>) {
    const value = cell.getValue();
    if (!value.categoryId) {
        return null;
    }

    if (!value.ranking) {
        return <span className="gx-table-new__contentFixedHeight">{'0%'}</span>;
    }

    return (
        <QualityDetail
            // TODO: check if this is correct
            shouldFetch={!isTuristicProperty(value.categoryId)}
            categoryId={value.categoryId}
            propertyId={String(cell.row.original.id)}
            type={row.original.type}
            status={row.original.filterStatus}
        >
            {value.ranking}%
        </QualityDetail>
    );
}

export const MemoQualityCell = memo(QualityCell);
