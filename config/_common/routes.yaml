getrix_app_login_check:
    path: /login_check

getrix_app_logout:
    path: /logout

getrix_account_blocked:
    path: /blocked

getrix_app_permanent_redirect:
    resource: App\Controller\Base\PermanentRedirectController
    type: annotation

getrix_dashboard:
    resource: App\Controller\Dashboard\DashboardController
    type: annotation

getrix_app_login:
    resource: App\Controller\Base\SecurityController
    type: annotation
    prefix: /

getrix_app_register:
    resource: App\Controller\Base\RegisterController
    type: annotation
    prefix: /

getrix_app_a2f:
    resource: App\Controller\Base\TwoFactorAuthenticationController
    type: annotation
    prefix: /login/a2f

getrix_app_landing_page:
    resource: App\Controller\Base\LandingPageController
    type: annotation
    prefix: /landing

getrix_legacy:
    path: /getrix/{url}
    defaults: { _controller: App\Controller\Base\LegacyController::redirect }

getrix_profile:
    resource: "../src/Controller/Profile/"
    type: annotation
    prefix: /profile

getrix_rest:
    resource: "../src/Controller/Rest/"
    type: annotation
    prefix: /rest

getrix_settings:
    resource: "../src/Controller/Settings/"
    type: annotation

getrix_mail:
    resource: "../src/Controller/Mail/"
    type: annotation
    prefix: /mail

getrix_property:
    resource: App\Controller\Base\PropertyController
    type: annotation
    prefix: /immobili

getrix_auctions_catalogue:
    resource: App\Controller\Base\AuctionsCatalogueController
    type: annotation
    prefix: /immobili/aste

getrix_portal_properties:
    resource: App\Controller\Base\PortalPropertiesController
    type: annotation
    prefix: /immobili/portale

getrix_portal_new_constructions:
    resource: App\Controller\Base\PortalNewConstructionsController
    type: annotation
    prefix: /nuove-costruzioni/portale

getrix_portal_auctions:
    resource: App\Controller\Base\PortalAuctionsController
    type: annotation
    prefix: /immobili/aste/portale

getrix_agency:
    resource: "../src/Controller/Agency/Base"
    type: annotation
    prefix: /agenzia

getrix_agency_api:
    resource: "../src/Controller/Agency/API/"
    type: annotation
    prefix: /api

getrix_agent_api:
    resource: "../src/Controller/Agent/API/"
    type: annotation
    prefix: /api

getrix_scheduled_visit_api:
    resource: "../src/Controller/ScheduledVisit/API/"
    type: annotation
    prefix: /api

getrix_messaging_api:
    resource: "../src/Controller/Messaging/API/"
    type: annotation
    prefix: /api

getrix_configuration_api:
    resource: "../src/Controller/Configuration/API/"
    type: annotation
    prefix: /api

getrix_lookup_api:
    resource: "../src/Controller/Lookup/API/"
    type: annotation
    prefix: /api

getrix_geography_api:
    resource: "../src/Controller/Geography/API/"
    type: annotation
    prefix: /api

getrix_searches_internal_api:
    resource: "../src/Controller/Searches/Internal/API/"
    type: annotation
    prefix: /internal/api

getrix_searches_api:
    resource: "../src/Controller/Searches/API/"
    type: annotation
    prefix: /api

getrix_received_searches_api:
    resource: "../src/Controller/ReceivedSearches/API/"
    type: annotation
    prefix: /api

getrix_property_internal_api:
    resource: "../src/Controller/Property/Internal/API/"
    type: annotation
    prefix: /internal/api

getrix_property_api:
    resource: "../src/Controller/Property/API/"
    type: annotation
    prefix: /api

getrix_portal_property_api:
    resource: "../src/Controller/PortalProperty/API/"
    type: annotation
    prefix: /api

getrix_portal_new_construction_api:
    resource: "../src/Controller/PortalNewConstruction/API/"
    type: annotation
    prefix: /api

getrix_portal_auction_api:
    resource: "../src/Controller/PortalAuction/API/"
    type: annotation
    prefix: /api

getrix_onboarding_tours_api:
    resource: "../src/Controller/OnboardingTour/API/"
    type: annotation
    prefix: /api

getrix_settings_api:
    resource: "../src/Controller/Settings/API/"
    type: annotation
    prefix: /impostazioni

getrix_virtual_tours_api:
    resource: "../src/Controller/VirtualTour/API/"
    type: annotation
    prefix: /api

getrix_zones_api:
    resource: "../src/Controller/Zones/API/"
    type: annotation
    prefix: /api

getrix_check_status_api:
    resource: "../src/Controller/CheckStatus/API/"
    type: annotation
    prefix: /api

getrix_matches:
    resource: App\Controller\Matches\Base\MatchesController
    type: annotation
    prefix: /clienti/match

getrix_acquisition_privates_properties:
    resource: App\Controller\Base\AcquisitionPrivatesPropertiesController
    type: annotation
    prefix: /acquisizione/annunci-privati

getrix_acquisition_privates_estimates:
    resource: App\Controller\Base\EstimatesController
    type: annotation
    prefix: /acquisizione/valutazioni

getrix_estimate:
    resource: App\Controller\Base\AgencyEstimatesController
    type: annotation
    prefix: /report

getrix_properties_lookup:
    resource: App\Controller\Base\PropertiesLookupController
    type: annotation
    prefix: /immobili/lookup

getrix_virtual_tour_360:
    resource: App\Controller\Base\VirtualTour360Controller
    type: annotation
    prefix: /virtual_tour_360

getrix_customers:
    resource: App\Controller\Base\CustomersController
    type: annotation
    prefix: /clienti

getrix_api_customers:
    resource: "../src/Controller/Customer/API/"
    type: annotation
    prefix: api/clienti

getrix_photoplan:
    resource: App\Controller\Base\PhotoplanController
    type: annotation
    prefix: /fotoplan

getrix_agency_leads:
    resource: App\Controller\Base\AgencyLeadsController
    type: annotation
    prefix: /contatti

getrix_property_ads:
    resource: App\Controller\Base\PropertyAdsController
    type: annotation
    prefix: /listing

getrix_administration:
    resource: "../src/Controller/Administration/Base/"
    type: annotation
    prefix: /amministrazione

getrix_administration_api:
    resource: "../src/Controller/Administration/API/"
    type: annotation
    prefix: /api

appointments:
    resource: App\Controller\Base\AppointmentsController
    type: annotation
    prefix: /appointments

financial_advice:
    resource: App\Controller\FinancialAdvice\Base\FinancialAdviceController
    type: annotation
    prefix: /consulenza-mutuo

financial_advice_api:
    resource: App\Controller\FinancialAdvice\API\GetFinancialAdviceController
    type: annotation
    prefix: /api

youdomus:
    resource: App\Controller\Base\YoudomusController
    type: annotation
    prefix: /youdomus

youdomus-base:
    resource: "../src/Controller/Youdomus/Base"
    type: annotation
    prefix: /youdomus

youdomus-api:
    resource: "../src/Controller/Youdomus/API"
    type: annotation
    prefix: /youdomus/api

messaging:
    resource: App\Controller\Base\MessagingController
    type: annotation
    prefix: /messaggi

events_tracker:
    resource: "../src/Controller/EventsTracker/"
    type: annotation
    prefix: /api/tracking

contact_us:
    resource: "../src/Controller/ContactUs/"
    type: annotation
    prefix: /api/contact-us

acquisition_sell_requests:
    resource: App\Controller\Base\SalesRequestsController
    type: annotation
    prefix: /acquisizione/richieste-vendita

immovisita_scheduled_visits:
    resource: App\Controller\Base\ImmovisitaController
    type: annotation
    prefix: /immovisita

searches:
    resource: App\Controller\Searches\Base\SearchesController
    type: annotation
    prefix: /ricerche/manuali

settings_remote_visit:
    resource: App\Controller\Settings\Base\RemoteVisitSettingsController
    type: annotation
    prefix: /impostazioni

settings_security:
    resource: App\Controller\Settings\Base\SecuritySettingsController
    type: annotation
    prefix: /impostazioni

settings_general:
    resource: App\Controller\Settings\Base\GeneralSettingsController
    type: annotation
    prefix: /impostazioni

getrix_multisend:
    resource: "../src/Controller/Multisend/Base"
    type: annotation
    prefix: /multinvio

getrix_multisend_api:
    resource: "../src/Controller/Multisend/API"
    type: annotation
    prefix: /api

settings_media:
    resource: App\Controller\Settings\Base\MediaSettingsController
    type: annotation
    prefix: /impostazioni

settings_headquarters:
    resource: App\Controller\Settings\Base\HeadquartersSettingsController
    type: annotation
    prefix: /impostazioni

menu_api:
    resource: "../src/Controller/Menu/API/"
    type: annotation
    prefix: /api

login_api:
    resource: "../src/Controller/Login/API/"
    type: annotation
    prefix: /api

zones_manager:
    resource: "../src/Controller/Zones/Base/"
    type: annotation

check_status:
    resource: "../src/Controller/CheckStatus/Base/"
    type: annotation
    prefix: /check-status

active_searches:
    resource: App\Controller\AgencyActiveSearches\Base\AgencyActiveSearchesController
    type: annotation
    prefix: /ricerche/attive

create_image_tag:
    resource: App\Controller\Property\API\CreateImageTagController
    type: annotation
    prefix: /api/properties

update_image_tag:
    resource: App\Controller\Property\API\UpdateImageTagController
    type: annotation
    prefix: /api/properties

list_threads:
    resource: App\Controller\Messaging\API\V2\ListThreadsController
    type: annotation
    prefix: /api/v2/messaging

get_thread:
    resource: App\Controller\Messaging\API\V2\GetThreadController
    type: annotation
    prefix: /api/v2/messaging

delete_multiple_threads:
    resource: App\Controller\Messaging\API\V2\DeleteMultipleThreadsController
    type: annotation
    prefix: /api/v2/messaging

delete_thread:
    resource: App\Controller\Messaging\API\V2\DeleteThreadController
    type: annotation
    prefix: /api/v2/messaging

create_message:
    resource: App\Controller\Messaging\API\V2\CreateMessageController
    type: annotation
    prefix: /api/v2/messaging

set_favourite_status:
    resource: App\Controller\Messaging\API\V2\SetFavouriteThreadController
    type: annotation
    prefix: /api/v2/messaging

set_not_favourite_status:
    resource: App\Controller\Messaging\API\V2\SetNotFavouriteThreadController
    type: annotation
    prefix: /api/v2/messaging

set_read_thread:
    resource: App\Controller\Messaging\API\V2\SetReadThreadController
    type: annotation
    prefix: /api/v2/messaging

set_read_multiple_threads:
    resource: App\Controller\Messaging\API\V2\SetReadMultipleThreadsController
    type: annotation
    prefix: /api/v2/messaging

set_read_all_threads:
    resource: App\Controller\Messaging\API\V2\SetReadAllThreadsController
    type: annotation
    prefix: /api/v2/messaging

archive_thread:
    resource: App\Controller\Messaging\API\V2\ArchiveThreadController
    type: annotation
    prefix: /api/v2/messaging

archive_multiple_threads:
    resource: App\Controller\Messaging\API\V2\ArchiveMultipleThreadsController
    type: annotation
    prefix: /api/v2/messaging

activate_thread:
    resource: App\Controller\Messaging\API\V2\ActivateThreadController
    type: annotation
    prefix: /api/v2/messaging

activate_multiple_threads:
    resource: App\Controller\Messaging\API\V2\ActivateMultipleThreadsController
    type: annotation
    prefix: /api/v2/messaging

count_threads:
    resource: App\Controller\Messaging\API\V2\CountThreadsController
    type: annotation
    prefix: /api/v2/messaging

inbox_threads:
    resource: App\Controller\Messaging\API\V2\GetInBoxThreadsController
    type: annotation
    prefix: /api/v2/messaging

set_answered_by_phone:
    resource: App\Controller\Messaging\API\V2\SetAnsweredByPhoneController
    type: annotation
    prefix: /api/v2/messaging

download_attachment:
    resource: App\Controller\Messaging\API\V2\DownloadAttachmentController
    type: annotation
    prefix: /api/v2/messaging

get_threads_stats:
    resource: App\Controller\Messaging\API\V2\GetThreadsStatsController
    type: annotation
    prefix: /api/v2/messaging

get_threads_parametric_stats:
    resource: App\Controller\Messaging\API\V2\GetThreadsParametricStatsController
    type: annotation
    prefix: /api/v2/messaging

create_planned_visit:
    resource: App\Controller\Messaging\API\V2\CreatePlannedVisitController
    type: annotation
    prefix: /api/v2/messaging

get_detailed_thread_stats:
    resource: App\Controller\Messaging\API\V2\GetDetailedThreadStatsController
    type: annotation
    prefix: /api/v2/messaging

getrix_notification_settings:
    resource: App\Controller\NotificationSettings\Base\NotificationSettingsController
    type: annotation
    prefix: /clienti/imposta-notifiche

report_insights:
    resource: App\Controller\ReportInsights\Base\ReportInsightsController
    type: annotation
    prefix: /report/insights

suggestions:
    resource: "../src/Controller/Suggestions/API/"
    type: annotation
    prefix: /api/suggestions
