import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { merge } from 'webpack-merge';
import common from './webpack.common.mjs';

const __filename_dev = fileURLToPath(import.meta.url);
const __dirname_dev = path.dirname(__filename_dev);

dotenv.config({
    path: path.join(__dirname_dev, '.env.it'),
});

/**
 * @type {import('webpack').Configuration}
 */
export default merge(common, {
    mode: 'development',
    devtool: 'inline-source-map',
});
