import { useEffect, useState } from "react"
import { IToursApiResponse } from "src/types/api"
import { getToursListApi } from "src/web-api"

export const useTourListFromApi = () => {
    const [tourListFromApi, setTourListFromApi] = useState<IToursApiResponse['data']>([])

    const updateTourListFromApi = () => {
        getToursListApi()
            .then(resp => {
                setTourListFromApi(resp.data)
            })
    }


    useEffect(() => {
        updateTourListFromApi()
    }, [])

    return {
        tourListFromApi,
        updateTourListFromApi
    }
}
