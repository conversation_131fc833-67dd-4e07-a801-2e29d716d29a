services:
  mls:
    search:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    contacts:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    bookmarks:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    collaborations:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  omi:
    lookup:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    evaluate:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gtxpfbe.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  lookup:
    provider:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["immobiliare-lookup.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  immobiliare:
    autocomplete:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["immobiliare-autocomplete.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  geography:
    geography:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["hhvm-be.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  getrix:
    agenti:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-agenzie.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 80
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    agenzie:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-agenzie.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 80
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    whitelabel_login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-whitelabel-login.dev.loc", 80, "/server.php"]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    login:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gestionale-login.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    estimates:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["gestionale-valutazioni.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 80
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    getrix:
      annunci:
        socket:
          className: \Thrift\Transport\THttpClient
          args: ["getrix-annunci.dev.loc", 80, "/server.php"]
          #xdebug: on
          #xdebug_port: 80
        transport:
          className: \Thrift\Transport\TBufferedTransport
          args: [1024, 1024]
        protocol:
          className: \Thrift\Protocol\TBinaryProtocolAccelerated
          args: []
      acquisizione:
        socket:
          className: \Thrift\Transport\THttpClient
          args: ["getrix-acquisizione.dev.loc", 80, "/server.php"]
          #xdebug: on
          #xdebug_port: 80
        transport:
          className: \Thrift\Transport\TBufferedTransport
          args: [1024, 1024]
        protocol:
          className: \Thrift\Protocol\TBinaryProtocolAccelerated
          args: []
      progetti:
        socket:
          className: \Thrift\Transport\THttpClient
          args: ["getrix-annunci.dev.loc", 80, "/server.php"]
          #xdebug: on
          #xdebug_port: 80
        transport:
          className: \Thrift\Transport\TBufferedTransport
          args: [1024, 1024]
        protocol:
          className: \Thrift\Protocol\TBinaryProtocolAccelerated
          args: []

  media:
    immagini:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["media-immagini.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

  trace:
    trace:
      socket:
        className: \Thrift\Transport\TSocket
        args: ["hhvm-be.dev.loc", 9090]
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
        mux: false

  immobiliarepro:
      agenzie:
        socket:
          className: \Thrift\Transport\THttpClient
          args: ["immopro-agenzie.dev.loc", 80, "/server.php"]
          #xdebug: on
          #xdebug_port: 8080
        transport:
          className: \Thrift\Transport\TBufferedTransport
          args: [1024, 1024]
        protocol:
          className: \Thrift\Protocol\TBinaryProtocolAccelerated
          args: []

  fatture:
    getrix:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["getrix-agenzie.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []
    immobiliarepro:
      socket:
        className: \Thrift\Transport\THttpClient
        args: ["immopro-agenzie.dev.loc", 80, "/server.php"]
        #xdebug: on
        #xdebug_port: 8080
      transport:
        className: \Thrift\Transport\TBufferedTransport
        args: [1024, 1024]
      protocol:
        className: \Thrift\Protocol\TBinaryProtocolAccelerated
        args: []

