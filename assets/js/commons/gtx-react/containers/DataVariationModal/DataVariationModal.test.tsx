/* eslint-disable testing-library/no-node-access */
import { describe, expect, it } from 'vitest';
import { DataVariationModal } from './DataVariationModal';
import { render, screen } from '#tests/react/testing-library-enhanced';
import { createQueryClient } from 'lib/queryClient';
import { NotifyProvider } from '@gx-design/snackbar';
import { QueryClientProvider } from '@tanstack/react-query';
import { server } from '#tests/vitest/setup';
import { http, HttpResponse } from 'msw';

const renderDataVariationModal = (propertyId) => {
    return render(<DataVariationModal propertyId={propertyId} />, {
        wrapper: ({ children }) => {
            const queryClient = createQueryClient();
            return (
                <NotifyProvider>
                    <QueryClientProvider client={queryClient}>
                        {children}
                    </QueryClientProvider>
                </NotifyProvider>
            );
        },
    });
};

describe('DataVariationModal', () => {
    it('should render without crashing', async () => {
        const propertyId = '12345';
        renderDataVariationModal(propertyId);

        const modalContent = await screen.findByTestId('data-variation-modal');
        expect(modalContent).toBeInTheDocument();
    });

    it('should show error alert when API call fails', async () => {
        server.use(
            http.get('/v2/lista-annunci/get-data-changes', () =>
                HttpResponse.error()
            )
        );

        const propertyId = '12345';
        renderDataVariationModal(propertyId);

        const errorMessage = await screen.findByText(
            'label.error_durin_operation'
        );
        expect(errorMessage).toBeInTheDocument();
    });

    it('should display data variation dates and changes', async () => {
        const propertyId = '12345';
        renderDataVariationModal(propertyId);

        await screen.findByTestId('data-variation-modal');

        const firstDate = await screen.findByText('02 set 2024');
        expect(firstDate).toBeInTheDocument();

        const secondDate = await screen.findByText('11 lug 2024');
        expect(secondDate).toBeInTheDocument();

        // Check if the changes are rendered correctly
        const actions = await screen.findAllByText('Immobile modificato');
        expect(actions).toHaveLength(6);

        const times = await screen.findAllByText(
            (text) => text === '18:09' || text === '16:43'
        );
        expect(times).toHaveLength(2);

        // Check if agent names are rendered
        const agentNames = await screen.findAllByText('Francesco Bianchino');
        expect(agentNames).toHaveLength(9);
    });

    it('should render price variations correctly', async () => {
        const propertyId = '12345';
        renderDataVariationModal(propertyId);

        await screen.findByTestId('data-variation-modal');

        // Check for the price changes action texts
        const priceChangeActions = await screen.findAllByText(
            'Prezzo vendita variato'
        );

        expect(priceChangeActions).toHaveLength(2);

        const oldPrices = await screen.findAllByTestId('old-price');

        // Check for the first price variation (with old price)
        expect(oldPrices[0]).toHaveTextContent('€ 450.000');

        // Check the second price variation (with old price)
        expect(oldPrices[1]).toHaveTextContent('€ 50.000');

        // Verify that the price variation has the correct direction class
        const firstPriceVariationElement = oldPrices[0]?.closest(
            '.data-variation-list__contentPriceVariation'
        );
        expect(firstPriceVariationElement).toHaveClass('down');

        const secondPriceVariationElement = oldPrices[1]?.closest(
            '.data-variation-list__contentPriceVariation'
        );
        expect(secondPriceVariationElement).toHaveClass('up');

        // Check for the new price element (property insertion)
        const newPrice = await screen.findByTestId('new-price');
        expect(newPrice).toHaveTextContent(/€ 450.000/);
    });
});
