<?php

declare(strict_types=1);

namespace Tests\Unit\DataProvider\Property\ApiGtx\DataMapper;

use App\DataProvider\Property\ApiGtx\DataMapper\CoWorkingTimetableDataMapper;
use App\Model\Property\CoWorkingTimetable;
use PHPUnit\Framework\TestCase;

class CoWorkingTimetableDataMapperTest extends TestCase
{
    private CoWorkingTimetableDataMapper $mapper;

    protected function setUp(): void
    {
        $this->mapper = new CoWorkingTimetableDataMapper();
    }

    public function testMapWithCompleteData(): void
    {
        $data = [
            'id' => 333,
            'dayOfWeek' => 1,
            'openTime' => '09:00',
            'closeTime' => '18:00',
            'closed' => false,
        ];

        $result = $this->mapper->map($data);

        $this->assertInstanceOf(CoWorkingTimetable::class, $result);
        $this->assertSame(1, $result->getDayOfWeek());
        $this->assertSame('09:00', $result->getOpenTime());
        $this->assertSame('18:00', $result->getCloseTime());
        $this->assertFalse($result->isClosed());
    }

    public function testMapWithClosedDay(): void
    {
        $data = [
            'id' => 333,
            'dayOfWeek' => 0,
            'openTime' => null,
            'closeTime' => null,
            'closed' => true,
        ];

        $result = $this->mapper->map($data);

        $this->assertInstanceOf(CoWorkingTimetable::class, $result);
        $this->assertSame(0, $result->getDayOfWeek());
        $this->assertNull($result->getOpenTime());
        $this->assertNull($result->getCloseTime());
        $this->assertTrue($result->isClosed());
    }

    public function testMapWithMissingOptionalFields(): void
    {
        $data = [
            'id' => 333,
            'dayOfWeek' => 0,
        ];

        $result = $this->mapper->map($data);

        $this->assertInstanceOf(CoWorkingTimetable::class, $result);
        $this->assertSame(0, $result->getDayOfWeek());
        $this->assertNull($result->getOpenTime());
        $this->assertNull($result->getCloseTime());
        $this->assertFalse($result->isClosed());
    }

    public function testMapWithEmptyStringValues(): void
    {
        $data = [
            'id' => 333,
            'dayOfWeek' => 3,
            'openTime' => '',
            'closeTime' => '',
            'closed' => false,
        ];

        $result = $this->mapper->map($data);

        $this->assertInstanceOf(CoWorkingTimetable::class, $result);
        $this->assertSame(3, $result->getDayOfWeek());
        $this->assertNull($result->getOpenTime());
        $this->assertNull($result->getCloseTime());
        $this->assertFalse($result->isClosed());
    }

    public function testMapWithDifferentTimeFormats(): void
    {
        $data = [
            'id' => 333,
            'dayOfWeek' => 0,
            'openTime' => '08:30:00',
            'closeTime' => '17:45:30',
            'closed' => false,
        ];

        $result = $this->mapper->map($data);

        $this->assertInstanceOf(CoWorkingTimetable::class, $result);
        $this->assertSame(0, $result->getDayOfWeek());
        $this->assertSame('08:30:00', $result->getOpenTime());
        $this->assertSame('17:45:30', $result->getCloseTime());
        $this->assertFalse($result->isClosed());
    }

    public function testMapWithBooleanTrueIsClosed(): void
    {
        $data = [
            'id' => 333,
            'dayOfWeek' => 0,
            'openTime' => '08:30:00',
            'closeTime' => '17:45:30',
            'closed' => true,
        ];

        $result = $this->mapper->map($data);

        $this->assertInstanceOf(CoWorkingTimetable::class, $result);
        $this->assertSame(0, $result->getDayOfWeek());
        $this->assertSame('08:30:00', $result->getOpenTime());
        $this->assertSame('17:45:30', $result->getCloseTime());
        $this->assertTrue($result->isClosed());
    }

    public function testMapWithNumericBooleanValues(): void
    {
        $data = [
            'id' => 333,
            'dayOfWeek' => 0,
            'openTime' => '08:30:00',
            'closeTime' => '17:45:30',
            'closed' => 0,
        ];

        $result = $this->mapper->map($data);

        $this->assertInstanceOf(CoWorkingTimetable::class, $result);
        $this->assertSame(0, $result->getDayOfWeek());
        $this->assertSame('08:30:00', $result->getOpenTime());
        $this->assertSame('17:45:30', $result->getCloseTime());
        $this->assertFalse($result->isClosed());
    }

    public function testMapWithStringBooleanValues(): void
    {
        $data = [
            'id' => 333,
            'dayOfWeek' => 0,
            'openTime' => '08:30:00',
            'closeTime' => '17:45:30',
            'closed' => '1',
        ];

        $result = $this->mapper->map($data);

        $this->assertInstanceOf(CoWorkingTimetable::class, $result);
        $this->assertSame(0, $result->getDayOfWeek());
        $this->assertSame('08:30:00', $result->getOpenTime());
        $this->assertSame('17:45:30', $result->getCloseTime());
        $this->assertTrue($result->isClosed());
    }
}
