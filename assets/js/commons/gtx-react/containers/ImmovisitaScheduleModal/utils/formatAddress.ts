import { IGeographyInfo } from '../ChangeRealEstate/types';
import { GeographyInformation, Properties } from '../types/realEstate';

export const formatAddress = (
    geographyInformation: GeographyInformation | IGeographyInfo
) => {
    let out = '';

    if (!geographyInformation) return out;

    if (geographyInformation?.city) {
        out += geographyInformation?.city?.name;
        out += ` (${geographyInformation?.city?.province?.id})`;
    }

    if (geographyInformation?.address) {
        out += ' - ';
        out += geographyInformation?.address.street;

        if (geographyInformation?.address?.number) {
            out += ', ';
            out += geographyInformation?.address.number;
        }
    }

    return out;
};
