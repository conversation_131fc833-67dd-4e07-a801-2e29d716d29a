import * as React from 'react'
import { useCustomEventsManager } from 'src/hooks/useCustomEventsManager'
import { useTourLogicManager } from 'src/hooks/useTourLogicManager'
import { useToursContext } from 'src/hooks/useToursContext'
import { OnboardingCard, OnboardingCardProps } from '@gx-design/onboarding'
import { trans } from '@pepita/babelfish'
import { useIsScreenBiggerThan } from 'src/hooks/useIsScreenBiggerThan'
import { useApplyCssClassOnChange } from 'src/hooks/useApplyCssClassOnChange'
import { useUpdateToursOnListFromApiChange } from 'src/hooks/useUpdateToursOnListFromApiChange'

export const TourManager = () => {

    const positionUpdate = React.useRef(()=> {})

    // useBuildAppToursOnDomReady()

    useUpdateToursOnListFromApiChange()

    const { showTour, stepIndex } = useToursContext()
    const { currentTourStep, currentTour, onClose, onNextClick, onPrevClick } = useTourLogicManager()
    const isDesktop = useIsScreenBiggerThan(1280)

    useCustomEventsManager(positionUpdate, isDesktop)
    useApplyCssClassOnChange(currentTourStep, isDesktop, currentTour)

    const TRANSLATION_LABELS: OnboardingCardProps['labels'] = {
        back: trans('btn.label.back'),
        forward: trans('btn.label.forward'),
        start: trans('label.immovisita.start'),
        stepSeparator: trans('label.out_of_2')
    }

    return (
        <>
            {(showTour && currentTourStep && currentTour && isDesktop) && (
                <OnboardingCard
                    anchorElement={currentTourStep.el}
                    title={currentTourStep.title && trans(currentTourStep.title)}
                    body={<div dangerouslySetInnerHTML={{__html: trans(currentTourStep.body)}}></div> }
                    image={currentTourStep.imageUrl}
                    indicatorStyle={currentTourStep.indicatorStyle}
                    totalSteps={currentTour?.steps.length}
                    currentStep={ stepIndex + 1 }
                    position={currentTourStep.position}
                    onCloseClick={onClose}
                    onNextStepClick={onNextClick }
                    onPrevStepClick={onPrevClick}
                    positionHandlerRef={posHandler => positionUpdate.current = posHandler}
                    labels={TRANSLATION_LABELS}
                />
            )}

        </>
    )
}
