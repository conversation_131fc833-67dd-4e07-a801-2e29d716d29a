import { Colorpicker } from '../../../js/commons/gtx-react/components/Colorpicker/Colorpicker';
import type { Meta, StoryObj } from '@storybook/react';

/** Colorpicker is a dropdown component used to pick the desired color. */
const meta: Meta<typeof Colorpicker> = {
    title: 'Components/Colorpicker',
    component: Colorpicker,
    parameters: {
        componentSubtitle: 'Component',
        componentTitle: 'Colorpicker',
        componentSlug: 'colorpicker',
        componentImport: 'Colorpicker',
        packageName: 'gtx-react/components/Colorpicker/Colorpicker',
    },
    tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof Colorpicker>;

export const DefaultColorpicker: Story = {
    render: (args) => {
        return (
            <Colorpicker
                onColorSelect={(color) => console.log('Saved color:', color)}
                colors={['red', 'blue', 'green']}
                colorDefault={'red'}
            />
        );
    },
};

// export const WithCounterAttachmentTag: Story = {
//     render: (args) => {
//         return <AttachmentTag type="video" count={3} />;
//     },
// };

DefaultColorpicker.storyName = 'Default';
