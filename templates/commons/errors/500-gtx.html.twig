<!doctype html>
<html lang="it">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
    <title>{% if title %}{{ title }}{% else %}Servizio momentaneamente non disponibile{% endif %} - Getrix</title>
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600" rel="stylesheet">
    <style>
        /* RESET */
        html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline; box-sizing: border-box;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block}body{line-height:1}ol,ul{list-style:none}blockquote,q{quotes:none}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none}table{border-collapse:collapse;border-spacing:0}
        /* SORRY */
        body {
            background: #f2f2f2;
            font-family: 'Open Sans', Arial, sans-serif;
        }

        .sorry-box {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            padding: 30px;
            border-radius: 2px;
            background: #ffffff;
            text-align: center;
        }

        .logo {
            width: 132px;
            height: 39px;
            margin-bottom: 30px;
        }

        h1 {
            margin-bottom: 16px;
            font-weight: 600;
            font-size: 20px;
            line-height: 1.2;
            color: #428CC6;
        }

        p {
            margin-bottom: 32px;
            font-size: 16px;
            color: #333333;
            line-height: 24px;
        }

        .btn {
            width: 100%;
            display: inline-block;
            padding: 13px 20px;
            background: #428CC6;
            border-radius: 2px;
            font-size: 14px;
            color: #FFFFFF;
            text-decoration: none;
        }

        @media screen and (min-width: 768px) {
            .sorry-box {
                margin: 0;
                width: 614px;
                padding: 60px 30px;
            }

            h1 {
                font-size: 22px;
            }

            .btn {
                width: auto;
            }
        }

        @media screen and (max-height: 567px) {
            .sorry-box {
                position: static;
                top: 0;
                left: 0;
                transform: translate(0, 0);
                margin: 30px auto;
            }
        }
    </style>
</head>
<body>

<div class="sorry-box">
    <img class="logo" src="data:image/png;base64,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">
    <h1>
        {% if title %}
            {{ title }}
        {% else %}
            Servizio momentaneamente non disponibile
        {% endif %}
    </h1>
    <p>
        {% if message %}
            {{ message }}
        {% else %}
            Gentile cliente, ci scusiamo per il disagio arrecato.
            Stiamo lavorando al fine di risolvere il problema, si prega di ritentare a breve.
        {% endif %}
    </p>
    <a class="btn" href="{{ targetUrl }}">{{ 'label.retry'|trans({}, gtxConstants.DOMAIN_CMS) }}</a>
</div>

</body>
</html>



