/**
 *  Returns the route matching the current route
 * @param routes - The routes to check
 * @param currentRoute - The current route
 * @returns The matching route (or null if no match)
 */
export const getRouteMatchingByRouteKeys = (routes: string[], currentRoute: string) => {
  const currentSegments = currentRoute.split('/');
  let matchingRoute = null;

  for (const route of routes) {
    const patternSegments = route.split('/');
    const isWildcard = patternSegments.includes('*');

    // Function to match segments of the pattern with the current route segments
    const matchSegments = (patternSegments: string[], currentSegments: string[]): boolean => {
      // If there are no pattern segments left, check if there are no current segments left
      if (patternSegments.length === 0) return currentSegments.length === 0;
      // If the first pattern segment is a wildcard, it's a match
      if (patternSegments[0] === '*') return true;
      // If the first pattern segment does not match the first current segment and is not a dynamic segment, it's not a match
      if (patternSegments[0] !== currentSegments[0] && !patternSegments[0].startsWith('{'))
        return false;
      // Recursively check the remaining segments
      return matchSegments(patternSegments.slice(1), currentSegments.slice(1));
    };

    if (isWildcard && matchSegments(patternSegments, currentSegments)) {
      return (matchingRoute = route);
    }

    if (patternSegments.length === currentSegments.length) {
      let isMatch = true;

      for (let i = 0; i < patternSegments.length; i++) {
        if (patternSegments[i] !== currentSegments[i] && !patternSegments[i].startsWith('{')) {
          isMatch = false;
          break;
        }
      }

      if (isMatch) {
        matchingRoute = route;
        break;
      }
    }
  }

  return matchingRoute;
};
