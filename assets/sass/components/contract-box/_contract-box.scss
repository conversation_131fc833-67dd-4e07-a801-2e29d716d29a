@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$contract-box: contract-box;
$box-visibility: box-visibility;
$gutter-contract-box: space(md);
$gutter-contract-box-big: space(xl);
$contract-cell-width: 22rem;
$contract-cell-width-small: 15rem;
$visits-width: ($gx-unit-size * 12);
$visits-margin: space(4xl);

$services-box: services-box;

.#{$contract-box} {
  padding: $gutter-contract-box-big $gutter-contract-box 0;
  margin-bottom: space(lg);
  border: 0.1rem solid color(border-main);
  border-width: 0.1rem 0;
  background: color(background-main);

  &--maxWidth {
    max-width: 96rem;
  }

  &-wrap {
    background-color: color(background-alt);
  }

  &__logo {
    background-repeat: no-repeat;
    background-size: cover;
    text-indent: -999rem;
    height: 2.8rem;

    img {
      max-width: 20rem;
      height: 2.8rem;
      object-fit: contain;
      object-position: left top;
    }
  }

  &__title {
    margin-bottom: space(lg);

    &Small {
      width: 100%;
      margin-bottom: $gutter-contract-box;
    }

    &--quality {
      display: flex;

      .data-item {
        font-weight: 400;
        margin-left: space(lg);
        display: flex;
        align-items: baseline;
        > * + * {
          margin-left: space(sm);
        }
      }

      .num-label {
        display: inline-flex;
        align-items: center;

        & > span {
          color: color(content-medium);
          text-transform: uppercase;
        }
      }
    }
  }

  &__row {
    &:last-child {
      padding-bottom: 0;
    }

    &:not(last-child) {
      padding-bottom: $gutter-contract-box-big;
    }

    & + .#{$contract-box}__row {
      padding-top: $gutter-contract-box-big;
      border-top: 0.1rem solid color(border-main);
    }

    &--fullWidth {
      margin-left: -$gutter-contract-box;
      margin-right: -$gutter-contract-box;
      padding-left: $gutter-contract-box;
      padding-right: $gutter-contract-box;
    }
  }

  &__portal {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;

    &Title {
      width: 100%;
    }

    &Field {
      width: calc(50% - #{$gx-unit-size});
    }

    &Action {
      width: 100%;
      margin-bottom: $gutter-contract-box-big;
      margin-top: $gutter-contract-box;

      button {
        width: 100%;
      }
    }
  }

  &__premium,
  &__abroad,
  &__agencyProperties,
  &__secret {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: space(md);

    &Field {
      width: calc(50% - #{$gx-unit-size});
      margin-bottom: $gutter-contract-box;
    }

    &Action {
      width: 100%;

      button {
        width: 100%;
      }
    }
  }

  &__visibility {
    &Row {
      display: flex;
      justify-content: space-between;
      margin-bottom: $gutter-contract-box;
      align-items: center;

      > div:first-child {
        min-width: ($gx-unit-size * 10);
      }

      a {
        font-weight: 600;
      }

      &End {
        width: ($gx-unit-size * 10);
        text-align: right;
      }
    }

    &-vetrina,
    &-star,
    &-top {
      margin-right: space(sm);
      font-weight: 600;
      color: color(background-main);
    }

    &-vetrina {
      background-color: #3698dc;
      border-color: #3698dc;
    }

    &-star {
      background-color: #ff8c00;
      border-color: #ff8c00;
    }

    &-top {
      background-color: #1ebc9d;
      border-color: #1ebc9d;
    }
  }

  &__wallet {
    padding: $gutter-contract-box;
    margin-top: $gutter-contract-box;
    border: 0.1rem solid color(border-main);

    &Action {
      width: 100%;

      button {
        width: 100%;
        margin-top: $gutter-contract-box;
      }
    }
  }

  &__agency {
    > span {
      display: block;
      margin-bottom: $gutter-contract-box;
    }

    &Action {
      width: 100%;

      button {
        width: 100%;
        margin-top: $gutter-contract-box;
      }
    }
  }

  &__statistics {
    padding-top: $gutter-contract-box-big;
    padding-left: $gutter-contract-box;
    padding-right: $gutter-contract-box;
    margin-top: $gutter-contract-box-big;
    margin-left: -$gutter-contract-box;
    margin-right: -$gutter-contract-box;
    border-top: 0.1rem solid color(border-main);

    &Head {
      display: flex;
      align-items: center;
      margin-bottom: space(xl);

      > div + div {
        margin-left: space(lg);
      }

      .styled-select {
        width: auto;
      }
    }

    &Content {
      width: 100%;

      .skeleton-contact-stats {
        width: 100%;
        max-width: 100px;
        height: 16px;
        background-image: linear-gradient(
          90deg,
          color(background-alt) 0%,
          color(background-alt) 20%,
          color(background-main) 20%,
          color(background-main) calc(20% + 0.1rem),
          color(background-alt) calc(20% + 0.1rem),
          color(background-alt) 100%
        );
      }
    }

    &Visits {
      margin-bottom: space(2xl);

      &Title {
        margin-bottom: space(md);

        span {
          font-weight: normal;
        }
      }
    }

    &Requests {
      &Title {
        margin-bottom: space(md);
      }

      ul {
        list-style: none;
        padding: 0;
        margin-bottom: space(sm);
      }

      li {
        padding: space(md) 0;
        display: flex;

        & + li {
          border-top: 0.1rem solid color(border-main);
        }

        > div {
          width: #{$contract-cell-width + ($contract-cell-width-small * 2) - $visits-width - $visits-margin};
          width: 34.4rem;

          a {
            margin-left: space(sm);
            font-weight: 600;
          }
        }
      }
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    padding: $gutter-contract-box-big;
    margin-bottom: $gutter-contract-box-big;
    border-width: 0.1rem;

    &__row {
      &--fullWidth {
        margin-left: -$gutter-contract-box-big;
        margin-right: -$gutter-contract-box-big;
        padding-left: $gutter-contract-box-big;
        padding-right: $gutter-contract-box-big;
      }
    }

    &__statistics {
      padding-left: $gutter-contract-box-big;
      padding-right: $gutter-contract-box-big;
      margin-left: -$gutter-contract-box-big;
      margin-right: -$gutter-contract-box-big;
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    border-radius: radius(sm);

    &-wrap {
      padding: space(xl);
    }

    &__logo {
      margin-bottom: 0;
    }

    &__row {
      &Double {
        display: flex;
        justify-content: space-between;
      }

      &--agencyStatistics {
        display: flex;
        padding: 0 !important;
        margin-bottom: -$gutter-contract-box-big;

        .#{$contract-box}__agencyDesc {
          margin-bottom: $gutter-contract-box;
        }

        .#{$box-visibility} {
          &-wrapper {
            flex-direction: column;
            margin-bottom: $gutter-contract-box-big;
          }

          & + .#{$box-visibility} {
            border-top: 0.1rem solid color(border-main);
            border-left: none;
          }
        }
      }
    }

    &__portal {
      justify-content: flex-start;

      &Title {
        width: $contract-cell-width;
      }

      &Field {
        width: $contract-cell-width-small;
      }

      &Action {
        width: auto;
        margin-bottom: 0;
        margin-top: 0;
        margin-left: auto;

        button {
          width: auto;
        }
      }
    }

    &__title {
      &Small {
        width: auto;
      }
    }

    &__premium,
    &__abroad,
    &__agencyProperties,
    &__secret {
      justify-content: flex-start;
      align-items: center;

      & .#{$contract-box}__titleSmall {
        width: $contract-cell-width;
        margin-bottom: 0;
      }

      &Field {
        width: $contract-cell-width-small;
        margin-bottom: 0;
      }

      &Action {
        width: auto;
        margin-left: auto;
        text-align: right;

        button {
          width: auto;
        }
      }
    }

    &__visibility {
      width: calc(100% - 30rem);

      &Row {
        justify-content: flex-start;

        > div:first-child {
          width: $contract-cell-width;
        }

        > span {
          width: $contract-cell-width-small;
        }
      }
    }

    &__wallet {
      width: 30rem;
      margin-top: 0;
    }

    &__agency {
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      width: percentage(1/3);
      min-width: 40rem;
      padding: $gutter-contract-box-big $gutter-contract-box-big 0;
      border-right: 0.1rem solid color(border-main);

      & .#{$contract-box}__title {
        margin-bottom: $gutter-contract-box;
      }

      > span {
        margin-bottom: $gutter-contract-box-big;
      }

      &Action {
        margin-top: $gutter-contract-box-big;

        button {
          width: auto;
          margin-top: 0;
        }
      }

      &--fullWidth {
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        padding: 0;
        border: none;

        > span {
          margin-bottom: 0;
        }

        .#{$contract-box}__agencyDesc {
          width: calc(52rem - #{$gx-unit-size * 3});
          margin-right: space(lg);
          margin-bottom: space(md);
        }

        .#{$contract-box}__agencyAction {
          width: auto;
          margin-top: 0;
          margin-left: auto;

          button {
            width: auto;
            margin-top: 0;
          }
        }
      }
    }

    &__statistics {
      width: 100%;
      padding: $gutter-contract-box-big $gutter-contract-box-big 0;
      margin-top: 0;
      margin-left: 0;
      margin-right: 0;
      border-top: none;

      &Head {
        width: 100%;
      }

      &Content {
        display: flex;
      }

      &Visits {
        width: $visits-width;
        margin-right: $visits-margin;
        margin-bottom: 0;
      }

      &Requests {
        flex-grow: 1;
      }
    }
  }
}

//Visibility-box
.#{$box-visibility} {
  padding: $gutter-contract-box-big $gutter-contract-box $gutter-contract-box;

  &-wrapper {
    margin-top: space(md);
    border: 0.1rem solid color(border-main);
  }

  & + & {
    border-top: 0.1rem solid color(border-main);
  }

  &__title {
    margin-bottom: space(xs);
  }

  &__item {
    padding: $gutter-contract-box;

    & + .#{$box-visibility}__item {
      border-top: 0.1rem solid color(border-main);
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    &-wrapper {
      display: flex;
    }

    & + & {
      border-top: none;
      border-left: 0.1rem solid color(border-main);
    }
  }
}

//Services box

.#{$services-box} {
  max-width: 96rem;

  &-wrap {
    padding: $gutter-contract-box-big $gutter-contract-box $gutter-contract-box;
    background-color: color(background-main);
  }

  &__title {
    margin-bottom: $gutter-contract-box-big;
  }

  &__item {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-bottom: space(lg);
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);

    &Head {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $gutter-contract-box;
    }

    &Content {
      display: flex;
      flex-direction: column;
      height: 100%;
      padding: space(lg);
    }

    &Info {
      display: flex;

      > div + div {
        margin-left: space(lg);
      }
    }

    &Action {
      display: flex;
      justify-content: flex-end;
      margin-top: auto;

      > * {
        margin-top: space(md);
      }
    }

    &Foot {
      padding: $gutter-contract-box space(lg);
      background-color: color(background-alt);
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    &-wrap {
      padding-left: $gutter-contract-box-big;
      padding-right: $gutter-contract-box-big;
    }

    &__list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    &__item {
      width: calc(50% - #{$gutter-contract-box});
      margin-bottom: $gutter-contract-box-big;
    }
  }
}
