import { useEffect } from 'react'
import { useToursContext } from './useToursContext'

export const useBuildAppToursOnDomReady = () => {

    const { updateValidTourFromDom } = useToursContext()

    useEffect(() => {
        const onLoadHandle = () => {
            updateValidTourFromDom()
        }
        document.addEventListener('DOMContentLoaded', onLoadHandle)

        return () => {
            document.removeEventListener('DOMContentLoaded', onLoadHandle)
        }
    }, [])



}
