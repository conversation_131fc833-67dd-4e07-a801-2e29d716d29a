@use '../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;
@use '@gx-design/utilities/styles' as *;

.gx-navigation-sidemenu {
  height: 100vh;
  background-color: color(background-main);
  position: fixed;
  left: 0;
  top: 0;
  color: color(content-selectable);
  user-select: none;
  @include z-index(navigation);

  @include media('screen', '<gx-medium') {
    top: $gx-new-menu-header-height;
    height: calc(100% - #{$gx-new-menu-header-height});
  }

  .multi-agency--switch & {
    top: $gx-multi-agency-banner-height;
    height: calc(100vh - #{$gx-multi-agency-banner-height});

    &__rail {
      top: $gx-multi-agency-banner-height;
      height: calc(100% - #{$gx-multi-agency-banner-height});
    }
  }

  &__fixedButton {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__backdrop {
    position: fixed;
    background-color: rgba(color(background-reversed, true), 0.5);
    top: $gx-new-menu-header-height;
    width: 100%;
    height: calc(100% - #{$gx-new-menu-header-height});
    left: 0;
    @include z-index(navigation, -3);
  }

  &__mainItems {
    position: relative;
    width: 100%;
    height: 100%;
    margin: auto;
    overflow-y: auto;
    scrollbar-width: none;
    //Prevents body scrolling if the mouse is over this element when it is scrollable
    overscroll-behavior: none;
    border-bottom: 0.1rem solid color(border-main);
    transition: border 0.3s ease-in-out;

    ul {
      padding-top: space(md);
    }

    @include media('screen', '<gx-medium') {
      border-bottom: none;

      ul {
        padding-top: 0;
      }
    }

    &.end-scroll,
    &.not-scrollable {
      border-bottom-color: transparent;
    }

    li + li {
      margin-top: space(md);
    }

    @include media('screen', '<gx-medium') {
      li + li {
        margin-top: space(sm);
      }
    }

    // @include media('screen', '>=gx-medium') {
    //   &:not(.not-scrollable) {
    //     &::before {
    //       content: '';
    //       position: sticky;
    //       z-index: 1;
    //       left: space(sm);
    //       top: 0;
    //       display: block;
    //       width: calc(100% - #{space(md)});
    //       height: 4rem;
    //       background: #ffffff;
    //       background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 70%);
    //       opacity: 1;
    //       transition: all 0.3s ease-in-out;
    //     }

    //     &::after {
    //       content: '';
    //       position: sticky;
    //       left: space(sm);
    //       bottom: 0;
    //       display: block;
    //       width: calc(100% - #{space(md)});
    //       height: 4rem;
    //       background: #ffffff;
    //       background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 70%);
    //       border-bottom: 0.1rem solid color(border-main);
    //       opacity: 1;
    //       transition: all 0.3s ease-in-out;
    //     }

    //     &.end-scroll {
    //       &::after {
    //         opacity: 0;
    //         z-index: -1;
    //       }
    //     }

    //     &.start-scroll {
    //       &::before {
    //         opacity: 0;
    //         z-index: -1;
    //       }
    //     }
    //   }
    // }
  }

  &__secondaryItems {
    padding: 2rem 0;
    width: 100%;

    .gx-navigation-sidemenu__item {
      text-align: left;

      &Label {
        font-size: 1.2rem;
      }
    }

    @include media('screen', '<gx-medium') {
      li + li {
        margin-top: space(sm);
      }
    }
  }

  &__hamburger {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: space(lg);
  }

  &__rail {
    width: $gx-new-menu-width;
    position: fixed;
    left: 0;
    top: 0;
    @include z-index(navigation, -1);
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: color(background-main);
    border-right: 0.1rem solid color(border-main);
    transition: 0.25s cubic-bezier(0.29, 1.13, 0.91, 0.88);
    overflow-y: auto;
    overflow-x: hidden;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */

    &::-webkit-scrollbar {
      display: none;
    }

    &Content {
      flex: 1;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      align-items: center;
      height: 100vh;
    }

    @include media('screen', '<gx-medium') {
      width: 26rem;
      top: $gx-new-menu-header-height;
      height: calc(100% - #{$gx-new-menu-header-height});
      padding: space(lg) space(md);
      transform: translateX(-100%);
      transition: 0.25s cubic-bezier(0.29, 1.13, 0.91, 0.88);

      &.is--open {
        transform: translateX(0);
        transition: 0.25s cubic-bezier(0.29, 1.13, 0.91, 0.88);
      }

      &Content {
        height: auto;
      }
    }
  }

  &__item {
    text-align: center;
    cursor: pointer;
    display: flex;
    @include typography(body-tiny);
    color: color(content-selectable);
    border-radius: radius(md);
    position: relative;

    // Message counter
    .gx-notification-badge {
      position: absolute;
      left: auto;
      right: 0;
      top: 0;
      transform: translate(50%, -50%);
      width: auto;

      span {
        color: color(content-notification);
      }

      @include media('screen', '<gx-medium') {
        left: auto;
        right: 0;
        top: 0;
        padding: 0;
        width: 1rem;
        height: 1rem;
        min-width: auto;
        border-radius: 100%;
        transform: translate(50%, -50%);

        span {
          text-indent: -999rem;
        }
      }
    }

    & > div {
      width: 100%;
      display: flex;
      align-items: center;
      flex-direction: column;

      @include media('screen', '<gx-medium') {
        width: auto;
        flex-direction: row;
      }
    }

    &More {
      color: color(content-selected);
      font-size: 2.4rem;
      flex-shrink: 0;
    }

    &:hover,
    &:focus {
      text-decoration: none;

      &:not(.gx-navigation-sidemenu__item--active, .gx-navigation-sidemenu__item--profile) {
        color: inherit;

        .gx-navigation-sidemenu__itemIcon {
          background-color: color(background-alt);
        }
      }
    }

    &--profile {
      margin-top: space(md);

      .gx-navigation-sidemenu__itemIcon {
        height: 4rem;

        @include media('screen', '<gx-medium') {
          height: auto;
        }
      }

      .gx-icon {
        font-size: 4rem !important;

        @include media('screen', '<gx-medium') {
          font-size: 2.4rem !important;
        }
      }
    }

    &--active {
      .gx-navigation-sidemenu__itemIcon {
        background-color: color(background-selected);
        color: color(content-selected);
      }

      > div > span {
        color: color(content-selected);
        font-weight: 600;
      }
    }

    &--subMenuOpen {
      .gx-navigation-sidemenu__itemIcon {
        background-color: color(background-alt);
      }
    }

    &Icon {
      width: 4rem;
      height: 3.2rem;
      display: flex;
      font-size: 2.4rem;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      color: color(content-selectable);
      border-radius: radius(md);
      position: relative;
      flex-shrink: 0;

      @include media('screen', '<gx-medium') {
        margin: 0;
        width: auto;
        height: auto;
        margin-right: space(sm);
      }
    }

    &Label {
      margin-top: space(xs);
      display: block;

      @include media('screen', '<gx-medium') {
        margin-top: 0;
        @include typography(body-small);
      }
    }

    @include media('screen', '<gx-medium') {
      flex-direction: row;
      width: 100%;
      align-items: center;
      padding: space(sm);
      justify-content: space-between;

      &--active {
        background-color: color(background-selected);
        color: color(content-selected);

        span {
          color: color(content-selected);
          font-weight: 600;
        }
      }
    }
  }

  &__menu {
    width: 26rem;
    background-color: color(background-main);
    padding: space(lg) space(md);
    margin-left: $gx-new-menu-width;
    margin-top: $gx-new-menu-header-height;
    height: calc(100% - #{$gx-new-menu-header-height});
    transform: translateX(-26rem);
    position: fixed;
    @include z-index(navigation, -5);
    transition: 0.2s transform cubic-bezier(0.29, 1.13, 0.91, 0.88);
    overflow-y: auto;
    overflow-x: hidden;
    //Prevents body scrolling if the mouse is over this element when it is scrollable
    overscroll-behavior: none;

    @include media('screen', '<gx-medium') {
      height: calc(100% - #{$gx-new-menu-header-height});
      margin-left: 0;
      margin-top: 0;
      @include z-index(navigation, -1);
    }

    &Back {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-bottom: space(lg);
      @include typography(body-small);

      .gx-icon {
        color: color(content-selected);
        font-size: 2.4rem;
        flex-shrink: 0;
      }

      span {
        margin-left: space(sm);
      }
    }

    &.is--open {
      transform: translateX(0);
      transition: 0.2s transform cubic-bezier(0.29, 1.13, 0.91, 0.88);
      box-shadow: elevation(overlay);
    }

    .has-menu-fixed & {
      transform: translateX(0);
      border-right: 0.1rem solid color(border-main);
      box-shadow: none !important;
      transition: none;
    }

    &Title {
      color: color(content-medium);
      margin-top: 0;
      @include typography(title-2);
      padding-left: space(sm);
    }

    &Items {
      margin-top: space(lg);

      li + li {
        margin-top: space(sm);
      }

      &Divider {
        margin: space(lg) 0;
        width: 100%;
        height: 0.1rem;
        background-color: color(border-main);
      }
    }

    &Item {
      cursor: pointer;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 4rem;
      padding: space(sm);
      color: color(content-selectable);
      border-radius: radius(md);
      @include typography('body-small');

      &:focus {
        text-decoration: none;
        color: inherit;
        background-color: inherit;
      }

      &:hover {
        text-decoration: none;
        color: color(content-selectable);
        background-color: color(background-alt);
      }

      & > div {
        display: flex;
        align-items: center;
      }

      span {
        margin-left: space(sm);
      }

      .gx-notification-badge span {
        margin-left: 0;
      }

      .gx-badge span {
        margin-left: 0;
      }

      .gx-icon {
        transition: 0.25s;
        font-size: 2.4rem;
      }

      &.is-active {
        background-color: color(background-selected);
        color: color(content-selected);
        font-weight: 600;
      }

      &.has--child-active {
        background-color: color(background-alt);
        color: color(content-selectable);
        font-weight: 400;
      }

      .rotate-icon {
        transform: rotate(180deg);
        transition: 0.25s;
      }

      li + li {
        margin-top: space(sm);
      }
    }
  }

  &__subMenu {
    margin: space(sm) 0 space(lg);

    &Item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      padding: space(sm) space(sm) space(sm) space(lg);
      height: 4rem;
      @include typography(body-small);
      color: color(content-selectable);
      border-radius: radius(md);

      &.is-active {
        background-color: color(background-selected);
        color: color(content-selected);
        font-weight: 600;
      }

      &:hover,
      &:focus {
        text-decoration: none;
      }

      &:hover:not(.is-active) {
        text-decoration: none;
        background-color: color(background-alt);
        color: color(content-selectable);
      }

      & + & {
        margin-top: space(sm);
      }
    }
  }
}

.gx-navigation-menu__profile {
  position: fixed;
  padding: 0 space(md);
  width: 28rem;
  bottom: 1.6rem;
  left: calc(#{$gx-new-menu-width} + 1.6rem);
  box-shadow: elevation(fixed-top);
  border-radius: radius(md);
  background-color: color(background-main);
  @include z-index(navigation, 1);

  @include media('screen', '<gx-medium') {
    position: static;
    box-shadow: none;
    width: 100%;
    padding: 0;
  }

  &Info {
    display: flex;
    align-items: center;
    padding: space(md) 0;
    border-bottom: 0.1rem solid color(border-main);
    @include typography(body-tiny);

    img {
      width: 3.2rem;
      height: 3.2rem;
      border-radius: radius(rounded);
      object-fit: cover;
      margin-right: space(sm);
    }

    &Email {
      color: color(content-low);
    }

    .gx-icon {
      margin-right: space(sm);
      font-size: 3.2rem !important;
      color: color(content-low);
    }
  }

  &ActionsGroup {
    padding-top: space(md);
    padding-bottom: space(md);
    @include typography(body-small);

    .gx-icon {
      @include icon-size(md);
    }

    a {
      display: flex;
      align-items: center;
      justify-content: space-between;
      min-height: 4rem;
      padding: space(sm) space(md);
      border-radius: radius(md);
      color: color(content-selectable);

      &:hover {
        text-decoration: none;
        background-color: color(background-alt);
        color: color(content-selectable);
      }

      & + a {
        margin-top: space(sm);
      }
    }

    & + & {
      border-top: 0.1rem solid color(border-main);
    }
  }
}

.gx-navigation-profile__image {
  width: 4rem;
  height: 4rem;
  border-radius: radius(rounded);
  object-fit: cover;

  @include media('screen', '<#{breakpoint(md)}') {
    width: 2.4rem;
    height: 2.4rem;
  }
}

.gx-navigation-skeleton {
  .multi-agency--switch & {
    &__menu {
      top: $gx-multi-agency-banner-height;
    }
    &__header {
      top: $gx-multi-agency-banner-height;
    }
  }

  &__menu {
    position: fixed;
    top: 0;
    left: 0;
    width: $gx-new-menu-width;
    height: 100%;
    background-color: color(background-main);
    border-right: 0.1rem solid color(border-main);
    @include z-index(navigation, -2);
    padding: space(lg) space(sm);
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    & > div {
      display: flex;
      align-items: center;
      flex-direction: column;
      div + div {
        margin-top: space(lg);
      }
    }
  }

  &__header {
    position: fixed;
    top: 0;
    left: $gx-new-menu-width;
    width: calc(100% - #{$gx-new-menu-width});
    height: $gx-new-menu-header-height;
    background-color: color(background-main);
    border-bottom: 0.1rem solid color(border-main);
    @include z-index(navigation, -2);
    padding: 1.2rem space(lg);
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include media('screen', '<#{breakpoint(md)}') {
      width: 100%;
      left: 0;
      padding: 1.2rem space(md);
    }

    & > div {
      display: flex;
      align-items: center;
      div + div {
        margin-left: space(lg);
      }
    }
  }
}

.gx-navigation-error {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  @include z-index(navigation, 9);
  background-color: color(background-main);
  display: flex;
  align-items: center;
  justify-content: center;

  &__content {
    text-align: center;
  }

  &__image {
    max-width: 18rem;
    margin: 0 auto space(xl);
  }

  &__title {
    margin-bottom: space(xl);
    @include typography('title-1');
  }
}
