{
    "compilerOptions": {
        "module": "ESNext",
        "moduleResolution": "Bundler",
        "sourceMap": true,
        "strict": true,
        "noImplicitAny": false,
        "strictNullChecks": true,
        "inlineSources": true,
        "sourceRoot": "/",
        "esModuleInterop": true,
        "jsx": "react-jsx",
        "baseUrl": "assets/js/commons",
        "noUncheckedIndexedAccess": true,
        "resolveJsonModule": true,
        "paths": {
            "gtx-react": [
                "gtx-react"
            ],
            "lib": [
                "lib"
            ],
            "gtx-constants": [
                "../../../node_modules/@getrix/common/js/gtx-constants.js"
            ],
            "#tests/*": [
                "../../../tests/*"
            ],
        }
    },
    "exclude": [
        "node_modules",
        "vendor",
        "tools",
        "packages"
    ]
}
