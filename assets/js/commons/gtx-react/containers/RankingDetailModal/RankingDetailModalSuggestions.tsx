import React from 'react';
import { Alert } from '@gx-design/alert';
import { trans } from '@pepita-i18n/babelfish';
import { RankingDetailsDataApiResponse } from 'lib/REST/types/rankingDetailModal';

type RankingDetailModalSuggestionsType = {
    tips: RankingDetailsDataApiResponse['tips'] | [];
};

export const RankingDetailModalSuggestions: React.FC<
    RankingDetailModalSuggestionsType
> = ({ tips }) => {
    if (!tips?.length) {
        return <Alert style="warning">{trans('ranking.no_suggestion')}</Alert>;
    }

    return (
        <ul>
            {tips &&
                Object.values(tips).map((tip, index) => {
                    return (
                        <li
                            key={index}
                            dangerouslySetInnerHTML={{ __html: tip }}
                        ></li>
                    );
                })}
        </ul>
    );
};
