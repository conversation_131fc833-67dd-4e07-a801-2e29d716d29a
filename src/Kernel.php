<?php

namespace App;

use App\Component\SyncScheduler\DependencyInjection\CompilerPass\UrlGeneratorPass;
use App\Component\SyncScheduler\DependencyInjection\SyncSchedulerExtension;
use App\DependencyInjection\Base\Compiler\LandingPageCollectorPass;
use App\DependencyInjection\Base\Compiler\OverrideServiceCompilerPass;
use App\DependencyInjection\Base\Compiler\SsoCompilerPass;
use App\DependencyInjection\Base\Factory\SecurityFactory;
use App\DependencyInjection\CompilerPass\FeatureToggleCompilerPass;
use App\DependencyInjection\CompilerPass\MenuAuthorizerCompilerPass;
use App\DependencyInjection\CompilerPass\ModuleAuthorizerCompilerPass;
use App\DependencyInjection\FeatureToggleExtension;
use App\DependencyInjection\Pf\Compiler\OverrideAliasServiceCompilerPass;
use App\Service\MigrationVerifier\DependencyInjection\MigrationVerifierExtension;
use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\Config\Loader\LoaderInterface;
use Symfony\Component\Config\Resource\FileResource;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;
use Symfony\Component\Routing\RouteCollectionBuilder;

class Kernel extends BaseKernel
{
    use MicroKernelTrait;

    const CONFIG_EXTS = '.{php,xml,yaml,yml}';

    private $productLang;

    public function __construct(string $lang, $environment, $debug)
    {
        $this->productLang = $lang;

        parent::__construct($environment, $debug);
    }

    public function getCacheDir(): string
    {
        return $this->getProjectDir() . '/var/cache/' . $this->productLang;
    }

    public function getLogDir(): string
    {
        return $this->getProjectDir() . '/var/logs/' . $this->productLang;
    }

    public function registerBundles()
    {
        $contents = require $this->getCommonConfigDir() . '/bundles.php';
        foreach ($contents as $class => $envs) {
            if (isset($envs['all']) || isset($envs[$this->environment])) {
                yield new $class();
            }
        }
    }

    protected function configureContainer(ContainerBuilder $container, LoaderInterface $loader)
    {
        $container->addResource(new FileResource($this->getCommonConfigDir() . '/bundles.php'));
        // Feel free to remove the "container.autowiring.strict_mode" parameter
        // if you are using symfony/dependency-injection 4.0+ as it's the default behavior
        $container->setParameter('container.autowiring.strict_mode', true);
        $container->setParameter('container.dumper.inline_class_loader', true);

        foreach ($this->getConfigDirs() as $confDir) {
            $loader->load($confDir . '/{access_control}' . self::CONFIG_EXTS, 'glob');
            $loader->load($confDir . '/{parameters}' . self::CONFIG_EXTS, 'glob');
            $loader->load($confDir . '/{parameters}_' . $this->environment . self::CONFIG_EXTS, 'glob');
            $loader->load($confDir . '/{packages}/*' . self::CONFIG_EXTS, 'glob');
            $loader->load($confDir . '/{packages}/' . $this->environment . '/**/*' . self::CONFIG_EXTS, 'glob');
            $loader->load($confDir . '/{services}' . self::CONFIG_EXTS, 'glob');
            $loader->load($confDir . '/{services}_' . $this->environment . self::CONFIG_EXTS, 'glob');
        }
    }

    public function build(ContainerBuilder $container)
    {
        $extension = $container->getExtension('security');
        $extension->addSecurityListenerFactory(new SecurityFactory());

        $container->registerExtension(new SyncSchedulerExtension());
        $container->registerExtension(new MigrationVerifierExtension());
        $container->registerExtension(new FeatureToggleExtension());

        $container->addCompilerPass(new OverrideServiceCompilerPass());
        $container->addCompilerPass(new LandingPageCollectorPass());
        $container->addCompilerPass(new SsoCompilerPass());
        $container->addCompilerPass(new OverrideAliasServiceCompilerPass());
        $container->addCompilerPass(new UrlGeneratorPass());
        $container->addCompilerPass(new FeatureToggleCompilerPass());
        $container->addCompilerPass(new ModuleAuthorizerCompilerPass());
        $container->addCompilerPass(new MenuAuthorizerCompilerPass());
    }

    protected function configureRoutes(RouteCollectionBuilder $routes)
    {
        foreach ($this->getConfigDirs() as $confDir) {
            $routes->import($confDir . '/{routes}/*' . self::CONFIG_EXTS, '/', 'glob');
            $routes->import($confDir . '/{routes}/' . $this->environment . '/**/*' . self::CONFIG_EXTS, '/', 'glob');
            $routes->import($confDir . '/{routes}' . self::CONFIG_EXTS, '/', 'glob');
        }
    }

    private function getConfigDirs(): array
    {
        return [
            'common'  => $this->getCommonConfigDir(),
            'product' => $this->getProductConfigDir(),
        ];
    }

    private function getCommonConfigDir(): string
    {
        return $this->getProjectDir() . '/config/_common';
    }

    private function getProductConfigDir(): string
    {
        return $this->getProjectDir() . '/config/' . $this->productLang;
    }
}
