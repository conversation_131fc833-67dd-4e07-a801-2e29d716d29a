{% extends 'mail/base-restyled.html.twig' %}

{% block content %}

    <p style="line-height: 1.5; font-size: 14px; color: #333333; font-family: Arial, sans-serif, 'Open Sans'; width: 100%; max-width: 520px; margin: 0 auto 30px;">
        {{ 'label.gentle'|trans({}, gtxConstants.DOMAIN_CMS) }} <b style="font-weight: bold;">
            {% if user.nome|trim or user.cognome|trim %}
                {{ user.nome ? user.nome|title : user.cognome|title }}
            {% else %}
                {{ 'label.user'|trans({}, gtxConstants.DOMAIN_CMS)|lower }}
            {% endif %}
        </b>,
    </p>

    <p style="line-height: 1.5; font-size: 14px; color: #333333; font-family: Arial, sans-serif, 'Open Sans'; width: 100%; max-width: 520px; margin: 0 auto 40px;">
        {{ 'mail.settings.edit_pass.text_1'|trans({}, gtxConstants.DOMAIN_CMS) }}
    </p>

    <table class="btn-wrap" style="border-spacing: 0; border-collapse: collapse; width: 398px; margin-left: auto; margin-right: auto;">
        <tr>
            <td align="center">
                <table style="width: 180px; border-spacing: 0; border-collapse: collapse;">
                    <tr>
                        <td class="btn" align="center" style="display: block; width: 180px; height: 44px; border-radius: 3px; background: {{ btnBgColor }}; margin: 0 auto; padding: 0;" bgcolor="{{ btnBgColor }}">
                            <a href="{{ globalConfigs.getrix.baseurl }}" style="display: table-cell; width: 180px; height: 44px; line-height: 44px; vertical-align: middle; color: #ffffff; text-transform: uppercase; text-decoration: none; font-weight: bold; font-size: 14px; font-family: Arial, sans-serif, 'Raleway';">{{ 'btn.label.login'|trans({}, gtxConstants.DOMAIN_CMS)|upper }}</a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td height="40"></td>
        </tr>
    </table>

    <p style="line-height: 1.5; font-size: 14px; color: #333333; font-family: Arial, sans-serif, 'Open Sans'; width: 100%; max-width: 520px; margin: 0 auto 30px;">{{ 'label.the_team'|trans({}, gtxConstants.DOMAIN_CMS) }} {{ gtxConstants.APP_FIRMA_EMAIL }}</p>

{% endblock %}

{% block footer %}
    {% include 'mail/settings/footer.html.twig' %}
{% endblock %}
