import { useMemo, useState } from 'react';
import { But<PERSON> } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { ActionList, ActionListItem } from '@gx-design/action-list';
import { IconInput } from '@gx-design/icon-input';
import { Modal } from '@gx-design/modal';
import { trans } from '@pepita-i18n/babelfish';
import { Formik, useFormikContext } from 'formik';
import * as Yup from 'yup';
import { Loader } from '@gx-design/loader';
import { useSuspenseQuery } from '@tanstack/react-query';
import {
    EstimateDetailQueryType,
    EstimateType,
} from 'lib/REST/types/acquisition-estimates';
import {
    getEmail,
    getEstimateAddress,
    getEstimateAvgPrice,
    getEstimateCity,
    getEstimateDate,
    getEstimateOutcomeNote,
    getEstimateOutcomeValue,
    getEstimateProvince,
    getEstimateSurface,
    getEstimateTypology,
    getEstimateUserEmail,
    getEstimateUserLastName,
    getEstimateUserName,
    getLastname,
    getName,
    getSetOutcomeActionLabel,
    hasOutcomeNote,
} from '../utils/selectors';
import { getMacroZone } from '../utils/getMacroZone';
import { createOutcomesQueryOptions } from 'lib/REST/requests/api/lookup/estimates/outcomes/query-factory';
import { getOutcomeIcon } from '../utils/getOutcomeIcon';
import { GxFkTextarea } from 'gtx-react/components/gx-formik';

const setOutcomeSchema = Yup.object().shape({
    outcome: Yup.number().required(),
    note: Yup.string().optional(),
});

export type SetOutcomeSchemaType = Yup.InferType<typeof setOutcomeSchema>;

const OutcomeModalFooter = ({
    isDisabled,
    onClose,
    saveBtnLabel,
}: {
    isDisabled: boolean;
    onClose: () => void;
    saveBtnLabel: string;
}) => {
    const { submitForm } = useFormikContext<SetOutcomeSchemaType>();

    return (
        <>
            <Button onClick={onClose} variant="ghost">
                {trans('label.cancel')}
            </Button>
            <Button
                variant="accent"
                onClick={submitForm}
                className={isDisabled ? 'disabled' : ''}
                disabled={isDisabled}
            >
                {saveBtnLabel}
            </Button>
        </>
    );
};

const SetOutcomeModal = ({
    estimate,
    isOpen,
    close,
    isPending,
    children,
}: {
    estimate: EstimateType | EstimateDetailQueryType;
    isOpen: boolean;
    close: () => void;
    isPending: boolean;
    children: React.ReactNode;
}) => {
    const { values, setFieldValue, resetForm } =
        useFormikContext<SetOutcomeSchemaType>();

    const [isOutcomeDropdownOpen, setIsOutcomeDropdownOpen] =
        useState<boolean>(false);

    const { data: outcomesMapping } = useSuspenseQuery({
        ...createOutcomesQueryOptions(),
        select: (data) =>
            data?.map((item) => ({ ...item, value: parseInt(item.value) })),
    });

    const outcomeLabel = useMemo(
        () =>
            outcomesMapping.find((item) => item.value === values?.outcome)
                ?.label,

        [values?.outcome]
    );

    const onClose = () => {
        resetForm();
        close();
    };

    return (
        <Modal
            isOpen={isOpen}
            title={`${trans(
                'label.valuation_request_status_of'
            )} ${getEstimateDate(estimate)}`}
            onClose={onClose}
            footer={
                <OutcomeModalFooter
                    isDisabled={isPending || !values?.outcome}
                    onClose={onClose}
                    saveBtnLabel={getSetOutcomeActionLabel(
                        hasOutcomeNote(estimate)
                    )}
                />
            }
        >
            {isPending && <Loader />}
            <>
                <div className="request-modal-info">
                    <div className="request-modal-info__address">
                        {getEstimateCity(estimate)} (
                        {getEstimateProvince(estimate)}){' '}
                        {getMacroZone(estimate?.geographyInformation) &&
                            `- ${getMacroZone(
                                estimate?.geographyInformation
                            )}`}{' '}
                        - {getEstimateAddress(estimate)}
                    </div>
                    <div className="request-modal-info__property">
                        {getEstimateTypology(estimate)}{' '}
                        {getEstimateSurface(estimate) &&
                            '| ' + getEstimateSurface(estimate)}{' '}
                        {getEstimateAvgPrice(estimate) &&
                            '| ' + getEstimateAvgPrice(estimate)}
                    </div>
                    {children}
                </div>
                <div className="row-with-tag">
                    <IconInput
                        isVerticalCentered
                        icon={getOutcomeIcon(values?.outcome)?.icon!}
                        variant={getOutcomeIcon(values?.outcome)?.style!}
                    >
                        <Button
                            onClick={() =>
                                setIsOutcomeDropdownOpen(!isOutcomeDropdownOpen)
                            }
                        >
                            <span>
                                {values?.outcome
                                    ? outcomeLabel
                                    : trans('label.outcome_type')}
                            </span>
                            <Icon
                                name="arrow-down"
                                className="gx-button__caret"
                            />
                        </Button>
                        <div
                            className="gx-dropdown gx-dropdown--bottomLeft"
                            style={{
                                display: isOutcomeDropdownOpen
                                    ? 'block'
                                    : 'none',
                            }}
                        >
                            <ActionList>
                                {Object.values(outcomesMapping).map(
                                    (item, index) => {
                                        return (
                                            <ActionListItem
                                                key={`${item.label}_key${index}`}
                                                onClick={() => (
                                                    setFieldValue(
                                                        'outcome',
                                                        item.value
                                                    ),
                                                    setIsOutcomeDropdownOpen(
                                                        false
                                                    )
                                                )}
                                                text={item.label}
                                                startElement={
                                                    <Icon
                                                        name={
                                                            getOutcomeIcon(
                                                                item.value
                                                            )?.icon!
                                                        }
                                                        className={`gx-text-${getOutcomeIcon(
                                                            item?.value
                                                        )?.style}`}
                                                    />
                                                }
                                            />
                                        );
                                    }
                                )}
                            </ActionList>
                        </div>
                    </IconInput>
                </div>
                <div className="row-with-tag row-with-tag--textarea">
                    <IconInput isVerticalCentered={false} icon="note">
                        <GxFkTextarea
                            id="note"
                            name="note"
                            label={trans('label.insert_note')}
                            isLabelVisible={false}
                            placeholder={trans('label.insert_note')}
                            maxLength={800}
                            rows={8}
                        />
                    </IconInput>
                </div>
            </>
        </Modal>
    );
};

export default ({
    estimate,
    isOpen,
    close,
    onSave,
    isPending,
    children,
}: {
    estimate: EstimateType | EstimateDetailQueryType;
    isOpen: boolean;
    close: () => void;
    onSave: (data) => void;
    isPending: boolean;
    children: React.ReactNode;
}) => {
    return (
        <Formik
            initialValues={{
                note: getEstimateOutcomeNote(estimate) || '',
                outcome: getEstimateOutcomeValue(estimate) || null,
            }}
            onSubmit={(data) =>
                onSave({ ...data, note: data?.note ? data.note.trim() : null })
            }
            enableReinitialize
            validationSchema={setOutcomeSchema}
        >
            <SetOutcomeModal
                estimate={estimate}
                isOpen={isOpen}
                close={close}
                isPending={isPending}
            >
                {children}
            </SetOutcomeModal>
        </Formik>
    );
};

export const SetOutcomeModalContactInfoDetail = ({
    estimate,
}: {
    estimate: EstimateDetailQueryType;
}) => {
    return (
        <div className="request-modal-info__contact">
            {getEstimateUserName(estimate)} {getEstimateUserLastName(estimate)}{' '}
            |{' '}
            {getEstimateUserEmail(estimate) && (
                <a href={`mailto: ${getEstimateUserEmail(estimate)}`}>
                    {getEstimateUserEmail(estimate)}
                </a>
            )}
        </div>
    );
};
export const SetOutcomeModalContactInfoList = ({
    estimate,
}: {
    estimate: EstimateType;
}) => {
    return (
        <div className="request-modal-info__contact">
            {getName(estimate)} {getLastname(estimate)} |{' '}
            {getEmail(estimate) && (
                <a href={`mailto: ${getEmail(estimate)}`}>
                    {getEmail(estimate)}
                </a>
            )}
        </div>
    );
};
