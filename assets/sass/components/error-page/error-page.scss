@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.gx-error-page {
  max-width: 40rem;
  min-height: calc(100vh - 20.1rem);
  padding: space(2xl) space(md) space(2xl);
  margin: 0 auto;
  color: color(content-high);
  text-align: center;

  &__pic {
    width: 18.8rem;
    height: 18rem;
    margin: 0 auto space(lg);
    background-image: url($gtx-common-base-path-img + 'error/error404.png');
    background-repeat: no-repeat;
    background-size: cover;
  }

  &__title {
    margin-bottom: space(md);
    @include typography(title-1);
  }

  &__text {
    @include typography(body);
  }
}
