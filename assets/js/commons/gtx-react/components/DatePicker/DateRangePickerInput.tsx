import { useState, useRef, FC, useMemo, useCallback, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { CalendarProps } from 'react-calendar';
import { useFormikContext } from 'formik';

import {
    CalendarValuePiece,
    DatePicker,
    DatePickerOnChangeValue,
} from './DatePicker';
import {
    generateInitialState,
    formatDate,
    parseDate,
    isValidDate,
} from './utils';
import { useOnClickOutside } from '@gx-design/use-on-click-outside';
import { PatternFormikInput } from './PatternFormikInput';

export type DateRangePickerInputProps = {
    /** formik field name for the start date range */
    startName: string;
    /** formik field name for the end date range */
    endName: string;
    /** single label to be shown on top of the input group */
    label?: string;
    /** placeholder for the start date range */
    startPlaceholder?: string;
    /** placeholder for the end date range */
    endPlaceholder?: string;
    /** minimum selectable date (days before this Date will be disabled) */
    minDate?: CalendarProps['minDate'];
    /** maximum selectable date (days after this Date will be disabled) */
    maxDate?: CalendarProps['maxDate'];
    ariaLabelStart?: string;
    ariaLabelEnd?: string;
    disabled?: boolean;
};

type RangeInput = 'start' | 'end';

type CalendarState = {
    values: [CalendarValuePiece, CalendarValuePiece];
    /** Useful to avoid bugs because, if only end is selected, react-calendar will select every day before end */
    isReversed: boolean;
};

type YAxis = 'top' | 'bottom' | undefined;

const COORDS_INITIAL_STATE: React.CSSProperties = {
    opacity: 0,
    top: 0,
    left: 0,
    position: 'absolute',
    zIndex: 2600,
};
/** Space between the wrapper of the two inputs and the calendar */
const SPACING = 20;

/**
 * Component that will render a group of 2 simple inputs, which includes a DatePicker, useful to select a date range, to be used in a formik form; it stores the start/end range as formatted strings "DD/MM/YYYY".
 *
 * User can either pick a date through:
 * - the `<DatePicker />` component (rendered in a portal);
 * - manually type the date through the `<PatternFormikInput />` masked input component; datepicker will read the input value when input will lose focus; if user types an invalid/incomplete date, form value will be reverted to last valid value (string date or empty).
 *
 * DatePicker will close when:
 * - user picks both start/end dates;
 * - user clicks outside the picker or both inputs;
 * - user will press the 'Tab' key while the end range input is focused
 *
 * Event listeners to close the picker will be attached to `window` only when the picker is open.
 *
 * Datepicker's position will be calculated based on the window's available position: if there is no enough space to render it below the two inputs, it will be rendered on top of them.
 */
export const DateRangePickerInput: FC<DateRangePickerInputProps> = ({
    startName,
    endName,
    label,
    startPlaceholder,
    endPlaceholder,
    minDate,
    maxDate,
    ariaLabelStart,
    ariaLabelEnd,
    disabled,
}) => {
    const [focusedInput, setFocusedInput] = useState<RangeInput | null>(null);
    const { values, setFieldValue } = useFormikContext<Date>();
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [calendar, setCalendar] = useState<CalendarState>({
        values: generateInitialState([values[startName], values[endName]]),
        isReversed: false,
    });

    // wrapping both inputs, useful to calculate the calendar portal's position
    const inputWrapperRef = useRef<HTMLInputElement>(null);

    // useful for `useOnClickOutside` hook
    const portalRef = useRef<HTMLDivElement>(null);
    const pickerWidth =
        portalRef?.current?.getBoundingClientRect()?.width ?? 320;

    // these refs are for the div which wraps the inputs, useful to calculate where to spawn the arrow which indicates the current focused input
    const startRef = useRef<HTMLInputElement>(null);
    const endRef = useRef<HTMLInputElement>(null);
    const startRect = startRef.current?.getBoundingClientRect();
    const endRect = endRef.current?.getBoundingClientRect();
    // useful states to calulate calendar position based on available space
    const [computedPosition, setComputedPosition] = useState<YAxis>(undefined);
    const [wrapperStyle, setWrapperStyle] =
        useState<React.CSSProperties>(COORDS_INITIAL_STATE);

    const arrowStyle: React.CSSProperties = {
        left:
            startRect && focusedInput === 'start'
                ? startRect.width / 2 - 8
                : startRect &&
                  endRect &&
                  startRect.width + 24 + endRect.width / 2 < pickerWidth
                ? startRect.width + endRect.width / 2
                : pickerWidth - 24,
        top: computedPosition === 'bottom' ? 0 : '100%',
    };

    const handleClickOutside = useCallback(() => setIsOpen(false), []);

    const onDayPick = (
        newValue: DatePickerOnChangeValue,
        _event: React.MouseEvent<HTMLButtonElement, MouseEvent>
    ) => {
        if (Array.isArray(newValue)) {
            let isReversed = false;

            if (newValue[0] && !newValue[1] && focusedInput === 'end') {
                isReversed = true;
            }

            if (newValue[0] && !newValue[1]) {
                if (isReversed) {
                    setFocusedInput('start');
                    startRef.current?.focus();
                    setFieldValue(endName, formatDate(newValue[0]));
                    setFieldValue(startName, '');
                } else {
                    setFocusedInput('end');
                    endRef.current?.focus();
                    setFieldValue(startName, formatDate(newValue[0]));
                    setFieldValue(endName, '');
                }
            }

            if (newValue[0] && newValue[1]) {
                setIsOpen(false);
                setFieldValue(startName, formatDate(newValue[0]));
                setFieldValue(endName, formatDate(newValue[1]));
            }
            setCalendar({
                values: isReversed ? [newValue[1], newValue[0]] : newValue,
                isReversed,
            });
        }
    };

    const onInputFocus = (focused: RangeInput) => () => {
        if (!isOpen) {
            // resetting internal calendar state: useful to have an updated state when some external trigger change the values (something like "reset form" button).
            setCalendar(({ isReversed }) => ({
                isReversed,
                values: generateInitialState([
                    values[startName],
                    values[endName],
                ]),
            }));
        }
        setFocusedInput(focused);
        setIsOpen(true);
    };

    const onBlur = (blurred: RangeInput) => () => {
        const newDate = parseDate(
            values[blurred === 'start' ? startName : endName]
        );

        if (!newDate) {
            // empty string
            setCalendar((prevState) => {
                const [oldStart, oldEnd] = prevState.values;
                let isReversed = false;
                if (blurred === 'start') {
                    if (oldEnd instanceof Date) {
                        isReversed = true;
                    }
                }
                const newStart = blurred === 'start' ? null : oldStart;
                const newEnd = blurred === 'end' ? null : oldEnd;
                return { values: [newStart, newEnd], isReversed };
            });
        } else if (isValidDate({ date: newDate, minDate, maxDate })) {
            // valid date
            setCalendar((prevState) => {
                const [oldStart, oldEnd] = prevState.values;
                let isReversed = false;
                if (blurred === 'end') {
                    if (!(oldStart instanceof Date)) {
                        isReversed = true;
                    }
                }
                const newStart = blurred === 'start' ? newDate : oldStart;
                const newEnd = blurred === 'end' ? newDate : oldEnd;

                return { values: [newStart, newEnd], isReversed };
            });
        } else {
            const dateToFormat = calendar.values[blurred === 'start' ? 0 : 1];

            // invalid date, resetting to empty or previous valid value
            const resettedValue =
                calendar.values[blurred === 'start' ? 0 : 1] instanceof Date &&
                dateToFormat
                    ? formatDate(dateToFormat)
                    : '';
            setFieldValue(
                blurred === 'start' ? startName : endName,
                resettedValue
            );
        }
    };

    /** Closes calendar when `Tab` key is pressed, only for the second input */
    const onInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Tab') {
            setIsOpen(false);
        }
    };

    const refs = useMemo(
        () => [startRef, endRef, portalRef],
        [startRef, endRef, portalRef]
    );

    useOnClickOutside({
        refs,
        handler: handleClickOutside,
        shouldListen: isOpen,
    });

    /**
     * Both of the following useEffects are a solution to check if the calendar
     * has enough space in the window to be rendered below the two inputs
     * (default behaviour); if not, it will render on top of the two inputs.
     * This mechanism is a simplified version from here: https://gitlab.pepita.io/getrix/mls-site/-/merge_requests/2496/diffs#2d2ed057d072d8fcab31b3dad5ad48619de72a28
     * This mechanism will sono became a reausable hook from gx-design TODO @vvitale
     */
    useEffect(() => {
        const fixPosition = () => {
            if (!isOpen || !inputWrapperRef?.current || !portalRef?.current) {
                return;
            }

            const inpWrapperRect =
                inputWrapperRef?.current?.getBoundingClientRect();
            const dropdownRect = portalRef?.current?.getBoundingClientRect();

            let computedYAxis: YAxis = 'bottom';
            const { innerHeight } = window;
            // fixing y-axis
            const yStartPoint =
                inpWrapperRect.y + SPACING + inpWrapperRect.height;
            const yBreakingPoint = innerHeight;

            if (yStartPoint + dropdownRect.height > yBreakingPoint) {
                computedYAxis = 'top';
            }
            setComputedPosition(computedYAxis);
        };
        if (isOpen) {
            fixPosition();
        }
    }, [isOpen]);

    useEffect(() => {
        const dropdownPositionHandler = () => {
            if (!isOpen || !inputWrapperRef?.current || !portalRef?.current) {
                // resetting state on dropdown close
                setComputedPosition(undefined);
                setWrapperStyle(COORDS_INITIAL_STATE);
                return;
            }
            if (!computedPosition) {
                return;
            }
            const inpWrapperRect =
                inputWrapperRef?.current?.getBoundingClientRect();
            const dropdownRect = portalRef?.current?.getBoundingClientRect();

            const newCoords: React.CSSProperties = {
                opacity: 1,
                left: inpWrapperRect.x,
                top:
                    computedPosition === 'top'
                        ? inpWrapperRect.y -
                          dropdownRect.height -
                          SPACING +
                          window.scrollY
                        : inpWrapperRect.y +
                          inpWrapperRect.height +
                          SPACING +
                          window.scrollY,
            };
            setWrapperStyle((prev) => ({ ...prev, ...newCoords }));
        };

        dropdownPositionHandler();

        window.addEventListener('resize', dropdownPositionHandler);
        window.addEventListener('scroll', dropdownPositionHandler);

        return () => {
            window.removeEventListener('resize', dropdownPositionHandler);
            window.removeEventListener('scroll', dropdownPositionHandler);
        };
    }, [isOpen, computedPosition]);

    return (
        <>
            {label && <label className="gx-label">{label}</label>}
            <div ref={inputWrapperRef} className="filter-box__range">
                <PatternFormikInput
                    disabled={disabled}
                    isRangeInput
                    name={startName}
                    onFocus={onInputFocus('start')}
                    onClick={onInputFocus('start')}
                    onBlur={onBlur('start')}
                    placeholder={startPlaceholder}
                    ref={startRef}
                    aria-label={ariaLabelStart}
                />
                <PatternFormikInput
                    disabled={disabled}
                    isRangeInput
                    name={endName}
                    onFocus={onInputFocus('end')}
                    onClick={onInputFocus('end')}
                    onBlur={onBlur('end')}
                    placeholder={endPlaceholder}
                    ref={endRef}
                    onKeyDown={onInputKeyDown}
                    aria-label={ariaLabelEnd}
                />
            </div>
            {isOpen &&
                createPortal(
                    <DatePicker
                        ref={portalRef}
                        wrapperStyle={wrapperStyle}
                        arrowStyle={arrowStyle}
                        showArrowIndicator
                        arrowStartsFrom={computedPosition}
                        selectRange
                        onChange={onDayPick}
                        value={
                            calendar.isReversed
                                ? [calendar.values[1], calendar.values[0]]
                                : calendar.values
                        }
                        defaultActiveStartDate={
                            focusedInput === 'end' &&
                            calendar.values[1] instanceof Date
                                ? calendar.values[1]
                                : undefined
                        }
                        minDate={minDate}
                        maxDate={maxDate}
                    />,
                    document.body
                )}
        </>
    );
};
