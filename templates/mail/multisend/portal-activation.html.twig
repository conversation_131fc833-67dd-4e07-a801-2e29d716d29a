{% extends 'mail/base-restyled.html.twig' %}

{% block content %}
    <p style="line-height: 1.5; font-size: 14px; color: #333333; font-family: Arial, sans-serif, 'Open Sans'; width: 100%; max-width: 500px; margin: 0 auto 30px; text-align:left;">
    Gentile <b style="font-weight: bold;">{{ payload.portalName }}</b>,
    </p>

    <p style="line-height: 1.5; font-size: 14px; color: #333333; font-family: Arial, sans-serif, 'Open Sans'; width: 100%; max-width: 500px; margin: 0 auto 30px; text-align:left;">
            inviamo questa email per informarti che il nostro cliente <b style="font-weight: bold;">{{ agency.nome }}</b> ha attivato l’esportazione dei suoi annunci verso il vostro portale.
    </p>


    <table border="0" width="500"  cellpadding="0" cellspacing="0" border="0" style="mso-table-lspace:0pt; mso-table-rspace:0pt; border-collapse:collapse; width: 100%; max-width: 500px; background-color: #f5f5f5; margin: 0 auto;">
        <tr>
            <td style="padding: 0 24px;">
                <table width="100%" cellpadding="0" cellspacing="0" border="0" style="mso-table-rspace: 0pt; border-collapse: collapse;">
                    <tr>
                        <td colspan="2" align="left" style="padding: 24px 0 8px; line-height:1; font-size: 16px; color: #666666; font-weight: bold;font-family: Arial, sans-serif, 'Open Sans';">Dati Agenzia</td>
                    </tr>
                    <tr>
                        <td colspan="2" align="left"  style="padding: 8px 0 16px;">
                            <img src="{{ globalConfigs.getrix.media }}/{{ gtxConstants.AGENCY_LOGO_IMAGE_ENDPOINT }}/{{ agency.fkLogo }}.jpg">
                        </td>
                    </tr>
                    <tr>
                        <td style="width:100%; padding: 8px 0 8px; border-top: 1px solid #dddddd;">
                            <!--[if gte mso]>
                            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="font-family: Arial; font-size: 14px; line-height: 20px;">
                                <tr>
                                    <td valign="top" width="140" style="border-collapse: collapse;line-height: 20px;">
                            <![endif]-->
                            <span style="text-align: left; vertical-align: top;font-size: 12px; min-width: 140px; float: left; display: inline-block; line-height: 20px; text-transform: uppercase; color:#999999;">
                                NOME
                            </span>
                            <!--[if gte mso]>
                                </td>
                                <td width="280" style="border-collapse: collapse;line-height: 20px;">
                            <![endif]-->
                            <b class="style-autolink" style="text-align: left; font-size: 14px; color: #333333; width: 280px; float: left;  display: inline-block; line-height: 20px; text-decoration: none;">
                                {{ agency.nome }}
                            </b>
                            <!--[if gte mso]>
                                    </td>
                                </tr>
                            </table>
                            <![endif]-->
                        </td>
                    </tr>
                    <tr>
                        <td style="width:100%; padding: 8px 0 8px; border-top: 1px solid #dddddd;">
                            <!--[if gte mso]>
                            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="font-family: Arial; font-size: 14px; line-height: 20px;">
                                <tr>
                                    <td valign="top" width="140" style="border-collapse: collapse;line-height: 20px;">
                            <![endif]-->
                            <span style="text-align: left; vertical-align: top;font-size: 12px; min-width: 140px; float: left; display: inline-block; line-height: 20px; text-transform: uppercase; color:#999999;">
                                INDIRIZZO
                            </span>
                            <!--[if gte mso]>
                            </td>
                            <td width="280" style="border-collapse: collapse;line-height: 20px;">
                            <![endif]-->
                            <b class="style-autolink" style="text-align: left; font-size: 14px; color: #333333; width: 280px; float: left;  display: inline-block; line-height: 20px; text-decoration: none;">
                                {{ agency.indirizzo|capitalize }}{% if agency.numeroCivico %}, {{ agency.numeroCivico }}{% endif %}{% if agency.comune %} - {{ agency.comune.nome }} ({{ agency.comune.sigla_provincia }}){% endif %}
                            </b>
                            <!--[if gte mso]>
                            </td>
                            </tr>
                            </table>
                            <![endif]-->
                        </td>
                    </tr>
                    {% if payload.username %}
                        <tr>
                            <td style="width:100%; padding: 8px 0 8px; border-top: 1px solid #dddddd;">
                                <!--[if gte mso]>
                                <table width="100%" cellpadding="0" cellspacing="0" border="0" style="font-family: Arial; font-size: 14px; line-height: 20px;">
                                    <tr>
                                        <td valign="top" width="140" style="border-collapse: collapse;line-height: 20px;">
                                <![endif]-->
                                <span style="text-align: left; vertical-align: top;font-size: 12px; min-width: 140px; float: left; display: inline-block; line-height: 20px; text-transform: uppercase; color:#999999;">
                                EMAIL
                            </span>
                                <!--[if gte mso]>
                                </td>
                                <td width="280" style="border-collapse: collapse;line-height: 20px;">
                                <![endif]-->
                                <b class="style-autolink" style="text-align: left; font-size: 14px; color: #333333; width: 280px; float: left;  display: inline-block; line-height: 20px; text-decoration: none;">
                                    {{ payload.username }}
                                </b>
                                <!--[if gte mso]>
                                </td>
                                </tr>
                                </table>
                                <![endif]-->
                            </td>
                        </tr>
                    {% endif %}
                    {% if payload.codice %}
                        <tr>
                            <td style="width:100%; padding: 8px 0 8px; border-top: 1px solid #dddddd;">
                                <!--[if gte mso]>
                                <table width="100%" cellpadding="0" cellspacing="0" border="0" style="font-family: Arial; font-size: 14px; line-height: 20px;">
                                    <tr>
                                        <td valign="top" width="140" style="border-collapse: collapse;line-height: 20px;">
                                <![endif]-->
                                <span style="text-align: left; vertical-align: top;font-size: 12px; min-width: 140px; float: left; display: inline-block; line-height: 20px; text-transform: uppercase; color:#999999;">
                                CODICE
                            </span>
                                <!--[if gte mso]>
                                </td>
                                <td width="280" style="border-collapse: collapse;line-height: 20px;">
                                <![endif]-->
                                <b class="style-autolink" style="text-align: left; font-size: 14px; color: #333333; width: 280px; float: left;  display: inline-block; line-height: 20px; text-decoration: none;">
                                    {{ payload.codice }}
                                </b>
                                <!--[if gte mso]>
                                </td>
                                </tr>
                                </table>
                                <![endif]-->
                            </td>
                        </tr>
                    {% endif %}
                    <tr>
                        <td style="width:100%; padding: 8px 0 8px; border-top: 1px solid #dddddd;">
                            <!--[if gte mso]>
                            <table width="100%" cellpadding="0" cellspacing="0" border="0" style="font-family: Arial; font-size: 14px; line-height: 20px;">
                                <tr>
                                    <td valign="top" width="140" style="border-collapse: collapse;line-height: 20px;">
                            <![endif]-->
                            <span style="text-align: left; vertical-align: top;font-size: 12px; min-width: 140px; float: left; display: inline-block; line-height: 20px; text-transform: uppercase; color:#999999;">
                                ID AGENZIA
                            </span>
                            <!--[if gte mso]>
                            </td>
                            <td width="280" style="border-collapse: collapse;line-height: 20px;">
                            <![endif]-->
                            <b class="style-autolink" style="text-align: left; font-size: 14px; color: #333333; width: 280px; float: left;  display: inline-block; line-height: 20px; text-decoration: none;">
                                {{ agency.idAgenzia }}
                            </b>
                            <!--[if gte mso]>
                            </td>
                            </tr>
                            </table>
                            <![endif]-->
                        </td>
                    </tr>
                    {% if agency.piva %}
                        <tr>
                            <td style="width:100%; padding: 8px 0 24px; border-top: 1px solid #dddddd;">
                                <!--[if gte mso]>
                                <table width="100%" cellpadding="0" cellspacing="0" border="0" style="font-family: Arial; font-size: 14px; line-height: 20px;">
                                    <tr>
                                        <td valign="top" width="140" style="border-collapse: collapse;line-height: 20px;">
                                <![endif]-->
                                <span style="text-align: left; vertical-align: top;font-size: 12px; min-width: 140px; float: left; display: inline-block; line-height: 20px; text-transform: uppercase; color:#999999;">
                                PARTITA IVA
                            </span>
                                <!--[if gte mso]>
                                </td>
                                <td width="280" style="border-collapse: collapse;line-height: 20px;">
                                <![endif]-->
                                <b class="style-autolink" style="text-align: left; font-size: 14px; color: #333333; width: 280px; float: left;  display: inline-block; line-height: 20px; text-decoration: none;">
                                    {{ agency.piva }}
                                </b>
                                <!--[if gte mso]>
                                </td>
                                </tr>
                                </table>
                                <![endif]-->
                            </td>
                        </tr>
                    {% endif %}
                </table>
            </td>
        </tr>
    </table>

    <br>

    <p style="line-height: 1.5; font-size: 14px; color: #333333; font-family: Arial, sans-serif, 'Open Sans'; width: 100%; max-width: 500px; margin: 10px auto 30px; text-align:left;">
        Per qualsiasi problema contattare <a href="mailto:{{ customerServiceEmail }}" style="color: #428CC6; text-decoration: none;">{{ customerServiceEmail }}</a>.
    </p>
{% endblock %}

{% block footer %}
    {% include 'mail/settings/footer.html.twig' %}
{% endblock %}
