{"$id": "http://gestionale.immobiliare.it/list_properties_suggestions_request.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "Suggestion Autocomplete Request", "description": "Schema for property autocomplete request parameters", "type": "object", "properties": {"term": {"type": "string", "description": "The search term to find matching suggestions", "minLength": 1}, "type": {"type": "string", "description": "The property type filter to apply to suggestions", "enum": ["active", "archived", "draft", "sold"]}}, "required": ["term", "type"], "additionalProperties": false}