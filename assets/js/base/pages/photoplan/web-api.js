import { http } from '@pepita/http';
import { mediaAssetsVersioning } from '../../../commons/lib/utility';

const orientationsMapping = [
    {
        id: '1',
        name: 'top',
    },
    {
        id: '7',
        name: 'topRight',
    },
    {
        id: '3',
        name: 'right',
    },
    {
        id: '5',
        name: 'bottomRight',
    },
    {
        id: '2',
        name: 'bottom',
    },
    {
        id: '6',
        name: 'bottomLeft',
    },
    {
        id: '4',
        name: 'left',
    },
    {
        id: '8',
        name: 'topLeft',
    },
];

let queue = Promise.resolve();
const noop = v => v;

function truncateToLength(value, length) {
    if (!length) {
        return value;
    }

    return value.substring(0, length);
}

function enqueueOperation(operation) {
    //we fork the promise in order to preserve errors;
    queue.then(noop);

    return (queue = queue.catch(noop).then(operation));
}

function capitalize(s) {
    return s.slice(0, 1).toUpperCase() + s.slice(1);
}

function getImageSize(url, path, from = null, to = null) {
    if (!from || !to) {
        return `${url}${path}`;
    }

    return `${url}${path.replace(from, to)}`;
}

export function getFloorLookup() {
    return http
        .get(`/ws/fotoplan/lookup/piani`)
        .json()
        .then(({ data }) =>
            data.map(v => ({
                id: v.idMultimediaPiano,
                name: capitalize(v.nome),
            }))
        );
}

export function getOrientationsLookup() {
    return http
        .get(`/ws/fotoplan/lookup/orientamenti`)
        .json()
        .then(({ data }) =>
            data.map(o => ({
                id: o.id,
                name: orientationsMapping.find(item => item.id === o.id).name,
            }))
        );
}

export function getFloors(idAnnuncio) {
    return http
        .get(`/ws/listing/${idAnnuncio}/floors/fotoplan`)
        .json()
        .then(({ data }) => {
            return data.map(f => ({
                id: parseInt(f.id),
                name: f.descrizione || '',
                lookupId: parseInt(f.fkMultimediaPiano),
            }));
        })
        .catch(() => []);
}

export function getImages(imgHost, idAnnuncio, propertyId = null) {
    const url = propertyId
        ? `/ws/listing/${idAnnuncio}/property/${propertyId}/images`
        : `/ws/listing/${idAnnuncio}/images`;
    return http
        .get(url)
        .json()
        .then(({ data }) => {
            return data.map(i => ({
                id: i.id,
                url: mediaAssetsVersioning(
                    getImageSize(imgHost, i.thumbPath, 'thumb', 'orig')
                ),
                extraUrls: {
                    md: mediaAssetsVersioning(
                        getImageSize(imgHost, i.thumbPath, 'thumb', 'print')
                    ),
                },
                title: i.didascalia,
            }));
        })
        .catch(() => []);
}

export function getInitialData(imgHost, idAnnuncio, propertyId = null) {
    const url = propertyId
        ? `/ws/listing/${idAnnuncio}/property/${propertyId}/plan`
        : `/ws/listing/${idAnnuncio}/plan`;
    return http
        .get(url)
        .json()
        .then(({ data }) => {
            if (!data.length) return;

            const plans = data.map(p => ({
                id: p.fkPlanimetria,
                image: p.thumbPath
                    ? {
                          url: mediaAssetsVersioning(
                              getImageSize(
                                  imgHost,
                                  p.thumbPath,
                                  'print',
                                  'orig'
                              )
                          ),
                          extraUrls: {
                              md: mediaAssetsVersioning(
                                  `${imgHost}${p.thumbPath}`
                              ),
                          },
                      }
                    : null,
                title: p.didascalia,
                floor: p.fotoplan
                    ? parseInt(p.fotoplan.fkBridgeAnnuncioMultimediaPiano)
                    : null,
                orientation: p.fotoplan ? p.fotoplan.orientamento : 0,
                ordering: p.ordinamento,
            }));

            const links = data
                .filter(
                    p =>
                        p.fotoplan &&
                        p.fotoplan.links &&
                        p.fotoplan.links.length
                )
                .reduce((accumulator, currentValue) => {
                    return accumulator.concat(currentValue.fotoplan.links);
                }, [])
                .map(l => ({
                    id: l.idFotoplanLink,
                    coords: JSON.parse(l.coordinate),
                    plan: l.fkPlanimetria,
                    orientation: l.fkOrientamento,
                    main: Boolean(Number(l.principale)),
                    image: l.fkImmagine,
                }));

            return {
                plans,
                links,
                id: idAnnuncio,
            };
        })
        .catch(() => {
            return {
                id: idAnnuncio,
                floors: null,
                plans: [],
                links: [],
            };
        });
}

export const getEditorWebApi = (imgHost, idAnnuncio, propertyId = null) => ({
    addFloor(plan, data) {
        return http
            .post(`/ws/fotoplan/piani/create_piano?tipo=1`, {
                json: {
                    idAnnuncio,
                    idMultimediaPiano: data.lookupId,
                    nome: data.name,
                    idAnnuncioMultimediaPianoPrecedente: data.prevFloor,
                },
            })
            .json()
            .then(({ data: res }) => {
                return enqueueOperation(() => {
                    const url = propertyId
                        ? `/ws/listing/${idAnnuncio}/property/${propertyId}/plan/${plan}`
                        : `/ws/listing/${idAnnuncio}/plan/${plan}`;

                    return http(url, {
                        method: 'PATCH',
                        json: { floor: res.idAnnuncioMultimediaPiano },
                    })
                        .json()
                        .then(() => ({
                            id: res.idAnnuncioMultimediaPiano,
                            lookupId: data.lookupId,
                            name: data.name,
                        }));
                });
            });
    },
    addLink(plan, data) {
        return http
            .post(`/ws/listing/${idAnnuncio}/plan/${plan}/link`, {
                json: {
                    coordinate: data.coords,
                    fkOrientamento: data.orientation,
                    fkImmagine: data.image,
                    principale: data.main,
                },
            })
            .json()
            .then(({ data: res }) => ({
                coords: JSON.parse(res.coordinate),
                plan: data.plan,
                image: res.fkImmagine,
                orientation: res.fkOrientamento,
                id: res.idFotoplanLink,
                main: Boolean(Number(res.principale)),
            }));
    },
    deleteLink(id, plan) {
        return http
            .delete(`/ws/listing/${idAnnuncio}/plan/${plan}/link/${id}`)
            .raw();
    },
    deletePlan(id) {
        const url = propertyId
            ? `/ws/listing/${idAnnuncio}/property/${propertyId}/plan/${id}`
            : `/ws/listing/${idAnnuncio}/plan/${id}`;
        return http.delete(url).raw();
    },
    deletePlanBatch(ids) {
        ids.forEach(id => this.deletePlan(id, idAnnuncio));
        return Promise.resolve();
    },
    editPlan(id, data) {
        const url = propertyId
            ? `/ws/listing/${idAnnuncio}/property/${propertyId}/plan/${id}`
            : `/ws/listing/${idAnnuncio}/plan/${id}`;
        return http(url, {
            method: 'PATCH',
            json: data,
        })
            .json()
            .then(() => true);
    },
    editPlanImage(id, data) {
        const jsonData = {
            imageTransformData: data.imageTransformData,
        };
        const url = propertyId
            ? `/ws/listing/${idAnnuncio}/property/${propertyId}/plan/${id}`
            : `/ws/listing/${idAnnuncio}/plan/${id}`;

        return http(url, {
            method: 'PATCH',
            json: jsonData,
        })
            .json()
            .then(({ data }) => {
                let links = null;

                if (
                    data.fotoplan &&
                    data.fotoplan.links &&
                    data.fotoplan.links.length
                ) {
                    links = data.fotoplan.links.map(l => ({
                        id: l.idFotoplanLink,
                        coords: JSON.parse(l.coordinate),
                        plan: l.fkPlanimetria,
                        orientation: l.fkOrientamento,
                        main: Boolean(Number(l.principale)),
                        image: l.fkImmagine,
                    }));
                }

                return {
                    id: data.fkPlanimetria,
                    image: data.thumbPath
                        ? {
                              url: mediaAssetsVersioning(
                                  getImageSize(
                                      imgHost,
                                      data.thumbPath,
                                      'print',
                                      'orig'
                                  )
                              ),
                              extraUrls: {
                                  md: mediaAssetsVersioning(
                                      `${imgHost}${data.thumbPath}`
                                  ),
                              },
                          }
                        : null,
                    links,
                };
            });
    },
    editLink(id, plan, data) {
        return http(`/ws/listing/${idAnnuncio}/plan/${plan}/link/${id}`, {
            method: 'PATCH',
            json: data,
        })
            .json()
            .then(() => true);
    },
    getPdfPlanImages(file) {
        const form = new FormData();

        form.append('planimetriaImmaginiPdf', file);

        return http
            .post(`/v2/item/upload-pdf-plans`, {
                form,
            })
            .json()
            .then(data =>
                data.images.map((url, index) => ({
                    index: index + 1,
                    name: file.name,
                    hash: url,
                }))
            )
            .catch(() => ({ error: true }));
    },
    swapPlans(from, to, plans) {
        const data = {
            ordering: {
                from: { ...from, ordering: parseInt(to.ordering) },
                to: { ...to, ordering: parseInt(from.ordering) },
            },
        };
        const url = propertyId
            ? `/ws/listing/${idAnnuncio}/property/${propertyId}/plan/${from.id}`
            : `/ws/listing/${idAnnuncio}/plan/${from.id}`;

        return http(url, {
            method: 'PATCH',
            json: data,
        })
            .json()
            .then(() => plans);
    },
    uploadPlanImage(file) {
        const form = new FormData();
        const url = propertyId
            ? `/ws/listing/${idAnnuncio}/property/${propertyId}/plan`
            : `/ws/listing/${idAnnuncio}/plan`;
        const fileName = truncateToLength(
            file.name.slice(0, file.name.lastIndexOf('.')),
            50
        );

        form.append('planimetria', file);
        form.append('didascalia', fileName);

        return http
            .post(url, {
                form,
            })
            .json()
            .then(({ data }) => ({
                id: data.fkPlanimetria,
                image: data.thumbPath
                    ? {
                          url: mediaAssetsVersioning(
                              getImageSize(
                                  imgHost,
                                  data.thumbPath,
                                  'print',
                                  'orig'
                              )
                          ),
                          extraUrls: {
                              md: mediaAssetsVersioning(
                                  `${imgHost}${data.thumbPath}`
                              ),
                          },
                      }
                    : null,
                title: fileName,
            }))
            .catch(() => Promise.reject());
    },
    uploadPdfPlanImage(data) {
        const form = new FormData();

        form.append('propertyId', idAnnuncio);
        form.append('imageUrl', data.hash);
        form.append(
            'didascalia',
            truncateToLength(
                `${data.name.slice(0, data.name.lastIndexOf('.'))} ${
                    data.index
                }`,
                50
            )
        );
        form.append('action', 'add');
        form.append('cid', idAnnuncio);

        return http
            .post(`/v2/item/upload-image-from-url`, {
                form,
            })
            .json()
            .then(({ element }) => ({
                id: element.id,
                title: element.didascalia,
                image: {
                    id: element.id,
                    url: mediaAssetsVersioning(
                        getImageSize(
                            imgHost,
                            element.thumbPath,
                            'print',
                            'orig'
                        )
                    ),
                    extraUrls: {
                        md: mediaAssetsVersioning(
                            `${imgHost}${element.thumbPath}`
                        ),
                    },
                },
            }))
            .catch(() => Promise.reject());
    },
});
