import React, { useEffect, useState, useReducer } from 'react';
import { lookupApi } from '../../web-api';
import { useFormikContext } from 'formik';
import { trans } from '@pepita-i18n/babelfish';
import { GxFkSelect } from 'gtx-react/components/gx-formik';

const reducer = (state = initialState, action) => {
    switch (action.type) {
        case 'SET_LOADING':
            return { ...state, ...action.payload };
        default:
            return state;
    }
};

const loadData = (uri, value) => lookupApi(uri, value);
/**
 * @typedef {Object} TypologyOption
 * @property {string} [label]
 * @property {any} [value]
 */

/**
 * @typedef {Object} TypologyFieldsInitialValues
 * @property {TypologyOption[]} [categories]
 * @property {TypologyOption[]} [typologies]
 * @property {TypologyOption[]} [subtypologies]
 * @property {TypologyOption[]} [contracts]
 */

/**
 * @typedef {Object} TypologyFieldsReqEndpointsMap
 * @property {string} [typology]
 * @property {string} [subtypology]
 * @property {string} [contract]
 */

/**
 * @typedef {Object} TypologyFieldsPlaceholders
 * @property {string} [category]
 * @property {string} [typology]
 * @property {string} [subtypology]
 * @property {string} [contract]
 */

/**
 * @typedef {Object} TypologyFieldsProps
 * @property {boolean} [hasSubtypology]
 * @property {boolean} [hasContract]
 * @property {TypologyFieldsInitialValues} [initialValues]
 * @property {string} [loadingClasses]
 * @property {Array} [noPlaceholder]
 * @property {TypologyFieldsReqEndpointsMap} reqEndpointsMap
 * @property {TypologyFieldsPlaceholders} [placeholders]
 */

/**
 * @param {TypologyFieldsProps} props
 */
const TypologyFields = ({
    initialValues,
    reqEndpointsMap,
    hasTypology,
    hasSubtypology = false,
    hasContract = true,
    placeholders = null,
}) => {
    const { values, setFieldValue } = useFormikContext();

    const [categories, setCategories] = useState(initialValues?.categories ?? []);
    const [typologies, setTypologies] = useState(initialValues?.typologies ?? []);
    const [subtypologies, setSubtypologies] = useState(initialValues?.subtypologies ?? []);
    const [contracts, setContracts] = useState(initialValues?.contracts ?? []);

    const [loadState, dispatch] = useReducer(reducer, {
        typology: false,
        subtypology: false,
        contract: false,
    });

    const loadTypologies = (category) => loadData(reqEndpointsMap.typology, category);

    const loadContracts = (category) => (hasContract ? loadData(reqEndpointsMap.contract, category) : null);

    useEffect(() => {
        const currentCategory = values['category'];

        if (hasTypology) {
            setFieldValue('typology', '');
            setTypologies([]);
        }

        if (hasContract) {
            setFieldValue('contract', '');
            setContracts([]);
        }

        if (hasSubtypology) {
            setFieldValue('subtypology', '');
            setSubtypologies([]);
        }

        dispatch({
            type: 'SET_LOADING',
            payload: {
                typology: true,
                contract: true,
            },
        });

        // load typologies
        const respTypology = hasTypology ? loadTypologies(currentCategory) : null;

        // load contracts
        const respContracts = loadContracts(currentCategory);

        Promise.all([respTypology, respContracts])
            .then((resp) => {
                const [typologies, contracts] = resp;
                if (hasTypology) {
                    setTypologies(typologies?.data);
                }
                if (hasContract) {
                    const contractsList = contracts && contracts.data ? contracts.data : initialValues.contracts;

                    setContracts([...contractsList]);
                }
                dispatch({
                    type: 'SET_LOADING',
                    payload: {
                        typology: false,
                        contract: false,
                    },
                });
            })
            .catch(() =>
                dispatch({
                    type: 'SET_LOADING',
                    payload: {
                        typology: false,
                        contract: false,
                    },
                })
            );
    }, [values['category']]);

    useEffect(() => {
        if (hasSubtypology && reqEndpointsMap.subtypology) {
            setFieldValue('subtypology', '');
            dispatch({ type: 'SET_LOADING', payload: { subtypology: true } });
            const respSubTypology = loadData(reqEndpointsMap.subtypology, values['typology']);
            respSubTypology
                ?.then((resp) => {
                    setSubtypologies(resp.data);
                    dispatch({
                        type: 'SET_LOADING',
                        payload: { subtypology: false },
                    });
                })
                .catch(() =>
                    dispatch({
                        type: 'SET_LOADING',
                        payload: { subtypology: false },
                    })
                );
            !respSubTypology &&
                dispatch({
                    type: 'SET_LOADING',
                    payload: {
                        subtypology: false,
                    },
                });
        }
    }, [values['typology']]);

    useEffect(() => {
        if (initialValues?.categories) {
            setCategories(initialValues.categories);
        }
    }, [initialValues?.categories]);

    useEffect(() => {
        if (initialValues?.contracts && hasContract) {
            setContracts(initialValues.contracts);
        }
    }, [initialValues?.contracts]);

    useEffect(() => {
        if (values?.contract && hasContract) {
            setFieldValue('contract', values.contract);
        }
    }, []);

    return (
        <>
            <div className="filter-box__section__item">
                <GxFkSelect
                    id="category"
                    name="category"
                    label={trans('label.category')}
                    options={categories}
                    isLoading={!categories || categories.length === 0}
                    placeholder={placeholders?.category}
                />
            </div>
            {hasTypology ? (
                <div className="filter-box__section__item">
                    <GxFkSelect
                        id="typology"
                        name="typology"
                        label={trans('label.typology')}
                        options={typologies || []}
                        disabled={!typologies || typologies.length < 2}
                        isLoading={loadState.typology}
                        placeholder={placeholders?.typology}
                    />
                </div>
            ) : null}
            {hasSubtypology ? (
                <div className="filter-box__section__item">
                    <GxFkSelect
                        id="subtypology"
                        name="subtypology"
                        label={trans('label.subtypology')}
                        options={subtypologies}
                        disabled={!subtypologies || subtypologies.length < 2}
                        isLoading={loadState.subtypology}
                        placeholder={placeholders?.subtypology}
                    />
                </div>
            ) : null}
            {hasContract ? (
                <div className="filter-box__section__item">
                    <GxFkSelect
                        id="contract"
                        name="contract"
                        label={trans('label.contract')}
                        options={contracts}
                        disabled={!contracts || contracts.length < 2}
                        isLoading={loadState.contract}
                        placeholder={placeholders?.contract}
                    />
                </div>
            ) : null}
        </>
    );
};

export default TypologyFields;
