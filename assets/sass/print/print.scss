@media print {
  @page {
    margin: 1cm 0.5cm;
  }

  #gx-navigation,
  .gx-navigation-skeleton {
    display: none !important;
  }

  *,
  *:before,
  *:after {
    background: transparent !important;
    color: #000 !important; // Black prints faster: h5bp.com/s
    box-shadow: none !important;
    text-shadow: none !important;
  }

  #content-wrapper {
    margin-top: 0;
  }

  abbr[title]:after {
    content: ' (' attr(title) ')';
  }

  pre,
  blockquote {
    border: 0.1rem solid #999;
    page-break-inside: avoid;
  }

  thead {
    display: table-header-group; // h5bp.com/t
  }

  tr,
  img {
    page-break-inside: avoid;
  }

  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  h2,
  h3 {
    page-break-after: avoid;
  }

  // Bootstrap specific changes start

  // Bootstrap components
  .navbar {
    display: none;
  }
  .btn,
  .dropup > .btn {
    > .caret {
      border-top-color: #000 !important;
    }
  }

  //Application specific styling
  #gtx-main-menu,
  .gtx-ask-help {
    display: none;
  }

  #container {
    padding-left: 0;
  }
}

.print-mode {
  header,
  #gtx-main-menu,
  .gtx-ah-footer-spacing,
  #gx-navigation,
  .gx-navigation-skeleton {
    display: none !important;
  }
  #content-wrapper {
    margin-left: 0;
  }
  &#container {
    padding-top: 0;
    padding-left: 0;
    min-width: inherit;
  }
  & #content-wrapper {
    min-height: inherit;
  }
}
