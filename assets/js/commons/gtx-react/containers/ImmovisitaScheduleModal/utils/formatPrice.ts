import { trans } from '@pepita-i18n/babelfish';
import { Prices } from '../types/realEstate';
import { currencyFormatter } from './currencyFormatter';

export const formatPrice = (priceItem: Prices) => {
    let out = '';
    if (!priceItem) return out;

    if (priceItem.price) {
        out += currencyFormatter(parseInt(priceItem.price));
    }
    if (priceItem.contractId == 2) {
        out += '/' + trans('label.month');
    }

    return out;
};
