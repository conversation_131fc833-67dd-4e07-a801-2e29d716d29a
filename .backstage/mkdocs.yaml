site_name: 'Gest<PERSON><PERSON>'
repo_url: https://gitlab.pepita.io/getrix/mls-site
edit_uri: getrix/mls-site/.backstage/docs/

nav:
    - "Home": index.md
    - "Requirements": requirements.md
    - "Documentation":
        "Frontend Scaffolding": fe-scaffolding.md
        "Polyfill": polyfill.md
        "Frontend testing": fe-testing.md
        "GX Navigation": gx-navigation.md
    - "Where it's published": deploy.md
    - "Where to find the logs": logs.md
    - "Links": links.md
    - "Dependencies": dependencies.md
    - "Supported Devices": devices.md
    - "Issues": issues.md
    - "Contributing": contributing.md
    - "Reference team": team.md

plugins:
  - techdocs-core
