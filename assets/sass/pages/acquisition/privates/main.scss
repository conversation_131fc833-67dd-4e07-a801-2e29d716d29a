@use '../../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

// Altezza helper
$helper-height: 9.6rem;

.acquisition {
  padding-bottom: 7.2rem;
}

.black-tooltip {
  max-width: 16.5rem;
  border-radius: radius(sm);
  background: rgba(color(background-reversed, true), 0.8) !important;
  color: color(content-accent);
  font-size: 1.2rem;
  box-shadow: none;

  .popover-content {
    padding: 0.6rem;
  }

  .arrow {
    border-top-color: transparent !important;

    &:after {
      border-top-color: rgba(color(background-reversed, true), 0.8) !important;
    }
  }
}

.black-tooltip--medium-screen {
  @extend .black-tooltip;
  max-width: 15rem;
}

.black-tooltip--small-screen {
  @extend .black-tooltip;
  max-width: 10.5rem;
}

.property-card {
  position: relative;
  padding: space(md) space(md) space(2xl);
  border-top: 0.1rem solid color(border-main);
  border-bottom: 0.1rem solid color(border-main);
  background-color: color(background-main);

  &__check {
    margin: (-(space(md))) (-(space(md))) space(md);
    padding: space(md);
    border-bottom: 0.1rem solid color(border-main);
    line-height: 1;

    @include media('screen', '<#{breakpoint(sm)}') {
      .property-card:first-child & {
        border-top: 0.1rem solid color(border-main);
      }
    }
  }

  @include media('screen', '<#{breakpoint(sm)}') {
    &:first-child {
      border-top: none;
    }
  }

  & + & {
    margin-top: space(sm);
  }

  &__info {
    display: flex;
    flex-wrap: wrap;
    margin-top: space(sm);

    &__item {
      width: 50%;
      margin-bottom: space(md);
      padding-right: space(sm);

      .title {
        color: color(content-low);
        font-size: 1.2rem;
        text-transform: uppercase;
      }

      .content--fixed-height {
        display: flex;
        align-items: center;
        height: 3.6rem;
      }

      &--id {
        width: 100%;
      }
    }
  }

  &__actions {
    display: flex;
    position: absolute;
    bottom: space(md);
    left: space(md);
    width: calc(100% - #{space(xl)});

    .btn {
      flex: 1 1 0;

      & + .btn,
      & + .btn-group {
        margin-left: space(xs);
      }
    }

    .dropup {
      flex-grow: initial;
    }

    &--grow {
      > * {
        width: 50%;
      }
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    width: calc(50% - 1.2rem);
    margin-bottom: space(lg);
    border: 0.1rem solid color(border-main);

    & + & {
      margin-top: 0;
    }
  }
}

.property-item {
  display: flex;
  padding-bottom: space(sm);
  border-bottom: 0.1rem solid color(border-main);

  &__photo {
    position: relative;
    width: ($gx-unit-size * 11);
    height: space(3xl);
    margin-right: space(sm);

    img {
      position: absolute;
      object-fit: cover;
      width: 100%;
      height: 100%;

      &[src$='placeholder.png'] {
        object-fit: unset;
      }
    }
  }

  &__desc {
    width: 100%;
    color: color(content-medium);
    font-size: 1.4rem;

    .property-item__photo + & {
      width: calc(100% - #{$gx-unit-size * 12});
    }

    &__features {
      display: flex;
      flex-wrap: wrap;
      max-height: 2rem;
      overflow: hidden;

      svg {
        width: 1em;
        height: 1em;
        margin-left: space(xs);
        fill: color(content-medium);
        font-size: 1.4rem;
        vertical-align: middle;
      }

      span {
        + span {
          margin-left: space(xs);
          padding-left: space(xs);
          border-left: 0.1rem solid color(border-main);
        }
      }
    }

    &__category,
    &__geography {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;

      span + span {
        margin-left: space(xs);
      }
    }

    &__category {
      font-size: 1.2rem;
    }
  }
}

.generic-content-block {
  color: color(content-medium);
  font-size: 1.2rem;

  svg {
    width: 1em;
    height: 1em;
    margin-left: space(xs);
    fill: color(content-medium);
    font-size: 1.4rem;
    vertical-align: middle;
  }

  .portal-logo {
    max-width: 8rem;
  }

  &--adress > div {
    max-width: 16rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .room,
  .floor {
    display: inline-flex;
    align-items: center;
    cursor: default;
  }

  .room + .floor {
    margin-left: 0.5rem;
  }

  .price {
    b {
      white-space: nowrap;
    }
  }

  &--ellipsis {
    max-width: 9rem;

    > div {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  @include media('screen', '>=#{emConverter(1200)}') {
    &--typology {
      max-width: 13rem;
    }
  }

  &--city {
    max-width: 10rem;

    @include media('screen', '>=#{emConverter(1200)}') {
      max-width: 14rem;
    }

    @include media('screen', '>=#{emConverter(1500)}') {
      max-width: 19rem;
    }

    @include media('screen', '>=#{emConverter(1600)}') {
      max-width: 24rem;
    }
  }
}

.property-card__info__item {
  .portal-logo {
    width: 100%;
    max-width: ($gx-unit-size * 11);
  }
}

.th-riscontri {
  width: 8.1rem;
}

.list__field--actionsGroup {
  width: 11.9rem;
  min-width: 11.9rem;

  .agency-estimates-list & {
    width: 14.4rem;
  }
}

.row-with-tag {
  display: flex;
  align-items: center;

  .gx-tagInput {
    width: 100%;
    .gx-input-wrapper {
      width: 100%;
    }

    // TODO: spostare a livello di gx-design
    .gx-action-list {
      .gx-tag {
        margin-right: 0;
      }
    }
  }

  & + & {
    margin-top: space(md);
  }

  > .gtx-tag,
  > .gx-tag {
    & + * {
      margin-left: space(md);
    }
  }

  &--textarea {
    align-items: flex-start;

    svg {
      fill: #999;
      @include icon-size(md);
    }

    .gx-textarea-wrapper {
      flex-grow: 1;
    }
  }
}

.contentCell {
  margin-top: space(xs);
  padding-right: space(sm);
  color: color(content-medium);

  @include media('screen', '>=#{breakpoint(md)}') {
    padding-right: 0;
  }

  &--fixed-height {
    display: flex;
    align-items: center;
    height: 3.6rem;
    margin-top: 0;
  }

  > * + * {
    margin-left: space(xs);
  }

  .no-content {
    font-size: 1.2rem;
  }

  &--outcome {
    display: flex;
    @include media('screen', '>=#{breakpoint(md)}') {
      flex-direction: column;
    }

    > svg {
      align-self: start;
      @include icon-size(md);

      & + svg {
        margin-top: space(xs);
        margin-left: 0;
      }
    }

    .gx-pointer {
      @include icon-size(md);
    }
  }
}

.list__field--information {
  width: ($gx-unit-size * 12);
}

.emptyList {
  min-height: calc(100vh - #{$gx-new-menu-header-height}); //altezza dispositivo meno altezza header e toolbar
  padding: space(2xl) space(md) $helper-height;
  background-color: color(background-alt);
  color: color(content-medium);
  text-align: center;

  &__pic {
    width: 16rem;
    height: 15.4rem;
    margin: 0 auto space(lg);
    background-repeat: no-repeat;
    background-size: cover;

    //TODO: da rivedere con un @each?
    &--noProperty {
      @include img-retina(
        $gtx-common-base-path-img + 'no-results/placeholder-nessun-annuncio.png',
        $gtx-common-base-path-img + 'no-results/<EMAIL>',
        16rem,
        15.4rem
      );
    }

    &--noResults {
      @include img-retina(
        $gtx-common-base-path-img + 'no-results/placeholder-nessun-risultato.png',
        $gtx-common-base-path-img + 'no-results/<EMAIL>',
        16rem,
        15.4rem
      );
    }

    &--noEstimates {
      @include img-retina(
        $gtx-common-base-path-img + 'no-results/placeholder-valutazioni.png',
        $gtx-common-base-path-img + 'no-results/<EMAIL>',
        16rem,
        15.4rem
      );
    }
  }

  &__title {
    margin-bottom: space(sm);
    font-size: 2.4rem;
    color: color(content-high);
  }

  &__text {
    max-width: 46rem;
    margin: 0 auto;
    color: color(content-low);
    font-size: 1.6rem;

    &--dark {
      color: color(content-medium);
    }

    .phone-number {
      white-space: nowrap;
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    &__pic {
      width: 20rem;
      height: 19.2rem;
    }
  }
}

.status-select {
  .dropdown-menu {
    width: 100%;
  }

  li a {
    display: flex;
    align-items: center;

    .gtx-tag,
    .gx-tag {
      margin-right: space(md);
    }
  }
}

.modal__set-outcome--loader {
  position: relative;
  height: 25.3rem;
}

.results-bar--ad .results-bar__number.results-acquisition {
  display: flex;
}

// Correzione barra helper
.content-acquisition {
  .list-footer {
    padding-bottom: $helper-height;
  }

  & + .gtx-ask-help + .gtx-ah-footer-spacing {
    height: 0;
  }

  form {
    &.invalid {
      div.filter-box__section__item.filter-box__section__item__code {
        margin-bottom: space(sm);
      }
    }
  }

  .filter-box {
    input.error {
      border: 0.1rem solid color(border-error);

      & + div {
        padding-top: space(sm);
        color: color(content-error);
      }
    }
  }
}

.results-bar-mobile-reset {
  padding: 1rem space(md);
  border-bottom: 0.1rem solid color(border-main);
}

.list__item,
.property-card {
  &.disabled {
    opacity: 0.4;
  }
}

.property-not-available {
  width: 100%;
  font-weight: 600;
}

// TODO: FIX acquisizione privati, rimuovere quando sarà messa la grid
.filter-boxEsito {
  display: flex;
  align-items: flex-end;
  flex-wrap: wrap;

  @include media('screen', '<#{breakpoint(md)}', '>=#{breakpoint(sm)}') {
    .filter-box__section__item {
      width: calc(25% - #{space(lg)});
    }

    .filter-box__section__item + .filter-box__section__item {
      margin-left: space(xl);
    }
  }

  @include media('screen', '<#{breakpoint(sm)}') {
    .filter-box__section__item {
      width: 100%;
      margin-bottom: space(md);
    }
  }
}

.request-modal-info {
  margin: -3rem -3rem space(xl);
  padding: space(md) 3rem;
  background-color: color(background-alt);

  @include media('screen', '<#{breakpoint(sm)}') {
    padding: space(md) 2rem;
    margin: -2rem -2rem space(xl);
  }

  div + div {
    margin-top: space(sm);
  }

  &__address {
    font-weight: 600;
    font-size: 1.4rem;
  }

  &__property,
  &__contact {
    font-size: 1.2rem;
  }
}

.acquisition-estimates-detail-actions {
  display: flex;
  align-items: center;

  .gx-badge {
    margin-right: space(md);
  }

  &__wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.acquisition-estimates-detail-title {
  padding: space(md);
  border-bottom: 0.1rem solid color(border-main);

  span {
    font-weight: 600;
    font-size: 1.8rem;
  }
}
