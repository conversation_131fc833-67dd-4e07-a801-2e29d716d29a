import { Modal } from '@gx-design/modal';
import { trans } from '@pepita-i18n/babelfish';
import { FC, PropsWithChildren, createContext, useMemo, useState } from 'react';

import { PortalEntityDetail, PortalEntityDetailProps } from '.';

type ClosedModal = {
    isOpen: false;
    category?: never;
    propertyId?: never;
};
type OpenedModal = {
    isOpen: true;
    category: PortalEntityDetailProps['category'];
    propertyId: string;
};
type DetailModal = ClosedModal | OpenedModal;

type ModalContext = null | {
    showModal: React.Dispatch<React.SetStateAction<DetailModal>>;
};

export const PortalEntityDetailModalContext = createContext<ModalContext>(null);

export const PortalEntityDetailModalProvider: FC<PropsWithChildren> = ({
    children,
}) => {
    const [modal, showModal] = useState<DetailModal>({ isOpen: false });

    const closeModal = () => showModal({ isOpen: false });

    const value = useMemo(() => ({ showModal }), []);

    return (
        <PortalEntityDetailModalContext.Provider value={value}>
            {children}
            {modal.isOpen && (
                <Modal
                    isOpen
                    size="large"
                    title={trans('label.ad_details')}
                    onClose={closeModal}
                >
                    <PortalEntityDetail
                        onError={closeModal}
                        category={modal.category}
                        propertyId={modal.propertyId}
                    />
                </Modal>
            )}
        </PortalEntityDetailModalContext.Provider>
    );
};
