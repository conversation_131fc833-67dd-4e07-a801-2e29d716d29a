parameters:
  # Authenticated DataProvider interfaces
  authenticated_data_provider_agency: App\DataProvider\Agency\ApiGtx\Authenticated\AuthenticatedAgencyProxy
  authenticated_data_provider_agent: App\DataProvider\Agent\ApiGtx\Authenticated\AuthenticatedAgentProxy
  authenticated_data_provider_lookup: App\DataProvider\Lookup\ApiGtx\Authenticated\AuthenticatedLookupProxy
  authenticated_data_provider_thread: App\DataProvider\Thread\ApiGtx\Authenticated\AuthenticatedThreadProxy
  authenticated_data_provider_message: App\DataProvider\Message\ApiGtx\Authenticated\AuthenticatedMessageProxy
  authenticated_data_provider_property: App\DataProvider\Property\ApiGtx\Authenticated\AuthenticatedPropertyProxy
  authenticated_data_provider_onboarding_tour: App\DataProvider\OnboardingTour\ApiGtx\Authenticated\AuthenticatedOnboardingTourProxy
  authenticated_data_provider_search: App\DataProvider\Search\ApiGtx\Authenticated\AuthenticatedSearchProxy
  authenticated_data_provider_received_search: App\DataProvider\ReceivedSearch\ApiGtx\Authenticated\AuthenticatedReceivedSearchProxy
  authenticated_data_provider_virtual_tour: App\DataProvider\VirtualTour\ApiGtx\Authenticated\AuthenticatedVirtualTourProxy
  authenticated_data_provider_invoice: App\DataProvider\Invoice\ApiGtx\Authenticated\AuthenticatedInvoiceProxy
  authenticated_data_provider_geography: App\DataProvider\Geography\ApiGtx\Authenticated\AuthenticatedGeographyProxy
  authenticated_data_provider_contact_us: App\DataProvider\ContactUs\ApiSalesforce\Authenticated\AuthenticatedContactUsProxy
  authenticated_data_provider_match: App\DataProvider\Match\ApiGtx\Authenticated\AuthenticatedMatchProxy
  authenticated_data_provider_agency_active_search: App\DataProvider\AgencyActiveSearch\ApiGtx\Authenticated\AuthenticatedAgencyActiveSearchProxy
  authenticated_data_provider_messaging: App\DataProvider\Messaging\ApiGtx\Authenticated\AuthenticatedMessagingProxy
  authenticated_data_provider_configuration: App\DataProvider\Configuration\ApiGtx\Authenticated\AuthenticatedConfigurationProxy
  authenticated_data_provider_dea_login: App\DataProvider\Login\ApiDea\Authenticated\AuthenticatedDeaLoginProxy
  authenticated_data_provider_events_tracker: App\DataProvider\EventsTracker\MixpanelEventCatcher\Authenticated\AuthenticatedEventsTrackerProxy
  authenticated_data_provider_suggestions: App\DataProvider\Suggestions\ApiGtx\Authenticated\AuthenticatedSuggestionsProxy

  # DataProvider interfaces
  data_provider_agency: App\DataProvider\Agency\ApiGtx\Proxy\AgencyProxy
  data_provider_agent: App\DataProvider\Agent\ApiGtx\Proxy\AgentProxy
  data_provider_lookup: App\DataProvider\Lookup\ApiGtx\Proxy\LookupProxy
  data_provider_thread: App\DataProvider\Thread\ApiGtx\Proxy\ThreadProxy
  data_provider_message: App\DataProvider\Message\ApiGtx\Proxy\MessageProxy
  data_provider_property: App\DataProvider\Property\ApiGtx\Proxy\PropertyProxy
  data_provider_onboarding_tour: App\DataProvider\OnboardingTour\ApiGtx\Proxy\OnboardingTourProxy
  data_provider_search: App\DataProvider\Search\ApiGtx\Proxy\SearchProxy
  data_provider_received_search: App\DataProvider\ReceivedSearch\WsGtx\Proxy\ReceivedSearchProxy
  data_provider_virtual_tour: App\DataProvider\VirtualTour\ApiGtx\Proxy\VirtualTourProxy
  data_provider_invoice: App\DataProvider\Invoice\ApiGtx\Proxy\InvoiceProxy
  data_provider_geography: App\DataProvider\Geography\ApiGtx\Proxy\GeographyProxy
  data_provider_contact_us: App\DataProvider\ContactUs\ApiSalesforce\Proxy\ContactUsProxy
  data_provider_match: App\DataProvider\Match\ApiGtx\Proxy\MatchProxy
  data_provider_agency_active_search: App\DataProvider\AgencyActiveSearch\ApiGtx\Proxy\AgencyActiveSearchProxy
  data_provider_messaging: App\DataProvider\Messaging\ApiGtx\Proxy\MessagingProxy
  data_provider_configuration: App\DataProvider\Configuration\ApiGtx\Proxy\ConfigurationProxy
  data_provider_dea_login: App\DataProvider\Login\ApiDea\Proxy\DeaLoginProxy
  data_provider_events_tracker: App\DataProvider\EventsTracker\MixpanelEventCatcher\Proxy\EventsTrackerProxy
  data_provider_suggestions: App\DataProvider\Suggestions\ApiGtx\Proxy\SuggestionsProxy

services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  # Authenticated DataProvider interfaces
  App\DataProvider\Agency\AuthenticatedAgencyInterface:
    class: '%authenticated_data_provider_agency%'
  App\DataProvider\Agent\AuthenticatedAgentInterface:
      class: '%authenticated_data_provider_agent%'
  App\DataProvider\Lookup\AuthenticatedLookupInterface:
    class: '%authenticated_data_provider_lookup%'
  App\DataProvider\Thread\AuthenticatedThreadInterface:
    class: '%authenticated_data_provider_thread%'
  App\DataProvider\Message\AuthenticatedMessageInterface:
    class: '%authenticated_data_provider_message%'
  App\DataProvider\Property\AuthenticatedPropertyInterface:
    class: '%authenticated_data_provider_property%'
  App\DataProvider\EventsTracker\AuthenticatedEventsTrackerInterface:
    class: '%authenticated_data_provider_events_tracker%'
  App\DataProvider\OnboardingTour\AuthenticatedOnboardingTourInterface:
    class: '%authenticated_data_provider_onboarding_tour%'
  App\DataProvider\Search\AuthenticatedSearchInterface:
    class: '%authenticated_data_provider_search%'
  App\DataProvider\ReceivedSearch\AuthenticatedReceivedSearchInterface:
    class: '%authenticated_data_provider_received_search%'
  App\DataProvider\OnboardingTour\AuthenticatedVirtualTourInterface:
    class: '%authenticated_data_provider_virtual_tour%'
  App\DataProvider\Invoice\AuthenticatedInvoiceInterface:
    class: '%authenticated_data_provider_invoice%'
  App\DataProvider\Geography\AuthenticatedGeographyInterface:
    class: '%authenticated_data_provider_geography%'
  App\DataProvider\ContactUs\AuthenticatedContactUsInterface:
    class: '%authenticated_data_provider_contact_us%'
  App\DataProvider\Match\AuthenticatedMatchInterface:
    class: '%authenticated_data_provider_match%'
  App\DataProvider\AgencyActiveSearch\AuthenticatedAgencyActiveSearchInterface:
    class: '%authenticated_data_provider_agency_active_search%'
  App\DataProvider\Messaging\AuthenticatedMessagingInterface:
    class: '%authenticated_data_provider_messaging%'
  App\DataProvider\Configuration\AuthenticatedConfigurationInterface:
    class: '%authenticated_data_provider_configuration%'
  App\DataProvider\Login\AuthenticatedDeaLoginInterface:
    class: '%authenticated_data_provider_dea_login%'
  App\DataProvider\Suggestions\AuthenticatedSuggestionsInterface:
      class: '%authenticated_data_provider_suggestions%'

  # DataProvider interfaces
  App\DataProvider\Agency\AgencyInterface:
    class: '%data_provider_agency%'
  App\DataProvider\Agent\AgentInterface:
      class: '%data_provider_agent%'
  App\DataProvider\Lookup\LookupInterface:
    class: '%data_provider_lookup%'
  App\DataProvider\Thread\ThreadInterface:
    class: '%data_provider_thread%'
  App\DataProvider\Message\MessageInterface:
    class: '%data_provider_message%'
  App\DataProvider\Property\PropertyInterface:
    class: '%data_provider_property%'
  App\DataProvider\EventsTracker\EventsTrackerInterface:
    class: '%data_provider_events_tracker%'
  App\DataProvider\OnboardingTour\OnboardingTourInterface:
    class: '%data_provider_onboarding_tour%'
  App\DataProvider\Search\SearchInterface:
    class: '%data_provider_search%'
  App\DataProvider\ReceivedSearch\ReceivedSearchInterface:
    class: '%data_provider_received_search%'
  App\DataProvider\VirtualTour\VirtualTourInterface:
    class: '%data_provider_virtual_tour%'
  App\DataProvider\Invoice\InvoiceInterface:
    class: '%data_provider_invoice%'
  App\DataProvider\Geography\GeographyInterface:
    class: '%data_provider_geography%'
  App\DataProvider\ContactUs\ContactUsInterface:
    class: '%data_provider_contact_us%'
  App\DataProvider\Match\MatchInterface:
    class: '%data_provider_match%'
  App\DataProvider\AgencyActiveSearch\AgencyActiveSearchInterface:
    class: '%data_provider_agency_active_search%'
  App\DataProvider\Messaging\MessagingInterface:
    class: '%data_provider_messaging%'
  App\DataProvider\Configuration\ConfigurationInterface:
    class: '%data_provider_configuration%'
  App\DataProvider\Login\DeaLoginInterface:
    class: '%data_provider_dea_login%'
  App\DataProvider\Suggestions\SuggestionsInterface:
      class: '%data_provider_suggestions%'
