@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.customer-list-print {
  padding: space(xl);

  &::before {
    content: var(--agencyName);
    font-weight: bold;
  }

  &__contacts {
    &::before {
      content: var(--agencyAddress);
    }

    &::after {
      display: block;
      content: var(--agencyContacts);
      margin-bottom: space(md);
    }
  }

  .list__head {
    border-bottom: 0.1rem solid color(border-main) !important;
  }

  .list,
  .list__body {
    display: revert;
    table-layout: fixed;
  }

  .list__item {
    display: table-row;
    page-break-inside: avoid;

    & + .list__item {
      border-top: 0.1rem solid color(border-main);
    }
  }

  .list__field {
    display: table-cell;
    width: 12%;
    padding: space(sm);

    &:first-child {
      padding-left: 0;

      .generic-content-block {
        > div {
          min-width: 8rem;
        }
      }
    }

    &:last-child {
      padding-right: 0;
    }

    .generic-content-block {
      font-size: 1.1rem;
      word-break: break-all;

      > div {
        word-break: break-all;
      }
    }

    &:nth-child(3) {
      width: 18%;
      font-size: 1rem;

      .generic-content-block {
        white-space: normal;
      }
    }
  }
}

.modal-with-cards {
  @include media('>=#{breakpoint(md)}') {
    .gx-alert {
      margin: space(md) space(lg);
    }
  }

  @include media('<#{breakpoint(md)}') {
    .modal-body {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      padding: space(xl) space(xl) space(sm);
    }

    .gx-alert {
      flex-grow: 1;
      margin: 0;
    }

    .list-footer {
      width: 100%;
      background-color: color(background-main);
      border-top-color: color(content-accent);
    }
  }

  @include media('<#{breakpoint(sm)}') {
    .modal-body {
      display: block;
    }

    .property-card {
      width: 100%;
      border-top: none;
    }
  }
}
