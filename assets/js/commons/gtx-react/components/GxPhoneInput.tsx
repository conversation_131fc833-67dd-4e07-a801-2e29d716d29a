import React from 'react';
import { HelperText } from '@gx-design/helper-text';
import { Icon } from '@gx-design/icon';
import { Tooltip } from '@gx-design/tooltip';
import clsx from 'clsx';
import { InputProps } from '@gx-design/input';
import { GxPrefixSelector } from './GxPrefixSelector';
import { CountryCallingCode } from 'gtx-react/utils/extractProfileData';

export type GxPhoneInputProps = InputProps & {
    prefixValue: CountryCallingCode;
    onPrefixChange: (_newShortCode: CountryCallingCode | undefined) => void;
    prefixName: string;
};

export const GxPhoneInput: React.FC<GxPhoneInputProps> = ({
    label,
    tooltipHelper,
    isLabelVisible = true,
    error = '',
    onPrefixChange,
    prefixValue,
    prefixName,
    ...props
}) => {
    return (
        <div className="gx-input-wrapper">
            <label
                htmlFor={props.id && props.id}
                className={clsx('gx-label', {
                    'gx-sr-only': !isLabelVisible,
                })}
            >
                {label}
                {props.required && (
                    <span className="gx-label__required">*</span>
                )}
                {tooltipHelper && (
                    <span className="gx-tip">
                        <Tooltip position="top" text={tooltipHelper}>
                            <Icon
                                className="gx-icon--info"
                                name="info-circle"
                            />
                        </Tooltip>
                    </span>
                )}
            </label>
            <div className="gx-input-select-wrapper">
                <GxPrefixSelector
                    name={prefixName}
                    value={prefixValue}
                    onChange={onPrefixChange}
                />
                <input
                    className={clsx('gx-input', {
                        'gx-input--negative': error,
                    })}
                    {...props}
                />
            </div>
            {error && <HelperText text={error} style="error" />}
        </div>
    );
};
