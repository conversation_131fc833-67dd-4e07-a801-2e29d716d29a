{"$id": "http://gestionale.immobiliare.it/list_properties_counters.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "List Properties Statistics", "type": "object", "properties": {"status": {"enum": ["success", "error"]}, "data": {"type": "object", "properties": {"premium": {"type": "object", "properties": {"sale": {"$ref": "#/definitions/sold"}, "rent": {"$ref": "#/definitions/rent"}, "newConstruction": {"type": ["object", "null"], "oneOf": [{"type": "null"}, {"$ref": "#/definitions/sold"}]}}, "required": ["sale", "rent", "newConstruction"], "additionalProperties": false}, "showcase": {"type": ["object", "null"], "oneOf": [{"type": "null"}, {"$ref": "#/definitions/counter"}]}, "secretProperty": {"type": ["object", "null"], "oneOf": [{"type": "null"}, {"$ref": "#/definitions/counter"}]}, "star": {"type": ["object", "null"], "oneOf": [{"type": "null"}, {"$ref": "#/definitions/counter"}]}, "top": {"type": ["object", "null"], "oneOf": [{"type": "null"}, {"$ref": "#/definitions/counter"}]}}, "required": ["premium", "showcase", "star", "top"], "additionalProperties": false}}, "definitions": {"counter": {"type": "object", "properties": {"count": {"type": "integer", "default": 0, "min": 0}, "totals": {"type": "integer", "default": 0, "min": 0}}, "required": ["sold", "totals"], "additionalProperties": false}, "rent": {"type": "object", "properties": {"rented": {"type": "integer", "default": 0, "min": 0}, "totals": {"type": "integer", "default": 0, "min": 0}}, "required": ["sold", "totals"], "additionalProperties": false}, "sold": {"type": "object", "properties": {"sold": {"type": "integer", "default": 0, "min": 0}, "totals": {"type": "integer", "default": 0, "min": 0}}, "required": ["sold", "totals"], "additionalProperties": false}}, "required": ["status", "data"], "additionalProperties": false}