@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;

.gx-range-input {
  min-width: 30rem;
  max-width: 32rem;

  &Control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: ($gx-unit-size * 5);
    padding: 0 space(sm) 0 space(md);
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);
    background-color: color(background-main);
    color: color(content-selectable);
    cursor: pointer;
    transition: none;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit  !important;
    text-transform: none !important;
    margin-left: 0 !important;

    &:hover,
    &:active,
    &:focus {
      background-image: none !important;
      background-color: color(background-main) !important;
      color: color(content-selectable) !important;
      border-color: color(border-main) !important;
      box-shadow: none !important;
      transform: none !important;
      filter: brightness(1) !important;
      opacity: 1 !important;
      text-decoration: none !important;
      outline: none !important;
    }
    

   

    &.is-selected {
      background-color: color(background-selected);
      color: color(content-selected);
      border-color: color(border-selected);

      &:hover,
      &:active,
      &:focus {
        background-image: none !important;
        background-color: color(background-selected) !important;
        color: color(content-selected) !important;
        border-color: color(border-selected) !important;
      }
    }

    svg {
      flex-shrink: 0;
      @include icon-size(md);
      color: currentColor;
    }

    &__caret {
      transition: transform 0.3s ease-in-out;
    }

    * + * {
      margin-left: space(sm);
    }
  }

  &__inputContainer {
    display: flex;
    padding: space(sm) space(md);
    gap: space(md);
    border-bottom: 0.1rem solid color(border-main);
  }

  &__list {
    padding: 0;
    overflow-y: auto;
    overscroll-behavior-y: contain;
    max-height: 21.6rem;
    background-attachment: local, local, scroll, scroll;
    background-image: linear-gradient(color(background-main), color(background-main)),
      linear-gradient(color(background-main), color(background-main)), linear-gradient(rgba(0, 0, 0, 0.1), transparent),
      linear-gradient(0deg, rgba(0, 0, 0, 0.1), transparent);
    background-repeat: no-repeat;
    background-position:
      0 0,
      0 100%,
      0 0,
      0 100%;
    background-size: 100% space(sm);
    transition: max-height 0.3s ease;
    background-color: color(background-main);

    &--right {
      text-align: right;
    }

    &Item {
      padding: space(sm) space(lg);
      cursor: pointer;

      &:hover {
        background-color: color(background-alt);
        color: color(content-selectable);
      }

      &.is-selected {
        background-color: color(background-selected);
        color: color(content-selected);
      }
    }
  }

  &__footer {
    padding: space(md);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 0.1rem solid color(border-main);

    a {
      cursor: pointer;
      color: color(content-action);
      @include typography(body-small);
    }
  }
}

  .is-open {
      .gx-range-inputControl__caret {
        transform: rotate(180deg);
      }
    }
 