import { Badge } from '@gx-design/badge';
import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import { FC, useMemo, useState } from 'react';
import {
    useColumnOrderState,
    useColumnSizingState,
    useColumnVisibilityState,
    useDefaultOrderingColumns,
} from 'gtx-react/components/tanstack-table/hooks';
import {
    ColumnPinningState,
    getCoreRowModel,
    getSortedRowModel,
    SortingState,
} from '@tanstack/react-table';
import {
    CrmTable,
    useReactTableV1,
} from 'gtx-react/components/tanstack-table/v1';
import { getDefaultColumnOrder } from 'gtx-react/components/tanstack-table/helpers';
import { formatDate } from 'lib/datefns';
import useInvoicesConfig from '../hooks/useInvoicesConfig';
import { Invoice } from '../types/apiResponse';
import { renderStatusTagStyle } from '../utils/utils';
import useInvoiceActions from '../hooks/useInvoiceActions';
import { convertCase, ucFirst } from 'lib/strings-formatter';

const useInvoiceColumns = (
    paymentLinkByIdAction: (id: Invoice['id']) => string,
    showPaymentButton: (data: Invoice) => boolean
) => {
    return useMemo(
        () => [
            {
                id: 'number',
                accessorKey: 'number',
                header: trans('label.document_number'),
                enableSorting: true,
                cell: ({ row }) => <span>{row.original.number}</span>,
            },
            {
                id: 'type',
                accessorKey: 'type',
                header: trans('label.type'),
                enableSorting: true,
                cell: ({ row }) => (
                    <span>
                        {row.original.type
                            ? ucFirst(
                                  trans(
                                      `invoice.type.${convertCase(
                                          row.original.type,
                                          'snake'
                                      )}`
                                  )
                              )
                            : '--'}
                    </span>
                ),
            },
            {
                id: 'date',
                accessorKey: 'date',
                header: trans('label.issue_date'),
                enableSorting: true,
                cell: ({ row }) => (
                    <span>
                        {formatDate(row.original.date, 'dd/MM/yy', {
                            fallBackStr: '--',
                        })}
                    </span>
                ),
            },
            {
                id: 'expirationDate',
                accessorKey: 'expirationDate',
                header: trans('label.expiration_date'),
                enableSorting: true,
                cell: ({ row }) => (
                    <span>
                        {formatDate(row.original.expirationDate, 'dd/MM/yy', {
                            fallBackStr: '--',
                        })}
                    </span>
                ),
            },
            {
                id: 'status',
                accessorKey: 'status',
                header: trans('label.status'),
                enableSorting: true,
                cell: ({ row }) =>
                    row.original.status ? (
                        <Badge
                            style={renderStatusTagStyle(row.original.status)}
                            text={ucFirst(
                                trans(
                                    `invoice.status.${convertCase(
                                        row.original.status,
                                        'snake'
                                    )}`
                                )
                            )}
                        />
                    ) : (
                        '--'
                    ),
            },
            {
                id: 'actions',
                header: '',
                enableSorting: false,
                cell: ({ row }) => (
                    <div className="gx-multiButton gx-multiButton--separeted-xs">
                        {/* Download action */}
                        {row.original.attachment && (
                            <Button
                                iconOnly
                                size="small"
                                title={trans('label.download')}
                                onClick={() =>
                                    window.open(
                                        `/download_fattura.php?id=${row.original.id}`
                                    )
                                }
                            >
                                <Icon name="download" />
                            </Button>
                        )}
                        {/* Payment action */}
                        {showPaymentButton(row.original) && (
                            <Button
                                as="a"
                                href={paymentLinkByIdAction(row.original.id)}
                                target="_blank"
                                rel="noopener noreferrer"
                                size="small"
                            >
                                {trans('label.pay_invoice')}
                            </Button>
                        )}
                    </div>
                ),
            },
        ],
        [paymentLinkByIdAction, showPaymentButton]
    );
};

type InvoicesBigScreenDevicesListProps = {
    data: { items: Invoice[] };
};

export const InvoicesBigScreenDevicesList: FC<
    InvoicesBigScreenDevicesListProps
> = ({ data }) => {
    const { paymentLinkByIdAction, showPaymentButton } = useInvoiceActions();
    const columns = useInvoiceColumns(paymentLinkByIdAction, showPaymentButton);
    const defaultColumns = useDefaultOrderingColumns(columns);

    const [columnOrder, setColumnOrder] = useColumnOrderState(
        getDefaultColumnOrder([], defaultColumns)
    );
    const [columnPinningState, setColumnPinningState] =
        useState<ColumnPinningState>({
            left: ['number'],
            right: ['actions'],
        });
    const [columnSizing, setColumnSizing] = useColumnSizingState({});
    const [columnVisibility, setColumnVisibility] = useColumnVisibilityState(
        {}
    );
    const [sorting, setSorting] = useState<SortingState>([]);

    const table = useReactTableV1({
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        data: data.items,
        columns,
        enableMultiSort: true,
        columnResizeMode: 'onChange',
        enableColumnResizing: true,
        enableDnd: true,
        manualSorting: false,
        manualPagination: true,
        manualFiltering: true,
        state: {
            dnd: {
                number: false,
                actions: false,
            },
            columnSizing,
            columnOrder: columnOrder,
            columnVisibility: columnVisibility,
            columnPinning: columnPinningState,
            sorting,
        },
        onColumnSizingChange: setColumnSizing,
        onColumnPinningChange: setColumnPinningState,
        onColumnVisibilityChange: setColumnVisibility,
        onColumnOrderChange: setColumnOrder,
        onSortingChange: setSorting,
    });

    return (
        <CrmTable table={table}>
            <CrmTable.Thead>
                {table.getHeaderGroups().map(({ id, headers }) => (
                    <CrmTable.Tr key={id}>
                        {headers.map((header) => (
                            <CrmTable.HorizontalDropping
                                key={header.id}
                                items={columnOrder}
                            >
                                <CrmTable.Th header={header}>
                                    <CrmTable.Th.PinningControls />
                                    <CrmTable.Th.ResizebleControls />
                                    <CrmTable.Th.DraggableButton />
                                </CrmTable.Th>
                            </CrmTable.HorizontalDropping>
                        ))}
                    </CrmTable.Tr>
                ))}
            </CrmTable.Thead>
            <CrmTable.TableBody>
                {table.getRowModel().rows.map((row) => (
                    <CrmTable.BodyRow row={row} key={row.id}>
                        {row.getVisibleCells().map((cell) => (
                            <CrmTable.HorizontalDropping
                                key={cell.id}
                                items={columnOrder}
                            >
                                <CrmTable.Td key={cell.id} cell={cell} />
                            </CrmTable.HorizontalDropping>
                        ))}
                    </CrmTable.BodyRow>
                ))}
            </CrmTable.TableBody>
        </CrmTable>
    );
};
