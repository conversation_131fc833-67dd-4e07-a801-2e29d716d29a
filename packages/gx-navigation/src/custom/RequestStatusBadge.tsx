import { Badge } from '@gx-design/badge';
import { useGxNavigationContext } from '../shared/components/GxNavigationContext';
import { trans } from '../shared/translations';

export const RequestStatusBadge: React.FC = () => {
  const context = useGxNavigationContext();

  return context?.hasScheduledVisitImmovisita ? (
    <div className='gx-is-hidden-md-up'>
      <Badge
        style={context?.hasVirtualVisitSettingsFlag ? 'warning' : 'success'}
        text={
          context?.hasVirtualVisitSettingsFlag
            ? trans('label.immovisita_list.scheduled_visit.disabled.tag')
            : trans('label.immovisita_list.scheduled_visit.active.tag')
        }
      ></Badge>
    </div>
  ) : null;
};
