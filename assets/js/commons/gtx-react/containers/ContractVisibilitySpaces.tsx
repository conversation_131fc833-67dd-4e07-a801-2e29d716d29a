import React from 'react';
import { Badge } from '@gx-design/badge';
import { trans } from '@pepita-i18n/babelfish';
import {
    getPropertyVisibilities,
    getPropertyVisibilityKeysFromEcommerceProduct,
} from 'lib/propertyVisibilities';
import { IEcommerceProduct } from 'types/api/property';
import { Visibility } from 'types/propertyVisibilities';

const ecommerceProductToVisibilityKeyMapping =
    getPropertyVisibilityKeysFromEcommerceProduct();
const propertyVisibilities = getPropertyVisibilities();

export const ContractVisibilitySpaces: React.FC<{
    visibility: IEcommerceProduct;
}> = ({ visibility }) => {
    const visibilityKey =
        ecommerceProductToVisibilityKeyMapping[visibility.service];
    const visibilityData = propertyVisibilities.find(
        (item: Visibility) => item.value === visibilityKey
    );
    return (
        <>
            <Badge
                text={visibilityData?.label}
                className={`visibility-${visibilityData?.icon}`}
            />
            <span>{`${visibility.used} ${trans('label.out_of')} ${
                visibility.total
            }`}</span>
        </>
    );
};
