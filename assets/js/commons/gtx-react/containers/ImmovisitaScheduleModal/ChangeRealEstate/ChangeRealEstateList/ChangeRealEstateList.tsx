import { trans } from '@pepita-i18n/babelfish';
import clsx from 'clsx';
import { RealEstateHorizontalCard } from '../RealEstateHorizontalCard/RealEstateHorizontalCard';
import { IApiRealEstateListItem } from '../types';

interface IChangeRealEstateList {
    realEstateList: IApiRealEstateListItem[];
    onSelect: (_item: IApiRealEstateListItem) => void;
    selectedItemId?: IApiRealEstateListItem['id'] | null;
}

export const ChangeRealEstateList: React.FC<IChangeRealEstateList> = ({
    realEstateList,
    selectedItemId,
    onSelect,
}) => (
    <div
        className={clsx('iv-changeRealEstate__list', {
            'iv-changeRealEstate__list--noresults': !realEstateList.length,
        })}
        role="list"
    >
        <h3>
            {trans(
                realEstateList.length
                    ? 'label.immovisita.change_realestate.list_title'
                    : 'label.no_results_found'
            )}
        </h3>
        {realEstateList.map((realEstateItem) => (
            <RealEstateHorizontalCard
                key={realEstateItem.id}
                data={realEstateItem}
                selected={selectedItemId === realEstateItem.id}
                onClick={() => onSelect(realEstateItem)}
            />
        ))}
    </div>
);
