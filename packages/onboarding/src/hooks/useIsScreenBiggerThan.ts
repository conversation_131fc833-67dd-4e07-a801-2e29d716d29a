import { useCallback, useEffect, useState } from "react"

export const useIsScreenBiggerThan = (screenWidth: number) => {

    const [isBigger, setIsBigger] = useState(false)

    const checkScreen = useCallback(() => {
        if(window.innerWidth >= screenWidth){
            setIsBigger(true)
        }else{
            setIsBigger(false)
        }
    }, [])

    useEffect(() => {
        checkScreen()
    }, [])

    useEffect(()=> {

        window.addEventListener('resize', checkScreen)

        return () => {
            window.removeEventListener('resize',  checkScreen)
        }
    }, [checkScreen])

    return isBigger
}
