import React, { FC, useMemo } from 'react';
import { Form, Formik, useFormikContext } from 'formik';
import { trans } from '@pepita-i18n/babelfish';
import { Button } from '@gx-design/button';
import { areEqualShallowCompare } from 'lib/utility';
import { ucFirst } from 'lib/strings-formatter';
import { parseIntNumberWithPoints } from 'gtx-react/utils/parseIntWithPoints';
import { LookupItem } from 'types/lookup';
import { SimilarAdsQuickFilter } from './SimilarAdsQuickFilter';
import { SimilarAdsApiFilters } from '../types/filters';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { IPropertyListItem } from 'types/api/property';
import { convertObjectKeysToSnakeCase } from 'lib/objects';
import { priceRangeFormatter } from 'gtx-react/components/ContentDropdown/DropdownRangeInput/utils';
import {
    DropdownRangeInput,
    RangeFilter,
} from 'gtx-react/components/ContentDropdown/DropdownRangeInput/DropdownRangeInput';
import { DropdownSelectInput } from 'gtx-react/components/ContentDropdown/DropdownSelectInput/DropdownSelectInput';
import { DEFAULT_PRICE_OPTIONS } from 'gtx-react/components/ContentDropdown/DropdownRangeInput/constants';

const FILTERS_FORM_KEYS = {
    ROOMS: 'rooms',
    PRICE: 'price',
    CONDITION: 'propertyCondition',
} as const;

export type SimilarAdsFormFilters = {
    [FILTERS_FORM_KEYS.ROOMS]: RangeFilter;
    [FILTERS_FORM_KEYS.PRICE]: RangeFilter;
    [FILTERS_FORM_KEYS.CONDITION]: string;
};

const INITIAL_VALUES: SimilarAdsFormFilters = {
    [FILTERS_FORM_KEYS.ROOMS]: {
        min: '',
        max: '',
    },
    [FILTERS_FORM_KEYS.PRICE]: {
        min: '',
        max: '',
    },
    [FILTERS_FORM_KEYS.CONDITION]: '',
};

const ROOM_OPTIONS = [
    { label: trans('label.indifferent'), value: '' },
    { label: '1', value: '1' },
    { label: '2', value: '2' },
    { label: '3', value: '3' },
    { label: '4', value: '4' },
    { label: '5', value: '5' },
];

const RemoveFiltersButton: FC = () => {
    const { submitForm, setValues, values } =
        useFormikContext<SimilarAdsFormFilters>();

    const showButton: boolean = useMemo(
        () =>
            Boolean(values[FILTERS_FORM_KEYS.ROOMS]['min']) ||
            Boolean(values[FILTERS_FORM_KEYS.ROOMS]['max']) ||
            Boolean(values[FILTERS_FORM_KEYS.PRICE]['min']) ||
            Boolean(values[FILTERS_FORM_KEYS.PRICE]['max']) ||
            Boolean(values[FILTERS_FORM_KEYS.CONDITION]),
        [
            values[FILTERS_FORM_KEYS.ROOMS]['min'],
            values[FILTERS_FORM_KEYS.ROOMS]['max'],
            values[FILTERS_FORM_KEYS.PRICE]['min'],
            values[FILTERS_FORM_KEYS.PRICE]['max'],
            values[FILTERS_FORM_KEYS.CONDITION],
        ]
    );

    const onResetClick = () => {
        setValues(INITIAL_VALUES);
        submitForm();
    };

    return showButton ? (
        <Button variant="ghost" onClick={onResetClick}>
            {trans('label.remove_filters')}
        </Button>
    ) : null;
};

type SimilarAdsFiltersProps = {
    submittedFilters: SimilarAdsApiFilters;
    setSubmittedFilters: (x: SimilarAdsApiFilters) => void;
    propertyConditions: LookupItem[];
    mainPropertyData: IPropertyListItem;
};

export const SimilarAdsFilters: FC<SimilarAdsFiltersProps> = React.memo(
    ({
        submittedFilters,
        setSubmittedFilters,
        propertyConditions,
        mainPropertyData,
    }) => {
        const { trackEvent } = useMixpanelContext();
        const PROPERTY_CONDITIONS = [
            { label: trans('label.indifferent'), value: '' },
            ...propertyConditions,
        ];

        const onSubmit = (data: SimilarAdsFormFilters) => {
            const filtersToSubmit: SimilarAdsApiFilters = {
                priceMin: data[FILTERS_FORM_KEYS.PRICE].min
                    ? parseIntNumberWithPoints(
                          data[FILTERS_FORM_KEYS.PRICE].min
                      )
                    : null,
                priceMax: data[FILTERS_FORM_KEYS.PRICE].max
                    ? parseIntNumberWithPoints(
                          data[FILTERS_FORM_KEYS.PRICE].max
                      )
                    : null,
                roomsMin: data[FILTERS_FORM_KEYS.ROOMS].min
                    ? parseIntNumberWithPoints(
                          data[FILTERS_FORM_KEYS.ROOMS].min
                      )
                    : null,
                roomsMax: data[FILTERS_FORM_KEYS.ROOMS].max
                    ? parseIntNumberWithPoints(
                          data[FILTERS_FORM_KEYS.ROOMS].max
                      )
                    : null,
                propertyCondition: data[FILTERS_FORM_KEYS.CONDITION]
                    ? parseInt(data[FILTERS_FORM_KEYS.CONDITION])
                    : null,
            };
            // preventing submission with data equal to "previous values"
            if (!areEqualShallowCompare(data, submittedFilters)) {
                setSubmittedFilters({ ...filtersToSubmit });
            }

            trackEvent({
                event: 'properties_filter_selection',
                extra: {
                    ['touch_point']: 'zone_ranking_compared_listings',
                    ...convertObjectKeysToSnakeCase(filtersToSubmit, [
                        'priceMin',
                        'priceMax',
                        'roomsMin',
                        'roomsMax',
                    ]),
                    ['property_condition']: filtersToSubmit?.propertyCondition
                        ? PROPERTY_CONDITIONS.find(
                              (item) =>
                                  item.value.toString() ===
                                  filtersToSubmit.propertyCondition
                          )?.label
                        : null,
                },
            });
        };

        return (
            <Formik initialValues={INITIAL_VALUES} onSubmit={onSubmit}>
                <Form noValidate>
                    <div className="filter-bar">
                        <span className="filter-bar__text">
                            {ucFirst(trans('label.filters'))}
                        </span>
                        <div className="filter-bar__actions">
                            <DropdownRangeInput
                                minInputName="rooms.min"
                                maxInputName="rooms.max"
                                icon="planimetry"
                                placeholder={trans('label.rooms')}
                                options={ROOM_OPTIONS}
                                placeholderMin={trans('label.from')}
                                placeholderMax={trans('label.to')}
                                submitLabel={trans('label.see_ads')}
                                selected={
                                    Boolean(submittedFilters['roomsMin']) ||
                                    Boolean(submittedFilters['roomsMax'])
                                }
                                formatter={priceRangeFormatter}
                                buttonClassName="gx-range-inputControl"
                            />
                            <DropdownRangeInput
                                minInputName="price.min"
                                maxInputName="price.max"
                                icon="money"
                                placeholder={trans('label.price')}
                                options={DEFAULT_PRICE_OPTIONS}
                                placeholderMin={trans('label.from')}
                                placeholderMax={trans('label.to')}
                                submitLabel={trans('label.see_ads')}
                                selected={
                                    Boolean(submittedFilters['priceMin']) ||
                                    Boolean(submittedFilters['priceMax'])
                                }
                                formatter={priceRangeFormatter}
                                buttonClassName="gx-range-inputControl"
                            />
                            <DropdownSelectInput
                                name={FILTERS_FORM_KEYS.CONDITION}
                                icon="helmet"
                                options={PROPERTY_CONDITIONS}
                                placeholder={trans('label.ad_status')}
                                selected={Boolean(
                                    submittedFilters['propertyCondition']
                                )}
                            />
                            <div className="filter-bar__quick-filter">
                                <SimilarAdsQuickFilter
                                    initialValues={INITIAL_VALUES}
                                    mainPropertyData={mainPropertyData}
                                    onSubmit={onSubmit}
                                />
                            </div>
                            <RemoveFiltersButton />
                        </div>
                    </div>
                </Form>
            </Formik>
        );
    }
);
