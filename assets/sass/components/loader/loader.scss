// ==========================================================================
// Loader - Components/Loader
// ==========================================================================
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$gtx-old-open-menu-width: 24rem;
$gtx-old-closed-menu-width: 5.6rem;
$gtx-new-closed-menu-width: 8.8rem;
$gtx-new-open-menu-width: 32.8rem;

// Overlay styling
.gx-loader-overlay {
  &--fixed {
    .gx-loader {
      left: 50%;

      @include media('screen', '>=#{breakpoint(md)}') {
        left: calc(50% + #{$gtx-new-closed-menu-width / 2});

        // New menu open
        .has-menu-fixed & {
          left: calc(50% + #{$gtx-new-open-menu-width / 2});
        }
      }
    }
  }
}
