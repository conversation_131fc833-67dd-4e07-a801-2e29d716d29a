<?php

declare(strict_types=1);

namespace App\DataMapper\EntityToResponse\Property;

use App\DataMapper\AbstractDataMapper;
use App\DataMapper\EntityToResponse\Geography\AddressDataMapper;
use App\DataMapper\EntityToResponse\Geography\CityDataMapper;
use App\Model\Property\CadastralDataAddress;
use App\Model\Response\Property\CadastralDataAddressResponse;
use ObjectMapper\Configuration\ObjectMapperConfig;

class CadastralDataAddressDataMapper extends AbstractDataMapper
{
    private CityDataMapper $cityDataMapper;
    private AddressDataMapper $addressDataMapper;

    public function __construct(
        CityDataMapper $cityDataMapper,
        AddressDataMapper $addressDataMapper
    ) {
        $this->cityDataMapper = $cityDataMapper;
        $this->addressDataMapper = $addressDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return CadastralDataAddressResponse::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var CadastralDataAddress $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(\get_class($data), $this->getDestinationClass())
            ->forMember('city', function () use ($data) {
                return $this->cityDataMapper->map($data->getCity());
            })
            ->forMember('address', function () use ($data) {
                return $this->addressDataMapper->map($data->getAddress());
            });

        return $config;
    }
}
