import { useSortable } from '@dnd-kit/sortable';
import { Column, type RowData, useReactTable } from '@tanstack/react-table';
import { CSSProperties, useMemo } from 'react';
import { CSS } from '@dnd-kit/utilities';
import { type TableOptions, type Table } from '@tanstack/table-core';
import { DragAndDropFeature } from './feature';

/**
 * Hook to use the react table with drag and drop feature
 */
export function useReactTableV1<TData extends RowData>({
    _features,
    enableDnd,
    ...options
}: TableOptions<TData>): Table<TData> {
    return useReactTable({
        _features: [DragAndDropFeature, ...(_features || [])],
        enableDnd: enableDnd ?? true,
        ...options,
    });
}

export const useCommonPinningStyles = <T extends object>(column: Column<T>): CSSProperties => {
    const isPinned = column.getIsPinned();

    const memo = useMemo(
        () =>
            ({
                // TODO: If there are two or more pinned items, it doesn't work correctly. Needs to be checked to understand why.
                left: isPinned === 'left' ? `${column.getStart('left') + 24}px` : undefined,
                right: isPinned === 'right' ? `${column.getAfter('right') + 24}px` : undefined,
            }) satisfies CSSProperties,
        [isPinned, column]
    );

    return memo;
};

/**
 * Hook to get the styles for a draggable element
 */
export const useColumnDragging = <T extends object>(column: Column<T>, disabled: boolean) => {
    // Check if the column is pinned
    const isPinned = Boolean(column.getIsPinned());

    const { attributes, isDragging, listeners, setNodeRef, transform } = useSortable({
        id: column.id,
        // Pinned columns are not draggable
        disabled: disabled || isPinned,
        // Can't drop columns on a pinned column
        data: {
            onDropOverDisabled: disabled || isPinned,
        },
    });

    const dragStyles =
        disabled || isPinned
            ? {}
            : ({
                  opacity: isDragging ? 0.2 : 1, // TODO remove after styling
                  // position: "relative",
                  transform: CSS.Translate.toString(transform), // translate instead of transform to avoid squishing
                  transition: 'width transform 0.2s ease-in-out', // TODO remove after styling
                  width: column.getSize(),
              } satisfies CSSProperties);

    return {
        isDragging,
        attributes,
        listeners,
        setNodeRef,
        style: dragStyles,
    };
};
