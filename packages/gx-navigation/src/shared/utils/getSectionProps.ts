export const getSectionProps = (sectionConfig: string | [string, string]) => {
  let section = '';
  let parentSection;

  if (typeof sectionConfig === 'string') {
    section = sectionConfig;
  } else if (Array.isArray(sectionConfig)) {
    if (sectionConfig.length > 1) {
      parentSection = sectionConfig[0];
      section = sectionConfig[1];
    } else {
      section = sectionConfig[0];
    }
  }

  return {
    section,
    parentSection,
  };
};
