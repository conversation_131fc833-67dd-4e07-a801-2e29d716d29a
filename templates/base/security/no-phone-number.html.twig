{% extends 'base/security/base.html.twig' %}

{% block defaultStylesheets %}
    <link rel="stylesheet" href="{{ asset('bootstrap/main-bootstrap.css', 'v_asset') }}" />
    {{ parent() }}
{% endblock %}

{% set customer = true %}

{% block inner_content %}
    <div class="login-content">
        <div class="form-account">
            <div data-component="no-phone-number">
                <h1 class="gx-title-1">{{ trans("a2f.number_selection.title", {}, gtxConstants.DOMAIN_CMS) }}</h1>
                <p>
                    {{ trans("a2f.number_selection.text_mail_send", {}, gtxConstants.DOMAIN_CMS) }}
                </p>
                <div class="gtx-std-margin-top">
                    {{ trans("a2f.number_selection.text_mail_unreceived", {}, gtxConstants.DOMAIN_CMS) }}
                    <a class="auth2-box__help" href="javascript:void(0);" data-action="show-customer-service">
                        {{ trans("a2f.insert_pin.request_assistance", {}, gtxConstants.DOMAIN_CMS) }}
                    </a>
                </div>
            </div>
            <div class="gx-alert gx-alert--marginTop gx-alert--info">
                <svg class="gx-icon gx-icon--info" xmlns="http://www.w3.org/2000/svg" id="info-circle" viewBox="0 0 24 24" fill-rule="evenodd">
                    <path d="M12 10.5a.75.75 0 0 0-.75.75v6a.75.75 0 1 0 1.5 0v-6a.75.75 0 0 0-.75-.75ZM12 7a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"/>
                    <path d="M2 12C2 6.34 6.34 2 12 2s10 4.34 10 10-4.34 10-10 10S2 17.66 2 12Zm10-8.5c-4.832 0-8.5 3.668-8.5 8.5s3.668 8.5 8.5 8.5 8.5-3.668 8.5-8.5-3.668-8.5-8.5-8.5Z"/>
                </svg>
                <span>{{ trans("a2f.info", {'%APP_NAME%': gtxConstants.APP_NAME}, gtxConstants.DOMAIN_CMS) }}</span>
            </div>
        </div>
    </div>
    {% include 'base/security/footer.html.twig' %}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ webpack_asset('base/a2f') }}
{% endblock %}
