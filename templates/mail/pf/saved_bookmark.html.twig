{% extends 'mail/base.html.twig' %}

{% block body %}

    <p style="font-size: small; font-family: arial,helvetica,sans-serif;" xmlns="http://www.w3.org/1999/html">
        Gentile <strong>{{ agenzia.nome }}</strong>,
    </p>

    <p style="font-size: small; font-family: arial,helvetica,sans-serif;">
        in relazione al suo cliente:
    </p>

    <p style="font-size: small; font-family: arial,helvetica,sans-serif; padding-top: 15px; padding-left: 20px; padding-bottom: 15px; padding-right: 20px; background-color: #F5F5F5; border: 1px solid #E0E0E0; line-height: 20px; text-decoration: none;">
        <strong>{{ cliente.first }} {{ cliente.last }}</strong><br/>
        {% if cliente.email %}
            <a href="mailto:{{ cliente.email }}">{{ cliente.email }}</a><br/>
        {% endif %}
        {% if cliente.phone %}
            {{ cliente.phone }}<br/>
        {% endif %}
        {% if cliente.mobile %}
            {{ cliente.mobile }}<br/>
        {% endif %}
    </p>

    <p style="font-size: small; font-family: arial,helvetica,sans-serif;">
        risulta interessato al seguente immobile:
    </p>

    <div style="margin-top: 10px; padding: 15px 20px; border: 1px solid #e0e0e0; text-decoration: none; background-color: #f5f5f5">
        <table border="0" cellpadding="4">
            <tbody>
            <tr>
                <td width="168px" style="vertical-align: top">
                    <a href="{{ annuncio.url }}">
                        <img style="border: 1px solid #60747f; padding: 5px" src="{{ annuncio.immaginePreferitaListing }}" border="0" alt=""/>
                    </a>
                </td>
                <td style="vertical-align: top">
                    <strong><a href="{{ annuncio.url }}">{{ annuncio.title }}</a></strong>
                    <br/><br/>

                    {% if annuncio.microZona %}
                        <strong>{{ annuncio.microZona }}</strong><br/>
                    {% endif %}

                    {% if annuncio.prezzo is defined %}
                        <strong>Prezzo:</strong> {% if annuncio.tipo == 'nc' %}da {% endif %}
                        {% if annuncio.tipoPrezzo is defined and annuncio.tipoPrezzo %}
                            {{ annuncio.prezzo }}
                        {% else %}
                            &euro; {{ annuncio.prezzo|number_format(0, ',', '.') }}
                        {% endif %}
                        <br/>
                    {% endif %}

                    {% if annuncio.mq != '' %}
                        <strong>Superfice:</strong> {% if annuncio.tipo == 'nc' %}da {% endif %} {{ annuncio.mq }} m&sup2;
                        <br/>
                    {% endif %}

                    {% if annuncio.numStanze != '' %}
                        <strong>Locali:</strong> {{ annuncio.numStanze }}<br/>
                    {% endif %}

                    <br/>{{ annuncio.descrizioneBreve }}<br/><br/>

                    <strong>Annuncio completo:</strong> <a href="{{ annuncio.url }}">{{ annuncio.url }}</a>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <br/>

    <p style="font-size: small; font-family: arial,helvetica,sans-serif;">
        <strong>Il Team Getrix</strong>
    </p>

{% endblock %}
