@use 'bootstrap/variables' as *;
@use 'bootstrap/mixins' as *;
@use '@gx-design/theme/custom-properties' as *;
@use '../../../../sass/variables/variables.scss' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

@import 'bootstrap/normalize';
// TODO: CONTROLLARE USO DI UTILITIES
// @use 'bootstrap/utilities';
@import 'bootstrap/type';
@import 'bootstrap/scaffolding';

$billboard-editable-bg-color: rgba(color(background-brand, true), 0.05);

// BILLBOARD IMMOBILE
.billboard-editable-field {
  display: flex;
  width: 100%;
  border: 1px solid color(border-reversed);

  &:hover {
    background-color: $billboard-editable-bg-color;
    border-color: color(border-action);
    cursor: pointer;
  }
}

.billboard {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.billboard__header {
  height: 15.3%;
}

.billboard__header__logo {
  display: none;

  img {
    max-width: 100%;
    max-height: 100%;
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.billboard__header__contract {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #045384;

  > div {
    color: color(content-accent);
    text-transform: uppercase;
    font-size: 19vw;
    line-height: 1;
    text-align: center;
    font-weight: bold;

    &.billboard__has-long-contract {
      font-size: 10vw;
    }
  }
}

.billboard__title {
  height: 6.7%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1% 2%;
  border: 1px solid transparent;
  border-radius: radius(sm);

  .billboard-editable-field {
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .title {
    font-size: 4vw;
    font-weight: 400;
  }
}

.billboard__feat {
  height: 5.1%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #6d6e71;

  > div {
    display: flex;
    align-items: center;
    justify-content: center;
    color: color(content-accent);
    font-size: 5vw;
    font-weight: 600;

    & + div:before {
      content: '';
      width: 1vw;
      height: 1vw;
      display: inline-block;
      border-radius: radius(rounded);
      margin: 0 2vw;
      background-color: color(background-main);
    }
  }
}

.billboard__photos {
  height: 21%;
  background: #f1f2f2;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  .photo {
    position: relative;
    width: 42%;
    height: 100%;
    overflow: hidden;
    cursor: pointer;

    img {
      display: block;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      &.horizontal {
        height: 100%;
      }

      &.vertical {
        width: 100%;
      }
    }

    .image-action {
      display: none;
    }

    &:hover {
      .image-action {
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(color(background-main, true), 0.5);
      }

      .image-action__btn {
        position: absolute;
        left: 0.4rem;
        bottom: 0.4rem;
        background: rgba(color(background-reversed, true), 0.8);
        border-radius: radius(sm);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 3.2rem;
        width: 3.2rem;
        color: color(content-accent);

        .gx-icon {
          fill: color(content-reversed);
        }
      }

      @include media('screen', '<640px') {
        .image-action__btn {
          span {
            display: none;
          }
        }
      }
    }
  }
}

.billboard__content {
  height: 41.9%;
  display: flex;
  overflow: hidden;
}

.billboard-editable-field {
  flex-direction: column;
  padding: 2%;
  position: relative;
  margin-bottom: 1%;

  .inner-tooltip {
    display: none;
  }

  &:hover {
    .inner-tooltip {
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(color(background-main, true), 0.5);
    }

    .inner-tooltip__btn {
      position: absolute;
      left: 0.4rem;
      top: 0.4rem;
      background: rgba(color(background-reversed, true), 0.8);
      border-radius: radius(sm);
      height: 3.2rem;
      width: 3.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 40px;
      color: color(content-reversed);

      .gx-icon {
        fill: color(content-reversed);
      }
    }

    @include media('screen', '<640px') {
      .inner-tooltip__btn {
        svg {
          margin-right: 0;
        }

        span {
          display: none;
        }
      }
    }
  }
}

.billboard__content__desc {
  width: 73%;
  padding: 2% 2.6%;
  color: color(content-high);
  display: flex;

  .address {
    font-size: 4vw;
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 2%;
  }

  .description {
    font-size: 3vw;
    font-weight: 100;
    line-height: 1.4;
    overflow: hidden;
    overflow-wrap: break-word;
  }
}

.billboard__side {
  width: 27%;
  padding: 2%;
  border-left: 1px solid #e6e7e8;
}

.billboard__side__item {
  .title {
    margin-bottom: 1.2vw;
    font-size: 1.4vw;
    font-weight: bold;
    text-transform: uppercase;
  }
}

.billboard__side__item--reference {
  margin-bottom: 2.8vw;

  .value {
    max-height: 9vw;
    overflow: hidden;
    font-size: 2.4vw;
    padding: 1vw 1.4vw;
    border: 2px solid #bcbec0;
    border-radius: 0.8em;
    word-wrap: break-word;

    &:hover {
      background-color: $billboard-editable-bg-color;
      border-color: color(border-action);
      cursor: pointer;
    }
  }
}

.no-certification {
  font-size: 1.9vw;
}

.energetic-class {
  svg {
    width: 100%;
    height: auto;
  }
}

.ep-season__title {
  font-size: 1.4vw;
}

.ep-season__box {
  display: flex;
  margin-top: 0.5vw;
  border: 2px solid #bcbec0;
  border-radius: 0.8em;

  svg {
    width: 2.2vw;
  }

  .season {
    width: 50%;
    padding: 1vw 0;
    font-size: 1.4vw;
    font-weight: bold;
    text-align: center;

    & + .season {
      border-left: 2px solid #bcbec0;
    }

    > div {
      margin-top: 0.5vw;
    }
  }
}

.billboard--Orizzontale {
  .ep-season__box {
    margin-top: 0.2vw;

    svg {
      width: 1.8vw;
    }

    .season {
      padding: 0.5vw 0;
      font-size: 1.1vw;

      > div {
        margin-top: 0.2vw;
      }
    }
  }
}

.billboard__footer {
  height: 10%;
  display: flex;
}

.billboard__footer__logo {
  width: 30%;
  padding: 1% 2.5%;

  img {
    max-width: 100%;
    max-height: 100%;
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  & + .billboard__footer__contacts {
    width: 70%;
  }
}

.billboard__footer__contacts {
  display: flex;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  padding: 0.8% 4%;
  background: #6d6e71;
  color: color(content-accent);
  text-align: center;

  .billboard-editable-field {
    align-items: center;
    border-color: transparent;

   &:hover {
    border-color: var(--border-action);
   }
  }

  .phone {
    display: flex;
    justify-content: center;
    font-size: 8vw;
    line-height: 1;
    font-weight: bold;
    align-items:center;

    span {
      height: 8vw;
      line-height: 7.6vw;
      padding: 0 2%;
      white-space: nowrap;

      &:hover {
        background-color: $billboard-editable-bg-color;
        cursor: pointer;
      }
    }

    .icon {
      display: block;
      width: 8vw;
      height: 8vw;
      margin-right: 1vw;
    }
  }

  .mail {
    display: none;
  }

  .billboard-editor__preview__web > div {
    font-size: 2vw;
    font-weight: 100;
  }
}

.billboard--photo0 {
  .billboard__content {
    height: 62.9%;
  }
}

.billboard--photo2 {
  .billboard__photos .photo {
    margin: 0 0.5%;
  }
}

.billboard--photo3 {
  .billboard__photos {
    width: 100%;
    justify-content: space-between;

    .photo {
      width: 33%;
    }
  }
}

.billboard__status {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 106vw;
  height: 24vw;
  line-height: 24vw;
  transform: translate(-50%, -50%) rotate(-45deg);
  transform-origin: 50% 50%;
  border-radius: 4vw;
  background: #da3832;
  color: color(content-accent);
  font-size: 19vw;
  font-weight: bold;
  text-align: center;
  text-transform: uppercase;
  z-index: 10;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;

  .billboard--Orizzontale & {
    width: 90vw;
    height: 18vw;
    line-height: 18vw;
    border-radius: 3vw;
    font-size: 15vw;
    transform: translate(-50%, -50%) rotate(-30deg);
  }

  .billboard:hover & {
    opacity: 0;
  }
}

// BILLBOARD IMMOBILE | ORIZZONTALE
.billboard--Immobile.billboard--Orizzontale {
  position: relative;

  .billboard__header {
    display: flex;
    height: 24%;
  }

  .billboard__header__logo {
    display: block;
    width: 20%;
    padding: 1% 2%;
    border-bottom: 1px solid #e6e7e8;

    & + .billboard__header__contract {
      width: 80%;

      > div {
        font-size: 15vw;

        &.billboard__has-long-contract {
          font-size: 8vw;
        }
      }
    }
  }

  .billboard__header__contract {
    width: 100%;
    height: 100%;
  }

  .billboard__title {
    width: 80%;
    height: 10%;
    padding: 0.5% 2%;
    justify-content: left;
  }

  .billboard__feat {
    width: 50%;
    height: 7%;
    margin-left: 30%;

    > div {
      font-size: 3.2vw;
    }
  }

  .billboard__content {
    width: 50%;
    height: 46%;
    margin-left: 30%;

    .adress {
      font-size: 2.8vw;
    }

    .description {
      font-size: 2.6vw;
    }
  }

  .billboard__content__desc {
    width: 100%;
    padding: 3%;
  }

  .billboard__photos {
    position: absolute;
    top: 34%;
    left: 0;
    width: 30%;
    height: 53%;
    flex-direction: column;

    .photo {
      width: 100%;

      .horizontal {
        width: auto;
        height: 100%;
      }
    }
  }

  .billboard__side {
    position: absolute;
    top: 24%;
    right: 0;
    width: 20%;
    height: 63%;
  }

  .no-certification {
    font-size: 1.4vw;
  }

  .billboard__side__item {
    .title {
      font-size: 1.1vw;
    }
  }

  .billboard__side__item--reference {
    .value {
      font-size: 1.9vw;
      max-height: 7.2vw;
    }
  }

  .billboard__footer {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 13%;
  }

  .billboard__footer__logo {
    display: none;
  }

  .billboard__footer__contacts {
    width: 100%;
    padding-left: 3%;
    padding-right: 3%;
    flex-direction: row;
    justify-content: space-between;

    .phone {
      text-align: left;
      align-self: center;
      font-size: 7vw;

      span {
        height: 7vw;
        line-height: 7vw;
      }

      .icon {
        width: 7vw;
        height: 7vw;
      }
    }

    .mail {
      display: block;
    }

    .billboard-editor__preview__web {
      text-align: right;
      align-self: center;

      > div {
        font-size: 2.2vw;
      }
    }
  }

  &.billboard--photo0 {
    .billboard__feat,
    .billboard__content {
      width: 80%;
      margin-left: 0%;
    }
  }

  &.billboard--photo1 {
    .billboard__photos .photo {
      height: 100%;
    }
  }

  &.billboard--photo2 {
    .billboard__photos .photo {
      height: 50%;
    }
  }

  &.billboard--photo3 {
    .billboard__photos {
      width: 16%;

      .photo:hover .image-action__btn {
        height: auto;
        padding: 10px;

        svg {
          margin-right: 0;
        }

        span {
          display: none;
        }
      }

      .horizontal {
        width: auto;
        height: 100%;
      }
    }

    .billboard__feat {
      width: 64%;
      margin-left: 16%;
    }

    .billboard__content {
      width: 64%;
      margin-left: 16%;
    }
  }
}

// BILLBOARD VETRINA
.billboard--Vetrina {
  .billboard__header {
    display: flex;
    height: 8.5%;
  }

  .billboard__header__logo {
    display: block;
    width: 30%;
    padding: 1% 2%;

    & + .billboard__header__contract {
      width: 70%;
    }
  }
  .billboard__header__contract {
    width: 100%;

    > div {
      font-size: 8.5vw;
    }
  }

  .billboard__footer {
    height: 7%;
  }

  .billboard__footer__logo {
    display: none;
  }

  .billboard__footer__contacts {
    width: 100%;
    flex-direction: row;
    justify-content: space-between;

    > div {
      align-self: center;
    }

    .billboard-editor__preview__web {
      > div {
        font-size: 2.5vw;
      }
    }

    .mail {
      display: block;
    }
  }

  &.billboard--photo0 .billboard__content {
    height: 72.7%;
  }

  .billboard__footer__contacts {
    .phone {
      font-size: 6vw;

      span {
        height: 6vw;
        line-height: 5.7vw;
      }

      .icon {
        width: 6vw;
        height: 6vw;
      }
    }
  }

  &.billboard--photo1 {
    .billboard__photos {
      height: 30%;

      .photo {
        width: 56%;
      }
    }

    .billboard__content {
      height: 42.7%;
    }
  }

  &.billboard--photo2 {
    .billboard__photos {
      height: 26%;
      justify-content: space-between;

      .photo {
        width: 49.5%;
        margin: 0;
      }
    }

    .billboard__content {
      height: 46.7%;
    }
  }

  &.billboard--photo3 {
    .billboard__photos {
      height: 18%;
      justify-content: space-between;

      .photo {
        width: 33%;
      }
    }

    .billboard__content {
      height: 54.7%;
    }
  }

  .billboard__footer__contacts {
    .phone {
      text-align: left;
    }
  }
}

// BILLBOARD VETRINA | ORIZZONTALE
.billboard--Vetrina.billboard--Orizzontale {
  .billboard__header {
    height: 12%;
  }

  .billboard__header__logo {
    width: 20%;

    & + .billboard__header__contract {
      width: 80%;
    }
  }

  .billboard__header__contract {
    width: 100%;

    > div {
      font-size: 8vw;
    }
  }

  .billboard__title {
    height: 10%;
    padding: 0.5% 2%;
  }

  .billboard__feat {
    height: 7%;

    > div {
      font-size: 4vw;
    }
  }

  .billboard__photos {
    position: absolute;
    top: 29%;
    left: 0;
    width: 30%;
    height: 59%;
    flex-direction: column;

    .photo {
      width: 100%;

      .horizontal {
        width: 100%;
        height: auto;
      }
    }
  }

  .billboard__content {
    .adress {
      font-size: 2.8vw;
    }

    .description {
      font-size: 2.6vw;
    }
  }

  .billboard__content {
    height: 59%;
  }

  .billboard__content__desc {
    width: 80%;
    padding: 1%;
  }

  .billboard__side {
    width: 20%;
  }

  .no-certification {
    font-size: 1.4vw;
  }

  .billboard__side__item {
    .title {
      font-size: 1.1vw;
    }
  }

  .billboard__side__item--reference {
    margin-bottom: 2.3vw;

    .value {
      max-height: 4.7vw;
      font-size: 1.9vw;
    }
  }

  .billboard__footer {
    height: 12%;
  }

  .billboard__footer__contacts {
    padding-left: 2%;
    padding-right: 2%;

    .icon {
      width: 5vw;
      height: 5vw;
    }

    .phone {
      font-size: 5vw;

      span {
        height: 5vw;
        line-height: 4.8vw;
      }
    }
  }

  .billboard-editor__preview__web > div {
    font-size: 2.2vw;
  }

  &.billboard--photo1 {
    .billboard__content__desc {
      width: 50%;
      margin-left: 30%;
    }
  }

  &.billboard--photo2 {
    .billboard__photos {
      .horizontal {
        width: auto;
        height: 100%;
      }
    }

    .billboard__content__desc {
      width: 50%;
      margin-left: 30%;
    }
  }

  &.billboard--photo3 {
    .billboard__photos {
      z-index: 1;
      width: 80%;
      height: 28%;
      flex-direction: row;

      .horizontal {
        width: auto;
        height: 100%;
      }
    }

    .billboard__content {
      position: relative;
    }

    .billboard__content__desc {
      position: absolute;
      left: 0;
      bottom: 2%;
      height: 48%;
    }

    .billboard__side {
      position: absolute;
      right: 0;
      top: 0;
      height: 100%;
    }
  }
}
