import { describe, expect, it } from 'vitest';
import { BaseMenuConfig } from '../../../types';
import { CustomersCounterAdapter } from './CustomersCounterContext';
import { act, render, screen } from '@testing-library/react';
import { UPDATE_STATS } from '../../../events/inputTypes';
import { CountersProvider } from './CountersContext';

const menuStub: BaseMenuConfig = {
  item: {
    label: 'Test Menu',
  },
};

const TestComponent = () => {
  return (
    <CustomersCounterAdapter menu={menuStub}>
      {(menu) => <>{menu.item?.notificationBadge?.number}</>}
    </CustomersCounterAdapter>
  );
};

describe('CustomersCounterContext', () => {
  it('Shows customers counter', () => {
    render(<TestComponent />, {
      wrapper({ children }) {
        return <CountersProvider>{children}</CountersProvider>;
      },
    });

    // Display counter
    act(() => {
      window.dispatchEvent(
        new CustomEvent(UPDATE_STATS, {
          detail: { newMatches: 10, unreadThreads: 5, newAgencyActiveSearches: 12 },
        }),
      );
    });

    // The sum of newMatches and newAgencyActiveSearches
    const textElement = screen.getByText('22');

    // Clear counter
    act(() => {
      window.dispatchEvent(
        new CustomEvent(UPDATE_STATS, {
          detail: { newMatches: 0, unreadThreads: 5, newAgencyActiveSearches: 0 },
        }),
      );
    });

    expect(textElement).toHaveTextContent('');

    // Show more then 100 counter
    act(() => {
      window.dispatchEvent(
        new CustomEvent(UPDATE_STATS, {
          detail: { newMatches: 90, unreadThreads: 5, newAgencyActiveSearches: 10 },
        }),
      );
    });

    expect(textElement).toHaveTextContent('99+');
  });
});
