@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$research-action-bar-height: 7.2rem;
$research-accent: #ebf6ff;

// TODO: temporary
.request .gtx-ah-footer-spacing {
  display: none;
}

.searches .gx-page-content {
  @include media('screen', '<#{breakpoint(md)}') {
    padding-bottom: 0;
  }
}

.research-tabs {
  position: sticky;
  top: 0;
  margin-top: space(md);
  background-color: color(background-main);
  @include z-index(base, 2);
}

.reasearch-active-date {
  margin-top: space(md);
  text-align: center;
  @include typography(body-small);
}

.research-section {
  margin: 0 space(xl);
  padding: space(md) 0;

  .gx-checkbox--button {
    width: 100%;
  }

  @include media('screen', '<=#{breakpoint(sm)}') {
    margin: 0 space(md);
  }
}

.research-map-zone {
  display: flex;
  align-items: flex-start;
  margin-bottom: space(xl);

  img {
    max-width: 16rem;

    @include media('screen', '<=#{breakpoint(sm)}') {
      max-width: 12.5rem;
    }
  }

  &__list {
    margin-left: space(sm);

    .gx-badge + .gx-badge {
      margin-left: 0;
    }

    .gx-badge {
      margin-right: space(sm);
      margin-bottom: space(sm);
    }
  }
}

.research-crosses-intro,
.research-crosses-sent-intro {
  padding: space(sm) 0 0;
}

.research-bulk-actions {
  display: flex;
  position: sticky;
  top: 4rem;
  align-items: center;
  padding: 0 space(xl) 0;
  border-bottom: 0.1rem solid color(border-action);
  background-color: color(background-main);
  @include z-index(base, 2);

  .gx-checkbox {
    margin: 1rem space(md) 1rem 0;
  }

  .gx-checkbox__control {
    margin-right: space(md);
  }

  @include media('screen', '<=#{breakpoint(sm)}') {
    padding: 0 space(md) space(sm);
  }
}

.research-property-list {
  position: relative;
  padding: space(lg) space(xl) ($research-action-bar-height + space(lg));

  &.is-disabled::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff9c;
    content: '';
    @include z-index(base, 9);
  }

  @include media('screen', '<=#{breakpoint(sm)}') {
    padding: space(lg) space(md);
  }

  &--sent {
    .research-property-row {
      justify-content: space-between;

      .gx-list {
        @include media('screen', '<=#{breakpoint(sm)}') {
          max-width: 40vw;
        }
      }
    }
  }
}

.research-property-row {
  display: flex;
  align-items: center;

  .gx-checkbox {
    margin-right: space(sm);
  }

  &__delete {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-left: space(xl);
  }

  &__sent-date {
    margin-bottom: space(sm);
    font-size: 1.2rem;
  }

  &__sent-date {
    margin-bottom: space(sm);
    font-size: 1.2rem;
  }

  & + & {
    margin-top: space(lg);
  }

  .gx-list {
    font-size: 1.6rem;
    line-height: 2.4rem;

    @include media('screen', '<=#{breakpoint(sm)}') {
      max-width: 50vw;
      font-size: 1.2rem;
      line-height: 1.6rem;
    }
  }

  .gx-property-item__pic {
    width: 12.5rem;
    height: 8.8rem;

    @include media('screen', '<=#{breakpoint(sm)}') {
      width: 8.5rem;
      height: 6rem;
    }
  }
}

.research-actionbar {
  position: fixed;
  bottom: 0;
  left: $gx-new-menu-width;
  width: calc(100% - #{$gx-new-menu-width} - 32rem);
  padding: space(md) 0;
  border-top: 0.1rem solid color(border-main);
  background-color: color(background-main);
  @include z-index(base, 2);

  &__content {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    padding-right: space(xl);
  }

  @include media('screen', '<#{breakpoint(xl)}') {
    width: calc(100% - #{$gx-new-menu-width} - 32rem);
  }

  @include media('screen', '<#{breakpoint(md)}') {
    left: 0;
    width: 100%;
  }

  @include media('screen', '<=#{breakpoint(sm)}') {
    &__content {
      padding-right: space(md);
    }
  }
}

.research-content-wrapper {
  display: flex;
  position: relative;
  flex-direction: column;
  height: 100%;
  // height of the actionbar
  padding-bottom: $research-action-bar-height;
  overflow: auto;

  .research-title {
    margin-bottom: space(xl);
    color: color(content-high);
    font-size: 2.4rem;
    line-height: 3.2rem;
  }

  // To prevent too much space in mobile create page that has no navigation tabs
  &--create {
    padding-bottom: ($research-action-bar-height * 2);

    .research-actionbar {
      &__content {
        max-width: 96rem;
      }

      @include media('screen', '>=#{breakpoint(md)}') {
        left: $gx-new-menu-width;
        width: calc(100% - #{$gx-new-menu-width});

        .has-menu-fixed & {
          left: $gx-new-menu-width + 26rem;
          width: calc(100% - #{$gx-new-menu-width + 26rem});
        }
      }
    }

    @include media('screen', '<=#{breakpoint(md)}') {
      margin-top: -($gx-header-size);
    }
  }

  .gx-checkbox--button {
    width: 100%;
  }
}

.research-add-customer {
  margin: space(sm) 0 space(xl);

  .gx-button {
    flex-grow: 0;
    margin-top: 2.3rem;
  }
}

.research-form-bottom-align {
  display: flex;
  align-items: flex-end;
}

.research-form-container {
  @include media('screen', '<=#{breakpoint(md)}') {
    padding: space(lg) space(xl);
  }

  @include media('screen', '<=#{breakpoint(sm)}') {
    padding: space(lg) space(md);
  }
}

.research-list-actions {
  display: flex;
}

.research-table-row {
  cursor: pointer;

  &:hover {
    background-color: $research-accent;
  }
}

.research-filter-preferred {
  width: 100%;

  @include media('screen', '>=#{breakpoint(sm)}') {
    margin-top: 2.3rem;
  }
}

.research-table-truncate {
  @include media('screen', '<=#{breakpoint(md)}') {
    max-width: 15rem;

    & > div {
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

.research-card {
  &__info {
    display: flex;
    align-items: center;
  }

  .gx-card__content {
    padding-bottom: 0;
  }
}

.research-card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: space(md);
  padding-bottom: space(md);
  border-bottom: 0.1rem solid color(border-main);

  .gx-button {
    margin-left: space(md);
  }
}

.gx-action-list__item > a > .gx-icon.preferred-active,
.research-card .preferred-active .gx-icon {
  color: color(content-warning);
}

.searches-customer-modal {
  .modal-body {
    @include media('screen', '<=#{breakpoint(md)}') {
      height: calc(100% - 4.8rem);
    }
  }
}
