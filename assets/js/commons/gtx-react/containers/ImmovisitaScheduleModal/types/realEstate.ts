export interface IRealEstateObj {
    id: number;
    mainThumbUrl: string;
    properties: Properties[];
    prices: Prices[];
    mainImage: string;
    created?: string;
    modified?: string;
}

export interface Properties {
    // id: Id;
    reference?: string;
    // typologyV2: TypologyV2;
    geographyInformation: GeographyInformation;
    surface: number;
    // isMain: boolean;
    // rooms: number;
    // bathrooms: number;
    // floor: number;
    // prices: {
    //     contractId: number
    //     isVisible: boolean
    //     price: string
    // }[]
}

export interface Id {}

export interface TypologyV2 {
    id: number;
    name: string;
    parent: Parent;
}

export interface Parent {
    id?: number;
    name?: string;
    parent?: Parent;
}

export interface GeographyInformation {
    city: City;
    // macrozone: Macrozone;
    address: Address;
    // showAddress: boolean;
    // coordinates: Coordinates;
}

export interface City {
    id?: number;
    name?: string;
    province?: Province;
    cityMacroZoneType?: CityMacroZoneType;
}

export interface Province {
    id: string | number;
    name: string;
    region?: Region;
}

export interface Region {
    id: string;
    name: string;
    country: Country;
}

export interface Country {
    id: string;
    name: string;
}

export interface CityMacroZoneType {}

export interface Macrozone {
    id: number;
    name: string;
    nameSn: NameSn;
    keyUrl: string;
    city: City;
}

export interface NameSn {}

export interface Address {
    street: string;
    number: string;
}

export interface Coordinates {
    latitude: number;
    longitude: number;
}

export interface Prices {
    price: string;
    isVisible: boolean;
    contractId: number;
}
