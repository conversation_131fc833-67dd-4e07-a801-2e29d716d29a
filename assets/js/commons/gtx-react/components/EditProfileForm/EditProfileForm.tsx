import { Button } from '@gx-design/button';
import { Loader } from '@gx-design/loader';
import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita/babelfish';
import { Form, Formik } from 'formik';
import { FC, ReactNode, useState } from 'react';
import * as Yup from 'yup';

import { GxNavigationBus } from 'lib/gx-navigation-bus';
import {
    AgentDataSection,
    BiographySection,
    ChangeEmailModal,
    DeleteUserModal,
    EditProfileSubmitButton,
    GeneralSection,
    PersonalDataSection,
    UserContactsSection,
} from './components';
import {
    BIO_KEY_REGEX,
    PHONE_NO_PREFIX_REGEX,
    PROFILE_FORM_KEYS,
    UPDATE_AGENCY,
} from './constants';
import {
    BioSubmitDataType,
    CountryCallingCode,
    DeleteUserFormKeysType,
    EmailFormKeysType,
    ProfileFormValues,
    UserAssociation,
    UserContact,
} from './types';
import { changeEmail<PERSON>pi, deleteUser<PERSON><PERSON>, updateUser<PERSON><PERSON> } from './web-api/api';

const VALIDATION_SCHEMA = Yup.object().shape({
    [PROFILE_FORM_KEYS.ROLE]: Yup.string(),
    [PROFILE_FORM_KEYS.FIRST_NAME]: Yup.string().required(
        trans('label.required_value')
    ),
    [PROFILE_FORM_KEYS.LAST_NAME]: Yup.string().required(
        trans('label.required_value')
    ),
    [PROFILE_FORM_KEYS.CONTACTS]: Yup.array().of(
        Yup.object().shape({
            fNumeroSenzaPrefisso: Yup.string()
                .matches(
                    PHONE_NO_PREFIX_REGEX,
                    trans('label.invalid_value.generic')
                )
                .when(['preferito'], {
                    is: (preferito: boolean) => preferito,
                    then: () =>
                        Yup.string()
                            .required(trans('label.required_value'))
                            .matches(
                                PHONE_NO_PREFIX_REGEX,
                                trans('label.invalid_value.generic')
                            ),
                }),
        })
    ),
    [PROFILE_FORM_KEYS.VAT]: Yup.string()
        .min(2, trans('label.invalid_value.length.min_n_digits', { N: 2 }))
        .max(11, trans('label.invalid_value.length.max_n_digits', { N: 11 })),
    [PROFILE_FORM_KEYS.POST_CODE]: Yup.string()
        .matches(/^[0-9]+$/, trans('label.invalid_value.generic'))
        .min(5, trans('label.invalid_value.length.exactly_n_digits', { N: 5 }))
        .max(5, trans('label.invalid_value.length.exactly_n_digits', { N: 5 })),
    [PROFILE_FORM_KEYS.FISCAL_CODE]: Yup.string()
        .min(
            16,
            trans('label.invalid_value.length.exactly_n_digits', { N: 16 })
        )
        .max(
            16,
            trans('label.invalid_value.length.exactly_n_digits', { N: 16 })
        ),
});

export type EditProfileFormProps = {
    associations: UserAssociation[];
    /** Languages to be shown into `<BiographySection />`, string like `it`, `en`, `de`, etc. */
    languages: string[];
    countryCallingCodes: CountryCallingCode[];
    /** Field `user.titolare` value here */
    isMainAgent?: boolean;
    /** Field `user.nuovaEmail` value here */
    storedNewEmail?: string;
    /** Field `user.idAgente` here */
    userId: number;
    initialValues: ProfileFormValues & { [x: string]: any };
    /** Destionation number of ImmoVox extension, useful to check if the user tries to edit/delete a number corresponding to it. */
    voxNumber?: string;
    /** If the user can change his role, this prop expose a the name to create a select **/
    changeRoleSelectElement?: ({ name }: { name: string }) => ReactNode;
    /** If false, hide the delete button */
    hideDeleteButton?: boolean;
};

/*
    show the select for the user's role if (read rule from tests)
*/

export const EditProfileForm: FC<EditProfileFormProps> = ({
    associations,
    languages,
    countryCallingCodes,
    isMainAgent,
    changeRoleSelectElement,
    storedNewEmail,
    userId,
    initialValues,
    hideDeleteButton,
    voxNumber,
}) => {
    const [isEmailModalOpen, setIsEmailModalOpen] = useState<boolean>(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const [isSubmittingModal, setIsSubmittingModal] = useState<boolean>(false);
    const [newEmail, setNewEmail] = useState<string | undefined>(undefined);
    const { showNotification } = useNotifyContext();

    const toggleEmailModal = (value: boolean) => () =>
        setIsEmailModalOpen(value);

    const toggleDeleteUserModal = (value: boolean) => () =>
        setIsDeleteModalOpen(value);

    const onUpdateUserSubmit = async (
        data: ProfileFormValues,
        { resetForm }
    ) => {
        try {
            setIsSubmitting(true);
            const {
                [PROFILE_FORM_KEYS.CONTACTS]: contacts,
                [PROFILE_FORM_KEYS.CITY_OBJ]: oldCityObj,
                ...rest
            } = data;
            // normalizing city
            const newCity = { ...oldCityObj, nome: oldCityObj?.name };
            // normalizing contacts
            const newContacts: UserContact[] = contacts
                .map(
                    ({
                        preferito,
                        idContattoAgente,
                        tipo,
                        pubblico,
                        prefisso,
                        fNumeroSenzaPrefisso,
                    }) => ({
                        preferito,
                        idContattoAgente,
                        tipo,
                        pubblico,
                        prefisso,
                        numero: fNumeroSenzaPrefisso,
                    })
                )
                .filter(({ numero }) => numero);

            // normalizing biographies
            const keysToDelete: string[] = [];
            const biographies: BioSubmitDataType[] = [];
            const entries = Object.entries(rest);
            entries.forEach((elem) => {
                if (elem[0].match(BIO_KEY_REGEX)) {
                    biographies.push({
                        selected: 'false',
                        id: '',
                        lang: elem[0].split('_')[1],
                        description: elem[1] as string,
                    });
                    keysToDelete.push(elem[0]);
                }
            });
            keysToDelete.forEach((elem) => delete rest[elem]);

            const response = await updateUserApi({
                ...rest,
                [PROFILE_FORM_KEYS.CONTACTS]: newContacts,
                [PROFILE_FORM_KEYS.BIOGRAPHY]: biographies,
                [PROFILE_FORM_KEYS.CITY_OBJ]: newCity,
            });

            if (response?.result && Object.keys(response.result).length) {
                showNotification({
                    type: 'success',
                    message: trans('generic.editSuccess'),
                });
                resetForm({ values: data });
                GxNavigationBus.dispatchEvent({
                    type: UPDATE_AGENCY,
                    payload: {
                        agentName: `${rest[PROFILE_FORM_KEYS.FIRST_NAME]} ${
                            rest[PROFILE_FORM_KEYS.LAST_NAME]
                        }`,
                    },
                });
            } else {
                throw new Error(
                    response?.message || trans('generic.editError')
                );
            }
        } catch (error) {
            let message: string;
            if (error instanceof Error) {
                message = error.message;
            } else {
                message = trans('generic.editError');
            }
            showNotification({ type: 'error', message });
        } finally {
            setIsSubmitting(false);
        }
    };

    const onChangeEmailSubmit = async (
        data: Record<EmailFormKeysType, string>
    ) => {
        try {
            setIsSubmittingModal(true);
            const response = await changeEmailApi(data);

            if (response?.email) {
                setNewEmail(response.email);
                setIsEmailModalOpen(false);
                showNotification({
                    type: 'success',
                    message: trans('userProfile.verifyEmailSent').replace(
                        '{{PLACEHOLDER}}',
                        `${response.email}`
                    ),
                });
            } else {
                throw new Error(
                    response?.message || trans('agency.error_during_operation')
                );
            }
        } catch (error) {
            let message: string;
            if (error instanceof Error) {
                message = error.message;
            } else {
                message = trans('agency.error_during_operation');
            }
            showNotification({ type: 'error', message });
        } finally {
            setIsSubmittingModal(false);
        }
    };

    const onDeleteUserSubmit = async (
        data: Record<DeleteUserFormKeysType, string>
    ) => {
        try {
            setIsSubmittingModal(true);
            await deleteUserApi(data);

            setIsDeleteModalOpen(false);
            showNotification({
                type: 'success',
                message: trans('userProfile.removeSuccess'),
            });
            location.href = '/logout';
        } catch (error) {
            if (typeof error === 'string') {
                showNotification({
                    type: 'error',
                    message: error,
                });
            } else if (error instanceof Error) {
                showNotification({
                    type: 'error',
                    message: trans('agency.error_during_operation'),
                });
            }
        } finally {
            setIsSubmittingModal(false);
        }
    };

    return (
        <>
            <Formik
                initialValues={initialValues}
                onSubmit={onUpdateUserSubmit}
                validateOnMount={false}
                validateOnBlur={false}
                validateOnChange={false}
                validationSchema={VALIDATION_SCHEMA}
            >
                <Form noValidate>
                    {isSubmitting && <Loader variant="fixed" />}
                    {changeRoleSelectElement
                        ? changeRoleSelectElement({
                              name: PROFILE_FORM_KEYS.ROLE,
                          })
                        : null}
                    <GeneralSection
                        openEmailModal={toggleEmailModal(true)}
                        newEmail={newEmail}
                        isMainAgent={isMainAgent}
                        storedNewEmail={storedNewEmail}
                    />
                    <PersonalDataSection />
                    <UserContactsSection
                        countryCallingCodes={countryCallingCodes}
                        voxNumber={voxNumber}
                    />
                    <AgentDataSection associations={associations} />
                    <BiographySection languages={languages} />
                    <div className="gx-section gx-end-xs">
                        {!isMainAgent && !hideDeleteButton && (
                            <Button onClick={toggleDeleteUserModal(true)}>
                                <span>{trans('label.remove')}</span>
                            </Button>
                        )}
                        <EditProfileSubmitButton voxNumber={voxNumber} />
                    </div>
                </Form>
            </Formik>
            {!isMainAgent && (
                <>
                    <ChangeEmailModal
                        userId={userId}
                        isOpen={isEmailModalOpen}
                        onClose={toggleEmailModal(false)}
                        onSubmit={onChangeEmailSubmit}
                        isSubmitting={isSubmittingModal}
                    />
                    <DeleteUserModal
                        userId={userId}
                        isOpen={isDeleteModalOpen}
                        onClose={toggleDeleteUserModal(false)}
                        onSubmit={onDeleteUserSubmit}
                        isSubmitting={isSubmittingModal}
                    />
                </>
            )}
        </>
    );
};
