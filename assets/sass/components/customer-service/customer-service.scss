@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.customer-service {
  &__consultant {
    padding: space(md) space(sm);
    border-bottom: 0.1rem solid color(border-main);

    &Contacts {
      display: flex;
      gap: 0 space(sm);
      margin-top: space(sm);

      .gx-avatar {
        flex-shrink: 0;
      }
    }

    &Name {
      @include typography(body);
      font-weight: 500;
      margin: 0 0 space(xs);
    }

    &ContactRow {
      display: flex;
      align-items: center;
      color: color(content-medium);

      svg {
        @include icon-size(md);
        margin-right: space(sm);
      }
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    &__consultant {
      padding: space(sm) space(lg) space(md);
    }
  }
}
