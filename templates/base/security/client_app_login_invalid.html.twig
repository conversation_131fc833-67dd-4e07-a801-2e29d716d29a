{% extends 'base.html.twig' %}

{% block rawContent %}
    <div class="client-app-login-box">
        <div class="client-app-login-box__logo">
            {% if gtxConstants.APP_NAME == 'Getrix' %}
                <img src="/bundles/base/getrix/common/img/country/{{ gtxConstants.COUNTRY_TAG }}/logo-gtx/logo.svg" alt="{{ appName }}" />
            {% else %}
                <img src="/bundles/base/getrix/common/img/country/{{ gtxConstants.COUNTRY_TAG }}/logo/logo.svg" alt="{{ appName }}" />
            {% endif %}                
        </div>
        <div class="gx-section">
            <h1 class="client-app-login-box__title">{{ trans("label.access_not_allowed", {}, gtxConstants.DOMAIN_CMS) }}</h1>
            <p class="client-app-login-box__text">{{ trans("label.client_app_login.descr_1", {}, gtxConstants.DOMAIN_CMS) }} </p>
            <p class="client-app-login-box__text">
                {{ trans("label.client_app_login.requirement_title", {}, gtxConstants.DOMAIN_CMS) }}
                <ul class="client-app-login-box__list">
                    <li>{{ trans("label.client_app_login.requirement_1", {}, gtxConstants.DOMAIN_CMS) | raw }},</li>
                    <li>{{ trans("label.client_app_login.requirement_2", {}, gtxConstants.DOMAIN_CMS) | raw }}.</li>
                </ul>
            </p>
            <p class="client-app-login-box__text"> {{ trans("label.client_app_login.descr_2", {}, gtxConstants.DOMAIN_CMS) }}</p>
            <a href="/logout">{{ trans("label.back_to_login", {}, gtxConstants.DOMAIN_CMS) }}</a>
        </div>
    </div>

{% endblock %}
