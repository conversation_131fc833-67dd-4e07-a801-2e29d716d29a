{% extends 'base/landing_page/index.html.twig' %}

{% block title %}{{ "label.messaging"|trans({}, gtxConstants.DOMAIN_CMS) }}{% endblock %}

{% block content %}
    <div class="messaging-landing">
        <header class="messaging-landing__header">
            <img class="logo" src="/bundles/base/getrix/common/img/country/it/logo/logo.svg" alt="Logo Immobiliare.it">
            <h2>{{ "label.messaging.landing_title"|trans({'%AD_PORTAL%': gtxConstants.AD_PORTAL}, gtxConstants.DOMAIN_CMS) }}</h2>
        </header>

        <section class="messaging-landing__body">
            <img src="/bundles/base/img/messaging/messaggistica-landing.png" alt="">
            <p>
                {{ "label.messaging.landing_text"|trans({}, gtxConstants.DOMAIN_CMS) }}
            </p>
            <a class="gx-button gx-button--accent" href="{{ globalConfigs.getrix.baseurl }}{{ path('app_messaging_list_index') }}">{{ "btn.label.login"|trans({}, gtxConstants.DOMAIN_CMS) }}</a>
        </section>

        <div class="landing-page__contact landing-page__contact--messaging">
            <div class="landing-page__contact__title landing-page__contact__title--messaging">{{ "label.know_more_contact"|trans({}, gtxConstants.DOMAIN_CMS) }}</div>

            <div class="landing-page__contact__item landing-page__contact__item--mail">
                <svg width="20" height="14" xmlns="http://www.w3.org/2000/svg">
                    <g fill="#666666" fill-rule="evenodd">
                        <path d="M9.885 7L20 2.505V1.319C20 .59 19.383 0 18.62 0H1.38C.617 0 0 .59 0 1.319v1.249L9.885 7z"/>
                        <path d="M9.763 9.035L0 4.07v8.442C0 13.335.618 14 1.38 14h17.24c.763 0 1.38-.665 1.38-1.488V4l-9.994 5.035h-.243z"/>
                    </g>
                </svg>
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
<script type="text/javascript">
{% if eventsTrackerToken %}
    mixpanel.track(
        "activation_landing_page",
        {type: "b2b_socials", entry_point: "email"}
    );
{% endif %}
</script>
{% endblock %}
