##########################################################
#  Esempio di configurazione del menu item
#     "key menu":
#           Viene restituito nel json modificato secondo le indicazioni di label_action
#           label: "label key or parameters"
#
#           Questo parametro indica l'azione da eseguire sulla label.
#           I valori ammessi sono:
#           -App\Component\Menu\Model\App\Component\Menu\Model::TRANSLATE (default) Il valore di label viene processato dal translator
#           -App\Component\Menu\Model\App\Component\Menu\Model::PARAMETER_REPLACE Il valore della label vie sostituito con il valore del parametro specificato
#           Il parametro è opzionale e se non specificato viene usato il valore di default
#
#           label_action: !php/const App\Component\Menu\Model\App\Component\Menu\Model::PARAMETER_REPLACE # default action translate
#
#           icon: "icon value" Viene restituito nel json così com'è
#           group: 1 Viene restituito nel json come stringa
#           expanded: true Viene restituito nel json così com'è
#           contexts:
#               - "message-counter"
#           params: Tutti i sotto oggetti di params vengono restituiti così come scritti
#              multimedia: "vt"
#           subMenu: Contiene i sotto menu
#
#########################################################

menu:
    primary:
        "/":
            label: "|label.summary|"
            icon: "thumb-list"
            visibility:
                featureToggleGroup: "dashboard"
        "#annunci":
            href: "/inserimento_annuncio.php"
            label: "|label.ads|"
            icon: "home"
            visibility:
                featureToggleGroup: "ads"
            subMenu:
                "#immobili":
                    label: "|label.estates|"
                    icon: "home"
                    group: 1
                    expanded: true
                    subMenu:
                        "/dettaglio.php":
                            hidden: true
                        "/inserimento_annuncio.php":
                            label: "|label.add|"
                        "/immobili/lista?typology=property":
                            label: "|label.ad_list|"
                            badge:
                                style: "promotion"
                                text: "label.brand_new"
                            params:
                                typology: "property"
                            visibility:
                                featureToggleFeature: "property_ads"
                            subMenu:
                                "/immmobili/{id}/cartellone":
                                    hidden: true
                                "/immobili/portale/{id}/prestazioni":
                                    hidden: true
                        "/immobili/lista/venduti-e-affittati":
                            label: "|label.sold_rented|"
                            visibility:
                                featureToggleFeature: "sold_rented_ads"
                            subMenu:
                                "/inserimento_venduto_affittato.php":
                                    hidden: true
                "#nuove-costruzioni":
                    label: "|label.new_buildings|"
                    icon: "helmet"
                    group: 1
                    visibility:
                        featureToggleGroup: "new_constructions_property"
                    subMenu:
                        "/v2/nuove-costruzioni":
                            label: "|label.add|"
                            visibility:
                                featureToggleFeature: "new_constructions_property_add"
                        "/immobili/lista?typology=new_construction":
                            label: "|label.ad_list|"
                            badge:
                                style: "promotion"
                                text: "label.brand_new"
                            params:
                                typology: "new_construction"
                            visibility:
                                featureToggleFeature: "agency_new_constructions_property"
                "#aste":
                    label: "|label.auctions|"
                    icon: "hammer"
                    group: 1
                    visibility:
                        featureToggleGroup: "auctions"
                    subMenu:
                        "/inserimento_asta.php":
                            label: "|label.add|"
                            visibility:
                                featureToggleFeature: "auction_add"
                        "/immobili/lista?typology=auction":
                            label: "|label.ad_list|"
                            badge:
                                style: "promotion"
                                text: "label.brand_new"
                            params:
                                typology: "auction"
                            visibility:
                                featureToggleFeature: "auctions_list"
                        "/immobili/aste/catalogo":
                            label: "|label.catalogue|"
                            visibility:
                                featureToggleFeature: "auctions_catalogue"
                            subMenu:
                                "/immobili/aste/catalogo/{auctionId}/dettaglio":
                                    label: "|label.catalogue|"
                                    hidden: true
                                    visibility:
                                        featureToggleFeature: "auctions_catalogue"
                "#multinvio":
                    label: "|label.multisend|"
                    icon: "shuffle"
                    group: 1
                    visibility:
                        featureToggleGroup: "multisend"
                    subMenu:
                        "/multinvio/annunci":
                            label: "|label.ads|"
                            visibility:
                                featureToggleFeature: "multisend_property"
                        "/multinvio/nuove-costruzioni":
                            label: "|label.new_buildings|"
                            visibility:
                                featureToggleFeature: "multisend_project"
                        "/multinvio/esportazioni/attive":
                            label: "|label.active_exports|"
                            visibility:
                                featureToggleFeature: "multisend_portal"
                        "/multinvio/esportazioni":
                            label: "|label.exports_management|"
                            visibility:
                                featureToggleFeature: "multisend_portal"
                "#virtual-tour":
                    label: "|label.virtual_tour_3d|"
                    icon: "tour"
                    group: 2
                    visibility:
                        featureToggleGroup: "virtual_tour_360"
                    subMenu:
                        "/servizi_extra.php?multimedia=vt":
                            label: "|label.ads|"
                            params:
                                multimedia: "vt"
                            visibility:
                                featureToggleFeature: "virtual_tour_360_property"
                        "/v2/virtual-tour-nuove-costruzioni":
                            label: "|label.new_buildings|"
                            visibility:
                                featureToggleFeature: "virtual_tour_360_new_constructions"
                "#immovisita":
                    label: "%app.service_remote_visits%"
                    icon: "video"
                    group: 2
                    visibility:
                        featureToggleGroup: "immovisita"
                    subMenu:
                        "/annunci_immovisita.php":
                            label: "|label.ads|"
                            visibility:
                                featureToggleFeature: "immovisita_realestate_list"
                        "/immovisita/scheduled-visits":
                            label: "|label.immovisita.scheduled_visits|"
                            visibility:
                                featureToggleFeature: "immovisita_scheduled_visits"
                "/servizi_extra.php":
                    label: "|label.plans|"
                    icon: "planimetry"
                    href: "/servizi_extra.php?multimedia=pl"
                    group: 2
                    params:
                        multimedia: "pl"
                    visibility:
                        featureToggleFeature: "plans"
        "#messaggi":
            label: "|label.messages|"
            icon: "chat"
            href: "/messaggi/lista"
            contexts:
                - "message-counter"
            visibility:
                featureToggleGroup: "customer_requests"
            subMenu:
                # Enable this only if direct_customer_requests is disabled
                "/messaggi/lista":
                    label: "|label.messages|"
                    icon: "chat"
                    visibility:
                        authorizer: "app.menu.authorizer.messaging-enabled"
                        featureToggleFeature: "requests_messages"
                    contexts:
                        - "message-counter"
                    subMenu:
                        "/messaggi/{id}":
                            hidden: true
                # Enable this only if requests_messages is disabled
                "/v2/contatti_agenzie.php":
                    label: "|label.messages|"
                    icon: "chat"
                    visibility:
                        authorizer: "app.menu.authorizer.messaging-disabled"
                        featureToggleFeature: "direct_customer_requests"
                "/v2/telefonate":
                    label: "|label.phone_calls|"
                    icon: "phone"
                    visibility:
                        featureToggleFeature: "telefono_smart_requests"
        "#clienti":
            label: "|label.customers|"
            icon: "user-group"
            href: "/ricerche/manuali/lista"
            contexts:
                - "customers-counter"
            visibility:
                featureToggleGroup: "clients"
            subMenu:
                "/clienti/match":
                    label: "|label.matches.page_title|"
                    icon: "sparkle"
                    contexts:
                        - "matches-counter"
                    visibility:
                        featureToggleFeature: "matches"
                        featureToggleGroup: "clients"
                        authorizer: "app.menu.authorizer.matches"
                    group: 1
                "/ricerche/attive":
                    label: "|label.active_searches|"
                    icon: "lens-sparkle"
                    contexts:
                        - "active-searches-counter"
                    group: 2
                    visibility:
                        featureToggleFeature: "agency_active_searches"
                        featureToggleGroup: "clients"
                "/richieste.php?id=spec":
                    label: "|label.specific_researches|"
                    icon: "chat-lens"
                    group: 2
                    href: "/richieste.php?id=spec"
                    tooltip: "|label.research_specific_info|"
                    params:
                        id: "spec"
                    visibility:
                        featureToggleFeature: "requests_intersections"
                # START - This element appears if the Getrix is disabled
                "/richieste.php?id=mie":
                    label: "|label.manual_searches|"
                    tooltip: "|label.manual_search_info|"
                    icon: "lens-pencil"
                    group: 2
                    visibility:
                        authorizer: "app.menu.authorizer.getrix-disabled"
                        featureToggleFeature: "searches_list"
                # END - This element appears if the Getrix is disabled
                # START - This element appears if the Getrix is enabled
                "/ricerche/manuali/lista":
                    label: "|label.manual_searches|"
                    tooltip: "|label.manual_search_info|"
                    icon: "lens-pencil"
                    group: 2
                    visibility:
                        module: !php/const App\Constants\Base\GtxConstants::GETRIX_MODULE_NAME
                        featureToggleFeature: "searches_list"
                    subMenu:
                        "/ricerche/manuali/{id}":
                            hidden: true
                # END - This element appears if the Getrix is enabled
                "/clienti":
                    label: "|label.customers_portfolio_2|"
                    icon: "user-group"
                    group: 3
                "/clienti/imposta-notifiche":
                    label: "|label.customers.set_notification|"
                    hidden: true
        "#acquisizione":
            label: "|label.acquisition|"
            icon: "pen"
            href: "/acquisizione/annunci-privati"
            visibility:
                featureToggleGroup: "acquisition"
            subMenu:
                "/acquisizione/annunci-privati":
                    label: "|label.private_properties|"
                    icon: "tag"
                    visibility:
                        featureToggleFeature: "acquisition_privates"
                    subMenu:
                        "/v2/zone":
                            hidden: true
                        "/v2/zone/add":
                            hidden: true
                "/acquisizione/valutazioni":
                    label: "|label.property_valuations|"
                    icon: "libra"
                    visibility:
                        featureToggleFeature: "real_estate_evaluation"
                    subMenu:
                        "/acquisizione/valutazioni/{id}/dettaglio":
                            hidden: true
                "/acquisizione/richieste-vendita":
                    label: "|label.sales_requests|"
                    icon: "euro-house"
                    visibility:
                        featureToggleFeature: "real_estate_sales_requests"
                    subMenu:
                        "/acquisizione/richieste-vendita/{id}/dettaglio":
                            hidden: true
        "/pacchetti-zona":
            label: "|label.zone_packages|"
            icon: "zone"
            visibility:
                featureToggleFeature: "zones"
                authorizer: "app.menu.authorizer.zones"
        "/agenda.php":
            label: "|label.agenda|"
            icon: "calendar"
            visibility:
                featureToggleGroup: "agenda"
            subMenu:
                "/v2/agenda/appuntamento":
                    hidden: true
        "#servizi":
            label: "|label.youdomus.services|"
            icon: "layers"
            href: "/youdomus/"
            visibility:
                featureToggleGroup: "youdomus"
            subMenu:
                # START - This element appears if the youdomus extension is disabled
                "/youdomus":
                    label: "|label.service_youdomus|"
                    icon: "youdomus"
                    href: "/youdomus/"
                    visibility:
                        authorizer: "app.menu.authorizer.youdomus-disabled"
                        featureToggleFeature: "youdomus"
                # END - This element appears if the youdomus extension is disabled
                # START - This element appears if the youdomus extension is enabled
                "#youdomus":
                    label: "|label.service_youdomus|"
                    icon: "youdomus"
                    visibility:
                        authorizer: "app.menu.authorizer.youdomus-enabled"
                        featureToggleGroup: "youdomus"
                    subMenu:
                        "/youdomus/":
                            label: "|label.youdomus.services|"
                            href: "/youdomus/"
                            visibility:
                                featureToggleFeature: "youdomus_services"
                        "/youdomus/Archivio/*":
                            label: "|label.youdomus.documents|"
                            href: "/youdomus/Archivio"
                            visibility:
                                featureToggleFeature: "youdomus_documents"
                        "/youdomus/Monitoraggio/*":
                            label: "|label.youdomus.monitoring|"
                            href: "/youdomus/Monitoraggio/IndexImmobile"
                            visibility:
                                featureToggleFeature: "youdomus_monitoring"
                        "/youdomus/Mappa/CatastoSuMappa?mod=Catasto":
                            href: "/youdomus/Mappa/CatastoSuMappa?mod=Catasto"
                            label: "|label.youdomus.cadastre_on_map|"
                            params:
                                mod: "Catasto"
                            visibility:
                                featureToggleFeature: "youdomus_cadastre_on_map"
                        "/youdomus/Mappa/CatastoSuMappa?mod=Compravendita":
                            href: "/youdomus/Mappa/CatastoSuMappa?mod=Compravendita"
                            label: "|label.youdomus.buy_and_sell|"
                            params:
                                mod: "Compravendita"
                            visibility:
                                featureToggleFeature: "youdomus_cadastre_on_map"
                        "/youdomus/CertificazioneAPE/*":
                            href: /youdomus/CertificazioneAPE
                            label: "|label.youdomus.ape_certifications|"
                            badge:
                                style: "promotion"
                                text: "label.brand_new"
                            visibility:
                                featureToggleFeature: "youdomus_ape_certifications"
                        "/youdomus/SchedaPersona/*":
                            label: "|label.subjects|"
                            href: "/youdomus/SchedaPersona"
                            visibility:
                                featureToggleFeature: "youdomus_subjects"
                        "/youdomus/SchedaImmobile/*":
                            label: "|label.estates|"
                            href: "/youdomus/SchedaImmobile"
                            visibility:
                                featureToggleFeature: "youdomus_estates"
                        "/youdomus/Utente/*":
                            label: "|label.youdomus.customers_area|"
                            href: "/youdomus/Utente/Profilo"
                            visibility:
                                featureToggleFeature: "youdomus_customer"
                        "/youdomus/Prodotto/*":
                            label: "|label.youdomus.services|"
                            visibility:
                                featureToggleFeature: "youdomus_services"
                            hidden: true
                        "/youdomus/obtainCadastralData/{id}":
                            label: "|label.youdomus.services|"
                            visibility:
                                featureToggleFeature: "youdomus_services"
                            hidden: true
                # END - This element appears if the youdomus extension is enabled

                "/report":
                     label: "|label.real_estate_report|"
                     icon: "chart"
                     visibility:
                         featureToggleGroup: "agency_estimates"
                         authorizer: "app.menu.authorizer.agency_estimates"
                     subMenu:
                         "/report/aggiungi":
                             label: "|label.add|"
                             featureToggleFeature: "agency_estimates_add"
                             subMenu:
                                 "/report/{type}/aggiungi":
                                     hidden: true
                         "/report":
                             label: "|label.list|"
                             featureToggleFeature: "agency_estimates_list"
                             subMenu:
                                 "/report/{id}/dettaglio/pro":
                                     hidden: true
                                 "/report/{id}/dettaglio/base":
                                     hidden: true
                                 "/report/{id}/modifica":
                                     hidden: true
                "/report/insights/*":
                    label: "|label.real_estate_report|"
                    href: "/report/insights/list"
                    icon: "chart"
                    badge:
                        style: "promotion"
                        text: "label.brand_new"
                    visibility:
                        featureToggleGroup: "agency_estimates"
                        authorizer: "app.menu.authorizer.report_insights"
                "/v2/servizi-web-marketing":
                    label: "|label.web_services|"
                    icon: "scope"
                    visibility:
                        featureToggleFeature: "web_service"
                "/sitoagenzia.php":
                    label: "|label.website|"
                    icon: "monitor"
                    visibility:
                        featureToggleFeature: "website"
                "/consulenza-mutuo/richiesta":
                    label: "|label.mortgage_advice|"
                    icon: "calculator"
                    visibility:
                        authorizer: "app.menu.authorizer.mortgage-advice"
                        featureToggleFeature: "mortgage_advice"
    secondary:
        "#impostazioni":
            label: "|label.settings_and_administration|"
            icon: "sliders"
            href: "/impostazioni/generale"
            visibility:
                featureToggleGroup: "settings"
                role: "ROLE_AMMINISTRATORE"
            subMenu:
                "/impostazioni/generale":
                    label: "|label.general|"
                    group: 1
                    visibility:
                        featureToggleFeature: "general_settings"
                "/impostazioni/sede":
                    label: "|label.head_office|"
                    group: 1
                    visibility:
                        featureToggleFeature: "office_place_settings"
                "/impostazioni/media":
                    label: "|label.images|"
                    group: 1
                    visibility:
                        featureToggleFeature: "images_video_settings"
                "/impostazioni/sicurezza":
                    label: "|label.security|"
                    group: 1
                    visibility:
                        authorizer: "app.menu.authorizer.twofactor_auth"
                        featureToggleFeature: "security_settings"
                "/impostazioni/utenti":
                    label: "|label.users|"
                    group: 1
                    visibility:
                        featureToggleFeature: "users_settings"
                    subMenu:
                        "/impostazioni/utenti/{id}":
                            hidden: true
                        "/impostazioni/utenti/{id}/edit":
                            hidden: true
                "/impostazioni/visite-a-distanza":
                    label: "|label.remote_visits|"
                    group: 1
                    visibility:
                        featureToggleFeature: "immovisita_settings"
                "/amministrazione/contratto":
                    label: "|label.contract|"
                    group: 2
                    visibility:
                        featureToggleFeature: "contract_info"
                "/amministrazione/dati-fatturazione":
                    label: "|label.billing_information|"
                    group: 2
                    visibility:
                        featureToggleFeature: "invoices_info"
                "/amministrazione/fatture":
                    label: "|label.invoice_plural|"
                    group: 2
                    visibility:
                        featureToggleFeature: "invoices"
