{"$id": "http://gestionale.immobiliare.it/models/extra_visibilities.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "Extra Visibilities", "type": "object", "properties": {"searchable": {"type": "boolean"}, "showcase": {"type": "boolean"}, "showcaseExpiration": {"type": ["null", "string"]}, "showcasePremium": {"type": "boolean"}, "star": {"type": "boolean"}, "starExpiration": {"type": ["null", "string"]}, "starPremium": {"type": "boolean"}, "top": {"type": "boolean"}, "topExpiration": {"type": ["null", "string"]}, "topPremium": {"type": "boolean"}}, "allOf": [{"if": {"properties": {"showcaseExpiration": {"type": "string", "not": {"type": "null"}}}, "required": ["showcaseExpiration"]}, "then": {"properties": {"showcaseExpiration": {"pattern": "^\\d{1,2}/\\d{2}/\\d{4}$"}}}}, {"if": {"properties": {"starExpiration": {"type": "string", "not": {"type": "null"}}}, "required": ["starExpiration"]}, "then": {"properties": {"starExpiration": {"pattern": "^\\d{1,2}/\\d{2}/\\d{4}$"}}}}, {"if": {"properties": {"topExpiration": {"type": "string", "not": {"type": "null"}}}, "required": ["topExpiration"]}, "then": {"properties": {"topExpiration": {"pattern": "^\\d{1,2}/\\d{2}/\\d{4}$"}}}}]}