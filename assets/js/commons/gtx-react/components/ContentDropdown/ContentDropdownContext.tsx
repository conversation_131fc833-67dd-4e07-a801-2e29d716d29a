import { createContext, RefObject, useContext } from 'react';

type DropdownContextType = {
    dropdownAnchorElement: RefObject<HTMLElement> | null;
    onDropdownChange?: (open: boolean) => void;
};

const ContentDropdownContext = createContext<DropdownContextType>({
    dropdownAnchorElement: null,
    onDropdownChange: undefined,
});

export const useContentDropdownContext = () => {
    const context = useContext(ContentDropdownContext);

    return context;
};

export const ContentDropdownProvider = ContentDropdownContext.Provider;
