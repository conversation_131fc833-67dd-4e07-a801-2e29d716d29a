import { Routes } from '../../routes';
import { AnyString, MenuConfig, Pathname } from '../../types';

const getDynamicRegex = (key: string) => {
  const dynamicKey = key.replace(/\{[^/]+\}/g, '(.+?)');
  const regexStr = `^${dynamicKey}$`;
  const regex = new RegExp(regexStr);

  return regex;
};

/**
 * Returns the parent path of a given path.
 * @description this function is useful to highlight the parent menu item of a given path.
 * @beta This function is still in beta.
 */
export const getParentPath = (
  path: AnyString<Routes>,
  config: MenuConfig,
  searchParams?: Record<string, string>,
): Pathname[] | null => {
  const pathArray: Pathname[] = [];
  const keys = Object.keys(config);

  // If path is empty it means we're on homepage so we return dashboard url
  if (path === '') {
    return ['/'];
  }

  for (let i = 0; i < keys.length; i++) {
    const key = keys[i] as Pathname;
    const value = config[key];
    const regex = getDynamicRegex(key);
    const endsWithWildcard = key.endsWith('/*') && path.startsWith(key.replace('/*', ''));

    // !!!!!! TO ABSOLUTELY REMOVE AS SOON UNIFIED LIST MENU (VERSION 2) WILL BE DONE
    if (
      path === '/inserimento_annuncio.php' &&
      searchParams &&
      (searchParams.tipologia === 'auction' || searchParams.tipo === '14')
    ) {
      return ['#annunci', '#aste', '/inserimento_asta.php'];
      // !!!!!! END OF TO REMOVE BLOCK
    } else {
      if (value.params && searchParams) {
        const params = Object.entries(value.params);

        // check if value.params entries match searchParams entries
        const paramsMatch = params.every(
          ([key, qsValue]) => searchParams[key] && searchParams[key] === qsValue,
        );

        if (paramsMatch) {
          return pathArray.concat(key);
        }
      } else if (key === path) {
        return pathArray.concat(key);
      }
      // ? Dynamic match [e.g. /dashboard/{id}]
      else if (key.includes('{') && regex.test(path)) {
        return pathArray.concat(key);
      }
      // ? Iterate for nested subMenu
      else if (value?.subMenu) {
        const result = getParentPath(path, value.subMenu, searchParams);
        if (result) {
          return pathArray.concat(key).concat(result);
        }
      }
      // ? if key ends with /* and the rest of the path matches, return the key
      else if (endsWithWildcard) {
        return pathArray.concat(key);
      }

      // ? if key ends with search params, return the key
      else if (key.includes('?')) {
        const keyWithoutParams = key.split('?')[0];
        if (keyWithoutParams === path) {
          return pathArray.concat(key);
        }
      }
    }
  }

  return null;
};

export const searchParamsToKeyValues = (searchParams: URLSearchParams) => {
  const entries = searchParams.entries();
  const keyValues: Record<string, string> = {};
  for (const [key, value] of entries) {
    keyValues[key] = value;
  }
  return keyValues;
};

export const removeTrailingSlash = (path: string): string => path.replace(/\/$/, '');
