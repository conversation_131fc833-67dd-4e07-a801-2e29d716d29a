import { Loader } from '@gx-design/loader';
import { Pager } from '@gx-design/pager';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { Formik } from 'formik';
import { useState } from 'react';

import { sanitizeObject } from 'lib/utility';
import { getProperties } from '../web-api';
import { ChangeRealEstateFilters } from './ChangeRealEstateFilters/ChangeRealEstateFilters';
import { ChangeRealEstateList } from './ChangeRealEstateList/ChangeRealEstateList';
import { GetPropertiesFilters, IApiRealEstateListItem } from './types';

export interface IChangeRealEstate {
    currentRealEstateItemId?: number | null;
    setSelectedTempRealEstate: React.Dispatch<
        React.SetStateAction<IApiRealEstateListItem | null>
    >;
}

const INITIAL_VALUES = {
    q: '',
    priceMin: '',
    priceMax: '',
    categories: '',
    contractId: '',
    typologyIds: '',
    surfaceMin: '',
    surfaceMax: '',
    withVirtualTour: '',
};

export const ChangeRealEstate: React.FC<IChangeRealEstate> = (props) => {
    const [filters, setFilters] = useState<GetPropertiesFilters>({});
    const [page, setPage] = useState(1);
    const sanitizedFilters: GetPropertiesFilters = sanitizeObject({
        objectToClean: filters,
        validKeys: Object.keys(filters),
        cleanEmptyStringsFrom: Object.keys(filters),
    });

    const { data, isFetching } = useQuery({
        queryKey: ['realEstates_properties', page, sanitizedFilters],
        queryFn: () => getProperties({ page, ...sanitizedFilters }),
        staleTime: Infinity,
        refetchOnMount: false,
        placeholderData: keepPreviousData,
    });

    const onSubmit = (values: GetPropertiesFilters) => {
        setPage(1);
        setFilters(values);
    };

    return (
        <div className="iv-changeRealEstate__listContainer">
            {isFetching && <Loader />}
            <Formik initialValues={INITIAL_VALUES} onSubmit={onSubmit}>
                <ChangeRealEstateFilters
                    hasFilters={Boolean(Object.keys(sanitizedFilters).length)}
                />
            </Formik>
            <ChangeRealEstateList
                realEstateList={data?.properties ?? []}
                selectedItemId={props.currentRealEstateItemId}
                onSelect={props.setSelectedTempRealEstate}
            />
            {data?.total ? (
                <div className="iv-changeRealEstate__pagination">
                    <Pager
                        activePage={page}
                        maxPagesToShow={3}
                        totalPages={Math.ceil(data.total / 4)}
                        onPageClick={setPage}
                    />
                </div>
            ) : null}
        </div>
    );
};
