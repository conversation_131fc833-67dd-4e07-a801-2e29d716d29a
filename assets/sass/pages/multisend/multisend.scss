@use './list' as *;
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

// ESPORTAZIONI ATTIVE

$cssRemoveTransitionTime: 0.5s;

.portal-card__head .dropdown-menu-right .sso-auth-checking {
  color: color(content-low);

  .fa-spin {
    float: right;
    line-height: 2.2rem;
  }
}

// GESTIONE ESPORTAZIONI

#gtx-portal-info-modal {
  .alert-gtx-notify {
    width: auto;
    top: 0 !important;
    margin: 0 0 2rem;
  }

  .modal-body {
    > div:not(.h5):not(.hidden) + div {
      margin-top: 3rem;
    }
  }
}

#gtx-portal-settings-modal {
  .modal-body {
    & > .gtx-cst-form {
      margin: 0 -1.1rem;

      .row + .row {
        margin-top: 1.1rem;
      }

      .row.mandatory-configuration-row {
        position: absolute;
        bottom: 0;
      }

      .row.preselection {
        & > div:first-child {
          min-width: 11rem;
        }

        & > div {
          padding-bottom: 0;
        }
      }

      .row.disattiva {
        margin-top: 2rem;
      }
    }

    [data-role='text'] {
      margin-bottom: 2rem;
    }

    input.form-control[disabled] {
      background: color(border-main) !important;
    }
  }

  .styled-radio-group {
    .radio-btn {
      border-radius: radius(sm) 0 0 radius(sm);
      height: 3.2rem;
      width: 4rem;
      padding: 0.5rem;

      &:before {
        content: 'Sì';
        color: color(content-low);
      }

      &.radio-btn-no {
        border-radius: 0 radius(sm) radius(sm) 0;
        margin-left: -0.1rem;

        &:before {
          content: 'No';
        }
      }
    }

    input:checked {
      + .radio-btn {
        border-radius: 0;

        &:before {
          content: 'Sì';
          color: color(content-accent);
          position: absolute;
          border-radius: radius(sm) 0 0 radius(sm);
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          width: auto;
          height: auto;
          margin: -0.1rem;
          padding: 0.5rem;
          box-shadow: 0.1rem 0.2rem 0.5rem 0 rgba(0, 0, 0, 0.2) inset;
          @include z-index(base, 1);
        }

        &.radio-btn-no {
          &:before {
            border-radius: 0 radius(sm) radius(sm) 0;
            content: 'No';
            box-shadow: -0.1rem 0.2rem 0.5rem 0 rgba(0, 0, 0, 0.2) inset;
          }
        }
      }
    }
  }
}

.multisend-modal-title {
  margin-bottom: space(md);
}

.gtx-form__ads-number {
  .form-control,
  .styled-checkbox {
    display: inline-block;
    width: calc(50% - 1.1rem);
  }

  .styled-checkbox {
    margin-left: 1.8rem;
  }
}

.marked-as-to-remove,
.marked-as-to-remove * {
  margin: 0 !important;
  padding: 0 !important;
  width: 0 !important;
  height: 0 !important;
  opacity: 0 !important;
  overflow: hidden !important;
}

.gtx-section__content--portals {
  padding-top: 3rem;
}

.portal-card-container--manual {
  margin-bottom: 6rem;
}

.portal-card-container__title {
  width: 100%;
  margin: 0 1.2rem 1.6rem;
  font-size: 1.8rem;
  color: color(content-medium);
  font-weight: 300;
}

.portal-card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  background-color: color(background-main);
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);
  opacity: 1;
  transition: all $cssRemoveTransitionTime ease-out;

  & + .portal-card {
    margin-top: 2.5rem;
  }
}

.portal-card__head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.6rem;
  border-bottom: 0.1rem solid color(border-main);

  .logo {
    position: relative;
    float: left;
    max-width: 13rem;
    height: 5.4rem;

    div {
      padding-top: 1.1rem;
    }

    img {
      position: relative;
      top: 50%;
      transform: translateY(-50%);
      display: block;
      max-width: 100%;
      max-height: 100%;
    }
  }
}

.portal-card__body {
  padding: 1.6rem;
  flex-grow: 1;

  .title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
  }

  .ads-list {
    padding: 0;
    margin-bottom: 1rem;
    list-style: none;

    li {
      float: left;
      padding-right: 0.5rem;
      font-weight: 300;
      margin-bottom: 1rem;

      & + li {
        padding-right: 0;
        padding-left: 0.5rem;
      }
    }
  }

  .ads-list-two-counters {
    @extend .ads-list;

    li {
      width: 50%;
    }
  }

  .ads-list-three-counters {
    @extend .ads-list;

    li {
      width: 33%;
    }
  }

  .ads-list-four-counters {
    @extend .ads-list-two-counters;

    li + li {
      padding-right: 0.5rem;
      padding-left: 0;
    }
  }

  .ads-list__value {
    display: block;
    font-size: 2.2rem;
    color: color(content-action);
  }

  .ads-list__label {
    display: block;
    font-size: 1.2rem;
    color: color(content-low);
    text-transform: uppercase;
  }
}

.portal-card__footer {
  width: 100%;
  padding: 1.6rem;
  background: color(background-alt);
  border-top: 0.1rem solid color(border-main);

  .last-send {
    font-size: 1.4rem;
    color: color(content-medium);

    b {
      display: block;
      clear: both;
      margin-bottom: 0.5rem;
      color: color(content-medium);
      font-weight: 600;
    }
  }

  .ge-badge {
    position: relative;
    bottom: 1.2rem;
    float: right;
  }
}

// ESPORTAZIONI ATTIVE | MOBILE
@include media('screen', '<#{emConverter(388)}') {
  #gtx-portal-settings-modal {
    .modal-body {
      & > .gtx-cst-form {
        .row.preselection {
          & > div:last-child {
            padding-top: 1.1rem;
            width: 100%;
          }
        }
      }
    }
  }
}

// ESPORTAZIONI ATTIVE | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .portal-card-container {
    margin: 0 -1.6rem;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-content: flex-start;
  }

  .portal-card-container--manual {
    margin-bottom: 3.5rem;
  }

  .portal-card {
    width: calc(50% - 3.2rem);
    margin: 0 1.6rem;
    margin-bottom: 3.2rem;

    & + .portal-card {
      margin-top: 0;
    }
  }

  .portal-card__head {
    padding: 1.6rem 2.4rem;
  }

  .portal-card__body {
    padding: 2.4rem;

    .ads-list__value {
      margin-right: 0.5rem;
    }
  }

  .portal-card__footer {
    padding: 1.6rem 2.4rem;
  }
}

@include media('screen', '>medium', '<#{emConverter(800)}') {
  .portal-card {
    width: 100%;
  }
}

// ESPORTAZIONI ATTIVE | DESKTOP
@include media('screen', '>=#{emConverter(1160)}') {
  .portal-card {
    width: calc(33.333% - 3.2rem);
  }
}

@include media('screen', '>=#{breakpoint(sm)}') {
  #gtx-portal-ordering-modal {
    .modal-body {
      max-height: 40rem;
      padding-bottom: 0;

      &:after {
        content: '';
        display: block;
        height: 3rem;
      }
    }
  }

  .portal-order__item-wrap {
    cursor: grab;
    cursor: -moz-grab;
    cursor: -webkit-grab;

    &:active {
      cursor: grabbing;
      cursor: -moz-grabbing;
      cursor: -webkit-grabbing;
    }
  }
}

@include media('screen', '>=#{emConverter(1670)}') {
  .portal-card {
    width: calc(25% - 3.2rem);
  }
}

.portal-card-linked-portal-logo {
  height: 6rem;
  min-width: 13rem;
  padding: 0;
  vertical-align: middle;
  text-align: center;
  border: 0;
  border-radius: 0;
  display: inline-block;

  .table-wrap {
    height: 100%;
    margin: 0 auto;

    > .cell-wrap {
      vertical-align: middle;
      text-align: center;
    }
  }

  img,
  span {
    height: auto;
    width: auto;
    max-width: 13rem;
    max-height: 5.4rem;
    display: block;
  }
}

// GESTIONE ESPORTAZIONI
.table--portal-activation {
  .td-portal-logo {
    width: 18rem;

    div {
      position: relative;
      max-width: 13rem;
      height: 5.4rem;

      img {
        position: relative;
        top: 50%;
        transform: translateY(-50%);
        display: block;
        max-width: 100%;
        max-height: 100%;
      }
    }
  }

  .td-portal-action {
    position: relative;

    .status-active {
      position: absolute;
      right: 1.6rem;
      bottom: 7.5rem;
      color: color(content-success);
      font-size: 1.4rem;
    }

    .btn-active {
      width: calc(100% - 5.6rem);
    }
  }
}

// GESTIONE ESPORTAZIONI | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .table--portal-activation {
    .td-description {
      h6 {
        font-size: 1.3rem;
        font-weight: bold;
        color: color(content-medium);
      }
    }

    .td-portal-action {
      text-align: right;
      width: 21rem;

      .status-active {
        position: static;
        margin-right: 2.4rem;
      }

      .btn-active {
        width: 10.3rem;
        margin-left: 1.2rem;
      }

      [data-action='toggle-info'] {
        min-width: 13.7rem;
      }
    }
  }
}

// GESTIONE ESPORTAZIONI | DESKTOP
@include media('screen', '>=#{breakpoint(md)}') {
  .table--portal-activation {
    table-layout: fixed;

    tr:hover {
      background: rgba(66, 140, 198, 0.05);
    }

    .td-description {
      h6 {
        margin-bottom: 0.5rem;
      }

      p {
        margin-bottom: 0;
        display: inline-block;
        font-size: 1.3rem;
        color: color(content-medium);
        width: 100%;
      }
    }

    .td-portal-action {
      text-align: right;

      .btn-active {
        margin-left: 1.2rem;
      }
    }
  }
}

// ORDINA PORTALI MODALE
.portal-order {
  position: relative;
  min-height: 34rem;
}

.portals-order-list > .portal-order__ghost-item-wrap,
.portal-order__item-wrap + .portal-order__ghost-item-wrap {
  background: color(background-alt);

  .logo,
  .drag,
  .portal-order__item {
    display: none;
  }
}

.portal-order__item-wrap {
  position: relative;
  height: 4.8rem;
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);
  margin-bottom: space(md);
}

.portal-order__item {
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 4.6rem;
  padding: 1.6rem 0 1.6rem 1.6rem;
  background: white;
  border: none;
  border-radius: radius(sm);
  cursor: grab;

  .logo {
    max-width: 13rem;
    height: 4rem;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  .drag {
    width: 4.6rem;
    height: 4.6rem;
    padding: 1.8rem 1.6rem 0 1.6rem;

    > div {
      position: relative;
      width: 1.6rem;
      height: 0.2rem;
      background: color(border-main);

      &:before,
      &:after {
        content: '';
        position: absolute;
        left: 0;
        width: 1.6rem;
        height: 0.2rem;
        background: color(border-main);
      }

      &:before {
        top: 0.4rem;
      }

      &:after {
        top: 0.8rem;
      }
    }
  }
}

.property-number-row {
  display: flex;
  align-items: center;
  justify-content: center;
}

.bootstrap-datetimepicker-widget {
  td,
  th {
    padding: 0.4rem;
  }
}

.list__head--portals {
  .gx-table__field {
    text-transform: none;
  }
}

.portal-settings-modal-contract {
  .styled-checkbox .styled-check {
    width: 2rem;
    height: 2rem;
  }
}

// TO-DO: move to gx-design
.grab-overlay * {
  cursor: grabbing;
}
