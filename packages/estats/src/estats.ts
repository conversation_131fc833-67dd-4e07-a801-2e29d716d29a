import { generateMetricsMacro } from '@pepita/estats-macro';

type EstatsConfigType = {
    url: string;
    environment: 'dev' | 'prod';
    sitePrefix: string;
    site: string;
    secret: string;
};

const ESTATS_SUPER_SECRET =
    'Bbnfn-uW6MJKanCQJcmsAW_F8tgc-v_s3DDLTZ5kFCXVcEhexx9eGFjvkWvUjHpAG24RqfRE4m69phv2fw-ztydr7GyXrbyM_DsRvFXE-JMt_k2Z_TeP9Fvj_tiX8ame';

const getMetricEnv = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';

type EstatsVariables = {
    site: string;
    sitePrefix: string;
    url: string;
};

const getStatsConfig = (variables: EstatsVariables): EstatsConfigType => ({
    url: variables.url,
    environment: getMetricEnv,
    sitePrefix: variables.sitePrefix,
    site: variables.site,
    secret: ESTATS_SUPER_SECRET,
});

export const estatsMetrics = (variables: EstatsVariables) => {
    const gxNavigationStats = generateMetricsMacro(getStatsConfig(variables), ['menu'], ['fail']);

    return {
        ...gxNavigationStats,
    };
};
