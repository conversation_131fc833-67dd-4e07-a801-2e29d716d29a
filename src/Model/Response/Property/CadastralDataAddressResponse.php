<?php

declare(strict_types=1);

namespace App\Model\Response\Property;

use App\Model\Response\Geography\AddressResponse;
use App\Model\Response\Geography\CityResponse;

class CadastralDataAddressResponse
{
    private ?CityResponse $city = null;
    private ?AddressResponse $address = null;

    public function getCity(): ?CityResponse
    {
        return $this->city;
    }

    public function setCity(?CityResponse $city): void
    {
        $this->city = $city;
    }

    public function getAddress(): ?AddressResponse
    {
        return $this->address;
    }

    public function setAddress(?AddressResponse $address): void
    {
        $this->address = $address;
    }
}
