import { EnergeticTag } from '@gx-design/energetic-tag';
import { Icon } from '@gx-design/icon';
import clsx from 'clsx';

import {
    FieldData,
    FieldId,
    REAL_ESTATE_FIELDS_DATA,
} from 'constants/realEstateFieldsData';
import { formatEnergeticTag } from 'lib/propertyEnergyClass';
import { DetailPropertyType } from 'lib/REST/types/detail-property';
import { VOID_VALUE } from './constants';
import { getValueByPath, isFieldValid } from './utils';

type FeatureItemProps = Omit<FieldData, 'validFor' | 'path'> & {
    value: React.ReactNode;
};

export const generateItemProps = (
    fieldId: FieldId,
    realEstate: DetailPropertyType
): FeatureItemProps => {
    const field: FieldData = REAL_ESTATE_FIELDS_DATA[fieldId];
    const { validFor, path, ...data } = field;
    const isValid = isFieldValid({ validFor, realEstate });
    const value = isValid
        ? getValueByPath(realEstate, path, VOID_VALUE)
        : undefined;
    return { ...data, value };
};

export const FeatureItem: React.FC<FeatureItemProps> = ({
    label,
    value,
    icon,
    isTwoColumn,
    component,
}) =>
    value !== undefined ? (
        <div
            className={clsx([
                'feature-section__listItem',
                { 'is-disabled': value === VOID_VALUE },
                {
                    'feature-section__listItem--twoColumn':
                        isTwoColumn && value !== VOID_VALUE,
                },
            ])}
        >
            {icon && <Icon name={icon} />}
            <div className="feature-section__listItemContent">
                <div className="gx-title-2">{label}</div>
                <div className="gx-body-small">
                    <FeatureValue value={value} component={component} />
                </div>
            </div>
        </div>
    ) : null;

export const FeatureValue: React.FC<
    Pick<FeatureItemProps, 'value' | 'component'>
> = ({ value, component }) => {
    if (typeof value !== 'string') {
        return value;
    }
    switch (component) {
        case 'energeticTag':
            return <EnergeticTag score={formatEnergeticTag(value)} />;
        case 'url':
            return (
                <a target="_blank" href={value}>
                    {value}
                </a>
            );
        default:
            return <span dangerouslySetInnerHTML={{ __html: value }} />;
    }
};
