import { useEffect, useState } from 'react';
import { getParentPath } from '../shared/utils';
import { MenuConfig, Pathname } from '../types';
import { URL_HISTORY_CHANGE } from '../events/inputTypes';
import { on } from '../events/utils';
import { removeTrailingSlash, searchParamsToKeyValues } from '../shared/utils/getParentPath';

const defaultPath: Pathname[] = [];

export const getActivePath = (menuConfig: MenuConfig | undefined) => {
  if (!menuConfig) {
    return defaultPath;
  }

  return getParentPath(
    removeTrailingSlash(window.location.pathname),
    menuConfig,
    searchParamsToKeyValues(new URLSearchParams(window.location.search)),
  );
};

/**
 * This hook returns the active path of the menu.
 * In the current version, it is **not possible** to have more than 3 path level for main menu and 2 for the secondary menu.
 * @param menuConfig
 * @returns
 */
export const useActivePath = (menuConfig: MenuConfig | undefined) => {
  const [activePath, setActivePath] = useState<Pathname[] | null>(() =>
    menuConfig ? getActivePath(menuConfig) : null,
  );

  /**
   * This effect listens to the history change event and updates the active path.
   * Not optimal, but it is the only way to keep the active path updated in these rare cases.
   */
  useEffect(() => {
    const unsubscribe = on(URL_HISTORY_CHANGE, (data: string) => {
      setActivePath(getActivePath(menuConfig));
    });

    return () => unsubscribe();
  }, []);

  return activePath || defaultPath;
};
