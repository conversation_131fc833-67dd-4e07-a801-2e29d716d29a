{"name": "estats", "version": "1.0.0", "license": "proprietary", "private": true, "source": "./src/estats.ts", "main": "./dist/estats.js", "scripts": {"build": "yarn tsc --project ./tsconfig.json"}, "volta": {"node": "18.14.2", "yarn": "3.4.1"}, "devDependencies": {"@types/node": "^17.0.2", "@typescript-eslint/eslint-plugin": "latest", "@typescript-eslint/parser": "latest", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.8", "ts-loader": "^9.4.2", "typescript": "^5.1.6", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.13.3", "webpack-merge": "^4.1.4"}, "repository": {"type": "git", "url": "https://gitlab.pepita.io/getrix/mls-site.git", "directory": "packages/estats"}, "dependencies": {"@getrix/common": "^7.38.2", "@pepita/estats-macro": "^1.1.0"}}