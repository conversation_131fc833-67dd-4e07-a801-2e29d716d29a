import webpack from 'webpack';
import path from 'path';
import fs from 'fs';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import { WebpackManifestPlugin } from 'webpack-manifest-plugin';
import CopyPlugin from 'copy-webpack-plugin';
import BundleTracker from 'webpack-bundle-tracker';
import { I18nPo2Json } from './packages/webpack-plugins/i18nPo2Json/dist/index.js';
import { createRequire } from 'node:module';
import { fileURLToPath } from 'url';
import { BabelfishLoaderWrapperPlugin } from './packages/webpack-plugins/babelfish-loader-wrapper/dist/index.js';

const require = createRequire(import.meta.url);
const __filename_common = fileURLToPath(import.meta.url);
const __dirname_common = path.dirname(__filename_common);

const { sentryWebpackPlugin } = require('@sentry/webpack-plugin');

export const PUBLIC_PATH = '/bundles/';
export const OUTPUT_PATH = path.join(__dirname_common, 'public/bundles');

const translationsDirs = [
    path.join(
        __dirname_common,
        'vendor/indomio-translations/cms-contents/translations/messages'
    ),
    path.join(
        __dirname_common,
        'vendor/indomio-translations/cms/translations/messages'
    ),
];

/**
 * Decora l'oggetto entries per webpack aggiungendo a tutte le entries appShell.js
 *
 * @param {{ [key:string]: string}| {[key: string]: {import: string[]}} | { [key: string]: string[]}} entries
 */
function addAppShell(entries) {
    const appShell = path.join(
        __dirname_common,
        'assets/js/base/common/app-shell.js'
    );

    const outEntries = entries;

    for (const key in entries) {
        const entry = entries[key];

        if (entry.import) {
            if (!entry.import.includes(appShell)) {
                outEntries[key] = [appShell, ...entry.import];
            }
        } else if (Array.isArray(entry)) {
            outEntries[key] = {
                import: [appShell, ...entry],
            };
        } else {
            outEntries[key] = {
                import: [appShell, entry],
            };
        }
    }

    return outEntries;
}

function locatePagesEntries() {
    let pagesDir = path.join(__dirname_common, 'assets/js/base/pages');

    let pages = fs.readdirSync(pagesDir);

    const entries = pages
        .map((page) => {
            let entry = path.join(pagesDir, page, 'main.js');

            if (!fs.existsSync(entry)) {
                return;
            }

            const pageName = `base/${page}`;

            return {
                [pageName]: entry,
            };
        })
        .filter(Boolean)
        .reduce((entries, entry) => Object.assign(entries, entry));

    return entries || {};
}

//Entries which will be included in every selective build
export const basicEntries = Object.assign(
    {},
    {
        main: path.join(__dirname_common, 'assets/sass/main.scss'),
        'bootstrap/main-bootstrap': path.join(
            __dirname_common,
            'assets/sass/bootstrap/main-bootstrap.scss'
        ),
        'main-pro': path.join(__dirname_common, 'assets/sass/main-pro.scss'),
    },
    addAppShell({
        'base/login': path.join(
            __dirname_common,
            'assets/js/base/common/login/main.js'
        ),
    })
);

export const all_entries = Object.assign(
    {},
    addAppShell(locatePagesEntries()),
    {
        'base/app-shell': path.join(
            __dirname_common,
            'assets/js/base/common/app-shell.js'
        ),
        'base/landing-page': path.join(
            __dirname_common,
            'assets/sass/pages/landing-page/main.scss'
        ),
        'base/billboard-print': path.join(
            __dirname_common,
            'assets/sass/pages/property/billboard/print.scss'
        ),
    },
    addAppShell({
        'base/a2f': path.join(
            __dirname_common,
            'assets/js/base/common/a2f/main.js'
        ),
        'base/set-password': [
            'jquery-extend',
            path.join(
                __dirname_common,
                'assets/js/base/common/set-password/main.js'
            ),
        ],
        'base/set-phone-number': path.join(
            __dirname_common,
            'assets/js/base/common/set-phone-number/main.js'
        ),
        'base/empty': path.join(
            __dirname_common,
            'assets/js/base/common/empty.js'
        ),
    })
);

const entries = Object.assign({}, basicEntries, all_entries);

const config = {
    mode: 'production',
    entry: entries,
    output: {
        publicPath: PUBLIC_PATH,
        path: OUTPUT_PATH,
        clean: {
            keep: /gx-navigation\/|.gitkeep/,
        },
        chunkFilename: '[name].[contenthash].js',
        filename: '[name].[contenthash].js',
        assetModuleFilename: 'images/[name].[contenthash][ext][query]',
    },
    resolve: {
        modules: [
            path.join(__dirname_common, 'node_modules'),
            path.join(__dirname_common, 'node_modules', '@bower_components'),
            path.join(__dirname_common, 'assets', 'js', 'commons'),
            __dirname_common, // templates
        ],
        extensions: ['.ts', '.tsx', '.jsx', '.js', '.mjs'],
        fallback: {
            path: require.resolve('path-browserify'),
        },
        alias: {
            'bootstrap-extend':
                'bootstrap-sass-official/assets/javascripts/bootstrap.js',
            datetimepicker:
                'eonasdan-bootstrap-datetimepicker/src/js/bootstrap-datetimepicker.js',
            'gtx-bootbox': '@getrix/common/js/gtx-bootbox.js',
            'gtx-notify': '@getrix/common/js/gtx-notify.js',
            'gtx-constants': '@getrix/common/js/gtx-constants.js',
            imgeditor: '@immobiliare-labs/imgeditor',
            'jquery-extend': '@getrix/common/js/jquery-plugins.js',
            'jquery-ui/ui/widget':
                'blueimp-file-upload/js/vendor/jquery.ui.widget.js',
            'styled-select':
                'core-utils/modules/styled-select/js/styled-select',
            swig: '@getrix/common/js/gtx-swig.js',
            'swig-root': path.join(__dirname_common, 'node_modules/swig'),
        },
    },
    plugins: [
        new BabelfishLoaderWrapperPlugin({
            project: 'mls',
            entrypoint: 'runtime',
            debug: false,
        }),
        new I18nPo2Json(translationsDirs, true),
        new webpack.DefinePlugin({
            __DEV__: JSON.stringify(process.env.NODE_ENV === 'dev'),
            //Hardcoded becuase .env is not accessible during CI
            SENTRY_DSN: JSON.stringify(
                'https://<EMAIL>/68'
            ),
            SENTRY_VERSION: JSON.stringify(process.env.SENTRY_VERSION),
        }),
        // sentryWebpackPlugin({
        //     applicationKey: 'mls-site',
        // }),
        new MiniCssExtractPlugin({
            filename:
                process.env.NODE_ENV === 'dev'
                    ? '[name].css'
                    : '[name].[contenthash].css',
        }),
        new WebpackManifestPlugin(),
        new CopyPlugin({
            patterns: [
                {
                    from: path.join(__dirname_common, 'assets/images/base'),
                    to: path.join(OUTPUT_PATH, 'base/img'),
                },
                {
                    from: path.join(__dirname_common, 'assets/images/mail'),
                    to: path.join(OUTPUT_PATH, 'mail/img'),
                },
                {
                    from: path.join(
                        __dirname_common,
                        'node_modules/@getrix/common/icons-set'
                    ),
                    to: path.join(OUTPUT_PATH, 'base/svg/icons-set'),
                },
                {
                    from: path.join(
                        __dirname_common,
                        'node_modules/@getrix/common/img'
                    ),
                    to: path.join(OUTPUT_PATH, 'base/getrix/common/img'),
                },
                {
                    from: path.join(
                        __dirname_common,
                        'node_modules/@getrix/common/views'
                    ),
                    to: path.join(
                        __dirname_common,
                        'templates/base/Vendor/getrix/common'
                    ),
                },
                {
                    from: path.join(
                        __dirname_common,
                        'node_modules/@getrix/common/js'
                    ),
                    to: path.join(
                        __dirname_common,
                        'templates/base/Vendor/getrix/common'
                    ),
                },
                {
                    from: path.join(__dirname_common, 'assets/js/base/vendor'),
                    to: path.join(OUTPUT_PATH, 'base'),
                },
                {
                    from: path.join(
                        __dirname_common,
                        'node_modules/@getrix/virtual-tour/assets'
                    ),
                    to: path.join(OUTPUT_PATH, 'base/virtual-tour'),
                },
                {
                    from: path.join(
                        __dirname_common,
                        'node_modules/@getrix/photoplan/assets'
                    ),
                    to: path.join(OUTPUT_PATH, 'base/photoplan'),
                },
                {
                    context: path.join(
                        __dirname_common,
                        'node_modules/jquery-1-10-2/'
                    ),
                    from: '*.js',
                    to: path.join(OUTPUT_PATH, 'base'),
                },
                {
                    from: path.join(
                        __dirname_common,
                        'node_modules/@getrix/common/js/workers'
                    ),
                    to: path.join(OUTPUT_PATH, 'workers'),
                },
                {
                    to({ context, absoluteFilename }) {
                        return `base/datetimepicker/${path.relative(
                            context,
                            absoluteFilename
                        )}`;
                    },
                    from: 'node_modules/@bower_components/eonasdan-bootstrap-datetimepicker/build/css',
                },
            ],
        }),
        new BundleTracker({
            filename: path.join(OUTPUT_PATH, 'webpack-stats.json'),
        }),
    ],
    externals: {
        jquery: 'jQuery',
        window: 'window',
    },
    module: {
        rules: [
            {
                test: /\.(png|svg|jpg|jpeg|gif)$/i,
                type: 'asset',
            },
            { test: /\.html\.twig/, use: 'raw-loader' },
            {
                test: /^swig$/,
                use: 'imports-loader?window=>{}!exports-loader?swig!swig/browser',
            },
            {
                test: /\.(sa|sc|c)ss$/,
                use: [
                    { loader: MiniCssExtractPlugin.loader },
                    'css-loader',
                    {
                        loader: 'sass-loader',
                        options: {
                            sassOptions: {
                                includePaths: [
                                    'node_modules/@bower_components/bootstrap-sass-official/assets/stylesheets/',
                                    'node_modules',
                                    'node_modules/getrix/common/sass',
                                    'node_modules/getrix/common/node_modules',
                                ],
                            },
                        },
                    },
                ],
            },
            {
                test: /\.(tsx|ts|jsx|js|mjs)$/,
                exclude: /node_modules\/(?!date-fns|@tanstack).*/, // excludes date-fns and @tanstack because of older browsers
                use: {
                    // `.swcrc` can be used to configure swc
                    loader: 'swc-loader',
                },
            },
            {
                test: /\.(woff|woff2)$/,
                use: {
                    loader: 'url-loader',
                },
            },
        ],
    },
    optimization: {
        runtimeChunk: 'single',
        splitChunks: {
            chunks: 'all',
            cacheGroups: {
                photoplan: {
                    test: /[\\/]node_modules[\\/]@getrix[\\/]photoplan[\\/]/,
                    // @TODO: vedere se è possibile assegnargli una cartella specifica es. (/base/photoplan)
                    name: 'photoplan',
                    chunks: 'all',
                },
                virtualTour360: {
                    test: /[\\/]node_modules[\\/]@getrix[\\/]virtual-tour[\\/]/,
                    // @TODO: vedere se è possibile assegnargli una cartella specifica es. (/base/virtual-tour)
                    name: 'virtual-tour-360',
                    chunks: 'all',
                },
            },
        },
    },
};

export default config;
