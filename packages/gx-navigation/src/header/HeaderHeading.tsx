import clsx from 'clsx';
import { PropsWithChildren } from 'react';
import { ImageProps } from '../types';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { useMediaMatch } from '@gx-design/use-media-match';
import { ChristmasLights } from '../header/HeaderContent/ChristmasLights';
import {
  useGxNavigationMenuContext,
  useGxNavigationMenuDispatcher,
} from '../shared/components/GxNavigationMenuContext';
import { getLogo, isChristmasTime } from '../shared/utils/getLogo';
import { HOLIDAY_DAYS_CHECK } from '../shared/constants';
import { useSecondaryMenuItemByPathname } from '../shared/hooks/menu';

export const HamburgerButton = () => {
  return <ToggleMenuButton />;
};

const ToggleMenuButton = () => {
  const { openMenu } = useGxNavigationMenuContext();
  const { dispatch } = useGxNavigationMenuDispatcher();
  const secondaryMenuItemByPathname = useSecondaryMenuItemByPathname();

  return (
    <Button
      onClick={() => {
        dispatch({ type: 'TOGGLE_MENU', payload: secondaryMenuItemByPathname });
      }}
      iconOnly
      className='gx-navigation-header__menu-hamburger'
      variant='ghost'
    >
      <Icon name={openMenu ? 'burger-menu-opened' : 'burger-menu'} />
    </Button>
  );
};

type LogoProps = {
  isVisible: boolean;
  url: string;
} & ImageProps;

export const Logo = (props: LogoProps) => {
  const logoUrl = getLogo(props.imageUrl, HOLIDAY_DAYS_CHECK);
  const isChristmas = isChristmasTime();
  return (
    <a
      href={props.url}
      className={clsx('gx-navigation-header__logo', {
        'gx-is-hidden-sm-down': !props.isVisible,
        'is-christmas': isChristmas,
      })}
    >
      <img src={logoUrl} alt={props.alt} />
    </a>
  );
};

type SectionBoxProps = {
  parentSection?: string;
  section: string;
};

/**
 * Is used to display the section name in the header.
 * Visible only on mobile.
 */
export const SectionBox = (props: PropsWithChildren<SectionBoxProps>) => {
  return (
    <div className='gx-navigation-header__sectionBox gx-is-hidden-md-up'>
      <div className='gx-navigation-header__section gx-body-small'>{props.parentSection}</div>
      <div className='gx-navigation-header__subSection gx-title-2'>{props.section}</div>
    </div>
  );
};

export const HeaderHeading = (props: PropsWithChildren<unknown>) => {
  const isLargeDesktop = useMediaMatch('largeDesktop');
  const isChristmas = isChristmasTime();
  return (
    <>
      <div className='gx-navigation-header__heading'>{props.children}</div>
      {isChristmas && isLargeDesktop && <ChristmasLights />}
    </>
  );
};
