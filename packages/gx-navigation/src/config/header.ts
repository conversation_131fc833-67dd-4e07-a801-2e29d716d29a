import { trans } from '../shared/translations';
import { ComponentKey, ComponentPropsMap } from '../custom';
import * as events from '../events/outputTypes';
import { GxNavigationFeatureFlags, HeaderConfiguration } from '../types';

/**
 * This is an utility function to create an action object
 */
export function createAction<T extends ComponentKey, K extends ComponentPropsMap[T]>(
  type: T,
  props?: K,
) {
  return { type, props: props || undefined } as const;
}

export const defaultProps = {
  menuCacheToken: '1234567890',
  agencyLanguage: 'it_IT',
  hasVirtualVisitSettingsFlag: true,
  hasScheduledVisitImmovisita: true,
  agencyId: 1,
  agencyImage: 'https://placehold.co/400x100',
  agencyName: 'CasaMia',
  agentName: '<PERSON>',
  agentEmail: '<EMAIL>',
  agentAvatar: 'https://placehold.co/100x100',
  logo: '/images/logo-getrix.svg',
  homeUrl: '/',
  siteName: 'Getrix',
  adPortal: 'Immobiliare.it',
  serviceRemoteVisit: 'Immovisita',
  defaultLanguage: 'it',
};

/**
 * This is the configuration for the header.
 * @example
 * {
 * '/multinvio/esportazioni': {
 *   section: ['Multinvio', 'Gestione esportazioni'],
 *   actions: [
 *     createAction('DefaultAction', {
 *       label: 'Riordina',
 *       event: MULTISEND_REORDER,
 *       visibility: 'md-up',
 *     }),
 *   ],
 * },
 * '/multinvio/esportazioni/attive': {
 *   section: ['Multinvio', 'Esportazioni attive'],
 * },
 * '/servizi_extra.php': {
 *   '?multimedia=vt': {
 *     section: ['Servizi extra', 'Virtual Tour'],
 *   },
 *   '?multimedia=xd': {
 *     section: ['Servizi extra', 'Pepperoni'],
 *   }
 *}
 */
export const createConfig = ({
  adPortal,
  serviceRemoteVisit,
  featureFlags,
}: {
  adPortal: string;
  serviceRemoteVisit: string;
  featureFlags: GxNavigationFeatureFlags;
}): HeaderConfiguration => {
  return {
    '/agenzia/agenzie-associate': {
      section: trans('label.multi_agency_section_title'),
    },
    '/': {
      section: null,
    },
    '/multinvio/esportazioni': {
      section: [trans('label.multisend'), trans('label.exports_management')],
      actions: [
        createAction('DefaultAction', {
          label: trans('label.reorder'),
          event: events.MULTISEND_REORDER,
          visibility: 'md-up',
        }),
      ],
    },
    '/multinvio/esportazioni/attive': {
      section: [trans('label.multisend'), trans('label.active_exports')],
      actions: [
        createAction('MultipleActions', {
          actions: [
            {
              label: trans('label.reorder'),
              event: events.MULTISEND_REORDER,
              visibility: 'md-up',
            },
            {
              label: trans('label.management'),
              url: '/multinvio/esportazioni',
              visibility: 'md-up',
              variant: 'accent',
            },
          ],
        }),
      ],
    },
    '/messaggi/lista': {
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          event: events.MARK_AS_READ_MESSAGES,
          icon: 'chat-check',
          iconOnly: true,
          label: trans('label.messages'),
        }),
      ],
      section: trans('label.messages'),
    },
    '/messaggi/{id}': {
      section: trans('label.messages'),
    },
    '/v2/telefonate': {
      section: [trans('label.messages'), trans('label.phone_calls')],
    },
    '/v2/contatti_agenzie.php': {
      section: trans('label.messages'),
    },
    '/annunci_portale.php': {
      section: [trans('label.estates'), `${trans('label.out_of')} ${adPortal}`],
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          url: '/inserimento_annuncio.php',
          icon: 'plus',
          label: trans('label.add'),
          variant: 'accent',
        }),
      ],
    },
    '/ricerche/manuali/lista': {
      section: [trans('label.customers'), trans('label.manual_searches')],
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          url: '/ricerche/crea',
          icon: 'plus',
          label: trans('label.searches.create_search'),
          variant: 'accent',
        }),
      ],
    },
    '/ricerche/manuali/crea': {
      section: [
        trans('label.customers'),
        `${trans('label.manual_searches')} - ${trans('label.searches.create_search')}`,
      ],
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          url: '/ricerche/crea',
          icon: 'plus',
          label: trans('label.searches.create_search'),
          variant: 'accent',
        }),
      ],
    },
    '/ricerche/manuali/{researchId}': {
      section: [
        trans('label.customers'),
        `${trans('label.manual_searches')} - ${trans('label.searches.create_search')}`,
      ],
    },
    '/richieste.php': {
      section: [trans('label.customers'), trans('label.specific_researches')],
    },
    '/v2/richieste_utente.php': {
      section: trans('label.received_researches'),
    },
    '/profile/me': {
      section: trans('label.my_profile'),
    },
    '/profile/force-password': {
      section: trans('label.update_password'),
    },
    '/sitoagenzia.php': {
      section: trans('label.website'),
    },
    '/amministrazione/contratto': {
      section: [trans('label.administration'), trans('label.contract')],
      actions: [createAction('ClientId')],
    },
    '/clienti/match': {
      section: [trans('label.customers'), trans('label.matches.page_title')],
    },
    '/clienti/aggiungi': {
      section: [trans('label.customers'), trans('label.add')],
    },
    '/clienti': {
      section: [trans('label.customers'), trans('label.customers_portfolio')],
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          event: events.CUSTOMERS_OPEN_MODAL_ADD_USER,
          icon: 'plus',
          label: trans('label.add'),
          variant: 'accent',
        }),
      ],
    },
    '/immobili/portale/lista': {
      section: [trans('label.estates'), `${trans('label.out_of')} ${adPortal}`],
    },
    '/annunci_immovisita.php': {
      section: [serviceRemoteVisit, trans('label.ads')],
      actions: [
        createAction('ActionsSlot', {
          slots: [
            {
              componentName: 'RequestStatusBadge',
            },
            {
              componentName: 'DefaultAction',
              visibility: 'md-up',
              url: '/impostazioni/visite-a-distanza',
              label: trans('label.edit'),
              icon: 'pencil',
            },
          ],
        }),
      ],
    },
    '/immovisita/scheduled-visits': {
      section: [serviceRemoteVisit, trans('label.immovisita.scheduled_visits')],
      actions: [
        createAction('DefaultAction', {
          event: events.IV_SCH_GX_NAVIGATION_ADD,
          visibility: 'md-up',
          label: trans('label.add'),
          icon: 'plus',
          variant: 'accent',
        }),
      ],
    },
    '/inserimento_annuncio.php': {
      section: [trans('label.estates'), trans('label.add')],
    },
    '/annunci_venduti_affittati.php': {
      section: [trans('label.estates'), trans('label.sold_rented')],
      actions: [
        createAction('DefaultAction', {
          url: '/inserimento_venduto_affittato.php',
          icon: 'plus',
          label: trans('label.add'),
          variant: 'accent',
          visibility: 'md-up',
        }),
      ],
    },
    '/report/aggiungi': {
      section: [trans('label.real_estate_report'), trans('label.add')],
    },
    '/report/contattaci': {
      section: [trans('label.real_estate_report'), trans('label.add')],
    },
    '/report/{type}/aggiungi': {
      section: [trans('label.real_estate_report'), trans('label.add')],
    },
    '/report/{reportId}/modifica': {
      section: [trans('label.real_estate_report'), trans('label.edit')],
    },
    '/report/{reportId}/dettaglio/{type}': {
      section: [trans('label.real_estate_report'), trans('label.report')],
    },
    '/report': {
      section: [trans('label.real_estate_report'), trans('label.list')],
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          url: '/report/aggiungi',
          icon: 'plus',
          label: trans('label.add'),
          variant: 'accent',
        }),
      ],
    },
    '/inserimento_venduto_affittato.php': {
      section: [trans('label.estates'), trans('label.sold_rented')],
    },
    '/annunci_agenzia.php': {
      section: [trans('label.estates'), trans('label.list')],
      actions: featureFlags.addPropertyEnabled
        ? [
            createAction('DefaultAction', {
              visibility: 'md-up',
              url: '/inserimento_annuncio.php',
              icon: 'plus',
              label: trans('label.add'),
              variant: 'accent',
            }),
          ]
        : undefined,
    },
    '/v2/nuove-costruzioni': {
      section: [trans('label.new_buildings'), trans('label.add')],
    },
    '/v2/nuove-costruzioni/lista': {
      section: [trans('label.new_buildings'), trans('label.list')],
      actions: featureFlags.addPropertyEnabled
        ? [
            createAction('DefaultAction', {
              url: '/v2/nuove-costruzioni',
              label: trans('label.add'),
              icon: 'plus',
              variant: 'accent',
              visibility: 'md-up',
            }),
          ]
        : undefined,
    },
    '/nuove-costruzioni/portale/lista': {
      section: [trans('label.new_buildings'), `${trans('label.out_of')} ${adPortal}`],
    },
    '/immobili/{estateId}/cartellone': {
      section: [trans('label.estates'), trans('label.create_billboard')],
      actions: [
        createAction('MultipleActions', {
          actions: [
            {
              event: events.BILLBOARD_PRINT,
              icon: 'print',
              label: trans('label.print'),
              visibility: 'md-up',
            },
            {
              event: events.BILLBOARD_SAVE_PDF,
              icon: 'download',
              label: trans('label.download_pdf'),
              visibility: 'md-up',
            },
          ],
        }),
      ],
    },
    '/multinvio/annunci': {
      section: [trans('label.multisend'), trans('label.ads')],
    },
    '/multinvio/nuove-costruzioni': {
      section: [trans('label.multisend'), trans('label.new_buildings')],
    },
    '/immobili/portale/{estateId}/prestazioni': {
      section: [trans('label.estates'), `${trans('label.out_of')} ${adPortal}`],
    },
    '/immobili/aste/catalogo': {
      section: [trans('label.auctions'), trans('label.catalogue')],
    },
    '/immobili/aste/portale/lista': {
      section: [trans('label.auctions'), `${trans('label.out_of')} ${adPortal}`],
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          url: '/inserimento_asta.php',
          icon: 'plus',
          label: trans('label.add'),
          variant: 'accent',
        }),
      ],
    },
    '/inserimento_asta.php': {
      section: [trans('label.auctions'), trans('label.add')],
    },
    '/aste.php': {
      section: [trans('label.auctions'), trans('label.list')],
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          url: '/inserimento_asta.php',
          icon: 'plus',
          label: trans('label.add'),
          variant: 'accent',
        }),
      ],
    },
    '/immobili/aste/catalogo/{auctionId}/dettaglio': {
      section: [trans('label.auctions'), trans('label.catalogue')],
    },
    '/immobili/lista/*': {
      section: [trans('label.ads'), trans('label.ad_list')],
    },
    '/acquisizione/annunci-privati': {
      section: [trans('label.acquisition'), trans('label.private_properties')],
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          url: '/v2/zone',
          label: trans('label.set_provinces'),
          variant: 'accent',
        }),
      ],
    },
    '/acquisizione/valutazioni': {
      section: [trans('label.acquisition'), trans('label.property_valuations')],
    },
    '/acquisizione/valutazioni/{estimateId}/dettaglio': {
      section: [trans('label.acquisition'), trans('label.property_valuations')],
    },
    '/acquisizione/richieste-vendita': {
      section: [trans('label.acquisition'), trans('label.sales_requests')],
    },
    '/acquisizione/richieste-vendita/{acquisitionId}/dettaglio': {
      section: [trans('label.acquisition'), trans('label.sales_requests')],
    },
    '/youdomus/*': {
      section: [trans('label.service_youdomus'), trans('label.youdomus.services')],
    },
    '/youdomus': {
      section: [trans('label.service_youdomus'), trans('label.youdomus.services')],
    },
    '/youdomus/Archivio': {
      section: [trans('label.service_youdomus'), trans('label.youdomus.documents')],
    },
    '/youdomus/Monitoraggio/IndexImmobile': {
      section: [trans('label.service_youdomus'), trans('label.youdomus.monitoring')],
      actions: [
        createAction('DefaultAction', {
          url: '/youdomus/Monitoraggio/InsertImmobile',
          variant: 'accent',
          label: trans('label.add'),
          icon: 'plus',
          visibility: 'md-up',
        }),
      ],
    },
    '/youdomus/Monitoraggio/InsertImmobile': {
      section: null,
    },
    '/youdomus/Mappa/CatastoSuMappa': {
      section: [trans('label.service_youdomus'), trans('label.youdomus.cadastre_on_map')],
    },
    '/youdomus/CertificazioneAPE': {
      section: [trans('label.service_youdomus'), trans('label.youdomus.ape_certifications')],
    },
    '/youdomus/Utente/Profilo': {
      section: [
        trans('label.service_youdomus'),
        trans('label.youdomus.welcome_modal.customer_area'),
      ],
    },
    '/youdomus/obtainCadastralData/{adId}': {
      section: null,
    },
    '/consulenza-mutuo/richiesta': {
      section: trans('label.mortgage_advice'),
    },
    '/impostazioni/generale': {
      section: [trans('label.settings'), trans('label.general')],
    },
    '/impostazioni/sede': {
      section: [trans('label.settings'), trans('label.head_office')],
    },
    '/impostazioni/media': {
      section: [trans('label.settings'), trans('label.images_and_videos')],
    },
    '/impostazioni/sicurezza': {
      section: [trans('label.settings'), trans('label.security')],
    },
    '/impostazioni/utenti': {
      section: [trans('label.settings'), trans('label.users')],
      actions: [
        createAction('DefaultAction', {
          event: events.ADD_USER,
          visibility: 'md-up',
          label: trans('label.add'),
          icon: 'plus',
          variant: 'accent',
        }),
      ],
    },
    '/impostazioni/utenti/{idUtente}': {
      section: [trans('label.settings'), trans('label.users')],
    },
    '/settings/users/{idUtente}': {
      section: [trans('label.settings'), trans('label.users')],
    },
    '/impostazioni/utenti/{idUtente}/edit': {
      section: [trans('label.settings'), trans('label.users')],
    },
    '/settings/users/{idUtente}/edit': {
      section: [trans('label.settings'), trans('label.users')],
    },
    '/impostazioni/utenti/{idUtente}/activation': {
      section: [trans('label.settings'), trans('label.users')],
    },
    '/impostazioni/visite-a-distanza': {
      section: [trans('label.settings'), trans('label.remote_visits')],
    },
    '/v2/zone': {
      section: trans('label.set_provinces'),
      actions: [
        createAction('DefaultAction', {
          url: '/v2/zone/add',
          label: trans('label.add'),
          visibility: 'md-up',
          variant: 'accent',
          icon: 'plus',
        }),
      ],
    },
    '/v2/zone/add': {
      section: trans('label.insert_province'),
    },
    '/v2/zone/edit': {
      section: trans('label.set_provinces'),
    },
    '/agenda.php': {
      section: trans('label.agenda'),
      actions: [
        createAction('DefaultAction', {
          event: '',
          label: trans('label.add'),
          icon: 'plus',
          variant: 'accent',
          visibility: 'md-up',
          dataset: {
            'data-role': 'gtx-apt-add',
          },
        }),
      ],
    },
    '/v2/agenda/appuntamento': {
      '?id=*': {
        section: trans('label.edit_appointment'),
      },
    },
    '/servizi_extra.php': {
      '?multimedia=vt': {
        section: [trans('label.virtual_tour_3d'), trans('label.ads')],
      },
      '?multimedia=pl': {
        section: trans('label.improve_ad_quality.plans_title'),
      },
    },
    '/v2/virtual-tour-nuove-costruzioni': {
      section: [trans('label.virtual_tour_3d'), trans('label.new_buildings')],
    },
    '/v2/servizi-web-marketing': {
      section: trans('label.web_services'),
    },
    '/amministrazione/dati-fatturazione': {
      section: [trans('label.administration'), trans('label.billing_information')],
      actions: [createAction('ClientId')],
    },
    '/amministrazione/fatture': {
      section: [trans('label.administration'), trans('label.invoices')],
      actions: [createAction('ClientId')],
    },
    '/dettaglio.php': {
      '?id=*': {
        section: [trans('label.ads'), trans('label.detail')],
      },
    },
    '/lista_contatti.php': {
      section: [trans('label.customers'), trans('label.customers_portfolio')],
      '?idContatto=*': {
        section: [trans('label.customers'), trans('label.customers_portfolio')],
      },
    },
    '/pacchetti-zona': {
      section: trans('label.zone_packages'),
    },
    '/ricerche/attive': {
      section: [trans('label.customers'), trans('label.active_searches')],
    },
    '/clienti/imposta-notifiche': {
      section: [trans('label.customers'), trans('label.notification_settings')],
    },
    '/report/insights/new/*': {
      section: [trans('label.real_estate_report'), trans('label.add')],
    },
    '/report/insights/list/*': {
      section: [trans('label.real_estate_report'), trans('label.list')],
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          url: '/report/insights/new',
          icon: 'plus',
          label: trans('label.add'),
          variant: 'accent',
        }),
      ],
    },
    '/report/insights/settings/*': {
      section: [trans('label.real_estate_report'), trans('label.settings')],
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          url: '/report/insights/new',
          icon: 'plus',
          label: trans('label.add'),
          variant: 'accent',
        }),
      ],
    },
    '/report/insights/*': {
      section: trans('label.real_estate_report'),
      actions: [
        createAction('DefaultAction', {
          visibility: 'md-up',
          url: '/report/insights/new',
          icon: 'plus',
          label: trans('label.add'),
          variant: 'accent',
        }),
      ],
    },
  };
};
