{"parser": "@typescript-eslint/parser", "extends": ["eslint:recommended", "plugin:you-dont-need-lodash-underscore/compatible", "plugin:you-dont-need-momentjs/recommended", "plugin:compat/recommended", "plugin:vitest/recommended", "plugin:react/recommended", "plugin:testing-library/react", "prettier"], "plugins": ["react", "react-hooks"], "globals": {"$": true, "__DEV__": true, "__MAPS_SERVICES_CONFIG__": true, "React": true, "JSX": true}, "env": {"browser": true, "es6": true, "node": true}, "parserOptions": {"sourceType": "module", "ecmaFeatures": {"modules": true, "jsx": true}}, "ignorePatterns": ["tools/webpack-plugins"], "rules": {"no-restricted-imports": ["error", {"paths": [{"name": "@pepita/http", "message": "@pepita/http has been deprecated from the Pepita team. Please use the fetch standard api instead."}]}], "vitest/expect-expect": ["error", {"additionalTestBlockFunctions": ["testForm", "test"]}], "react/react-in-jsx-scope": "off", "react-hooks/exhaustive-deps": "warn", "react-hooks/rules-of-hooks": "error", "camelcase": "error", "eqeqeq": "warn", "new-cap": ["error", {"capIsNewExceptionPattern": "^(Immutable|chart|\\$).\\w"}], "no-unused-vars": ["warn", {"ignoreRestSiblings": true, "argsIgnorePattern": "^_", "args": "none"}], "strict": "off", "no-undef": "error", "dot-notation": "off", "no-console": "warn", "curly": "error", "no-underscore-dangle": "off", "no-useless-escape": "off", "react/jsx-uses-vars": 1, "react/prop-types": "off"}, "settings": {"react": {"version": "detect"}, "polyfills": ["Promise", "Object.fromEntries"]}}