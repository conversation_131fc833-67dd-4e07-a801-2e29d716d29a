import { IPropertiesSpaces } from 'types/api/property';
import { formatNumberToLocale } from '../../../commons/lib/formatter';
import { trans } from '@pepita-i18n/babelfish';

export const PremiumSpaces: React.FC<{
    spaces: IPropertiesSpaces[];
    contractId: number;
    label: string;
}> = ({ spaces, contractId, label }) => {
    if (!spaces) {
        return null;
    }

    const data = spaces.find(item => item.type.id === contractId) as IPropertiesSpaces;
    const used = formatNumberToLocale(data.applied) ?? 0;
    const total =
        data.applied >= 0 && data.available >= 0
            ? formatNumberToLocale(data.applied + data.available)
            : 0;

    return <span role="premium-spaces">{`${used} ${trans('label.out_of')} ${total} ${label}`}</span>;
};
