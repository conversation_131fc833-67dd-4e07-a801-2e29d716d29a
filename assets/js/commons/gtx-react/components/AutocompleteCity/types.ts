
export interface Country {
	id: string;
	name: string;
}
export interface Region {
	id: string;
	name: string;
	country: Country;
}

export interface Province {
	id: string;
	name: string;
	region: Region;
}
export interface CityMacroZoneType {
	id: number;
	name: string;
}

export interface LookupApiGeographyCitiesData {
	id: number;
	name: string;
	province: Province;
	cityMacroZoneType: CityMacroZoneType;
	chiefTown: boolean;
}
