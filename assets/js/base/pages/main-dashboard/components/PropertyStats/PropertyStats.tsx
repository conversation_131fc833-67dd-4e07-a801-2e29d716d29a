import { Radio, RadioGroup } from '@gx-design/radio';
import { Select } from '@gx-design/select';
import { trans } from '@pepita-i18n/babelfish';
import { FormikProvider, useFormik, useFormikContext } from 'formik';
import { GxFkRadio } from 'gtx-react/components/gx-formik';
import { PropsWithChildren, useMemo } from 'react';
import { FeatureAvailability } from '../FeatureAvailability';
import { usePropertyStatsQuery } from '../../hooks/queries';
import { PropertyStatsStatus } from '../../types';
import { PropertyStatsList } from './PropertyStatsList';
import {
    PropertyStatsPieChart,
    PropertyStatsPieText,
} from './PropertyStatsPieChart';

const propertyOptions = [
    {
        label: trans('label.active_plural'),
        value: 'active',
    },
    {
        label: trans('label.archived_plural'),
        value: 'archived',
    },
];

type PropertyStatsProps = {
    enableChooseBetweenPublicationStatus: boolean;
};

export function PropertyStats(props: PropertyStatsProps) {
    const initialValues: PropertyStatsFormValues = useMemo(() => {
        return {
            status: 'active',
            'publication-status': props.enableChooseBetweenPublicationStatus
                ? 'published'
                : 'all',
        };
    }, [props.enableChooseBetweenPublicationStatus]);

    return (
        <PropertyStatsForm initialValues={initialValues}>
            <div className="gx-card" data-component="ads-stats">
                <div className="gx-card__content gx-card__content--paddingDouble property-stats">
                    <div className="property-stats__head">
                        <div className="gx-title-1">{trans('label.ads')}</div>
                        <PropertyStatsFields
                            enableChooseBetweenPublicationStatus={
                                props.enableChooseBetweenPublicationStatus
                            }
                        />
                    </div>
                    <div data-role="ads-stats-view-container">
                        <PropertyStatsContent />
                    </div>
                </div>
            </div>
        </PropertyStatsForm>
    );
}

type PropertyStatsFormValues = {
    status: 'active' | 'archived';
    'publication-status': 'published' | 'all';
};

function PropertyStatsFields(props: PropertyStatsProps) {
    const { setValues, getFieldProps } =
        useFormikContext<PropertyStatsFormValues>();

    return (
        <>
            <Select
                {...getFieldProps('status')}
                onChange={(evt) => {
                    setValues((prev) => {
                        if (evt.target.value === 'archived') {
                            return {
                                'publication-status': 'all',
                                status: evt.target.value,
                            };
                        }

                        return {
                            ...prev,
                            status: evt.target
                                .value as PropertyStatsFormValues['status'],
                        };
                    });
                }}
                label=""
                aria-label={trans('label.status')}
                autoComplete="off"
                options={propertyOptions}
            />
            <FeatureAvailability
                available={props.enableChooseBetweenPublicationStatus}
            >
                <div className="property-stats__headGroup">
                    <RadioGroup
                        variant="button"
                        label=""
                        isLabelVisible={false}
                    >
                        <GxFkRadio
                            label={trans('label.everyone')}
                            name="publication-status"
                            value="all"
                        />
                        <Radio
                            {...getFieldProps({
                                type: 'radio',
                                name: 'publication-status',
                                value: 'published',
                            })}
                            label={trans('label.in_advertising')}
                            onChange={(evt) => {
                                setValues((prev) => {
                                    if (prev.status === 'archived') {
                                        return {
                                            status: 'active',
                                            'publication-status': evt.target
                                                .value as PropertyStatsFormValues['publication-status'],
                                        };
                                    }

                                    return {
                                        ...prev,
                                        'publication-status': evt.target
                                            .value as PropertyStatsFormValues['publication-status'],
                                    };
                                });
                            }}
                        />
                    </RadioGroup>
                </div>
            </FeatureAvailability>
        </>
    );
}

function PropertyStatsContent() {
    const { values } = useFormikContext<PropertyStatsFormValues>();
    const status: PropertyStatsStatus = useMemo(() => {
        if (values.status === 'active') {
            if (values['publication-status'] === 'published') {
                return 'active-published';
            }

            return 'active-all';
        }

        return 'archived-all';
    }, [values]);

    const propertyStatsQuery = usePropertyStatsQuery(status);

    if (propertyStatsQuery.isLoading || !propertyStatsQuery.data) {
        return null;
    }

    return (
        <div className="property-stats__content">
            <PropertyStatsPieChart
                dataset={propertyStatsQuery.data.chart.dataset}
            >
                <PropertyStatsPieText
                    total={propertyStatsQuery.data.chart.content.value}
                    label={
                        <div>
                            <span data-testid="pie-text">
                                {
                                    propertyStatsQuery.data.chart.content
                                        .firstLine
                                }
                                <br />
                                {
                                    propertyStatsQuery.data.chart.content
                                        .secondLine
                                }
                            </span>
                        </div>
                    }
                />
            </PropertyStatsPieChart>

            <PropertyStatsList list={propertyStatsQuery.data.list} />
        </div>
    );
}

type PropertyStatsFormProps = PropsWithChildren<{
    initialValues: PropertyStatsFormValues;
}>;

export function PropertyStatsForm(props: PropertyStatsFormProps) {
    const formik = useFormik<PropertyStatsFormValues>({
        initialValues: props.initialValues,
        onSubmit: () => {},
    });

    return <FormikProvider value={formik}>{props.children}</FormikProvider>;
}
