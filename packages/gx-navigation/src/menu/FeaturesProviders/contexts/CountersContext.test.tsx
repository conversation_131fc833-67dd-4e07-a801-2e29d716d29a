import { render, act } from '@testing-library/react';
import { CountersProvider, useCountersContext } from './CountersContext';
import { describe, expect, it, vi } from 'vitest';
import { UPDATE_STATS } from '../../../events/inputTypes';

const TestComponent = () => {
  const counters = useCountersContext();
  return <div data-testid='counter'>{counters.newMatches}</div>;
};

describe('CounterProvider', () => {
  it('should provide counter value to children', () => {
    const { getByTestId } = render(
      <CountersProvider>
        <TestComponent />
      </CountersProvider>,
    );

    expect(getByTestId('counter')).toHaveTextContent('0');

    act(() => {
      window.dispatchEvent(new CustomEvent(UPDATE_STATS, { detail: { newMatches: 10 } }));
    });

    expect(getByTestId('counter')).toHaveTextContent('10');
  });
});

describe('useCounterContext', () => {
  it('should throw error when used outside CounterProvider', () => {
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error');
    consoleSpy.mockImplementation(() => {});

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useCountersContext must be used within a CountersProvider');

    consoleSpy.mockRestore();
  });

  it('should return context when used within CounterProvider', () => {
    const { getByTestId } = render(
      <CountersProvider>
        <TestComponent />
      </CountersProvider>,
    );

    expect(getByTestId('counter')).toBeInTheDocument();
  });
});
