{% extends 'base.html.twig' %}

{% block header %}
    <div id="header"></div>
{% endblock %}

{% block content %}
    <div id="content"></div>
{% endblock %}

{% block reactApp %}
    <div id="root"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script id="site-section-title" type="application/json">{{ trans(title_trans_key, {}, gtxConstants.DOMAIN_CMS) }}</script>
    {% include 'commons/core/js-scripts.html.twig' %}
    <script id="profile-data" type="application/json">{{ data|json_encode|raw }}</script>
    {{ webpack_asset('base/profile') }}
{% endblock %}
