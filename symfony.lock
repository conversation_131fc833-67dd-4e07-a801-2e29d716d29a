{"apache/thrift": {"version": "0.9.2"}, "davidepastore/codice-fiscale": {"version": "v0.4.2"}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "cb4152ebcadbe620ea2261da1a1c5a9b8cea7672"}}, "doctrine/cache": {"version": "v1.6.2"}, "doctrine/collections": {"version": "v1.4.0"}, "doctrine/common": {"version": "v2.7.3"}, "doctrine/dbal": {"version": "v2.5.13"}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-cache-bundle": {"version": "1.3.5"}, "doctrine/inflector": {"version": "v1.2.0"}, "doctrine/instantiator": {"version": "1.0.5"}, "doctrine/lexer": {"version": "v1.0.1"}, "doctrine/orm": {"version": "v2.5.14"}, "egulias/email-validator": {"version": "2.1.7"}, "ekbl/stats": {"version": "2.0.0"}, "ekbl/stats-bundle": {"version": "2.1.0"}, "firebase/php-jwt": {"version": "1.0.0"}, "friendsofphp/php-cs-fixer": {"version": "2.19", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.19", "ref": "34d1a22f840953909c581b8f993093b14cc9491b"}, "files": [".php-cs-fixer.dist.php"]}, "getrix/pnformatter": {"version": "v0.0.8"}, "guzzlehttp/guzzle": {"version": "6.3.3"}, "guzzlehttp/promises": {"version": "v1.3.1"}, "guzzlehttp/psr7": {"version": "1.5.2"}, "indomio-translations/cms-contents": {"version": "dev-master"}, "kylekatarnls/update-helper": {"version": "1.2.1"}, "maxakawizard/po-parser": {"version": "1.2.1"}, "monolog/monolog": {"version": "1.24.0"}, "nesbot/carbon": {"version": "2.56.0"}, "pepita/tax-code": {"version": "1.1.1"}, "pepita/wsse-header-generator-bundle": {"version": "1.0.0"}, "pepita/wsse-header-generator-php": {"version": "1.0.0"}, "php": {"version": "7.4"}, "php-amqplib/php-amqplib": {"version": "v2.2.6"}, "php-http/discovery": {"version": "1.20", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.18", "ref": "f45b5dd173a27873ab19f5e3180b2f661c21de02"}, "files": ["config/packages/http_discovery.yaml"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "6a9341aa97d441627f8bd424ae85dc04c944f8b4"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.0.0"}, "psr/http-message": {"version": "1.0.1"}, "psr/log": {"version": "1.0.2"}, "psr/simple-cache": {"version": "1.0.1"}, "ralouphie/getallheaders": {"version": "2.0.5"}, "sensio/framework-extra-bundle": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}}, "sentry/sentry": {"version": "1.10.0"}, "sentry/sentry-symfony": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "1.0", "ref": "fa1a2dfc020798cd7076b5419596e72dca07047a"}}, "soa/common": {"version": "2.4.4"}, "soa/rpc-security": {"version": "2.4.0"}, "soa/sdk": {"version": "********"}, "soa/sdk-bundle": {"version": "5.2.4"}, "soa/stubs": {"version": "2019020100"}, "soa/thrifter": {"version": "3.5.4"}, "swiftmailer/swiftmailer": {"version": "v6.1.3"}, "symfony/asset": {"version": "v3.4.22"}, "symfony/cache": {"version": "v3.4.22"}, "symfony/class-loader": {"version": "v3.4.22"}, "symfony/config": {"version": "v3.4.22"}, "symfony/console": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "0fa049c19069a65f52c1c181d64be3de672c1504"}}, "symfony/css-selector": {"version": "v3.4.22"}, "symfony/debug": {"version": "v3.4.22"}, "symfony/dependency-injection": {"version": "v3.4.22"}, "symfony/doctrine-bridge": {"version": "v3.4.22"}, "symfony/dom-crawler": {"version": "v3.4.22"}, "symfony/dotenv": {"version": "v3.4.22"}, "symfony/event-dispatcher": {"version": "v3.4.22"}, "symfony/expression-language": {"version": "v3.4.22"}, "symfony/filesystem": {"version": "v3.4.22"}, "symfony/finder": {"version": "v3.4.22"}, "symfony/flex": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "dc3fc2e0334a4137c47cfd5a3ececc601fa61a0b"}}, "symfony/form": {"version": "v3.4.22"}, "symfony/framework-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "af951c413e84511daefa4ddb3eb8166108a30b6c"}}, "symfony/http-foundation": {"version": "v3.4.22"}, "symfony/http-kernel": {"version": "v3.4.22"}, "symfony/inflector": {"version": "v3.4.22"}, "symfony/intl": {"version": "v3.4.22"}, "symfony/lts": {"version": "v3"}, "symfony/monolog-bridge": {"version": "v3.4.22"}, "symfony/monolog-bundle": {"version": "3.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.1", "ref": "18ebf5a940573a20de06f9c4060101eeb438cf3d"}}, "symfony/options-resolver": {"version": "v3.4.22"}, "symfony/phpunit-bridge": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.1", "ref": "2f91477d6efaed3fb857db87480f7d07d31cbb3e"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-apcu": {"version": "v1.10.0"}, "symfony/polyfill-intl-icu": {"version": "v1.10.0"}, "symfony/polyfill-intl-idn": {"version": "v1.10.0"}, "symfony/polyfill-mbstring": {"version": "v1.10.0"}, "symfony/polyfill-php72": {"version": "v1.10.0"}, "symfony/polyfill-php80": {"version": "v1.24.0"}, "symfony/profiler-pack": {"version": "v1.0.4"}, "symfony/property-access": {"version": "v3.4.22"}, "symfony/routing": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "4d36f55d8edd7bd88b730754c2d3f3dfc2cd316b"}}, "symfony/security": {"version": "v3.4.22"}, "symfony/security-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "f8a63faa0d9521526499c0a8f403c9964ecb0527"}}, "symfony/serializer": {"version": "v3.4.29"}, "symfony/stopwatch": {"version": "v3.4.22"}, "symfony/swiftmailer-bundle": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.5", "ref": "3db029c03e452b4a23f7fc45cec7c922c2247eb8"}}, "symfony/templating": {"version": "v3.4.22"}, "symfony/translation": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "1fb02a6e1c8f3d4232cce485c9afa868d63b115a"}}, "symfony/twig-bridge": {"version": "v3.4.22"}, "symfony/twig-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "369b5b29dc52b2c190002825ae7ec24ab6f962dd"}}, "symfony/validator": {"version": "v3.4.22"}, "symfony/var-dumper": {"version": "v3.4.22"}, "symfony/web-profiler-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}}, "symfony/yaml": {"version": "v3.4.22"}, "twig/extensions": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "ddb2e0a77773b7fd75d8d649545f174e664500ab"}}, "twig/twig": {"version": "v2.6.2"}, "whichbrowser/parser": {"version": "v2.0.37"}}