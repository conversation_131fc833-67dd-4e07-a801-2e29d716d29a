<!DOCTYPE html>
<html lang="{{ gtxConstants.DEFAULT_LANG }}">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="{% if metaDescription is defined %}{{ metaDescription }}{% endif %}">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}{% if title is defined %}{{ title|trans({}, gtxConstants.DOMAIN_CMS) }} - {% endif %}{% if gtxConstants.APP_TITLE %}{{ gtxConstants.APP_TITLE }}{% else %}{{ gtxConstants.APP_NAME }}{% endif %}{% endblock %}</title>
    <base href="{{ globalConfigs.app.baseUrl }}" />
    {% block stylesheets %}{% endblock %}
    {% block defaultStylesheets %}
        <link rel="stylesheet" href="{{ asset('bootstrap/main-bootstrap.css', 'v_asset') }}" />
        {% module 'getrix' %}
            <link rel="stylesheet" href="{{ asset('main.css', 'v_asset') }}" />
        {% else %}
            <link rel="stylesheet" href="{{ asset('main-pro.css', 'v_asset') }}" />
        {% endmodule %}
    {% endblock %}
    {% if app.environment != 'prod' and app.environment != 'demo' %}
        {% include 'debug.html.twig' %}
    {% endif %}
    {% block favicon %}
        {% include 'base/Vendor/getrix/common/snippets/favicon.html.twig' with {'faviconPath': faviconPath} %}
    {% endblock %}
    {% block headerscripts %}
        {% include 'commons/core/js-global-vars.html.twig' %}
        {% include 'base/Vendor/getrix/common/snippets/gtx-logged-user.html.twig' %}
        {% include 'base/Vendor/getrix/common/snippets/feature-toggle-configs.html.twig' %}
        {% include 'base/Vendor/getrix/common/snippets/webfont.html.twig' %}
    {% endblock %}
    {% block vendorScripts %}
        {% if gtxConstants.FULLSTORY_ENABLED %}
            {% include 'base/Vendor/getrix/common/snippets/fullstory.html.twig' %}
        {% endif %}
    {% endblock %}
</head>
<body class="app-domain-{% module 'getrix' %}getrix{% else %}{{ gtxConstants.BODY_CSS_CLASS_SUFFIX }}{% endmodule %}{% if multiAgency is not null and multiAgency.masterId is not null %} multi-agency--switch{% endif %} body-{{ site_section }}{% if site_subsection is defined %} body-{{ site_subsection }}{% endif %}">
{% block rawContent %}
 {% include 'base/Vendor/getrix/common/multi-agency.html.twig' with { backToYourAgencyUrl: "/api/login/multi-agency/switch-agency" } %}
    <div id="container" class="{% if vertical is defined %} {{ vertical }}{% endif %}{% if printMode is defined and printMode %} print-mode{% endif %}{% if globalConfigs.app.whiteLabel %} white-label{% endif %}{% if noResponsiveMode is defined and noResponsiveMode %} container-tablet{% endif %} {% if fixed_height_content is defined %} fixed-height {% endif %}">
        {% block sidemenu %}
            {% include 'commons/navigation-skeleton.html.twig' %}
        {% endblock %}
        <div id="gx-navigation"></div>
        {% block reactApp %}{% endblock %}
        {% block header %}
        {% endblock %}
        <div id="content-wrapper" class="{{ site_section }}{% if site_subsection is defined %} {{ site_subsection }}{% endif %}">
            {% block content %}{% endblock %}

            {% block footer %}
                {% if printMode is not defined %}
                    {% if gtxConstants.SALESFORCE_LIVE_CHAT_ENABLED %}
                        {% include 'base/Vendor/getrix/common/snippets/sf-chat.html.twig' %}
                    {% else %}
                        {% include 'base/Vendor/getrix/common/snippets/ask-help.html.twig' %}
                    {% endif %}
                    <div class="gtx-ah-footer-spacing"></div>
                {% endif %}
            {% endblock %}
        </div>
    </div>
{% endblock %}
<script src="{{ asset('base/jquery.js', 'v_asset') }}"></script>

{% include 'commons/core/js-translations-hydration.html.twig' %}

{% block javascripts %}
    {% include 'commons/js-gx-navigation.html.twig' %}
    {# {% include 'commons/js-helpjuice.html.twig' with {enabled: gtxConstants.HELPJUICE_ENABLED } %} #}
{% endblock %}
</body>
</html>
