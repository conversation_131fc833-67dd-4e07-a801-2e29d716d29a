//TODO: valutare se mantenerlo come componente
// gx-summary-list
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.gx-summary-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  & + & {
    padding-top: space(lg);
    border-top: 0.1rem solid color(border-main);
  }

  &__title {
    @include typography(title-2);
    margin-bottom: space(md);
    width: 100%;
  }

  &__item {
    width: calc(50% - #{$gx-unit-size});
    margin-bottom: space(md);

    &--fullWidth {
      width: 100%;
    }

    &--fullWidthMobile {
      @include media('screen', '<#{breakpoint(sm)}') {
        width: 100%;
      }
    }

    &--fullWidthTablet {
      @include media('screen', '<#{breakpoint(md)}') {
        width: 100%;
      }
    }
  }

  &__label {
    color: color(content-medium);
    @include typography(overline);
  }

  &__value {
    margin-top: space(xs);
    @include typography(body-small);
  }

  &__badges {
    margin-top: space(sm);

    .gx-tag {
      max-width: 100%;
      margin: 0 space(xs) space(xs) 0;
    }
  }

  &__location {
    width: 100%;
    margin-bottom: space(lg);
    border: 0.1rem solid color(border-main);
    border-radius: 0 0 radius(sm) radius(sm);

    &Map {
      position: relative;
      height: 19.4rem;
      overflow: hidden;

      img {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
      }
    }

    &Address {
      padding: space(sm) space(md);
      @include typography(body-small);

      div + div {
        margin-top: space(xs);
      }
    }
  }

  &--padding-top {
    padding-top: space(sm);
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    & + & {
      padding-top: space(xl);
      margin-top: space(sm);
    }

    &__title {
      margin-bottom: space(lg);
    }

    &__item {
      margin-bottom: space(lg);
    }
  }
}
