import { createOutputEventType } from './utils';

export const ADD_PROPERTY = createOutputEventType('add-property');
export const OPEN_MENU = createOutputEventType('open-menu');
export const MULTISEND_REORDER = createOutputEventType('multisend-reorder');
export const ADD_USER = createOutputEventType('add-user');
export const MARK_AS_READ_MESSAGES = createOutputEventType('messages-mark-as-read-all');
export const CUSTOMERS_OPEN_MODAL_ADD_USER = createOutputEventType('customer-open-modal-add-user');
export const BILLBOARD_SAVE_PDF = createOutputEventType('save-billboard-pdf');
export const BILLBOARD_PRINT = createOutputEventType('print-billboard');
export const IV_SCH_GX_NAVIGATION_ADD = createOutputEventType('iv_sch_add');
export const DEACTIVATE_USER = createOutputEventType('deactivate-user');
export const REMOVE_USER = createOutputEventType('remove-user');
export const ACTIVATE_USER = createOutputEventType('activate-user');
