import { trans } from '@pepita-i18n/babelfish';
import { PROPERTY_FIELDS_DATA } from 'constants/propertyFieldsData';

const sectionsData = [
    /**
     * Procedure detail
     */
    {
        name: 'procedureDetail',
        title: trans('label.procedure_detail'),
        data: [
            'auctionCourt',
            'auctionProcedureType',
            'auctionProcedureNumber',
            'auctionProcedureYear',
            'auctionProcedureProceeding',
            'auctionProcedureCode',
            'auctionInsertionType',
            'auctionRegister',
            'auctionPublicSalePortalUrl',
            'auctionProcedureRepresentative',
        ],
    },
    /**
     * Participation modality
     */
    {
        name: 'participationModality',
        title: trans('label.participation_modality'),
        data: [
            'auctionSaleLocality',
            'auctionPresentationLocality',
            'auctionPresentationEnd',
            'auctionReferent',
            'auctionBasePrice',
            'auctionMinimumPrice',
            'auctionReportValue',
            'auctionSecurityDeposit',
            'auctionExpenseAmountDeposit',
            'auctionMinimumRise',
            'auctionReservedDebitExpense',
            'auctionNotRequiredDuty',
            'auctionBidMinimumRise',
            'auctionExpenseDeposit',
            'auctionExemptionReason',
            'auctionDepositModality',
            'auctionNotes',
        ],
    },
    /**
     * Sales detail
     */
    {
        name: 'auctionSales',
        title: trans('label.sales_detail'),
        data: ['auctionSales'],
    },
    /**
     * intermediation data
     */
    {
        name: 'intermediation',
        title: trans('label.intermediation_data'),
        data: ['reference', 'agentName', 'collaboration'],
    },
    /**
     * Auction Properties
     */
    {
        name: 'auctionProperties',
        title: trans('label.auction_estates'),
        data: ['auctionProperties'],
    },
];

const useAuctionDetail = () => {
    return {
        sections: sectionsData.map((section) => ({
            ...section,
            data: section.data.map((item) => {
                return PROPERTY_FIELDS_DATA.find((field) => field.name === item);
            }),
        })),
    };
};

export default useAuctionDetail;
