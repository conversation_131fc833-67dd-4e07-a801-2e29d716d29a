# Testing guidelines

- [Testing guidelines](#testing-guidelines)
  - [What to test?](#what-to-test)
  - [Thinking about the tests](#thinking-about-the-tests)
  - [Where to put the tests?](#where-to-put-the-tests)
    - [Example](#example)
      - [The test driven approach](#the-test-driven-approach)
  - [How to write the tests effectively?](#how-to-write-the-tests-effectively)
  - [Mock http requests](#mock-http-requests)
    - [The structure of MSW](#the-structure-of-msw)
  - [Configuration](#configuration)
    - [What to put in tests/msw](#what-to-put-in-testsmsw)
    - [What to put in tests/vitest](#what-to-put-in-testsvitest)
    - [What to put in tests/react](#what-to-put-in-testsreact)
  - [How to run the tests](#how-to-run-the-tests)
  - [Dos and Don'ts](#dos-and-donts)
    - [Example](#example-1)
      - [🚩 test the implementation](#-dont)
      - [✅ test what user see](#-do)
      - [🚩 destructure `render`](#-dont-1)
      - [✅ use `screen`](#-do-1)
  - [A note about the coverage](#a-note-about-the-coverage)


## What to test?

The aim of these testing, expecially on frontend, is to ensure that ui responds in a deterministic way. The code should be tested for all the possible scenarios, including the edge cases - in a perspective of enhancing the confidence in the codebase.

Remember:

    Do not test the implementation details, but the behaviour of the application.

## Thinking about the tests

Supposing that we are testing a component, we should think about the following questions:

- What is the component doing?
- What are the possible scenarios?
- What are the possible inputs?
- What are the possible outputs?

So we can write the tests.

## Where to put the tests?

The tests should be put in the same folder of the component that we are testing. The name of the test file should be the same of the component, with the `.test.{ts|tsx|js|jsx}` extension.

### Example

Let's suppose that we have to create a component that is rendering a list of items. The component is receiving a list of items and it is rendering them in a list. This component should be clickable and should be able to select an item. After the selection, we expect to receive a feedback - positive, or negative.

#### The test driven approach

So let's imagine that we have to write a test for this component. We can start by writing the test first, and then the implementation.

```ts
// List.test.tsx // List is the name of the component we choose for this example

describe('List', () => {
  it.todo('should render a list of items');

  it.todo('should be clickable');

  it.todo('should be able to select an item');

  it.todo('should give a feedback after the selection');
});
```

With this approach we are thinking about the behaviour of the component, and we are not thinking about the implementation details first. 
Now we can start to write the implementation.

## How to write the tests effectively?

Assuming we have already an implementation, we can start to write the tests. We can start by writing the tests for the first scenario: *the component should render a list of items*.
A good start could be to write the test for the happy path, and then write the tests for the edge cases.
Assuming we want to see the list of items rendered in a list, we can write the following test:

```tsx
// List.test.tsx

describe('List', () => {
  it('should render a list of items', () => {
    const items = ['item1', 'item2', 'item3'];
    render(<List items={items} />);

    // for each item in the list, we expect to see the item rendered in the list
    items.forEach((item) => {
      const itemElement = screen.getByText(item);
      expect(itemElement).toBeInTheDocument();
    });
  });
});
```

See? We are not thinking about the implementation details, but we are thinking about the behaviour of the component. We are expecting to see the items rendered in a list. We are not thinking about the implementation details, like the fact that the items are rendered in a `ul` element, or that the items are rendered in a `li` element. We are just thinking about the behaviour of the component.

## Mock http requests

For every test in MLS, we mock the http requests. We do not want to test the backend, but we just want to test how the frontend reacts.
From the doc of MSW:

    Instead of asserting that a particular request was made, test how your application reacts to that request.

Each test have a `server` instance, that is created before each test, and that is closed after each test. This is done by using the `setupServer` function from MSW, in `tests/vitest/setup.ts`.

So each endpoint that we want to mock, should be added to the `/tests/msw/handlers/*` (and imported to `/tests/msw/handlers/index.ts`).

### The structure of MSW

To stub a request, we have to create a handler. A handler is a function that is returning a response.  
Each handler pick a stub from `tests/mock/api/*` and return it as a response.
Each stub **musts** respect the api path and the api method.
Here is an example: the stubs of the api `/api/agencies/multi-agency/members` are stored in `tests/mock/api/agencies/multi-agency/`. 
There are few name conventions that we have to respect:

```js
// example:
// /api/agencies/multi-agency/members
// GET_members_1.json

<METHOD>_<ACTION>-whatever_meaninful.json
```

The `METHOD` is the method of the request, and the `ACTION` is the action that we are doing. In short, it reflects the name of the endpoint.

## Configuration

What is inside `tests` folder and as the name suggests, is the configuration of the tests. This folder has a special alias `#tests` that is used to import some configuration.
- msw: contains the configuration of msw
- react: contains the configuration of react, like a set of handy features for react testing library
- vitest: contains the configuration of vitest
- mock: contains the configuration of the mocks, in a general way. As you can see there are not bound to specific test framework or library.

### What to put in tests/msw
- handlers: contains the handlers of the requests
- utis: related to msw (not used yet)

### What to put in tests/vitest
- setup: contains the setup of the tests
- globals: contains the globals of the tests (like general stubs, etc)
- modules: sometimes we need to mock a module, so we can put it here. It should be a rare case.

### What to put in tests/react

All utils related to react testing library, or to react in general.


## How to run the tests

To run the tests, we can run the following command:

```bash
yarn test

# or with UI

yarn test --ui

# or with UI and coverage

yarn test --ui --coverage
```

## Dos and Don'ts

It seems to be repetitive, but: do not test the implementation details, but the behaviour of the application.

Sometimes we can be tempted to test using some classes or ids, but we should avoid it. 

### Example

#### 🚩 DON'T 

```tsx

// SomeComponent.test.tsx

describe('SomeComponent', () => {
  it('should render a list of items', () => {
    const {container} = render(<SomeComponent />);

    expect(container.querySelector('.some-stuff')).toBeInTheDocument();

  });
});
```

#### ✅ DO
Supposing that we have to test the behaviour of a button inside a table: 

```tsx
    // SomeComponent.tsx
const SomeComponent = () => {
    return (
    <table>
      <tbody>
        <tr>
          <td>Some stuff</td>
          <td><button>add</button></td>
        </tr>
        <tr>
          <td>Another stuff</td>
          <td><button>add</button></td>
        </tr>
      </tbody>
    </table>
    )}
```
As you can see the button is not unique, so how can we test it? We can use the `getByRole` query, and we can use the `name` attribute to identify the row.

So we can write the following test using `within` from `react-testing-library`:

```ts
it('test the button', () => {
    render(<SomeComponent />);

    const row = getByRole('row', {name: /Another stuff/});
    const button = within(row).getByRole('button', {name: 'add'});

    expect(button).toBeInTheDocument();
});
```
What is `within`? It is a function that is returning a new container, that is scoped to the element that we are passing as argument. So we can use it to find (for example) the button inside the row.


#### ✅ DO

```tsx

// SomeComponent.test.tsx

describe('SomeComponent', () => {
  it('should render a list of items', () => {
    render(<SomeComponent />);

    expect(screen.getByText(/the-text-i-expect-to-see-just-like-user-does/)).toBeInTheDocument();
  });
});
```

Sometimes we can't use `getByText` because of some reasons, like the fact that the text is not visible, or because we are testing a component that is not rendering text. In this case we can use `getByRole` or `getByLabelText` or `getByPlaceholderText` or `getByAltText` or `getByTitle` or `getByDisplayValue` or `getByTestId` (the last one should be used as a last resort). 

#### 🚩 DON'T 

```tsx

// SomeComponent.test.tsx

describe('SomeComponent', () => {
  it('should render a list of items', () => {
    // 🚩 Don't destructuring the result of render, use screen instead
    const {getByText} = render(<SomeComponent />);

    expect(getByText(/the-text-i-expect-to-see-just-like-user-does/)).toBeInTheDocument();

  });
});

```
#### ✅ DO

```tsx

// SomeComponent.test.tsx

describe('SomeComponent', () => {
  it('should render a list of items', () => {
    render(<SomeComponent />);

    // ✅ Use screen instead
    expect(screen.getByText(/the-text-i-expect-to-see-just-like-user-does/)).toBeInTheDocument();

  });
});
```

## A note about the coverage

The coverage is not a goal, but it is a tool that can help us to understand if we are testing enough. We should not aim to have a 100% coverage, but we should aim to have a good coverage. We should aim to have a good coverage, because it means that we are testing enough, and we are confident about the codebase.
