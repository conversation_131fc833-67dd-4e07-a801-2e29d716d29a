# MLS

[![Link to gestionale-mls in Backstage | Immobiliare Labs, Component: gestionale-mls](https://backstage.pepita.io/api/badges/entity/default/component/gestionale-mls/badge/pingback "Link to gestionale-mls in Backstage | Immobiliare Labs")](https://backstage.pepita.io/catalog/default/component/gestionale-mls)
[![Entity lifecycle badge, lifecycle: production](https://backstage.pepita.io/api/badges/entity/default/component/gestionale-mls/badge/lifecycle "Entity lifecycle badge")](https://backstage.pepita.io/catalog/default/component/gestionale-mls)
[![Entity owner badge, owner: b2b-web](https://backstage.pepita.io/api/badges/entity/default/component/gestionale-mls/badge/owner "Entity owner badge")](https://backstage.pepita.io/catalog/default/component/gestionale-mls)
[![Entity docs badge, docs: gestionale-mls](https://backstage.pepita.io/api/badges/entity/default/component/gestionale-mls/badge/docs "Entity docs badge")](https://backstage.pepita.io/catalog/default/component/gestionale-mls/docs)

> Multi-tenant symfony frontend of the product gestionale

## Documentation

[Here](.backstage/docs/index.md) you can find the complete documentation.

## Issues

You found a bug or need a new feature? Please [open an issue](https://gitlab.pepita.io/getrix/mls-site/issues/new).

## Changelog

Please refer to the [changelog notes](CHANGELOG.md).

## Contributing

Please refer to the [contributing notes](CONTRIBUTING.md).
