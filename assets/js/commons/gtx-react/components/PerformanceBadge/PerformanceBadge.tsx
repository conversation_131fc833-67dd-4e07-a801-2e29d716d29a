import React, { FC, PropsWithChildren } from 'react';
import classNames from 'classnames';
/** Valid props for both kinds of badge. */
interface PerformanceBadgeBaseProps {
    /** If `true`, it will have an height of 36px and bigger text inside it. */
    large?: boolean;
    /** Makes the badge colored green/yellow/red respectively. */
    performance: 'high' | 'medium' | 'low';
    /** Custom className to be applied on the wrapper-div. */
    className?: string;
}
/** Valid props for badge-content specified only via props */
interface PerformanceBadgeWithoutChildrenProps
    extends PerformanceBadgeBaseProps {
    /** Text shown on the left, bigger and bold (optional). */
    boldContent?: string;
    /** Normal text content, required if there is no children. */
    normalContent: string;
    /** Custom content rendered inside the badge. Not allowed when using props `normalContent` and `boldContent`. */
    children?: never;
}
/** Valid props for badge-content specified only via children */
interface PerformanceBadgeWithChildrenProps
    extends PropsWithChildren<PerformanceBadgeBaseProps> {
    /** Not allowed when using children. */
    boldContent?: never;
    /** Not allowed when using children. */
    normalContent?: never;
}

export type PerformanceBadgeProps =
    | PerformanceBadgeWithoutChildrenProps
    | PerformanceBadgeWithChildrenProps;
/**
 * Modern-looking badge introduced in Performance section;
 * Can be colored in green/yellow/red indicating an high/medium/low performance;
 * Its content can be specified via props or children.
 */
export const PerformanceBadge: FC<PerformanceBadgeProps> = React.forwardRef<
    HTMLDivElement,
    PerformanceBadgeProps
>(
    (
        { large, performance, boldContent, normalContent, children, className },
        ref
    ) => (
        <div
            ref={ref}
            className={classNames(
                'performance-badge',
                {
                    'performance-badge--large': large,
                    'performance-badge--high': performance === 'high',
                    'performance-badge--medium': performance === 'medium',
                    'performance-badge--low': performance === 'low',
                },
                className
            )}
        >
            {children || (
                <span>
                    {Boolean(boldContent) && (
                        <b
                            className={classNames({
                                'performance-badge__highlight': !large,
                                'performance-badge__highlight--large': large,
                            })}
                        >
                            {boldContent}
                        </b>
                    )}
                    <span>{normalContent}</span>
                </span>
            )}
        </div>
    )
);
