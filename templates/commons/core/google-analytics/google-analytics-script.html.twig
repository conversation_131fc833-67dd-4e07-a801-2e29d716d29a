{#Google Analytics tracking Script Inclusion #}
{% if globalConfigs.google.googleAnalyticsCode is defined %}
  <script type="text/javascript">var _gaq = _gaq || [];
    var pluginUrl = "//www.google-analytics.com/plugins/ga/inpage_linkid.js";
    _gaq.push(["_setAccount", "{{ globalConfigs.google.googleAnalyticsCode }}"]);
    _gaq.push(["_setDomainName", "{{ globalConfigs.google.googleAnalyticsDomain }}"]);
    _gaq.push(["_require", "inpage_linkid", pluginUrl]);
    _gaq.push(["_addIgnoredRef", ".{{ globalConfigs.google.googleAnalyticsDomain }}"]);
    (function () {
      var ga = document.createElement("script");
      ga.type = "text/javascript";
      ga.async = true;
      ga.src = ('https:' == document.location.protocol ? 'https://' : 'http://') + 'stats.g.doubleclick.net/dc.js';
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(ga, s)
    })();
    (function (i, s, o, g, r, a, m) {
      i.GoogleAnalyticsObject = r;
      i[r] = i[r] || function () {
        (i[r].q = i[r].q || []).push(arguments)
      }, i[r].l = 1 * new Date();
      a = s.createElement(o), m = s.getElementsByTagName(o)[0];
      a.async = 1;
      a.src = g;
      m.parentNode.insertBefore(a, m)
    })(window, document, "script", "//www.google-analytics.com/analytics.js", "ga");
    ga("create", "{{ globalConfigs.google.googleAnalyticsCode }}", {cookieDomain: "{{ globalConfigs.google.googleAnalyticsCode }}" });
    ga("require", "linkid", "linkid.js");
  </script>
{% endif %}
{# End Google Analytics tracking Script Inclusion #}
