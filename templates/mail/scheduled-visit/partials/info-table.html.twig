<table class="dati-list" width="100%" bgcolor="#f5f5f5" cellpadding="0" cellspacing="0" border="0" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse: collapse;margin-top: 16px; margin-bottom: 16px;">
  <tbody>
    <tr>
      <td class="print-no-padding" style="border-collapse: collapse; padding: 20px 20px 0; line-height: 1.3">
        <span class="raleway-font" style="line-height: 1.3; font-size: 16px; color: #666666">
          <b class="raleway-rollback" style="line-height: 1.3">
              {{ trans('label.visit_details', {}, gtxConstants.DOMAIN_CMS) }}
          </b>
        </span>
      </td>
    </tr>
    <tr>
      <td class="print-no-padding" align="center" style="border-collapse: collapse; padding: 10px 20px">
        <table width="100%" cellpadding="0" cellspacing="0" border="0" style="mso-table-rspace: 0pt; border-collapse: collapse; font-family: Arial; font-size: 14px; line-height: 1.6">
          <tbody>
            <tr>
              <td style="border-collapse: collapse; padding: 10px 0; line-height: 100%; border-bottom: 1px solid #dcdcdc">
                <span style="vertical-align: top; font-size: 12px; min-width: 180px; float: left; display: inline-block; line-height: 20px; text-transform: uppercase; color: #666666">
                  {{ trans('label.day', {}, gtxConstants.DOMAIN_CMS) }}:
                </span>
                <p class="style-autolink" style="color: #666666; min-width: 180px; margin: 0; float: left; display: inline-block; line-height: 20px; max-width: 320px; text-decoration: none">
                    {{ room.scheduled_date }}
                </p>
              </td>
            </tr>
            <tr>
              <td style="border-collapse: collapse; padding: 10px 0; line-height: 100%; border-bottom: 1px solid #dcdcdc">
                <span style="vertical-align: top; font-size: 12px; min-width: 180px; float: left; display: inline-block; line-height: 20px; text-transform: uppercase; color: #666666">
                  {{ trans('label.hour', {}, gtxConstants.DOMAIN_CMS) }}:
                </span>
                <p class="style-autolink" style="color: #666666; min-width: 180px; margin: 0; float: left; display: inline-block; line-height: 20px; max-width: 320px; text-decoration: none">
                    {{ room.scheduled_time }}
                </p>
              </td>
            </tr>

            <tr>
              <td style="border-collapse: collapse; padding: 10px 0; line-height: 100%;">
                <span style="vertical-align: top; font-size: 12px; min-width: 180px; float: left; display: inline-block; line-height: 20px; text-transform: uppercase; color: #666666">
                  {{ room.guests|length }} {{ trans('label.immovisita.add_guest.guests', {}, gtxConstants.DOMAIN_CMS) }}:
                </span>
                <p class="style-autolink" style="color: #666666; min-width: 180px; margin: 0; float: left; display: inline-block; line-height: 20px; max-width: 320px; text-decoration: none">
                    {% for email in room.guests %}
                        <a rel="nofollow" style="text-decoration: none; color: #666666;">{{ email }}</a><br>
                    {% endfor %}
                </p>
              </td>
            </tr>
            {% if visit_type == 'distance' %}
              <tr>
                <td style="border-collapse: collapse; padding: 10px 0; line-height: 100%; border-top: 1px solid #dcdcdc">
                  <span style="vertical-align: top; font-size: 12px; min-width: 180px; float: left; display: inline-block; line-height: 20px; text-transform: uppercase; color: #666666">
                    {{ trans('label.to_participate', {}, gtxConstants.DOMAIN_CMS) }}:
                  </span>
                  <p style="color: #666666; min-width: 180px; margin: 0; float: left; display: inline-block; line-height: 20px; max-width: 320px; text-decoration: none">
                      {% if user_type == 'guest' %}
                          <a title=" {{ trans('label.participate', {}, gtxConstants.DOMAIN_CMS) }}" href="{{ guest.link }}" style="display: inline-block; text-decoration: none; text-align: center; text-transform: uppercase; vertical-align: middle; line-height: 1.7; background-color: rgb(193, 36, 0); border: 1px solid rgb(193, 36, 0); margin-bottom: 8px; padding: 8px 10px; font-weight: bold; font-size: 13px; color: rgb(255, 255, 255); border-top-left-radius: 2px; border-top-right-radius: 2px; border-bottom-right-radius: 2px; border-bottom-left-radius: 2px" target="_blank" rel="noreferrer">
                              {{ trans('label.participate', {}, gtxConstants.DOMAIN_CMS) }}
                          </a>
                      {% else %}
                          <a title="{{ trans('label.participate', {}, gtxConstants.DOMAIN_CMS) }}" href="{{ room.host_link }}" style="display: inline-block; text-decoration: none; text-align: center; text-transform: uppercase; vertical-align: middle; line-height: 1.7; background-color: rgb(193, 36, 0); border: 1px solid rgb(193, 36, 0); margin-bottom: 8px; padding: 8px 10px; font-weight: bold; font-size: 13px; color: rgb(255, 255, 255); border-top-left-radius: 2px; border-top-right-radius: 2px; border-bottom-right-radius: 2px; border-bottom-left-radius: 2px" target="_blank" rel="noreferrer">
                              {{ trans('label.participate', {}, gtxConstants.DOMAIN_CMS) }}
                          </a>
                      {% endif %}

                    {% if user_type == 'guest' %}
                      <a style="color: #0074c1; display: inline-block; margin-bottom: 16px; word-break: break-all" href="{{ guest.link }}">
                            {{ guest.link }}
                      </a>
                    {% else %}
                        <a style="color: #0074c1; display: inline-block; margin-bottom: 16px; word-break: break-all" href="{{ room.host_link }}">
                            {{ room.host_link }}
                        </a>
                    {% endif %}
                    <br>
                    <b>{{ trans('label.requirements_to_participate', {}, gtxConstants.DOMAIN_CMS) }}:</b>
                    <br>
                    <span>
                      {{ trans('label.requirements_to_participate_description', {}, gtxConstants.DOMAIN_CMS) }}
                    </span>
                  </p>
                </td>
              </tr>
            {% endif %}
            {% if room.note is not empty %}
            <tr>
              <td style="border-collapse: collapse; padding: 10px 0; line-height: 100%;  border-top: 1px solid #dcdcdc">
                <span style="vertical-align: top; font-size: 12px; display: inline-block; line-height: 20px; text-transform: uppercase; color: #666666">
                  {{ trans('label.additional_agent_notes', {}, gtxConstants.DOMAIN_CMS) }}:
                </span>
                <p style="color: #666666; margin: 0;  line-height: 20px; text-decoration: none">
                    {{ room.note }}
                </p>
              </td>
            </tr>
          {% endif %}
          </tbody>
        </table>
      </td>
    </tr>
  </tbody>
</table>
