### BUILD

.artifact_build:
    cache:
        policy: $CACHE_POLICY
        key: "${CACHE_KEY}-build"
        paths:
            - .yarn/
            - packages/gx-navigation/.yarn/
    before_script:
        - !reference [.artifact_php, before_script]
        - !reference [.artifact_js, before_script]
    script:
        - export SENTRY_VERSION=$CI_COMMIT_REF_NAME
        - !reference [.artifact_php, script]
        - !reference [.artifact_js, script]
    variables:
        CACHE_POLICY: pull
    rules:
        - if: $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH
          variables:
              CACHE_POLICY: push
        - when: on_success
