#!/bin/bash
set -x

printf "\n\nENV\n\n"

# POPEYE_NAMESPACE, PRODUCT and POPEYE_URL are environment variables passed by k8s
URL=${POPEYE_URL}
WEBSERVER_DIR="/etc/nginx"
ROOT_DIR="/application"

languages="it es ca en de el fr nl pl pt ro ru sl zh"

mkdir -p -m 777 ${ROOT_DIR}/var/logs/

for LANGUAGE in $languages
do
    test -f ${WEBSERVER_DIR}/sites-enabled/gestionale-${LANGUAGE}.conf || continue
    sed -i "s,server_name .*;,server_name ${LANGUAGE}-${URL};," ${WEBSERVER_DIR}/sites-enabled/gestionale-${LANGUAGE}.conf

    test -f ${ROOT_DIR}/envs/${LANGUAGE}/reviewapp.env || continue
    echo "REVIEWAPP_URL=${LANGUAGE}-${URL}" > ${ROOT_DIR}/.env.${LANGUAGE}
    cat ${ROOT_DIR}/envs/${LANGUAGE}/reviewapp.env >> ${ROOT_DIR}/.env.${LANGUAGE}

    mkdir -m 777 -p ${ROOT_DIR}/var/cache/${LANGUAGE}/
done

cd ${ROOT_DIR}
cat composer.json
curl -L https://github.com/composer/composer/releases/download/1.10.26/composer.phar -o composer.phar
chmod +x composer.phar
./composer.phar install 2>&1

curl -sL https://sentry.io/get-cli/ | sh
sentry-cli releases new ${version}
sentry-cli releases set-commits ${version} --local --ignore-missing
sentry-cli sourcemaps inject public/bundles/
sentry-cli releases files ${version} upload-sourcemaps public/bundles/
sentry-cli releases finalize ${version}

printf "\n\nStarting PHP 7.0 daemon...\n\n"
service php7.0-fpm start

printf "Starting Nginx...\n\n"
set -e

if [[ "$1" == -* ]]; then
    set -- nginx -g 'daemon off;' "$@"
fi

exec "$@"
