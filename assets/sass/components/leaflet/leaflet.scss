// stylelint-disable selector-class-pattern, selector-max-type, selector-no-qualifying-type, scss/dollar-variable-pattern, no-descending-specificity
// Variables

//Spacing
$leaflet-spacing: 1rem !default;

// Generic
$leaflet-border-radius: 0.2rem !default;
$leaflet-shadow: rgba(black, 0.2) !default;
$leaflet-box-shadow: 0 0.1rem 0.3rem $leaflet-shadow !default;
$leaflet-border-color: #777 !default;
$leaflet-background-color: #fff !default;

// Controls
$leaflet-zoom-box-border-color: #38f !default;
$leaflet-zoom-box-background-color: rgba(white, 0.5) !default;
$leaflet-control-zoom-svg-color: 666 !default;
$leaflet-control-zoom-in-svg: "%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E %3Cpath fill='%23#{$leaflet-control-zoom-svg-color}' fill-rule='evenodd' d='M10,10 L10,16 L6,16 L6,10 L-7.10542736e-15,10 L-7.10542736e-15,6 L6,6 L6,-1.0658141e-14 L10,-1.0658141e-14 L10,6 L16,6 L16,10 L10,10 Z'/%3E %3C/svg%3E" !default;
$leaflet-control-zoom-out-svg: "%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='4' viewBox='0 0 16 4'%3E %3Crect width='16' height='4' fill='%23#{$leaflet-control-zoom-svg-color}' fill-rule='evenodd'/%3E %3C/svg%3E" !default;

// Popup
$leaflet-popup-tip-box-shadow: 0 0.3rem 1.4rem $leaflet-shadow !default;
$leaflet-popup-close-background: transparent !default;
$leaflet-popup-close-color: #c3c3c3 !default;
$leaflet-popup-close-color-hover: #999 !default;
$leaflet-popup-border-color: #999 !default;
$leaflet-popup-text-color: #666 !default;

// Tooltip
$leaflet-tooltip-color: #222 !default;
$leaflet-tooltip-background: #fff !default;
$leaflet-tooltip-border-color: #fff !default;
$leaflet-tooltip-border-radius: $leaflet-border-radius !default;
$leaflet-tooltip-box-shadow: 0 0.1rem 0.3rem $leaflet-shadow !default;

// Container
$leaflet-container-background-color: rgba(255, 255, 255, 0.7) !default;

// Bar
$leaflet-bar-a-background-color: #fff !default;
$leaflet-bar-a-hover-color: #000 !default;

// Disabled
$leaflet-disabled-color: #bbb !default;
$leaflet-disabled-background-color: #f4f4f4 !default;

// DivIcon
$leaflet-divicon-border-color: #666 !default;
$leaflet-divicon-background-color: #fff !default;

.gx-leaflet {
  // Required
  .leaflet-pane,
  .leaflet-tile,
  .leaflet-marker-icon,
  .leaflet-marker-shadow,
  .leaflet-tile-container,
  .leaflet-pane > svg,
  .leaflet-pane > canvas,
  .leaflet-zoom-box,
  .leaflet-image-layer,
  .leaflet-layer {
    position: absolute;
    top: 0;
    left: 0;
  }

  .leaflet-pane,
  .leaflet-pane svg,
  .leaflet-tile,
  .leaflet-marker-icon,
  .leaflet-marker-shadow {
    outline: none;
    user-select: none;
  }

  // Marker & Overlays Interactivity
  .leaflet-marker-icon,
  .leaflet-marker-shadow,
  .leaflet-image-layer,
  .leaflet-pane > svg path,
  .leaflet-tile-container {
    pointer-events: none;
  }

  // Hack that prevents hw layers "stretching" when loading new tiles
  .leaflet-safari .leaflet-tile-container {
    width: 160rem;
    height: 160rem;
    transform-origin: 0 0;
  }

  .leaflet-marker {
    &-icon,
    &-shadow {
      display: block;
    }
  }

  .leaflet-map-pane {
    canvas {
      max-width: inherit;
      z-index: inherit;
    }
    svg {
      z-index: 200;
    }
  }

  // Workaround for https://bugzilla.mozilla.org/show_bug.cgi?id=888319
  .leaflet-overlay-pane {
    svg {
      user-select: none;
    }
  }

  .leaflet-control-scale {
    .leaflet-left & {
      margin-left: (0.5 * $leaflet-spacing);
    }

    .leaflet-bottom & {
      margin-bottom: (0.5 * $leaflet-spacing);
    }

    &-line {
      box-sizing: border-box;
      padding: (0.2 * $leaflet-spacing) (0.5 * $leaflet-spacing) (0.1 * $leaflet-spacing);
      border: 0.2rem solid #777;
      border-top: none;
      background: rgba(255, 255, 255, 0.5);
      font-size: (1.2 * $leaflet-spacing);
      line-height: 1.333;
      white-space: nowrap;
      overflow: hidden;

      &:not(:first-child) {
        margin-top: -(0.2 * $leaflet-spacing);
        border-top: (0.2 * $leaflet-spacing) solid $leaflet-border-color; // this is relative to the layout
        border-bottom: none;
      }

      &:not(:first-child):not(:last-child) {
        border-bottom: (0.2 * $leaflet-spacing) solid $leaflet-border-color;
      }
    }
  }

  .leaflet-container {
    position: relative;

    outline: 0;
    background: $leaflet-container-background-color;
    overflow: hidden;
    z-index: 0;
    -webkit-tap-highlight-color: transparent;

    // Leaflet Tiles: map is broken in FF if you have max-width: 100% on tiles
    .leaflet-overlay-pane svg,
    .leaflet-marker-pane img,
    .leaflet-shadow-pane img,
    .leaflet-tile-pane img,
    img.leaflet-image-layer {
      max-width: none !important;
      max-height: none !important;
    }

    a {
      color: #0078a8;
      -webkit-tap-highlight-color: rgba(51, 181, 229, 0.4);

      &.leaflet-active {
        outline: 0.2rem solid orange;
      }
    }

    a.leaflet-popup-close-button {
      position: absolute;
      top: 0;
      right: 0;
      width: (1.8 * $leaflet-spacing);
      height: (1.4 * $leaflet-spacing);
      padding: (0.4 * $leaflet-spacing) (0.4 * $leaflet-spacing) 0 0;
      border: none;
      background-color: $leaflet-popup-close-background;
      color: $leaflet-popup-close-color;
      font-size: (1.6 * $leaflet-spacing);
      font-weight: bold;
      line-height: 1.5;
      text-align: center;
      text-decoration: none;

      &:hover {
        color: $leaflet-popup-close-color-hover;
      }
    }

    .leaflet-control-attribution,
    .leaflet-control-scale {
      font-size: (1.1 * $leaflet-spacing);
    }

    .leaflet-control-attribution {
      padding: 0.5rem;
      background-color: rgba(255, 255, 255, 0.7);

      a {
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    &.leaflet-touch-zoom {
      touch-action: pan-x pan-y;
    }

    &.leaflet-touch-drag {
      /* Fallback for FF which doesn't support pinch-zoom */
      touch-action: none;
      touch-action: pinch-zoom;
    }

    &.leaflet-touch-drag.leaflet-touch-zoom {
      touch-action: none;
    }
  }

  .leaflet-tile {
    filter: inherit;
    visibility: hidden;

    &-loaded {
      visibility: inherit;
    }
  }

  // Depth stack
  // all the panes
  .leaflet-pane {
    z-index: 200;
  }
  // map tiles
  .leaflet-tile-pane {
    z-index: 300;
  }
  // overlay elements (like polygons)
  .leaflet-overlay-pane {
    z-index: 400;
  }
  // credits: just above normal panes but below other UI elements
  .leaflet-control-attribution {
    z-index: 450;
  }
  // Marker shadown
  .leaflet-shadow-pane {
    z-index: 500;
  }
  // Actual Marker
  .leaflet-marker-pane {
    z-index: 600;
  }
  // Tolltips
  .leaflet-tooltip-pane {
    z-index: 650;
  }
  // popups, zooming and custom controls
  .leaflet-popup-pane {
    z-index: 700;
  }
  .leaflet-zoom-box,
  .leaflet-control {
    z-index: 800;
  }

  /* Control Positioning */

  .leaflet-control {
    position: relative;
    max-width: 100%;
    border-radius: $leaflet-border-radius;
    pointer-events: auto;

    &:not(:first-child) {
      margin-top: (1.6 * $leaflet-spacing);
    }
  }

  // Cursors
  .leaflet-interactive {
    cursor: pointer;
  }

  .leaflet-grab {
    cursor: grab;
  }

  .leaflet-crosshair {
    &,
    .leaflet-interactive {
      cursor: crosshair;
    }
  }

  .leaflet-popup-pane,
  .leaflet-control {
    cursor: auto;
  }

  .leaflet-dragging {
    .leaflet-grab,
    .leaflet-grab .leaflet-interactive,
    .leaflet-marker-draggable {
      cursor: move;
      cursor: grabbing;
    }
  }

  .leaflet-marker-icon.leaflet-interactive,
  .leaflet-image-layer.leaflet-interactive,
  .leaflet-pane > svg path.leaflet-interactive {
    pointer-events: auto;
  }

  .leaflet-zoom-box {
    box-sizing: border-box;
    width: 0;
    height: 0;
    border: 0.2rem dotted;
    border-color: $leaflet-zoom-box-border-color;
    background: $leaflet-zoom-box-background-color;
  }

  // General Toolbar Styles
  .leaflet-bar {
    a {
      display: block;
      width: (3.2 * $leaflet-spacing);
      height: (3.2 * $leaflet-spacing);
      border-radius: $leaflet-border-radius;
      background-color: $leaflet-bar-a-background-color;
      color: $leaflet-bar-a-hover-color;
      line-height: inherit;
      text-align: center;
      text-decoration: none;

      &:hover {
        background-color: $leaflet-disabled-background-color;
        color: $leaflet-bar-a-hover-color;
        text-decoration: none;
      }

      &:first-child {
        border-top-left-radius: inherit;
        border-top-right-radius: inherit;
      }

      &:last-child {
        border-bottom-left-radius: inherit;
        border-bottom-right-radius: inherit;
      }

      & + a {
        border-top: 0.1rem solid $leaflet-border-color;
      }

      &.leaflet-disabled {
        background-color: $leaflet-disabled-background-color;
        color: $leaflet-disabled-color;
        cursor: default;
      }
    }
  }

  // Zoom Control
  .leaflet-control-zoom {
    padding: 0.2rem;

    &-in {
      &::before {
        background-image: url('data:image/svg+xml,#{$leaflet-control-zoom-in-svg}');
      }
    }

    &-out {
      &::before {
        background-image: url('data:image/svg+xml,#{$leaflet-control-zoom-out-svg}');
      }
    }

    &-in,
    &-out {
      position: relative;
      color: transparent !important;
      font-size: 0;
      font-weight: bold;
      text-indent: 0;

      &::before {
        position: absolute;
        top: 0;
        left: 0;
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        background-position: center;
        content: '';
      }
    }
  }

  // Layers Control
  .leaflet-control-layers {
    border-radius: $leaflet-border-radius;
    background-color: $leaflet-background-color;
    box-shadow: $leaflet-box-shadow;

    &-toggle {
      width: (3.6 * $leaflet-spacing);
      height: (3.6 * $leaflet-spacing);

      .leaflet-bar & {
        display: block;
        background-repeat: no-repeat;
        background-position: center;
      }

      .leaflet-retina & {
        background-size: (2.6 * $leaflet-spacing) (2.6 * $leaflet-spacing);
      }

      .leaflet-touch & {
        width: (4.4 * $leaflet-spacing);
        height: (4.4 * $leaflet-spacing);
      }
    }

    & &-list,
    &-expanded &-toggle {
      display: none;
    }

    &-expanded &-list {
      display: block;
      position: relative;
    }

    &-expanded {
      padding: (0.6 * $leaflet-spacing) (1 * $leaflet-spacing) (0.6 * $leaflet-spacing) (0.6 * $leaflet-spacing);
      background-color: $leaflet-background-color;
      color: #333;
    }

    &-scrollbar {
      padding-right: (0.5 * $leaflet-spacing);
      overflow-x: hidden;
      overflow-y: scroll;
    }

    &-selector {
      position: relative;
      top: (0.1 * $leaflet-spacing);
      margin-top: (0.2 * $leaflet-spacing);
    }

    label {
      display: block;
    }

    &-separator {
      height: 0;
      margin: (0.5 * $leaflet-spacing) - (1 * $leaflet-spacing) (0.5 * $leaflet-spacing) - (0.6 * $leaflet-spacing);
      border-top: 0.1rem solid border;
    }
  }

  // Attribution and Scale controls
  .leaflet-control-attribution,
  .leaflet-control-scale-line {
    padding: 0 (0.5 * $leaflet-spacing);
    color: #333;
  }

  .leaflet-touch {
    .leaflet-control-attribution,
    .leaflet-control-layers {
      box-shadow: none;
    }

    .leaflet-control-layers,
    .leaflet-bar {
      background-clip: padding-box;
    }
  }

  // Popup
  .leaflet-popup {
    position: absolute;
    margin-bottom: (2 * $leaflet-spacing);
    text-align: center;

    &-content-wrapper {
      padding: (0.1 * $leaflet-spacing);
      border-radius: (1.2 * $leaflet-spacing);
      text-align: left;
    }

    &-content {
      margin: (1.3 * $leaflet-spacing) (1.9 * $leaflet-spacing);
      line-height: 1.4;

      p {
        margin: (1.8 * $leaflet-spacing) 0;
      }
    }

    &-tip-container {
      position: absolute;
      left: 50%;
      width: (4 * $leaflet-spacing);
      height: (2 * $leaflet-spacing);
      margin-left: -(2 * $leaflet-spacing);
      pointer-events: none;
      overflow: hidden;
    }

    &-tip {
      width: (1.7 * $leaflet-spacing);
      height: (1.7 * $leaflet-spacing);
      margin: -(1 * $leaflet-spacing) auto 0;
      padding: (0.1 * $leaflet-spacing);
      transform: rotate(45deg);
    }

    &-content-wrapper,
    &-tip {
      background-color: $leaflet-background-color;
      color: #333;

      box-shadow: $leaflet-popup-tip-box-shadow;
    }

    &-scrolled {
      border-top: 0.1rem solid $leaflet-popup-border-color;
      border-bottom: 0.1rem solid $leaflet-popup-border-color;
      overflow: auto;
    }
  }

  // Div Icon
  .leaflet-div-icon {
    border: 0.1rem solid $leaflet-divicon-border-color;
    background: $leaflet-divicon-background-color;
  }

  // Tooltip
  // Base styles for the element that has a tooltip
  .leaflet-tooltip {
    position: absolute;
    padding: (0.4 * $leaflet-spacing) (0.8 * $leaflet-spacing);
    border: 0.1rem solid $leaflet-tooltip-border-color;
    border-radius: $leaflet-tooltip-border-radius;
    background-color: $leaflet-tooltip-background;
    color: $leaflet-tooltip-color;
    font-size: (1.4 * $leaflet-spacing);
    white-space: nowrap;
    pointer-events: none;
    user-select: none;
    box-shadow: $leaflet-tooltip-box-shadow;

    &.leaflet-clickable {
      cursor: pointer;
      pointer-events: auto;
    }

    &-top::before,
    &-bottom::before,
    &-left::before,
    &-right::before {
      position: absolute;
      border: (0.6 * $leaflet-spacing) solid transparent;
      background: transparent;
      pointer-events: none;
      content: '';
    }

    // Directions
    &-bottom {
      margin-top: (0.6 * $leaflet-spacing);

      &::before {
        top: 0;
        margin-top: -(1.2 * $leaflet-spacing);
        margin-left: -(0.6 * $leaflet-spacing);
        border-bottom-color: #fff;
      }
    }

    &-top {
      margin-top: -(0.6 * $leaflet-spacing);

      &::before {
        bottom: 0;
        margin-bottom: -(1.2 * $leaflet-spacing);
        border-top-color: #fff;
      }
    }

    &-bottom::before,
    &-top::before {
      left: 50%;
      margin-left: -(0.6 * $leaflet-spacing);
    }

    &-left {
      margin-left: -(0.6 * $leaflet-spacing);

      &::before {
        right: 0;
        margin-right: -(1.2 * $leaflet-spacing);
        border-left-color: #fff;
      }
    }

    &-right {
      margin-left: (0.6 * $leaflet-spacing);

      &::before {
        left: 0;
        margin-left: -(1.2 * $leaflet-spacing);
        border-right-color: #fff;
      }
    }

    &-left::before,
    &-right::before {
      top: 50%;
      margin-top: -(0.6 * $leaflet-spacing);
    }
  }

  // Zoom and fade animations
  .leaflet-fade-anim {
    .leaflet-tile {
      will-change: opacity;

      .leaflet-popup {
        transition: opacity 0.2s linear;
        opacity: 0;
      }

      .leaflet-map-pane .leaflet-popup {
        opacity: 1;
      }
    }
  }

  .leaflet-pan-anim .leaflet-tile {
    transition: none;
  }

  .leaflet-zoom-anim {
    &ated {
      transform-origin: 0 0;
    }

    .leaflet-zoom-animated {
      transition: transform 0.25s cubic-bezier(0, 0, 0.25, 1);
      will-change: transform;

      .leaflet-tile {
        transition: none;
      }

      .leaflet-zoom-hide {
        visibility: hidden;
      }
    }
  }

  // Controllers
  .leaflet-control-container {
    display: flex;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    flex-direction: column;
    height: 100%;
    padding: 1rem;
    pointer-events: none;

    .leaflet-top {
      position: absolute;
      top: 0;
      margin: 0;
    }

    .leaflet-right {
      position: absolute;
      right: 0;
      margin: 0;
    }

    .leaflet-bottom {
      position: absolute;
      bottom: 0;
      margin: 0;
    }

    .leaflet-left {
      position: absolute;
      margin: 0;
      border-left-width: 0;
    }
  }

  .leaflet-control-row {
    display: flex;
    flex: 1 1 50%;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 100%;

    &:first-child {
      align-content: flex-start;
      align-items: flex-start;
    }

    &:last-child {
      align-content: flex-end;
      align-items: flex-end;
    }
  }

  .leaflet-top,
  .leaflet-right,
  .leaflet-bottom,
  .leaflet-left,
  .leaflet-center {
    max-width: 100%;
    pointer-events: auto;
  }
}
