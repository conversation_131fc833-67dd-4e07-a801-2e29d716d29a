import { Icon } from '@gx-design/icon';
import { useMediaMatch } from '@gx-design/use-media-match';
import clsx from 'clsx';
import React, {
  memo,
  PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { createPortal } from 'react-dom';
import { AgencyConsumer } from '../shared/components/AgencyContext';
import { useEnsureGxNavigationContext } from '../shared/components/GxNavigationContext';
import {
  useGxNavigationMenuContext,
  useGxNavigationMenuDispatcher,
} from '../shared/components/GxNavigationMenuContext';
import { useSecondaryMenuItemByPathname } from '../shared/hooks/menu';
import { trans } from '../shared/translations';
import { mapMenuConfig } from '../shared/utils';
import { MenuConfig } from '../types';
import { FeaturesProviders } from './FeaturesProviders';
import { MenuItem } from './components/MenuItem';
import { MenuProfile } from './components/MenuProfile';
import { MenuSecondaryMenu } from './components/MenuSecondaryMenu';
import { useActivePath } from './useActivePath';
import {
  isMenuItem,
  isMenuItemDefinedOrCustomMenuComponent,
  isSubmenuHidden,
} from '../shared/utils/type-guards';
import { useGxNavigationFeatureFlagsContext } from '../shared/components/GxNavigationFeatureFlagsContext';

export const Menu = () => {
  const { primaryMenu: mainMenuConfig, secondaryMenu: secondaryMenuConfig } =
    useEnsureGxNavigationContext();
  const [main1stLevel] = useActivePath(mainMenuConfig);
  const [secondary1stLevel] = useActivePath(secondaryMenuConfig);
  const { saleforceLiveChatEnabled } = useGxNavigationFeatureFlagsContext();
  const { openMenu, openSideMenu, openProfile, currentSecondaryMenu } =
    useGxNavigationMenuContext();
  const { dispatch } = useGxNavigationMenuDispatcher();

  const secondaryMenuRef = useRef<HTMLUListElement>(null);

  const delay = useRef(0);

  const isDesktop = useMediaMatch('largeDesktop');

  const resetDelay = useCallback(() => {
    window.clearTimeout(delay.current);
  }, []);

  const onDelayedClose = useCallback(() => {
    window.clearTimeout(delay.current);
    delay.current = window.setTimeout(() => {
      dispatch({ type: 'CLOSE_SIDE_MENU' });
    }, 350);
  }, [dispatch]);

  const onOpenProfile = () => {
    if (isDesktop) {
      dispatch({ type: 'OPEN_PROFILE' });
    } else {
      dispatch({ type: 'OPEN_PROFILE_AS_SIDE_MENU' });
    }
  };

  const handleCustomerCare = () => {
    resetDelay();
    if (delay.current) {
      window.clearTimeout(delay.current);
    }

    delay.current = window.setTimeout(() => {
      if (!isDesktop) {
        dispatch({ type: 'OPEN_CUSTOMER_CARE_AS_SIDE_MENU' });
      }
    }, 350);
  };

  const onToggleSecondaryMenu = useCallback(
    (menuItem: MenuConfig) => {
      window.clearTimeout(delay.current);

      const handleMenu = () => {
        if (menuItem.subMenu && !isSubmenuHidden(menuItem.subMenu)) {
          dispatch({
            type: 'SET_CURRENT_SECONDARY_MENU',
            payload: {
              item: menuItem.item,
              subMenu: menuItem.subMenu,
            },
          });
        } else {
          onDelayedClose();
        }
      };

      if (isDesktop) {
        delay.current = window.setTimeout(handleMenu, 350);
      } else {
        handleMenu();
      }
    },
    [isDesktop, dispatch, onDelayedClose],
  );

  return (
    <FeaturesProviders>
      <div className='gx-navigation-sidemenu'>
        {openMenu && <Backdrop />}
        <div className={`gx-navigation-sidemenu__rail ${openMenu ? 'is--open' : ''}`}>
          <div className='gx-navigation-sidemenu__railContent'>
            {/* Primary Menu */}
            <ScrollableMenu className='gx-navigation-sidemenu__mainItems'>
              <ul>
                {mapMenuConfig(mainMenuConfig, ([key, menuItem]) => {
                  return (
                    <MenuItem
                      isActive={main1stLevel === key}
                      showLabel={true}
                      key={key}
                      isSubmenuOpen={
                        isMenuItem(currentSecondaryMenu) &&
                        currentSecondaryMenu.item?.label === menuItem.item?.label &&
                        openSideMenu
                      }
                      path={key}
                      menuItem={menuItem}
                      handleSideMenuOpening={onToggleSecondaryMenu}
                      resetDelay={resetDelay}
                    />
                  );
                })}
              </ul>
            </ScrollableMenu>
            {/* Secondary Menu */}
            <ul ref={secondaryMenuRef} className='gx-navigation-sidemenu__secondaryItems'>
              <li className='gx-is-hidden-md-up'>
                <a className='gx-navigation-sidemenu__item' id='new-menu-link-news'>
                  <div>
                    <div className='gx-navigation-sidemenu__itemIcon'>
                      <Icon name='bell' />
                    </div>
                    <span className='gx-is-hidden-md-up'>{trans('label.notifications')}</span>
                  </div>
                </a>
              </li>
              {saleforceLiveChatEnabled && (
                <li
                  className='gx-is-hidden-md-up'
                  onClick={!isDesktop ? handleCustomerCare : undefined}
                >
                  <a className='gx-navigation-sidemenu__item' id='new-menu-customer-care'>
                    <div>
                      <div className='gx-navigation-sidemenu__itemIcon'>
                        <Icon name='question-mark-circle' />
                      </div>
                      <span className='gx-is-hidden-md-up'>{trans('label.customer_service')}</span>
                    </div>
                    <Icon
                      className='gx-is-hidden-md-up gx-navigation-sidemenu__itemMore'
                      name='arrow-right'
                    />
                  </a>
                </li>
              )}
              {mapMenuConfig(secondaryMenuConfig, ([key, menuItem]) => {
                return (
                  <MenuItem
                    isActive={secondary1stLevel === key}
                    showLabel={false}
                    key={key}
                    isSubmenuOpen={
                      isMenuItem(currentSecondaryMenu) &&
                      currentSecondaryMenu.item?.label === menuItem.item?.label &&
                      openSideMenu
                    }
                    path={key}
                    menuItem={menuItem}
                    handleSideMenuOpening={onToggleSecondaryMenu}
                    resetDelay={resetDelay}
                  />
                );
              })}
              <>
                <li
                  onMouseEnter={isDesktop ? onOpenProfile : undefined}
                  onClick={!isDesktop ? onOpenProfile : undefined}
                  className='gx-navigation-sidemenu__item gx-navigation-sidemenu__item--profile'
                >
                  <div>
                    <div className='gx-navigation-sidemenu__itemIcon'>
                      <AgencyConsumer>
                        {({ agentAvatar }) =>
                          agentAvatar ? (
                            <img className='gx-navigation-profile__image' src={agentAvatar} />
                          ) : (
                            <Icon name='user-round--active' />
                          )
                        }
                      </AgencyConsumer>
                    </div>
                    <span className='gx-is-hidden-md-up'>{trans('label.profile')}</span>
                  </div>
                  <Icon
                    className='gx-is-hidden-md-up gx-navigation-sidemenu__itemMore'
                    name='arrow-right'
                  />
                </li>
                {openProfile &&
                  isDesktop &&
                  createPortal(
                    <div onMouseLeave={() => dispatch({ type: 'CLOSE_PROFILE' })}>
                      <MenuProfile />
                    </div>,
                    document.body,
                  )}
              </>
            </ul>
          </div>
        </div>

        {/* Secondary Menu */}
        <SecondaryMenu onClose={onDelayedClose} />
      </div>
    </FeaturesProviders>
  );
};

type SecondaryMenuProps = {
  onClose: () => void;
};
const SecondaryMenu = (props: SecondaryMenuProps) => {
  const { openSideMenu, currentSecondaryMenu } = useGxNavigationMenuContext();
  const { secondaryMenu: secondaryMenuConfig, primaryMenu: mainMenuConfig } =
    useEnsureGxNavigationContext();
  const [, main2ndLevel, main3rdLevel] = useActivePath(mainMenuConfig);
  const [, secondary2ndLevel] = useActivePath(secondaryMenuConfig);

  const secondaryMenuByPathname = useSecondaryMenuItemByPathname();
  const isSettingsMenu = useMemo(
    () =>
      isMenuItem(currentSecondaryMenu) &&
      secondaryMenuConfig['#impostazioni']?.item === currentSecondaryMenu?.item,
    [currentSecondaryMenu, secondaryMenuConfig],
  );

  return (
    <div
      onMouseLeave={props.onClose}
      className={clsx('gx-navigation-sidemenu__menu', {
        'is--open': openSideMenu,
        'gx-navigation-sidemenu__menu--secondary': isSettingsMenu,
      })}
    >
      <MenuSecondaryMenu
        item={
          isMenuItemDefinedOrCustomMenuComponent(currentSecondaryMenu)
            ? currentSecondaryMenu // use the current active menu item
            : secondaryMenuByPathname // fallback to the default active menu item
        }
        activePath={main3rdLevel || main2ndLevel || secondary2ndLevel}
      />
    </div>
  );
};

const ScrollableMenu = memo(function MemoScrollablemenu(
  props: PropsWithChildren<{
    className?: string;
  }>,
) {
  const isDesktop = useMediaMatch('largeDesktop');
  const [scrollability, setScrollability] = useState<'not-scrollable' | 'scrollable'>(
    'not-scrollable',
  );

  const [scrollStatus, setScrollStatus] = useState<'start-scroll' | 'end-scroll' | 'undetermined'>(
    'start-scroll',
  );

  const checkScrollable = useCallback((element: HTMLElement | null) => {
    if (!element) {
      return false;
    }
    return element.scrollHeight > element.clientHeight;
  }, []);

  useEffect(() => {
    const menuElement = document.querySelector(`.${props.className}`);
    if (isDesktop && menuElement && menuElement instanceof HTMLElement) {
      const isScrollable = checkScrollable(menuElement);
      setScrollability(isScrollable ? 'scrollable' : 'not-scrollable');
    }
  }, [isDesktop, props.children, checkScrollable, props.className]);

  const handleScrollOrWheel = ({ currentTarget }: React.WheelEvent<HTMLDivElement>) => {
    requestAnimationFrame(() => {
      if (isDesktop) {
        const isScrollable = checkScrollable(currentTarget);
        const scrolledToEnd =
          currentTarget.scrollTop + currentTarget.clientHeight === currentTarget.scrollHeight ||
          currentTarget.scrollTop + currentTarget.clientHeight === currentTarget.scrollHeight + 1; // workaround for Firefox
        const scrolledToStart = currentTarget.scrollTop === 0;

        if (!isScrollable) {
          setScrollability('not-scrollable');
          setScrollStatus('undetermined');
        } else {
          setScrollability('scrollable');
          if (scrolledToEnd) {
            setScrollStatus('end-scroll');
          } else if (scrolledToStart) {
            setScrollStatus('start-scroll');
          } else {
            setScrollStatus('undetermined');
          }
        }
      }
    });
  };

  return (
    <div
      className={clsx(
        {
          'not-scrollable': scrollability === 'not-scrollable',
          'start-scroll': scrollStatus === 'start-scroll',
          'end-scroll': scrollStatus === 'end-scroll',
        },
        props.className,
      )}
      onWheel={handleScrollOrWheel}
    >
      {props.children}
    </div>
  );
});

function Backdrop(props: PropsWithChildren) {
  const { openMenu } = useGxNavigationMenuContext();
  const { dispatch } = useGxNavigationMenuDispatcher();

  const onClose = () => {
    if (openMenu) {
      dispatch({ type: 'CLOSE_MENU' });
    }
  };

  return (
    <div
      id='js-gx-navigation-sidemenu__backdrop'
      className='gx-navigation-sidemenu__backdrop'
      onClick={onClose}
    >
      {props.children}
    </div>
  );
}
