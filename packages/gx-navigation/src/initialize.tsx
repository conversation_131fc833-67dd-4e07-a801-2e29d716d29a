import { createRoot } from 'react-dom/client';
import { createConfig as baseHeaderConfig } from './config/header';
import { getHeaderConfiguration } from './shared/utils/getHeaderConfig';
import { GxNavigation } from './GxNavigation';
import { fetchMenuConfig } from './shared/api/menu';
import { BaseNavigationContextType } from './config/types';
import { MenuConfig } from './types';
import { estatsMetrics } from 'estats';
import { sendMetricRequest, ESTATS_TYPE } from '@pepita/estats';

export async function initialize(props: {
  context: Omit<BaseNavigationContextType, 'primaryMenu' | 'secondaryMenu'>;
}) {
  window.GxNavigation.isDisabled = false;

  const config = baseHeaderConfig({
    adPortal: props.context.adPortal,
    serviceRemoteVisit: props.context.serviceRemoteVisit,
    featureFlags: props.context.featureFlags,
  });

  const currentConfig = getHeaderConfiguration(config, new URL(window.location.href));

  if (!currentConfig) {
    window.GxNavigation.isDisabled = true;
    console.warn('GxNavigation: Cannot mount GxNavigation app, no route found for current path');
    return;
  }

  const renderGxNavigation = (primary?: MenuConfig, secondary?: MenuConfig) => {
    const targetElement = document.getElementById('gx-navigation');
    if (!targetElement) {
      console.warn(
        'GxNavigation: Cannot mount GxNavigation app, div with id gx-navigation missing in DOM',
      );
      return;
    }

    const gxNavigationProps = {
      ...props.context,
      ...(primary && { primaryMenu: primary }),
      ...(secondary && { secondaryMenu: secondary }),
    };

    const root = createRoot(targetElement);

    root.render(<GxNavigation context={gxNavigationProps} config={currentConfig} />);
  };

  try {
    const { primary, secondary } = await fetchMenuConfig(
      {
        cacheToken: props.context.menuCacheToken,
        lang: props.context.agencyLanguage,
      },
      3,
      2000,
    );
    renderGxNavigation(primary, secondary);
  } catch (e) {
    renderGxNavigation();
    const metric = estatsMetrics({
      site: props.context.metricsSite,
      sitePrefix: props.context.metricsSitePrefix,
      url: props.context.metricsUrl,
    });
    sendMetricRequest(metric['menu.fail'], ESTATS_TYPE.singleCounter, 1);
    console.error(
      'GxNavigation: Menu config is not available, GxNavigation app will not be mounted',
    );
  }
}
