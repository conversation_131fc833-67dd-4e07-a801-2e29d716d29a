import { describe, expect, it } from 'vitest';
import { RankingDetailModal } from './RankingDetailModal';
import { render, screen } from '#tests/react/testing-library-enhanced';
import { createQueryClient } from 'lib/queryClient';
import { NotifyProvider } from '@gx-design/snackbar';
import { QueryClientProvider } from '@tanstack/react-query';
import { server } from '#tests/vitest/setup';
import { http, HttpResponse } from 'msw';

const renderRankingDetail = (propertyId) => {
    return render(<RankingDetailModal propertyId={propertyId} />, {
        wrapper: ({ children }) => {
            const queryClient = createQueryClient();
            return (
                <NotifyProvider>
                    <QueryClientProvider client={queryClient}>
                        {children}
                    </QueryClientProvider>
                </NotifyProvider>
            );
        },
    });
};

describe('RankingDetailModal', () => {
    it('should render without crashing', async () => {
        const propertyId = '12345';
        renderRankingDetail(propertyId);

        const modalContent = await screen.findByTestId('ranking-detail-modal');

        expect(modalContent).toBeInTheDocument();
    });

    it('should show error alert', async () => {
        server.use(
            http.get('/v2/lista-annunci/get-ranking-detail', () =>
                HttpResponse.error()
            )
        );

        const propertyId = '12345';
        renderRankingDetail(propertyId);

        const errorMessage = await screen.findByText(
            'label.error_durin_operation'
        );
        expect(errorMessage).toBeInTheDocument();
    });

    it('should show the suggestions tab with no suggestions', async () => {
        const propertyId = '12345';
        renderRankingDetail(propertyId);

        const suggestionsTab = await screen.findByText('label.suggestions');

        expect(suggestionsTab).toBeInTheDocument();

        const noSuggestionsMessage = await screen.findByText(
            'ranking.no_suggestion'
        );

        expect(noSuggestionsMessage).toBeInTheDocument();
    });
    it('should show the scores when clicking on the score tab', async () => {
        const propertyId = '12345';
        const { user } = renderRankingDetail(propertyId);
        const scoresTab = await screen.findByText(/label.score/);
        expect(scoresTab).toBeInTheDocument();

        // Use user.click() which internally uses act()
        await user.click(scoresTab);

        const insufficentInfo = await screen.findByText(
            /label.info_insufficient/
        );
        const improvableInfo = await screen.findByText(/label.info_improvable/);
        const sufficientInfo = await screen.findByText(/label.info_sufficient/);

        expect(insufficentInfo).toBeInTheDocument();
        expect(improvableInfo).toBeInTheDocument();
        expect(sufficientInfo).toBeInTheDocument();
    });
    it('should show the comparison tab when clicking on the compare tab', async () => {
        const propertyId = '12345';
        const { user } = renderRankingDetail(propertyId);
        const comparisonTab = await screen.findByText(/label.compare/);
        expect(comparisonTab).toBeInTheDocument();

        // Use user.click() which internally uses act()
        await user.click(comparisonTab);

        const comparisonAlertId =
            await screen.findByText(/ranking.insert_ad_is/);

        expect(comparisonAlertId).toBeInTheDocument();
    });
    it('should show the comparison data when clicking on the compare button', async () => {
        const propertyId = '12345';
        const { user } = renderRankingDetail(propertyId);
        const comparisonTab = await screen.findByText(/label.compare/);
        expect(comparisonTab).toBeInTheDocument();

        await user.click(comparisonTab);

        const compareButton = await screen.findByText(/label.compare/, {
            selector: 'button',
        });
        expect(compareButton).toBeInTheDocument();
        expect(compareButton).toBeDisabled();

        const currentPropertyIdCompareInput =
            await screen.findByDisplayValue(propertyId);

        expect(currentPropertyIdCompareInput).toBeInTheDocument();
        expect(currentPropertyIdCompareInput).toHaveValue(propertyId);

        const comparisonInput =
            await screen.findByPlaceholderText(/label.compare_with/);
        expect(comparisonInput).toBeInTheDocument();
        expect(comparisonInput).toHaveValue('');
        expect(comparisonInput).toBeEnabled();

        await user.type(comparisonInput, '67890');

        expect(comparisonInput).toHaveValue('67890');
        expect(compareButton).toBeEnabled();

        await user.click(compareButton);

        const comparisonData = await screen.findByText('Mappa');

        expect(comparisonData).toBeInTheDocument();
    });
    it('should show error alert when comparison fails', async () => {
        server.use(
            http.get('/v2/lista-annunci/get-ranking-detail-comparison', () =>
                HttpResponse.json({
                    success: false,
                })
            )
        );

        const propertyId = '12345';
        const { user } = renderRankingDetail(propertyId);
        const comparisonTab = await screen.findByText(/label.compare/);
        expect(comparisonTab).toBeInTheDocument();

        await user.click(comparisonTab);

        const compareButton = await screen.findByText(/label.compare/, {
            selector: 'button',
        });
        expect(compareButton).toBeInTheDocument();
        expect(compareButton).toBeDisabled();

        const currentPropertyIdCompareInput =
            await screen.findByDisplayValue(propertyId);

        expect(currentPropertyIdCompareInput).toBeInTheDocument();
        expect(currentPropertyIdCompareInput).toHaveValue(propertyId);

        const comparisonInput =
            await screen.findByPlaceholderText(/label.compare_with/);
        expect(comparisonInput).toBeInTheDocument();
        expect(comparisonInput).toHaveValue('');
        expect(comparisonInput).toBeEnabled();

        await user.type(comparisonInput, '67890');

        expect(comparisonInput).toHaveValue('67890');
        expect(compareButton).toBeEnabled();

        await user.click(compareButton);

        const errorMessage = await screen.findByText(
            'label.error_durin_operation'
        );

        expect(errorMessage).toBeInTheDocument();
    });
});
