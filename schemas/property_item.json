{"$id": "http://gestionale.immobiliare.it/property_item.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "Property Item", "type": "object", "properties": {"id": {"type": "integer"}, "code": {"type": ["string", "null"]}, "favourite": {"type": ["boolean"]}, "category": {"type": "string"}, "categoryId": {"type": "integer"}, "contract": {"type": "string"}, "contractId": {"type": "integer"}, "mainImageThumbUrl": {"type": "string"}, "images": {"type": ["array", "null"], "items": {"$ref": "models/image_refs.json"}}, "externalMedia": {"$ref": "models/external_media.json"}, "extraVisibilities": {"$ref": "models/extra_visibilities.json"}, "performanceInvalidityReason": {"type": ["object", "null"], "oneOf": [{"type": "null"}, {"$ref": "models/performance_invalidity_reason.json"}]}, "performanceRelativeIndex": {"type": ["object", "null"], "oneOf": [{"type": "null"}, {"$ref": "models/performance_relative_index.json"}]}, "performanceRelativeIndexMissingDays": {"type": ["integer", "null"]}, "price": {"type": ["string", "null"]}, "priceByRequest": {"type": ["boolean", "null"]}, "properties": {"type": "array", "items": {"$ref": "models/property.json"}}, "ranking": {"type": "integer"}, "statusId": {"type": "integer"}, "subTypology": {"type": "string"}, "subTypologyId": {"type": "integer"}, "type": {"enum": ["auction", "property", "new_construction"]}, "typology": {"type": "string"}, "typologyId": {"type": "integer"}, "usersStats": {"$ref": "models/user_stats.json"}, "created": {"type": "string", "pattern": "^\\d{1,2}/\\d{2}/\\d{4}$"}, "modified": {"type": "string", "pattern": "^\\d{1,2}/\\d{2}/\\d{4}$"}}, "required": ["id", "statusId", "contract", "contractId", "category", "categoryId", "extraVisibilities", "favourite", "mainImageUrl", "mainImageThumbUrl", "price", "properties", "type", "usersStats", "created", "modified"], "additionalProperties": true}