import { Button, ButtonProps } from '@gx-design/button';
import { Icon, IconProps } from '@gx-design/icon';
import { useMediaMatch } from '@gx-design/use-media-match';
import { trans } from '../shared/translations';
import { createCustomEvent, dispatchEvent } from '../events/utils';
import { VisibilityType } from './types';
import clsx from 'clsx';

type BaseActionProps = {
  label: string;
  iconOnly?: boolean;
  icon?: IconProps['name'];
  variant?: ButtonProps['variant'];
  visibility?: VisibilityType;
  className?: string;
  dataset?: Record<`data-${string}`, string>;
};

export type DefaultActionProps =
  | (BaseActionProps & {
      url: string;
    })
  | (BaseActionProps & {
      event: string;
    });

export const DefaultAction = (props: DefaultActionProps) => {
  const isEvent = 'event' in props;
  const visibilityClasess = props.visibility
    ? `gx-is-hidden${props.visibility === 'hidden' ? '' : '-'}${props.visibility}`
    : undefined;

  const isSmall = useMediaMatch('small-screen');
  const largeDesktop = useMediaMatch('largeDesktop');

  const onClick = () => {
    if (isEvent) {
      props.event ? dispatchEvent(createCustomEvent(props.event)) : undefined;
    }
  };

  return (
    <Button
      {...props.dataset}
      iconOnly={props.iconOnly || (isSmall && !!props.icon)}
      size={props.iconOnly || (isSmall && !!props.icon) || !largeDesktop ? 'small' : undefined}
      className={clsx(visibilityClasess, props.className)}
      onClick={onClick}
      as={isEvent ? undefined : 'a'}
      href={isEvent ? undefined : props.url}
      variant={props.variant}
    >
      {props.icon && <Icon name={props.icon} />}
      {props.iconOnly || (isSmall && props.icon) ? null : <span>{trans(props.label)}</span>}
    </Button>
  );
};
