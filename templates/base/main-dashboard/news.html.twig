{% extends 'base.html.twig' %}

{% block headerscripts %}
    {{ parent() }}
    {% include 'commons/core/hotjar/visual-analytics-script.html.twig' %}
{% endblock %}

{% block rawContent %}
   {% if globalConfigs.app.beamer_news == 0 %}
	{% feature 'news' %}
	{% include 'dashboard/partials/news.html.twig' %}
	{% endfeature %}

	{% include 'dashboard/partials/news-popup.html.twig' with {'newsPopup': data.newsPopup} %}
{% endif %}
{% endblock %}

{% block javascripts %}
    {{ webpack_asset('dashboard/main') }}
{% endblock %}

{% block onboarding_js %}
{% endblock %}
