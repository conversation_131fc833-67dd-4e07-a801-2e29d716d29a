import React, { FunctionComponent, useRef, useEffect } from 'react';
import { useLeafletMap } from '../hooks/useLeafletMap';
import { useLeafletMapMarker } from '../hooks/useLeafletMapMarker';
import { useLeafletMapAlert, AlertConfig } from '../hooks/useLeafletMapAlert';
import { useLeafletMapMarkerListener } from '../hooks/useLeafletMapMarkerListener';
import { useLeafletMapListener } from 'gtx-react/hooks/useLeafletMapListener';

export type PropertyMapProps = {
    config;
    markerConfig;
    alertConfig: AlertConfig;
};

export const PropertyMap: FunctionComponent<PropertyMapProps> = ({
    config,
    markerConfig,
    alertConfig,
}) => {
    const mapEl = useRef<HTMLDivElement | null>(null);
    const map = useLeafletMap(mapEl, config);
    const marker = useLeafletMapMarker(map, config.center, markerConfig);

    const handleMarkerDragEnd = (e: any) => {
        if (!markerConfig.draggable || !markerConfig.onDragEnd) {
            return;
        }

        const latLng = e.target.getLatLng();
        markerConfig.onDragEnd(latLng);
        map.current.panTo(latLng);
    };

    const handleZoomEnd = (e: any) => {
        if (!config.zoomControl || !config.onZoomEnd) {
            return;
        }

        const zoom = e.target.getZoom();
        config.onZoomEnd(zoom);
        return zoom;
    };

    useLeafletMapListener(map, 'zoomend', handleZoomEnd);
    useLeafletMapMarkerListener(marker, 'dragend', handleMarkerDragEnd);
    useLeafletMapAlert(map, alertConfig);

    /**
     * added to get the initial map zoom (it could be different for different locations).
     */
    useEffect(() => {
        const zoom = map.current?.getZoom();
        if (config.onZoomEnd && typeof zoom === 'number') {
            config.onZoomEnd(zoom);
        }
    }, []);

    return (
        <div className="gx-leaflet">
            <div
                className={config?.mapClassName || 'gx-property-map'}
                ref={mapEl}
            ></div>
        </div>
    );
};
