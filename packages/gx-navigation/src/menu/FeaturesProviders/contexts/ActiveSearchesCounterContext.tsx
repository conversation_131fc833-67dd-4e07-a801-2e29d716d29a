import { getSmartCounterString } from '../../../shared/utils/getSmartCounterString';
import { BaseMenuConfig } from '../../../types';
import { AdapterFC, AdapterHook } from '../types';
import { useCountersContext } from './CountersContext';

export const useActiveSearchesCounterAdapter: AdapterHook = (menu: BaseMenuConfig) => {
  const counters = useCountersContext();
  const counter = counters.newAgencyActiveSearches || 0;
  const shouldShowCounter = counter > 0;

  if (!shouldShowCounter) {
    return menu;
  }

  const number = getSmartCounterString(counter);

  return {
    ...menu,
    item: {
      ...menu.item,
      notificationBadge: {
        number,
      },
    },
  };
};

export const ActiveSearchesCounterAdapter: AdapterFC = (props) => {
  const remappedMenu = useActiveSearchesCounterAdapter(props.menu, props.level);

  return props.children(remappedMenu);
};
