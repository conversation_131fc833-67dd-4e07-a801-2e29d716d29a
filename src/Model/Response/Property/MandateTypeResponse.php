<?php

declare(strict_types=1);

namespace App\Model\Response\Property;

class MandateTypeResponse
{
    public string $id;
    public ?string $name = null;

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }
}
