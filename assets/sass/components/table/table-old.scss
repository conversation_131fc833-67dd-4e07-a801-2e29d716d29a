// ==========================================================================
// Table - Components/Table
// ==========================================================================
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$gx-table: 'gx-table';
$gx-table-border: color(border-main);
$gx-table-head-bg: color(background-alt);

.#{$gx-table} {
  width: 100%;

  &__head {
    background: $gx-table-head-bg;
    border-bottom: 0.1rem solid $gx-table-border;

    .#{$gx-table}__field {
      text-transform: uppercase;
      @include typography(body-tiny);
      color: color(content-medium);
    }
  }

  &__row {
    padding: space(md);

    + .#{$gx-table}__row {
      border-top: 0.1rem solid $gx-table-border;
    }
  }

  &__field {
    padding: space(md) space(sm);

    &:first-child {
      padding-left: space(xl);
    }

    &:last-child {
      padding-right: space(xl);
    }

    &--checkbox {
      width: 3.5rem;
      padding-right: space(xl);
      box-sizing: content-box;

      .#{$gx-table}__cell {
        justify-content: center;
      }

      .gx-checkbox__control {
        margin: 0;
      }
    }

    &--preferred {
      color: color(content-action);
      width: 3.5rem;
      padding-right: space(xl);
      box-sizing: content-box;

      .#{$gx-table}__cell {
        justify-content: center;
      }

      svg {
        cursor: pointer;
        @include icon-size(sm);
      }
    }

    &--lock {
      width: 3.5rem;
      padding-right: space(xl);
      box-sizing: content-box;
      color: color(content-low);

      .#{$gx-table}__cell {
        justify-content: center;
      }

      .#{$gx-table}__cell--interactive {
        svg:first-child {
          @include typography(body-small);
        }
      }

      svg {
        cursor: pointer;
        @include icon-size(sm);
      }

      &Active {
        color: color(content-action);
      }

      &Disabled {
        opacity: 0.3;

        svg {
          cursor: not-allowed;
        }
      }
    }

    &--actions {
      // TODO: this should be dynamic based on actions width
      width: 10.5rem;
      min-width: 10.5rem;
      box-sizing: content-box;

      .gx-table__cell {
        justify-content: flex-end;
      }
    }
  }

  &__body {
    background: color(background-main);
  }

  &__cell {
    @include typography(body-tiny);
    display: flex;
    align-items: center;

    &--center {
      justify-content: center;
    }

    // TODO: find another way
    .gx-list {
      @include typography(body-tiny);
    }

    &--withIcon {
      svg {
        font-size: 1.2rem;
        margin-left: space(xs);
      }
    }

    &--interactive {
      color: color(content-action);
      cursor: pointer;
    }
  }
}
