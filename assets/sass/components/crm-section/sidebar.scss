@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$crm-sidebar: 'crm-sidebar';
$crm-sidebar-width: 28rem;

.#{$crm-sidebar} {
  @include z-index(base);
  position: relative;
  display: flex;
  flex-direction: column;
  width: $crm-sidebar-width;
  height: 100%;
  background-color: color(background-main);
  transition: transform 0.3s ease-in-out;

  &__scrollable {
    display: flex;
    flex-direction: column;
    max-height: 100%;
  }

  &__header {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    gap: space(sm);
    height: 5.6rem;
    padding: 0 space(md);
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: space(md);
    padding: 0 space(md) space(md);
    overflow-y: auto;
  }

  &__openButton {
    transition: all 0.3s ease-in-out;

    &.is-closed {
      opacity: 1;
    }

    & {
      &.is-open {
        opacity: 0;
      }

      &--left.is-open {
        margin-left: -4rem;
      }

      &--right.is-open {
        margin-right: -4rem;
      }
    }
  }

  &__closeButton {
    position: absolute;
    top: 1.2rem;
    transition: all 0.3s ease-in-out;

    .is-open & {
      opacity: 1;
    }

    .#{$crm-sidebar}--left.is-closed & {
      right: -5.6rem;
    }

    .is-closed & {
      opacity: 0;
    }

    .#{$crm-sidebar}--right.is-closed & {
      left: -5.6rem;
    }
  }

  &--left {
    border-right: 1px solid color(border-main);
    left: 0;

    &.is-open {
      position: relative;
      transform: translateX(0);
      pointer-events: auto;

      & + .crm-section__content {
        margin-left: 0;
      }
    }

    &.is-closed {
      transform: translateX(-100%);
      pointer-events: none;

      & + .crm-section__content {
        margin-left: -28rem;
      }
    }

    .#{$crm-sidebar} {
      &__header {
        padding-right: 5.6rem;
      }

      &__closeButton {
        right: space(md);
      }
    }
  }

  &--right {
    border-left: 1px solid color(border-main);
    right: 0;

    &.is-open {
      position: relative;
      transform: translateX(0);
      pointer-events: auto;

      .crm-section__content:has(+ &) {
        margin-right: 0;
      }
    }

    &.is-closed {
      transform: translateX(100%);
      pointer-events: none;
    }

    .#{$crm-sidebar} {
      &__header {
        padding-left: 5.6rem;
      }

      &__closeButton {
        left: space(md);
      }
    }
  }

  &__dropdown {
    width: $crm-sidebar-width - (space(md) * 2);
  }

  &__addButton {
    justify-content: space-between;

    > span {
      display: flex;
      align-items: center;
    }
  }

  &-nav {
    display: flex;
    flex-direction: column;
    gap: space(sm);

    &__item {
      display: flex;
      align-items: center;
      gap: space(sm);
      min-height: 3.2rem;
      padding: space(xs) space(sm);
      border-radius: radius(md);
      background-color: color(background-selectable);
      color: color(content-selectable);

      span {
        font-size: 1.4rem;
        line-height: 1.2;
      }

      &:hover,
      &:focus {
        background-color: color(background-alt);
        text-decoration: none;
      }

      &.is-active {
        &,
        &:hover {
          background-color: color(background-selected);
          color: color(content-selected);
        }
      }

      svg {
        align-self: flex-start;
        margin-top: 0.2rem;
        @include icon-size(sm);
      }
    }
  }

  .gx-paginationBar {
    padding: space(md);
    border-top: none;

    &__resultsRange {
      margin-left: 0;
      font-size: 1.4rem;
      color: color(content-high);
    }

    button:not(:first-child):not(:last-child) {
      display: none;
    }
  }
}

//TODO: reorganize this file
.sidebar-property-list {
  display: flex;
  flex-direction: column;
  gap: space(sm);
  margin-top: space(md);

  .crm-property-item {
    align-items: flex-start;
    padding: 1.2rem 0 1.2rem 1.2rem;
    border: 1px solid transparent;
    border-radius: radius(md);
    line-height: 1.6rem;
    font-size: 1.4rem;

    &:hover {
      background-color: color(background-alt);
      border-color: color(background-alt);
      cursor: pointer;
    }

    a:hover {
      text-decoration: none;
    }

    &.is-selected {
      border-color: color(background-selected-high);
    }

    &__pic {
      width: 5.6rem;
      height: 5.6rem;
    }

    &__descBadges {
      .gx-badge {
        padding: 0;

        span {
          display: none;
        }
      }
    }
  }
}
