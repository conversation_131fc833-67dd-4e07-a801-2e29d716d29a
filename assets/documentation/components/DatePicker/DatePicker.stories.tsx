import { DatePicker } from '../../../js/commons/gtx-react/components/DatePicker/DatePicker';
import type { Meta, StoryObj } from '@storybook/react';

/** Colorpicker is a dropdown component used to pick the desired color. */
const meta: Meta<typeof DatePicker> = {
    title: 'Components/DatePicker',
    component: DatePicker,
    parameters: {
        componentSubtitle: 'Component',
        componentTitle: 'DatePicker',
        componentSlug: 'DatePicker',
        componentImport: 'DatePicker',
        packageName: 'gtx-react/components/DatePicker/DatePicker',
    },
    tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof DatePicker>;

export const DefaultDatePicker: Story = {
    render: (args) => {
        return (
            <DatePicker
                onColorSelect={(color) => console.log('Saved color:', color)}
                colors={['red', 'blue', 'green']}
                colorDefault={'red'}
            />
        );
    },
};

// export const WithCounterAttachmentTag: Story = {
//     render: (args) => {
//         return <AttachmentTag type="video" count={3} />;
//     },
// };

DefaultDatePicker.storyName = 'Default';
