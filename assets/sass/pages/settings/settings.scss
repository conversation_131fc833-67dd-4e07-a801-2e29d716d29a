@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$gtx-standard-space: 1.1rem;

.gx-input-clear-wrapper {
  .suggestion-spinner {
    &:after {
      top: 0.9rem;
      right: space(xl);
    }
  }
}

.sts-logo {
  max-width: 23rem;
  max-height: 6rem;
}

.sts-image-agency {
  max-width: 40rem;
  max-height: 30rem;
}

.sts-preview-video {
  iframe {
    max-width: 100%;
  }
}

.gtx-sts-container {
  position: relative;

  [data-component='user-profile'] {
    .header {
      padding: $gtx-standard-space;
      background-color: color(background-alt);
    }
  }

  .user-list {
    margin: 0;
    border-top: 0;
    border-right: 0;
    border-left: 0;

    &__roleSelect {
      min-width: 20rem;
    }

    .panel-heading {
      border: none;
      border-bottom: 0.1rem solid color(border-main);
      background-color: color(background-main);
      font-size: 1.4rem;

      #gtx-sts-users-bulk-actions {
        .btn-group {
          margin-left: $gtx-standard-space;
        }
      }
    }

    table {
      thead {
        background-color: #fafafa;
        color: color(content-medium);
      }

      tbody > tr > td {
        button > i {
          color: color(content-action);
        }
      }
    }

    a {
      i.fa {
        padding-top: 0.3rem;
      }
    }

    button {
      i.fa-cog {
        padding-top: 0.3rem;
      }
    }
  }

  #fatture .gtx-list {
    margin: 0;
    border-top: 0;
    border-right: 0;
    border-left: 0;

    .panel-heading {
      border: none;
      border-bottom: 0.1rem solid color(border-main);
      background-color: color(background-main);
      font-size: 1.4rem;

      .alert {
        margin-right: 0;
        margin-left: 0;
        padding-top: 0;
        padding-bottom: 0;
        color: color(content-high);
      }
    }

    table {
      thead {
        background-color: #fafafa;
        color: color(content-medium);
      }

      tbody > tr > td {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;

        &:first-child {
          padding-left: 3rem;
        }

        &:last-child {
          padding-right: 3rem;
        }

        button > i {
          color: color(content-action);
        }
      }
    }
  }

  .gtx-message-required {
    padding: $gtx-standard-space * 3 $gtx-standard-space * 3 0;
  }

  .gtx-sts-general {
    .first-depth {
      padding: $gtx-standard-space * 2;

      .h4 {
        margin: $gtx-standard-space 0;
      }
    }

    .styled-checkbox label {
      padding-left: 0;
    }

    .gtx-textarea-counter-container {
      margin-top: 3rem;

      label {
        margin-right: $gtx-standard-space;
        font-weight: 400;
      }

      .gtx-textarea-counter {
        width: 6rem;
      }
    }

    .submit-container {
      padding: $gtx-standard-space * 3 $gtx-standard-space * 2;
    }

    textarea.description {
      margin-top: 3rem;
    }
  }

  .gtx-sts-media {
    .row {
      padding: $gtx-standard-space * 2;

      .h4 {
        margin-top: 0;
      }

      [class*='col-xs-'],
      [class*='col-sm-'] {
        padding: $gtx-standard-space;
      }

      [data-role='logo'] {
        max-width: 23rem;
      }

      [data-role='image'] {
        max-width: 40rem;
      }

      @media (min-width: 992px) {
        img,
        iframe {
          float: right;
        }
      }
    }

    [data-role='logo-container'],
    [data-role='image-container'],
    [data-role='video-container'] {
      position: relative;
    }
  }

  .gtx-sts-password {
    padding: $gtx-standard-space * 2;
  }

  & > {
    .gtx-sts-validate-email-msg {
      padding: 3rem;
    }
  }
}

.sts-headquarters {
  .gtx-message-required {
    padding: 0 $gtx-standard-space 2.2rem $gtx-standard-space;
  }

  .table {
    margin-bottom: 0;
  }

  .gtx-form {
    padding: $gtx-standard-space * 2;
  }

  .map-on {
    position: relative;
    padding: 0;
    background: color(border-main);
    text-align: center;
    vertical-align: middle;

    .detail-map-box {
      position: absolute !important;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      min-height: 50rem;
      background: color(background-alt);
      overflow: hidden;
    }

    .detail-map-msg {
      position: absolute;
      right: 0;
      bottom: space(xl);
      left: 0;
      max-width: 64rem;
      margin-right: auto;
      margin-left: auto;
      padding: space(sm) space(sm) space(md);
      border-radius: radius(sm);
      background: color(background-main);
      text-align: center;
      @include z-index(base, 2);

      &__head {
        position: relative;

        p {
          margin-right: space(lg);
        }
      }

      &__actions {
        display: flex;

        .gx-button {
          flex-shrink: 0;
        }
      }

      &--negative {
        background-color: color(background-error);

        p {
          color: color(content-error);
        }
      }

      .content {
        @include typography(body-small);
        margin-top: space(sm);
        margin-bottom: space(md);
      }
    }

    [data-component='address-confirm'] {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      max-width: 64rem;
      margin-right: auto;
      margin-left: auto;
      text-align: left;
    }

    &.map-on {
      position: relative;
      height: 50rem;

      .gx-alert {
        position: absolute;
        top: space(sm);
        right: space(sm);
        left: space(sm);

        &.detail-map-msg {
          top: auto;
          text-align: left;
        }
      }
    }
  }

  .gtx-opening-time {
    width: 100%;

    .gtx-opening-time-label {
      width: 16%;
      margin-top: $gtx-standard-space * 3;
      padding: 0 $gtx-standard-space $gtx-standard-space;
      float: left;
      font-weight: 600;
    }

    @media (max-width: 102.3rem) {
      .gtx-opening-time-label.gtx-opening-time-label-long {
        margin-top: $gtx-standard-space * 2;
      }
    }

    .gtx-opening-time-input-box {
      width: 42%;
      padding: 0 $gtx-standard-space $gtx-standard-space;
      float: left;
    }
  }

  .submit-container {
    padding: $gtx-standard-space * 3 $gtx-standard-space * 2;
    border-top: 0.1rem solid color(border-main);
  }
}

.styled-radio-group {
  .radio-btn {
    width: 4rem;
    height: 3.2rem;
    padding: 0.5rem;
    border-radius: radius(sm) 0 0 radius(sm);

    &:before {
      color: color(content-medium);
      content: 'M';
    }

    &.radio-btn-f {
      margin-left: -0.1rem;
      border-radius: 0 radius(sm) radius(sm) 0;

      &:before {
        content: 'F';
      }
    }
  }

  input:checked {
    + .radio-btn {
      border-radius: 0;

      &:before {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: auto;
        height: auto;
        margin: -0.1rem;
        padding: 0.5rem;
        border-radius: radius(sm) 0 0 radius(sm);
        color: color(content-accent);
        box-shadow: 0.1rem 0.2rem 0.5rem 0 rgba(0, 0, 0, 0.2) inset;
        content: 'M';
        @include z-index(base, 1);
      }

      &.radio-btn-f {
        &:before {
          border-radius: 0 radius(sm) radius(sm) 0;
          box-shadow: -0.1rem 0.2rem 0.5rem 0 rgba(0, 0, 0, 0.2) inset;
          content: 'F';
        }
      }
    }
  }
}

/*-- Panel heading new --*/
.getrix-panel .panel-heading--new {
  padding: space(md) space(xl);
}

.users-infobox {
  padding: space(md) space(xl) 0;
}

/*-- Lista utenti --*/
%padding-checkbox {
  padding-right: 3rem;
  padding-left: 3rem;
}

.table > .header-user > tr > th.checkbox-cell {
  @extend %padding-checkbox;
}

.table > .header-user > tr > th {
  height: 5rem;
  border-bottom: 0.1rem solid color(border-main);
  color: color(content-low);
  font-size: 1.2rem;
  font-weight: normal;

  &:first-child {
    padding-left: 3rem;
  }

  &:last-child {
    padding-right: 3rem;
  }
}

.row-user {
  &:last-of-type:not(:only-child) {
    .gx-dropdown {
      top: auto;
      bottom: calc(100% + (#{space(sm)} / 2));
    }
  }

  .gx-helper {
    margin: 0;
  }

  .label-cell {
    .gx-table__cell {
      font-size: 1.4rem;
    }
  }

  .gx-dropdown {
    .gx-action-list__item a {
      white-space: nowrap;
    }
  }

  .checkbox-cell {
    @extend %padding-checkbox;
  }

  .user-pic {
    width: 4rem;
    height: 4rem;
    margin-right: 2rem;
    float: left;
    border-radius: radius(rounded);
    background: #ccc;
    color: color(content-accent);
    line-height: 4rem;
    text-align: center;
    overflow: hidden;
  }

  .user-name {
    width: calc(100% - 6rem);
    word-wrap: break-word;
  }

  .utente-cell {
    width: 40rem;
  }

  .ruolo-cell,
  .visibilita-cell {
    width: 15rem;

    .gx-select {
      width: 15rem;
    }
  }

  .contatti-cell {
    width: 15%;
  }

  .stato-cell {
    width: 15rem;

    .gx-select,
    .gx-button {
      width: 15rem;
    }

    a {
      font-size: 1.4rem;
    }
  }
}

/*-- nav-tabs --*/
.gtx-nav-tabs-bb {
  & > li.active {
    > a,
    > a:hover,
    > a:focus {
      border: none;
      border-bottom: 0.3rem solid color(border-selected);
      color: #333333;
    }
  }

  > li > a,
  > li > a:hover {
    border: none;
    border-bottom: 0.3rem solid transparent;
  }

  > li > a {
    padding-bottom: 2rem;
    color: #777777;

    @media screen and (min-width: #{emConverter(769)}) and (max-width: #{emConverter(830)}) {
      padding-right: 1.2rem;
      padding-left: 1.2rem;
    }
  }

  > li > a:hover {
    border-color: transparent;
    background: none;
    color: #333333;
  }
}

.panel-default {
  border-color: color(border-main);
}

.labs-img-editor {
  @include z-index(modal);
}

.gtx-opening-day {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: ($gx-unit-size * 9);
  padding-top: space(sm);
  padding-bottom: space(sm);
  font-size: 1.4rem;

  & + & {
    border-top: 0.1rem solid color(border-main);
  }

  &__label {
    width: 8rem;
    color: color(content-low);
  }

  &__times {
    width: 8rem;
    margin-left: space(2xl);
    font-size: 1.2rem;
    text-align: center;
  }

  .gx-button {
    margin-left: space(2xl);
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    justify-content: flex-start;
  }
}

.orari-list-wrap {
  padding-bottom: 18rem;
}

.gtx-opening-time-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: space(md);

  label {
    height: 1.9rem;
  }

  > div {
    flex-grow: 1;
    width: calc(50% - 3.6rem);
  }

  ul {
    padding-left: 0;
    list-style: none;
  }

  > * + * {
    margin-left: space(md);
  }
}

.custom-error {
  border-color: #a94442;
  background: #f9e2e1 !important;
}

.sts-user-form {
  width: 100%;

  @include media('>#{breakpoint(md)}') {
    min-width: 70rem;
    max-width: 90rem;
  }
}

//Profile pic
.user-profile-pic {
  position: relative;
  width: ($gx-unit-size * 22);
  height: ($gx-unit-size * 22);
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);
  overflow: hidden;

  &__wrapper {
    margin-right: space(2xl);
    margin-bottom: space(lg);

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  &__status {
    position: absolute;
    right: space(xs);
    bottom: space(xs);
  }

  &__name {
    margin-top: space(md);
  }

  &__action {
    position: absolute;
    top: space(sm);
    right: space(sm);
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    margin: 0;
    transition: color 0.3s ease-in-out;
    background: color(background-alt);
    cursor: pointer;

    svg {
      margin-bottom: space(sm);
      transition: color 0.3s ease-in-out;
      color: color(border-main);
      font-size: ($gx-unit-size * 9);
    }

    &:hover {
      color: color(content-action);

      svg {
        color: color(content-action);
      }
    }
  }
}

// user-form
.user-profile-wrapper {
  width: 100%;
  max-width: 89.6rem;
}

.sts-user-phone {
  margin-bottom: space(sm);

  .gx-select {
    min-width: 12.5rem;
  }
}

.sts-summary-list {
  @include media('>=#{breakpoint(md)}') {
    max-width: 60%;
  }
}

.sts-bioSelect {
  width: 20rem;
  margin-bottom: space(md);
}

.sts-user-phone-visibility {
  display: flex;

  .gx-multiButton {
    margin-left: space(md);

    .gx-button:last-child {
      margin-left: -0.1rem;
    }
  }

  .gx-icon {
    .gx-icon__interactive {
      display: none;
    }

    &.main-contact {
      color: color(content-action);

      svg {
        display: none;
      }

      .gx-icon__interactive {
        display: block;
      }
    }
  }
}

// Responsive table users
.table-sts-responsive {
  @include media('<#{breakpoint(lg)}') {
    display: block;
    background: color(background-alt);

    .gx-table__head {
      display: none;
    }

    .gx-table__cell {
      width: 100%;
    }

    .gx-dropdown {
      top: auto;
      bottom: calc(100% + (#{space(sm)} / 2));
    }

    tbody {
      display: flex;
      flex-wrap: wrap;
    }

    tr {
      display: flex;
      flex-wrap: wrap;
      width: calc(33% - #{$gx-unit-size * 4});
      margin: space(md);
      padding: space(md);
      border: 0.1rem solid color(border-main);
      background: #fff;

      &:last-child {
        border-bottom: 0.1rem solid color(border-main);
      }

      &:hover {
        background: #fff !important;
      }

      .azioni-cell .dropdown-menu {
        top: auto;
        bottom: 100%;
        margin-bottom: 0.2rem;
      }
    }

    td {
      display: block;
      width: 100% !important;
      margin: 0 !important;
      padding: 0 !important;
      border: none !important;
    }

    .utente-cell {
      display: flex;
      margin-bottom: space(md) !important;
      padding-bottom: space(sm) !important;
      border-bottom: 0.1rem solid color(border-main) !important;
    }

    .label-cell {
      width: calc(50% - #{space(sm)}) !important;
      margin-bottom: space(md) !important;

      &::before {
        display: block;
        margin-bottom: space(xs);
        color: color(content-low);
        font-size: 1.2rem;
        text-transform: uppercase;
        content: attr(aria-label);
      }

      .form-control,
      .status-button {
        width: 100%;
      }
    }

    .ruolo-cell,
    .visibilita-cell {
      margin-right: space(md) !important;

      .gx-select {
        width: 100%;
      }
    }

    .stato-cell {
      margin-left: 0 !important;

      .gx-select {
        width: 100%;
      }

      span.status-verifying {
        display: flex;
        align-items: center;
        height: ($gx-unit-size * 5);
      }
    }

    .azioni-cell {
      display: flex;
      margin-top: space(sm) !important;

      .gx-table__cell {
        width: 100%;
      }

      .gx-multiButton {
        display: flex;
        width: 100%;

        .gx-button {
          border-radius: radius(sm) !important;
        }

        .gx-button--iconOnly {
          width: 100%;
        }

        > a {
          flex-grow: 1;
        }
      }

      .gx-dropupButton {
        margin-left: space(md);
      }
    }
  }

  @include media('<#{emConverter(1100)}') {
    tr {
      width: calc(50% - #{$gx-unit-size * 4});
    }
  }

  @include media('<#{breakpoint(sm)}') {
    tr {
      width: 100%;
      margin-right: 0;
      margin-left: 0;
      border-right: none;
      border-left: none;
    }
  }

  &--users {
    @include media('<#{breakpoint(lg)}') {
      .gx-table__body {
        padding: space(md);
        background-color: color(background-alt);
      }
    }

    @include media('<#{breakpoint(sm)}') {
      .gx-table__body {
        padding: space(sm) 0;
      }
    }
  }
}

// Responsive table security
.td-dispositivo {
  position: relative;

  > div {
    display: flex;
    align-items: center;
  }

  svg {
    @include icon-size(md);
  }

  .device {
    margin-left: space(md);
    text-transform: capitalize;

    span {
      display: block;
    }
  }
}

.td-status {
  text-align: center;

  .in-uso {
    color: color(content-success);
    font-weight: 600;
    text-transform: uppercase;
  }

  .btn-action {
    width: 100%;
  }
}

.table-sts-security {
  @include media('<#{breakpoint(md)}') {
    .td-dispositivo {
      margin-bottom: space(md) !important;
      padding-bottom: space(sm) !important;
      border-bottom: 0.1rem solid color(border-main) !important;
    }

    .td-status {
      .gx-button {
        width: 100%;
      }
    }
  }
}

.col-sesso .styled-radio {
  display: block;
}

.gtx-sts-user-notify {
  margin-bottom: space(lg);
}

.is-vertical-centered {
  display: flex;
  align-items: center;
}

.settings-video-input-container {
  display: flex;

  button {
    margin-left: space(md);
  }
}

.settings-remote-request-info-modal {
  // Hack to remove cancel button
  .modal-footer .gx-button--ghost {
    display: none;
  }

  &__body {
    img {
      max-width: 100%;
    }

    img,
    ul {
      margin-bottom: space(md);
    }

    p:last-of-type {
      margin-bottom: 0;
    }
  }
}

.settings-media-image-modal {
  &__content {
    display: flex;
    justify-content: center;

    &Image {
      max-width: 26rem;

      @include media('<#{emConverter(500)}') {
        max-width: 16rem;
      }
    }

    &Legend {
      margin-right: -2rem;
    }

    &LegendItem {
      display: flex;
      position: relative;
      top: 20%;
      align-items: center;
      justify-content: flex-end;

      &:last-of-type {
        top: 50%;
      }

      // Text
      span {
        font-size: 1.4rem;
        font-weight: 600;

        @include media('<#{emConverter(500)}') {
          font-size: 1.2rem;
        }
      }

      // Pointer
      div {
        position: relative;
        width: space(4xl);
        height: 0.1rem;
        margin-left: space(sm);
        background-color: color(content-action);

        @include media('<#{emConverter(500)}') {
          width: space(lg);
          margin-left: space(xs);
        }

        &:after {
          position: absolute;
          top: -0.3rem;
          right: -0.4rem;
          width: $gx-unit-size;
          height: $gx-unit-size;
          border-radius: radius(rounded);
          background-color: color(content-action);
          content: '';
        }
      }
    }
  }
}

.settings-general-privacy {
  .gx-list {
    .gx-icon {
      color: color(background-brand);
    }

    .gx-list__item + .gx-list__item {
      margin-top: space(sm);
    }
  }
}

.single-user-header {
  @include media('<#{breakpoint(md)}') {
    position: absolute;
    top: -56px;
    left: 0;
    @include z-index(base, 3);
    width: 100%;
    background-color: color(background-main);
    padding-left: 0;
    padding-right: space(md);

    .gx-head-section__back {
      position: static;
    }

    .gx-head-section__title {
      @include typography(title-2);
    }

    .gx-head-section__actions {
      flex-grow: 0;

      .gx-button {
        flex-grow: 0;
        margin-top: 0;
        margin-bottom: 0;
        height: $gx-unit-size * 4;
        padding: 0 space(sm);

        &--iconOnly {
          min-width: $gx-unit-size * 4 !important;
          padding: 0;
        }
      }
    }
  }
}

// Exception for website protocol
.settings-website-input {
  button {
    width: 9.6rem;
    height: 100%;
    border-width: 0 0.1rem 0 0;
    border-radius: 0;
    text-transform: none;
  }
}

// Tab trasnlations user profile
.tab-translations span {
  position: absolute;
  width: 0.1rem;
  height: 0.1rem;
  padding: 0;
  margin: -0.1rem;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

// Fix provvisorio Immotop
.app-domain-immotoppro-lu .settings_immovisita a {
  display: none;
}
