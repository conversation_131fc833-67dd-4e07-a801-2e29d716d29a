<?php

declare(strict_types=1);

namespace App\Formatter\Property;

use App\Constants\Base\GtxConstants;
use App\Formatter\Shared\DateFormatter;
use App\Handler\Agency\AgencyHandler;
use App\Helper\PropertiesHelper;
use App\Model\Property\AdExport;
use App\Utils\TranslatorTrait;
use Symfony\Component\Translation\TranslatorInterface;

class AdExportFormatter
{
    use TranslatorTrait;

    private const CONTRACT_RENT = 'sale_rent';

    private AgencyHandler $agencyHandler;
    private DateFormatter $dateFormatter;
    private PropertiesHelper $propertiesHelper;
    private TranslatorInterface $translator;
    private string $portalName;
    private string $getrixName;

    public function __construct(
        AgencyHandler $agencyHandler,
        DateFormatter $dateFormatter,
        PropertiesHelper $propertiesHelper,
        TranslatorInterface $translator,
        string $getrixName,
        string $portalName
    ) {
        $this->agencyHandler = $agencyHandler;
        $this->dateFormatter = $dateFormatter;
        $this->getrixName = $getrixName;
        $this->portalName = $portalName;
        $this->propertiesHelper = $propertiesHelper;
        $this->translator = $translator;
    }

    public function format(AdExport $data, bool $isPro): array
    {
        $row = [
            $data->getCode(),
            $data->getId(),
            $this->translateContent(
                'db_category.id_',
                (string) $data->getCategoryId()
            ),
            $this->translateContent(
                'db_typology.id_',
                (string) $data->getTypologyId()
            ),
            self::CONTRACT_RENT === $data->getContractType()
                ? $this->translateCms('label.sale_rent')
                : $this->translateContent('db_contract.id_', $data->getContractType()),
            null !== $data->getSurface()
                ? $data->getSurface()
                : '',
            null !== $data->getPriceRangeId()
                ? \str_replace(
                    '&euro;',
                    '€',
                    $this->translateContent('db_price_ranges.id_', (string) $data->getPriceRangeId())
                )
                : '',
            null !== $data->getSalePrice()
                ? $data->getSalePrice() :
                '',
            null !== $data->getRentalPrice()
                ? $data->getRentalPrice()
                : '',
            null !== $data->getPropertyConditionId()
                ? $this->translateContent('db_status.id_', (string) $data->getPropertyConditionId())
                : '',
            null !== $data->getRooms()
                ? $data->getRooms()
                : '',
            null !== $data->getBathrooms()
                ? $data->getBathrooms()
                : '',
            null !== $data->getKitchenId()
                ? $this->translateContent('db_kitchen.id_', (string) $data->getKitchenId())
                : '',
            null !== $data->getFloorId()
                ? $this->translateContent('db_floor.id_', (string) $data->getFloorId())
                : '',
            null !== $data->getHeatingId()
                ? $this->translateContent('db_heating.id_', (string) $data->getHeatingId())
                : '',
            $data->getTerrace()
                ? $this->translateCms('label.yes')
                : '',
            null !== $data->getGarageId()
                ? $this->translateContent('db_boxauto.id_', (string) $data->getGarageId())
                : '',
            null !== $data->getRegionName()
                ? $data->getRegionName()
                : '',
            null !== $data->getProvinceName()
                ? $data->getProvinceName()
                : '',
            null !== $data->getCityName()
                ? $data->getCityName()
                : '',
            null !== $data->getMacroZoneName()
                ? $data->getMacroZoneName()
                : '',
            null !== $data->getMicroZoneName()
                ? $data->getMicroZoneName()
                : '',
            null !== $data->getPostalCode()
                ? $data->getPostalCode()
                : '',
            null !== $data->getAddress()
                ? $data->getAddress()
                : '',
            null !== $data->getStreetNumber()
                ? $data->getStreetNumber()
                : '',
            null !== $data->getVirtualTour()
                ? $data->getVirtualTour()
                : '',
            null !== $data->getDescription()
                ? $data->getDescription()
                : '',
            null !== $data->getNotes()
                ? $data->getNotes()
                : '',
        ];

        if ($isPro) {
            $row = \array_merge($row, [
                null !== $data->getVisitCount()
                    ? $data->getVisitCount()
                    : '',
                null !== $data->getSavedCount()
                    ? $data->getSavedCount()
                    : '',
                null !== $data->getHiddenCount()
                    ? $data->getHiddenCount()
                    : '',
                null !== $data->getContactCount()
                    ? $data->getContactCount()
                    : '',
                $data->getIsPremium()
                    ? $this->translateCms('label.yes')
                    : '',
                $data->getHasShowcaseVisibility()
                    ? $this->translateCms('label.yes')
                    : '',
                $data->getHasStarVisibility()
                    ? $this->translateCms('label.yes')
                    : '',
                $data->getHasTopVisibility()
                    ? $this->translateCms('label.yes')
                    : '',
                $data->getIsGuaranteed()
                    ? $this->translateCms('label.yes')
                    : '',
                $this->formatQualityIndex($data),
            ]);
        }

        $row = \array_merge($row, [
            null !== $data->getCreationDate()
                ? $this->dateFormatter->shortFormat(new \DateTime($data->getCreationDate()))
                : '',
            null !== $data->getLastModifiedDate()
                ? $this->dateFormatter->shortFormat(new \DateTime($data->getLastModifiedDate()))
                : '',
            $this->formatLicense($data),
            $this->formatSellRent($data),
            $this->formatBuildingUse($data),
            $this->formatAdState($data),
        ]);

        if ($isPro) {
            $row = \array_merge($row, [
                null !== $data->getSearchPosition()
                    ? $data->getSearchPosition()
                    : '',
            ]);
        }

        return \array_merge($row, [
            null !== $data->getAgentEmail()
                ? $data->getAgentEmail()
                : '',
        ]);
    }

    private function formatAdState(AdExport $data): string
    {
        switch ($data->getAdStatusId()) {
            case GtxConstants::PROPERTY_ACTIVE_PORTAL_STATUS:
                return \sprintf(
                    '%s %s %s',
                    $this->translateCms('label.active'),
                    $this->translateCms('label.out_of'),
                    $this->portalName
                );
            case GtxConstants::PROPERTY_ACTIVE_STATUS:
                return \sprintf(
                    '%s %s %s',
                    $this->translateCms('label.active'),
                    $this->translateCms('label.out_of'),
                    $this->getrixName
                );
            case GtxConstants::PROPERTY_ARCHIVED_PORTAL_STATUS:
            case GtxConstants::PROPERTY_DRAFT_PORTAL_STATUS:
            case GtxConstants::PROPERTY_ARCHIVED_STATUS:
            case GtxConstants::PROPERTY_DRAFT_STATUS:
                return $this->translateCms('label.archived_2');
            default:
                return $this->translateCms('label.unknown');
        }
    }

    private function formatBuildingUse(AdExport $data): string
    {
        if (
            !\in_array($data->getCategoryId(), GtxConstants::CATEGORIES_USING_IMMOBILE)
            || null === $data->getBuildingUse()
        ) {
            return '';
        }

        return \implode(', ', \array_map(function (string $id) {
            $this->translateContent('db_building_use.id_', $id);
        }, $data->getBuildingUse()));
    }

    private function formatLicense(AdExport $data): string
    {
        if (
            !\in_array($data->getCategoryId(), GtxConstants::CATEGORIES_USING_IMMOBILE)
            || GtxConstants::CATEGORIA_PALAZZI_EDIFICI !== $data->getCategoryId()
        ) {
            return '';
        }

        return $this->translateContent('db_license.id_', (string) $data->getLicenseId());
    }

    private function formatQualityIndex(AdExport $data): string
    {
        if (empty($data->getQualityIndex())) {
            return '';
        }

        return \sprintf(
            '%s%%',
            $this->propertiesHelper->calculateRankingPenaltyBalance(
                $this->agencyHandler->getRanking(),
                \intval($data->getQualityIndex() / 10),
            )
        );
    }

    private function formatSellRent(AdExport $data): string
    {
        $result = [];
        if (!empty($data->getHasCommercialActivity())) {
            $result[] = $this->translateCms('label.business');
        }
        if (!empty($data->getHasWalls())) {
            $result[] = $this->translateCms('label.shop_walls');
        }
        if (!empty($data->getHasWorkshop())) {
            $result[] = $this->translateCms('label.laboratory');
        }

        return \implode(', ', $result);
    }
}
