#!/bin/bash

printf "\n\nENV\n\n"

#it tenant
IT="gestionale.it.localhost"
sed -i 's#gestionale-it\.it\.dev\.loc#'${IT}'#g' .env.it
sed -i 's#media\.getrix\.it\.dev\.loc#media.'${IT}'#g' .env.it
sed -i 's#https://media#http://media#g' .env.it
sed -i 's#\.it\.dev\.loc#'${IT}'#g' .env.it

#es tenant
ES="gestionale.es.localhost"
sed -i 's#gestionale-es\.es\.dev\.loc#'${ES}'#g' .env.es
sed -i 's#\.es\.dev\.loc#'${ES}'#g' .env.es

#lu tenant
LU="gestionale.lu.localhost"
sed -i 's#gestionale-lu\.lu\.dev\.loc#'${LU}'#g' .env.lu
sed -i 's#\.lu\.dev\.loc#'${LU}'#g' .env.lu

#hr tenant
HR="gestionale.hr.localhost"
sed -i 's#gestionale-hr\.hr\.dev\.loc#'${HR}'#g' .env.hr
sed -i 's#\.hr\.dev\.loc#'${HR}'#g' .env.hr

#rs tenant
RS="gestionale.rs.localhost"
sed -i 's#gestionale-rs\.rs\.dev\.loc#'${RS}'#g' .env.rs
sed -i 's#\.rs\.dev\.loc#'${RS}'#g' .env.rs

# Removing symlinks
rm /application/.env.it
rm /application/.env.es
rm /application/.env.lu
rm /application/.env.hr
rm /application/.env.rs

# Creating symlinks
ln -s /application/envs/it/docker.env /application/.env.it
ln -s /application/envs/es/docker.env /application/.env.es
ln -s /application/envs/lu/docker.env /application/.env.lu
ln -s /application/envs/hr/docker.env /application/.env.hr
ln -s /application/envs/rs/docker.env /application/.env.rs

sed -i 's#post_max_size = 8M#post_max_size = 31M#g' /etc/php/7.4/fpm/php.ini
sed -i 's#upload_max_filesize = 8M#upload_max_filesize = 30M#g' /etc/php/7.4/fpm/php.ini

printf "\n\nStarting PHP 7.4 daemon...\n\n"

service php7.4-fpm start

printf "Starting Nginx...\n\n"
set -e

if [[ "$1" == -* ]]; then
    set -- nginx -g 'daemon off;' "$@"
fi

exec "$@"
