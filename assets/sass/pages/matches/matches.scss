@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.matches-managed-on {
  display: flex;
  align-items: center;
  @include typography(body-small);

  strong {
    display: block;
  }

  &__icon {
    fill: color(content-success);
    margin-left: space(xs);
    font-size: 1.6rem;
    flex-shrink: 0;
  }
}
.matches-search-field-popover {
  padding-top: space(sm);
  padding-bottom: space(sm);

  &__map > img {
    width: 100%;
    height: auto;
    border-radius: radius(sm);
  }

  &__text {
    @include typography(body-small);
    margin-top: space(md);
  }
}

.matches-search-field {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  gap: space(sm);
  width: 100%;

  &__map > img {
    width: 9rem;
    height: auto;
    border-radius: radius(sm);
  }

  &__details {
    @include typography(body-small);

    .matches-search-field__category {
      .gx-list__item--withElement {
        align-items: flex-start;
      }
    }

    .matches-filters-list-link {
      display: block;
      margin-top: space(sm);
    }

    .matches-typologies-list-link,
    .matches-filters-list-link {
      &:hover {
        text-decoration: underline;
      }
    }

    .gx-list {
      .gx-list__item {
        &:not(:first-child) {
          margin-top: space(sm);
        }
        .gx-icon {
          @include icon-size(md);
        }
      }
    }
  }
}

ul.matches-typologies-list-popover {
  color: color(content-high);
  li {
    margin-top: space(sm);
    @include typography(body-small);
    ::marker {
      @include typography(body-small);
    }
  }
}

.matches-filters-list-popover {
  color: color(content-high);
  .gx-list {
    .gx-list__item {
      &:not(:first-child) {
        margin-top: space(xs);
      }
      @include typography(body-small);
      .gx-icon {
        @include icon-size(md);
      }
    }
  }
}

.matches-list-empty-state {
  display: flex;
  gap: 1.5rem;
  flex-direction: column;
  max-width: 30rem;
  text-align: center;

  &__title {
    @include typography(title-2);
  }

  &__body {
    @include typography(body);
  }
}

.gx-property-item {
  &__pic {
    border-radius: radius(sm);
  }
  &__property-badge {
    margin-top: space(sm);
  }
}

.matches-property {
  &__tags {
    margin-top: space(sm);

    @include media('screen', '<#{breakpoint(md)}') {
      .gx-badge + .gx-badge {
        margin: 0;
        margin-top: space(sm);
      }
    }
  }
}

.matches-filters-modal {
  .gx-modal__body {
    overflow: visible;
  }

  &__content {
    padding: 2.2rem 0;
  }

  .gx-autocomplete__listItem {
    height: auto;
  }
}

.matches-cardlist {
  .gx-card-row__label {
    width: 30%;
  }

  .gx-card-row__value {
    width: 70%;
  }
}

#content-wrapper.matches {
  @include media('screen', '<#{breakpoint(md)}') {
    .gtx-ask-help {
      display: none;
    }
  }
}

.gx-container {
  &.alert-wrapper {
    border-bottom: 0.1rem solid color(border-main);
    @include media('screen', '>=#{breakpoint(md)}') {
      padding-top: space(md);
      padding-bottom: space(md);
    }
  }
}
