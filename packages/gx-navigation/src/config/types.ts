import { GxNavigationFeatureFlags, MenuConfig } from '../types';

/**
 * A typical menu object:
 * ```
 * {
    menuCacheToken: '**********',
    agencyLanguage: 'it_IT',
    hasVirtualVisitSettingsFlag: true,
    hasScheduledVisitImmovisita: true,
    agencyId: 1,
    agencyImage: 'https://placehold.co/400x100',
    agencyName: 'CasaMia',
    agentName: '<PERSON>',
    agentEmail: '<EMAIL>',
    agentAvatar: 'https://placehold.co/100x100',
    logo: '/images/logo-getrix.svg',
    homeUrl: '/',
    siteName: 'Getrix',
    adPortal: 'Immobiliare.it',
    isMasterAccount: true,
    responsibleName: '<PERSON>',
    responsiblePhone: '+39 123 456 789',
    responsibleAvatar: 'https://placehold.co/100x100',
    hasTwoFactorAuth: true,
    metricsSite: 'gestionale-it',
    metricsSitePrefix: 'getrix',
    metricsUrl: 'https://estats.get'
    }
 * ```
 */
type DefaultProps = {
  menuCacheToken: string;
  agencyLanguage: string;
  hasVirtualVisitSettingsFlag: boolean;
  hasScheduledVisitImmovisita: boolean;
  agencyId: number;
  agencyImage: string;
  agencyName: string;
  agentName: string;
  agentEmail: string;
  agentAvatar: string;
  logo: string;
  homeUrl: string;
  siteName: string;
  adPortal: string;
  isMasterAccount: boolean;
  responsibleName: string;
  responsiblePhone: string;
  responsibleAvatar: string;
  hasTwoFactorAuth: boolean;
  serviceRemoteVisit: string;
  defaultLanguage: string;
  profileUrl: string;
  isAdmin: boolean;
  metricsSite: string;
  metricsSitePrefix: string;
  metricsUrl: string;
  helpJuiceAccountUrl: string;
  featureFlags: GxNavigationFeatureFlags;
};

export type BaseNavigationContextType = DefaultProps & {
  primaryMenu?: MenuConfig;
  secondaryMenu?: MenuConfig;
};
