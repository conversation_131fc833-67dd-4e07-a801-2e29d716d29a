import { describe, expect, it } from 'vitest';
import { BaseMenuConfig } from '../../../types';
import { ActiveSearchesCounterAdapter } from './ActiveSearchesCounterContext';
import { act, render, screen } from '@testing-library/react';
import { UPDATE_STATS } from '../../../events/inputTypes';
import { CountersProvider } from './CountersContext';

const menuStub: BaseMenuConfig = {
  item: {
    label: 'Test Menu',
  },
};

const TestComponent = () => {
  return (
    <ActiveSearchesCounterAdapter menu={menuStub}>
      {(menu) => <>{menu.item?.notificationBadge?.number}</>}
    </ActiveSearchesCounterAdapter>
  );
};

describe('ActiveSearchesCounterContext', () => {
  it('Shows active searches counter', () => {
    render(<TestComponent />, {
      wrapper({ children }) {
        return <CountersProvider>{children}</CountersProvider>;
      },
    });

    // Display counter
    act(() => {
      window.dispatchEvent(
        new CustomEvent(UPDATE_STATS, {
          detail: { newMatches: 10, unreadThreads: 5, newAgencyActiveSearches: 12 },
        }),
      );
    });

    const textElement = screen.getByText('12');

    // Clear counter
    act(() => {
      window.dispatchEvent(
        new CustomEvent(UPDATE_STATS, {
          detail: { newMatches: 0, unreadThreads: 5, newAgencyActiveSearches: 0 },
        }),
      );
    });

    expect(textElement).toHaveTextContent('');

    // Show more then 100 counter
    act(() => {
      window.dispatchEvent(
        new CustomEvent(UPDATE_STATS, {
          detail: { newMatches: 90, unreadThreads: 5, newAgencyActiveSearches: 100 },
        }),
      );
    });

    expect(textElement).toHaveTextContent('99+');
  });
});
