import { AttachmentTag } from '../../../js/commons/gtx-react/components/AttachmentTag/AttachmentTag';
import type { Meta, StoryObj } from '@storybook/react';

/**
    The Action List is a list component, the items of the list can contain icons or other elements.
    Action list are almost always wrapped with a `gx-dropdown`.
   */
const meta: Meta<typeof AttachmentTag> = {
    title: 'Components/AttachmentTag',
    component: AttachmentTag,
    parameters: {
        componentSubtitle: 'Component',
        componentTitle: 'AttachmentTag',
        componentSlug: 'attachment-tag',
        componentImport: 'AttachmentTag',
        packageName: 'gtx-react/components/AttachmentTag/AttachmentTag',
    },
    tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof AttachmentTag>;

export const DefaultAttachmentTag: Story = {
    render: (args) => {
        return <AttachmentTag type="photo" />;
    },
};

DefaultAttachmentTag.storyName = 'Default';

export const WithCounterAttachmentTag: Story = {
    render: (args) => {
        return <AttachmentTag type="video" count={3} />;
    },
};

WithCounterAttachmentTag.storyName = 'With Counter';
