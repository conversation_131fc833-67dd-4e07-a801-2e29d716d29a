framework:
  secret: '%secret%'
  #default_locale: en
  #csrf_protection: true
  http_method_override: true
  # Enables session support. Note that the session will ONLY be started if you read or write from it.
  # Remove or comment this section to explicitly disable session support.
  session:
    handler_id: Symfony\Component\HttpFoundation\Session\Storage\Handler\MemcachedSessionHandler
    name: "%app.cookie_session_name%"
    cookie_domain: "%app.cookie_session_domain%"
    cookie_secure: true
  #   save_path: /var/lib/php/sessions
  templating:
    engines: ['twig']
  #esi: true
  #fragments:
  assets:
    json_manifest_path: "%kernel.project_dir%/public/bundles/manifest.json"
    packages:
      v_asset:
        json_manifest_path: "%kernel.project_dir%/public/bundles/manifest.json"
      nav_asset:
        json_manifest_path: "%kernel.project_dir%/public/bundles/gx-navigation/nav-manifest.json"
  php_errors:
    log: true
  form:            ~
  csrf_protection: ~
  validation:      { enable_annotations: true }
  serializer: { enable_annotations: true }
  cache:
    pools:
      cache.growthbook:
        adapter: cache.adapter.filesystem
        default_lifetime: 0
