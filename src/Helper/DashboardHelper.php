<?php

namespace App\Helper;

use App\Component\FeatureToggle\Exception\FeatureNotExistException;
use App\Component\FeatureToggle\Handler\FeatureToggleHandler;
use App\Component\Module\Authorizer\AuthorizerRegistry;
use App\Constants\Base\DashboardConstants;
use App\Constants\Base\GtxConstants;
use App\Constants\Base\LanguagesConstants;
use App\Constants\Base\PropertyConstants;
use App\Entity\Agency;
use App\Exception\AgencyNotAvailableException;
use App\Exception\ApiException;
use App\Handler\Agency\AgencyHandler;
use App\Helper\Adapter\AuthenticatedAdApiClientHelper;
use App\Helper\Adapter\AuthenticatedAgencyApiClientHelper;
use App\Helper\Adapter\AuthenticatedAgencyLeadApiClientHelper;
use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;
use App\Model\Agency\AdvertisingSpaces;
use App\Model\Dashboard\AdsByCategoryCount;
use App\Model\Dashboard\AdsByCategoryGroups;
use App\Model\Dashboard\AgencyLeadsStats;
use App\Service\Base\Session\GetrixInterface as GetrixSessionInterface;
use GetrixBe\GrowthbookBundle\GrowthbookProxy;
use Sentry\SentryBundle\SentrySymfonyClient;
use Symfony\Component\Translation\TranslatorInterface;

class DashboardHelper
{
    private TranslatorInterface $translator;
    private FeatureToggleHandler $featureToggleHandler;
    private AuthorizerRegistry $authorizerRegistry;
    private GetrixSessionInterface $getrixSession;
    private AuthenticatedAdApiClientHelper $adApiClientHelper;
    private AuthenticatedAgencyApiClientHelper $agencyApiClientHelper;
    private AuthenticatedAgencyLeadApiClientHelper $agencyLeadApiClientHelper;
    private ParametersBag $parametersBag;
    private HttpClientHelper $httpClientHelper;
    private AppProductsHelper $appProductsHelper;
    private AgencyHandler $agencyHandler;
    private SentrySymfonyClient $sentrySymfonyClient;
    private GrowthbookProxy $growthbookProxy;
    private array $adsStatsCategoriesAllowed = [];

    public function __construct(
        TranslatorInterface $translator,
        FeatureToggleHandler $featureToggleHandler,
        AuthorizerRegistry $authorizerRegistry,
        GetrixSessionInterface $getrixSession,
        AuthenticatedAdApiClientHelper $adApiClientHelper,
        AuthenticatedAgencyApiClientHelper $agencyApiClientHelper,
        AuthenticatedAgencyLeadApiClientHelper $authenticatedAgencyLeadApiClientHelper,
        ParametersBag $parametersBag,
        HttpClientHelper $httpClientHelper,
        AppProductsHelper $appProductsHelper,
        AgencyHandler $agencyHandler,
        GrowthbookProxy $growthbookProxy,
        SentrySymfonyClient $sentrySymfonyClient,
        array $adsStatsCategoriesAllowed
    ) {
        $this->translator = $translator;
        $this->featureToggleHandler = $featureToggleHandler;
        $this->authorizerRegistry = $authorizerRegistry;
        $this->getrixSession = $getrixSession;
        $this->adApiClientHelper = $adApiClientHelper;
        $this->agencyApiClientHelper = $agencyApiClientHelper;
        $this->agencyLeadApiClientHelper = $authenticatedAgencyLeadApiClientHelper;
        $this->parametersBag = $parametersBag;
        $this->httpClientHelper = $httpClientHelper;
        $this->appProductsHelper = $appProductsHelper;
        $this->agencyHandler = $agencyHandler;
        $this->adsStatsCategoriesAllowed = $adsStatsCategoriesAllowed;
        $this->growthbookProxy = $growthbookProxy;
        $this->sentrySymfonyClient = $sentrySymfonyClient;
    }

    /**
     * @throws AgencyNotAvailableException
     * @throws ApiException
     * @throws FeatureNotExistException
     */
    public function loadDashboardData($request): array
    {
        $agency = $this->getrixSession->getAgency();

        $adsByCategoryGroups = $this->filterAdsStats($this->getAdsByCategoryGroups());

        $hasPropertyListExtension = $agency->hasExtensions(GtxConstants::EXTENSION_PROPERTY_LIST);

        $adsStats = $this->formatAdsStats($adsByCategoryGroups, $agency);

        $plafond = null !== $agency->contract ?
            $agency->contract->plafond :
            null;

        if (
            $this->featureToggleHandler->featureIsEnabled('news')
            && !$this->parametersBag->get('app.beamer_news')
        ) {
            $getNewsUrl = $this->parametersBag->get('getrix.get_news_content_url');
            $popupShownCookie = $request->cookies->get('popupShown');
            $newsPopup = $this->httpClientHelper->execGetCall($getNewsUrl, ['idAgenzia' => $agency->idAgenzia, 'popupShown' => $popupShownCookie]);
        }

        return [
            'hasPropertyListExtension' => $hasPropertyListExtension,
            'adsStats' => $adsStats,
            'jsonAdsStats' => $this->getJsonAdStatsData($adsStats),
            'publicationStats' => $this->getPublicationStats(),
            'contactsStats' => $this->getContactsStats(),
            'contractExtra' => $this->getContractExtraFormattedData($agency->opzionePremium, $plafond),
            'newsPopup' => !empty($newsPopup) ? $newsPopup : null,
        ];
    }

    private function filterAdsStats(AdsByCategoryGroups $adsByCategoryGroups): AdsByCategoryGroups
    {
        $archived = \array_filter($adsByCategoryGroups->getArchived(), function (AdsByCategoryCount $item) {
            return \in_array($item->getCategory()->getId(), $this->adsStatsCategoriesAllowed);
        });
        $adsByCategoryGroups->setArchived($archived);

        $active = \array_filter($adsByCategoryGroups->getActive(), function (AdsByCategoryCount $item) {
            return \in_array($item->getCategory()->getId(), $this->adsStatsCategoriesAllowed);
        });
        $adsByCategoryGroups->setActive($active);

        $published = \array_filter($adsByCategoryGroups->getPublished(), function (AdsByCategoryCount $item) {
            return \in_array($item->getCategory()->getId(), $this->adsStatsCategoriesAllowed);
        });
        $adsByCategoryGroups->setPublished($published);

        return $adsByCategoryGroups;
    }

    /**
     * @throws AgencyNotAvailableException
     * @throws ApiException
     */
    public function getPublicationStats(): array
    {
        $advertisingSpaces = $this->agencyApiClientHelper
            ->getAgencyAdvertisingSpacesDetails($this->getPublishedAdsStatuses());

        $eCommerceProducts = $this->agencyHandler->getEcommerceProducts();

        $agency = $this->getrixSession->getAgency();

        $agencyRanking = !empty($agency->ranking) && !empty($advertisingSpaces->getRankingAvg()) ?
            PropertiesHelper::calculateRankingPenaltyBalance($agency->ranking, $advertisingSpaces->getRankingAvg()) :
            $advertisingSpaces->getRankingAvg();

        $result = [
            'vendita' => [
                'usati' => null !== $advertisingSpaces->getSellAds() && null !== $advertisingSpaces->getSellAds()->getUsed() ?
                    $advertisingSpaces->getSellAds()->getUsed() :
                    0,
                'totali' => null !== $advertisingSpaces->getSellAds() && null !== $advertisingSpaces->getSellAds()->getTotal() ?
                    $advertisingSpaces->getSellAds()->getTotal() :
                    0,
            ],
            'affitto' => [
                'usati' => null !== $advertisingSpaces->getRentAds() && null !== $advertisingSpaces->getRentAds()->getUsed() ?
                    $advertisingSpaces->getRentAds()->getUsed() :
                    0,
                'totali' => null !== $advertisingSpaces->getRentAds() && null !== $advertisingSpaces->getRentAds()->getTotal() ?
                    $advertisingSpaces->getRentAds()->getTotal() :
                    0,
            ],
            'nuoveCostruzioni' => [
                'usati' => null !== $advertisingSpaces->getProjects() && null !== $advertisingSpaces->getProjects()->getUsed() ?
                    $advertisingSpaces->getProjects()->getUsed() :
                    0,
                'totali' => null !== $advertisingSpaces->getProjects() && null !== $advertisingSpaces->getProjects()->getTotal() ?
                    $advertisingSpaces->getProjects()->getTotal() :
                    0,
                'attivo' => $advertisingSpaces->getProjects() && null !== $advertisingSpaces->getProjects()->getActive() ?
                    $advertisingSpaces->getProjects()->getActive() :
                    null,
            ],
            'ranking' => $agencyRanking,
            'visibilita' => [],
        ];

        $result['premium'] = [
            'usati' => $result['vendita']['usati'] + $result['affitto']['usati'] + $result['nuoveCostruzioni']['usati'],
            'totali' => $result['vendita']['totali'] + $result['affitto']['totali'] + $result['nuoveCostruzioni']['totali'],
        ];

        /** @var AdvertisingSpaces $eCommerceProduct */
        foreach ($eCommerceProducts as $eCommerceProduct) {
            $name = $eCommerceProduct->getName();
            $result['visibilita'][$name] = [
                'service' => $eCommerceProduct->getService(),
                'usati' => null !== $eCommerceProduct->getUsed() ?
                    $eCommerceProduct->getUsed() :
                    0,
                'totali' => null !== $eCommerceProduct->getTotal() ?
                    $eCommerceProduct->getTotal() :
                    0,
                'attivo' => true === $eCommerceProduct->getActive(),
                'scadenza' => null !== $eCommerceProduct->getExpiration() ?
                    $eCommerceProduct->getExpiration()->format('d/m/Y') :
                    null,
            ];
        }

        return $result;
    }

    /**
     * @throws ApiException
     */
    public function getContactsStats(int $range = null): array
    {
        if (null === $range) {
            $range = DashboardConstants::CONTACTS_STATS_RANGE;
        }

        try {
            $agencyLeadsStats = $this->agencyLeadApiClientHelper->getAgencyStats($range);

            return $this->getLeads($agencyLeadsStats);
        } catch (ApiException $e) {
            $this->sentrySymfonyClient->captureException($e);
        }

        return [];
    }

    private function getAdsByCategoryGroups(): AdsByCategoryGroups
    {
        $ads = $this->adApiClientHelper
            ->getAdsByCategoryGroups($this->getAdsActiveStatuses(), $this->getAdsArchivedStatuses());

        return \App\Builder\Model\Dashboard\AdsByCategoryGroupsBuilder::newBuilder()
            ->withActive($ads->getActive())
            ->withPublished($ads->getPublished())
            ->withArchived($ads->getArchived())
            ->build();
    }

    private function formatAdsStats(AdsByCategoryGroups $adsByCategoryGroups, Agency $agency): array
    {
        $hasPropertyListExtension = $agency->hasExtensions(GtxConstants::EXTENSION_PROPERTY_LIST);

        $initStatsByStatusPlaceholders = function (array &$result) use ($agency) {
            foreach (DashboardConstants::CATEGORIES_FEATURE_NAMES as $categoryId => $featureToggleName) {
                if ((true !== $agency->visibilitaProgetti && GtxConstants::CATEGORIA_PROGETTI === $categoryId) || (GtxConstants::CATEGORIA_PROGETTI === $categoryId && !$this->featureToggleHandler->featureIsEnabled('portal_new_constructions_property'))) {
                    continue;
                }

                /**
                 * Skip categories not allowed for ads stats
                 */
                if (!\in_array($categoryId, $this->adsStatsCategoriesAllowed)) {
                    continue;
                }

                $result[$categoryId] = [
                    'categoryId' => $categoryId,
                    'totale' => 0,
                    'link' => '',
                    'feature' => $featureToggleName,
                    'venditaAffittoAttivo' => false,
                ];

                if (\in_array($categoryId, DashboardConstants::CATEGORIES_VENDITA_AFFITTO)) {
                    $result[$categoryId]['venditaAffittoAttivo'] = true;
                    $result[$categoryId]['vendita'] = 0;
                    $result[$categoryId]['affitto'] = 0;
                }
            }
        };

        $processAdsByCategoryCount = function (array &$result, AdsByCategoryCount $adsByCategoryCount, string $status, bool $hasPropertyListExtension) {
            $categoryId = null !== $adsByCategoryCount->getCategory() && null !== $adsByCategoryCount->getCategory()->getId() ?
                $adsByCategoryCount->getCategory()->getId() :
                null;

            if (null === $categoryId || !\array_key_exists($categoryId, $result)) {
                return;
            }

            if (!$this->featureToggleHandler->featureIsEnabled($result[$categoryId]['feature'])) {
                unset($result[$categoryId]);

                return;
            }

            $result[$categoryId]['totale'] = $adsByCategoryCount->getTotal();
            $result[$categoryId]['link'] = $this->growthbookProxy->IsOn('crm_properties_list') ? $this->getUnifiedListLink($categoryId, $status) : $this->getListLink($categoryId, $status, $hasPropertyListExtension);
            $result[$categoryId]['linkVendita'] = null;
            $result[$categoryId]['linkAffitto'] = null;
            if (isset($result[$categoryId]['vendita']) && !empty($adsByCategoryCount->getSell())) {
                $result[$categoryId]['vendita'] = $adsByCategoryCount->getSell();
                $result[$categoryId]['linkVendita'] = $this->growthbookProxy->IsOn('crm_properties_list') ? $this->getUnifiedListSellLink($result[$categoryId]['link'], $hasPropertyListExtension) : $this->getSellLink($result[$categoryId]['link'], $hasPropertyListExtension);
            }
            if (isset($result[$categoryId]['affitto']) && !empty($adsByCategoryCount->getRent())) {
                $result[$categoryId]['affitto'] = $adsByCategoryCount->getRent();
                $result[$categoryId]['linkAffitto'] = $this->growthbookProxy->IsOn('crm_properties_list') ? $this->getUnifiedListRentLink($result[$categoryId]['link'], $hasPropertyListExtension) : $this->getRentLink($result[$categoryId]['link'], $hasPropertyListExtension);
            }

            $result[$categoryId]['transLabel'] = $this->translator->trans('db_category.id_' . $categoryId, [], LanguagesConstants::DOMAIN_CONTENTS);
        };

        $activeAllData = [];
        $initStatsByStatusPlaceholders($activeAllData);
        foreach ($adsByCategoryGroups->getActive() as $adsByCategoryCount) {
            $processAdsByCategoryCount($activeAllData, $adsByCategoryCount, 'pubblicati', $hasPropertyListExtension);
        }

        $activePublishedData = [];
        $initStatsByStatusPlaceholders($activePublishedData);
        foreach ($adsByCategoryGroups->getPublished() as $adsByCategoryCount) {
            $processAdsByCategoryCount($activePublishedData, $adsByCategoryCount, 'pubblicita', $hasPropertyListExtension);
        }

        $archivedAllData = [];
        $initStatsByStatusPlaceholders($archivedAllData);
        foreach ($adsByCategoryGroups->getArchived() as $adsByCategoryCount) {
            $processAdsByCategoryCount($archivedAllData, $adsByCategoryCount, 'archiviati', $hasPropertyListExtension);
        }

        return [
            'active-all' => [
                'data' => \array_values($activeAllData),
                'chart' => $this->getAdsStatsSectionChart($activeAllData, \strtolower($this->translator->trans('label.available_plural', [], LanguagesConstants::DOMAIN_CMS))),
            ],
            'active-published' => [
                'data' => \array_values($activePublishedData),
                'chart' => $this->getAdsStatsSectionChart($activePublishedData, \strtolower($this->translator->trans('label.in_advertising', [], LanguagesConstants::DOMAIN_CMS))),
            ],
            'archived-all' => [
                'data' => \array_values($archivedAllData),
                'chart' => $this->getAdsStatsSectionChart($archivedAllData, \strtolower($this->translator->trans('label.archived_plural', [], LanguagesConstants::DOMAIN_CMS))),
            ],
        ];
    }

    protected function getSellLink($categoryId, $hasPropertyListExtension)
    {
        return $hasPropertyListExtension || !$this->featureToggleHandler->featureIsEnabled('portal_properties')
            ? \sprintf('%s&idContratto=%s', $categoryId, GtxConstants::CONTRATTO_VENDITA)
            : \sprintf('%s&contract=%s', $categoryId, GtxConstants::CONTRATTO_VENDITA);
    }

    protected function getRentLink($categoryId, $hasPropertyListExtension)
    {
        return $hasPropertyListExtension || !$this->featureToggleHandler->featureIsEnabled('portal_properties')
            ? \sprintf('%s&idContratto=%s', $categoryId, GtxConstants::CONTRATTO_AFFITTO)
            : \sprintf('%s&contract=%s', $categoryId, GtxConstants::CONTRATTO_AFFITTO);
    }

    /**
     * @throws FeatureNotExistException
     */
    protected function getListLink($categoryId, $status, $hasPropertyListExtension): string
    {
        $endpoint = 'annunci_agenzia.php';

        if (!$this->featureToggleHandler->featureIsEnabled('portal_properties')) {
            $endpoint = 'annunci_portale.php';
        } elseif (!$hasPropertyListExtension) {
            $endpoint = 'immobili/portale/lista';
        }

        $endpointNc = $hasPropertyListExtension ? 'v2/nuove-costruzioni/lista' : 'nuove-costruzioni/portale/lista';
        $endpointAuction = $hasPropertyListExtension ? 'aste.php' : 'immobili/aste/portale/lista';
        $statusParameter = [
            'pubblicati' => '',
            'pubblicita' => 'premium=1',
            'archiviati' => $hasPropertyListExtension ? 'stato=archivio' : 'status=archived',
        ];

        if (GtxConstants::CATEGORIA_PROGETTI == $categoryId) {
            $link = $endpointNc;
            $link .= !empty($statusParameter[$status]) ? '?' . $statusParameter[$status] : '';
        } elseif (GtxConstants::CATEGORIA_ASTE == $categoryId && $this->featureToggleHandler->featureIsEnabled('auctions_ads')) {
            $link = $endpointAuction;
            $link .= !empty($statusParameter[$status]) ? '?' . $statusParameter[$status] : '';
        } else {
            $link = $hasPropertyListExtension || !$this->featureToggleHandler->featureIsEnabled('portal_properties')
                ? \sprintf('/%s?idCategoria=%s%s', $endpoint, $categoryId, !empty($statusParameter[$status]) ? '&' . $statusParameter[$status] : '')
                : \sprintf('/%s?category=%s%s', $endpoint, $categoryId, !empty($statusParameter[$status]) ? '&' . $statusParameter[$status] : '');
        }

        return $link;
    }

    /**
     * @throws FeatureNotExistException
     */
    protected function getUnifiedListLink($categoryId, $status): string
    {
        $endpoint = 'immobili/lista';

        if ('archiviati' === $status) {
            $endpoint = \sprintf('%s/%s', $endpoint, 'archiviati');
        }

        $statusParameter = [
            'pubblicita' => 'visibility=searchable',
        ];

        if (GtxConstants::CATEGORIA_PROGETTI == $categoryId) {
            $link = $endpoint . '?typology=new_construction';
            $link .= !empty($statusParameter[$status]) ? '&' . $statusParameter[$status] : '';
        } elseif (GtxConstants::CATEGORIA_ASTE == $categoryId && $this->featureToggleHandler->featureIsEnabled('auctions_ads')) {
            $link = $endpoint . '?typology=auction';
            $link .= !empty($statusParameter[$status]) ? '&' . $statusParameter[$status] : '';
        } else {
            $link = \sprintf('/%s?category=%s%s', $endpoint, $categoryId, !empty($statusParameter[$status]) ? '&' . $statusParameter[$status] : '');
        }

        return $link;
    }

    protected function getUnifiedListSellLink($categoryId)
    {
        return \sprintf('%s&contract=%s', $categoryId, GtxConstants::CONTRATTO_VENDITA);
    }

    protected function getUnifiedListRentLink($categoryId)
    {
        return \sprintf('%s&contract=%s', $categoryId, GtxConstants::CONTRATTO_AFFITTO);
    }

    protected function getAdsStatsSectionChart($data, $tipoAnnunci)
    {
        $result = [];
        $totale = 0;

        if (!empty($data)) {
            $result['data'] = [];
            $totale = \array_reduce($data, function ($carry, $item) {
                return $carry + $item['totale'];
            });

            foreach ($data as $categoria => $item) {
                $result['data'][] = [
                    'value' => $item['totale'],
                    'percentage' => $totale > 0 ? ($item['totale'] * 100) / $totale : 0,
                    'name' => DashboardConstants::CATEGORIES_FEATURE_NAMES[$categoria],
                ];
            }
        }

        $result['content'] = [
            'nAds' => ['value' => $totale, 'color' => DashboardConstants::COLORS['AdsStatsChart']['nAds']],
            'firstLine' => ['value' => $this->translator->trans('label.ads', [], LanguagesConstants::DOMAIN_CMS), 'color' => DashboardConstants::COLORS['AdsStatsChart']['firstLine']],
            'secondLine' => ['value' => $tipoAnnunci, 'color' => DashboardConstants::COLORS['AdsStatsChart']['secondLine']],
        ];

        return \json_encode($result);
    }

    protected function getContractExtraFormattedData($visibilita, $borsellino): array
    {
        $output = [
            'idVisibilita' => !empty($visibilita) ? $visibilita : null,
            'visibilita' => !empty($visibilita) && !empty(GtxConstants::$agencyVisibilityLabel[$visibilita]) ? $this->translator->trans(GtxConstants::$agencyVisibilityLabel[$visibilita], [], LanguagesConstants::DOMAIN_CMS) : $this->translator->trans(GtxConstants::$agencyVisibilityLabel[0], [], LanguagesConstants::DOMAIN_CMS),
        ];

        if ($this->parametersBag->get('app.digital_wallet_enabled')) {
            $output['borsellino'] = !empty($borsellino) ? \number_format($borsellino, 2, ',', '.') : '0,00';
        }

        return $output;
    }

    protected function getJsonAdStatsData($data)
    {
        $result = \array_map(function ($values) {
            return [
                'chart' => $values['chart'],
                'data' => \array_values($values['data']),
            ];
        }, $data);

        return \json_encode($result);
    }

    protected function getAdsActiveStatuses(): array
    {
        $hasGetrixEnabled = $this->authorizerRegistry
            ->get(GtxConstants::GETRIX_MODULE_NAME)
            ->moduleIsEnabled(GtxConstants::GETRIX_MODULE_NAME);

        $activeStatuses = [
            PropertyConstants::STATUS_IMMOBILIARE_ACTIVE,
            PropertyConstants::STATUS_IMMOBILIARE_SECRET,
        ];

        return $hasGetrixEnabled
            ? \array_merge($activeStatuses, [PropertyConstants::STATUS_GETRIX_ACTIVE])
            : $activeStatuses;
    }

    protected function getPublishedAdsStatuses(): array
    {
        return [
            PropertyConstants::STATUS_IMMOBILIARE_ACTIVE,
        ];
    }

    protected function getAdsArchivedStatuses(): array
    {
        $hasGetrixEnabled = $this->authorizerRegistry
            ->get(GtxConstants::GETRIX_MODULE_NAME)
            ->moduleIsEnabled(GtxConstants::GETRIX_MODULE_NAME);

        return $hasGetrixEnabled ?
            PropertyConstants::GETRIX_ADS_ARCHIVED_STATUSES :
            PropertyConstants::PRO_ADS_ARCHIVED_STATUSES;
    }

    public function getLeads(AgencyLeadsStats $agencyLeadsStats): array
    {
        $hasVox = null !== $agencyLeadsStats->getVoxContacts();

        $data = [
            'visite' => null !== $agencyLeadsStats->getVisits() ?
                $agencyLeadsStats->getVisits() :
                0,
            'telefonate' => null !== $agencyLeadsStats->getPhoneCalls() ?
                $agencyLeadsStats->getPhoneCalls() :
                0,
            'specifiche' => null !== $agencyLeadsStats->getLeadTypes() && null !== $agencyLeadsStats->getLeadTypes()->getSpecific() ?
                $agencyLeadsStats->getLeadTypes()->getSpecific() :
                0,
            'generiche' => null !== $agencyLeadsStats->getLeadTypes() && null !== $agencyLeadsStats->getLeadTypes()->getGeneric() ?
                $agencyLeadsStats->getLeadTypes()->getGeneric() :
                0,
            'telefonateSmart' => [
                'label' => $this->appProductsHelper->getPhoneCallsContactsLabel(),
                'attivo' => false,
                'telefonate' => 0,
            ],
        ];

        if ($hasVox) {
            $data['telefonateImmovox'] = [
                'attivo' => true,
                'telefonate' => $hasVox ? $agencyLeadsStats->getVoxContacts() : 0,
            ];
        }

        return $data;
    }
}
