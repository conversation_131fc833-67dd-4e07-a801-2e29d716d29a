parameters:
  thrifter_rpc_services: config/%env(COUNTRY_TAG)%/sdk/rpc_services_staging.yml
  immobiliare.responsabile_avatar_sapi_url: '%env(GETRIX_MEDIA_URL)%/image'

  app.youdomusEndpoint: 'https://youdomus-test.immobiliare.it'

  app.agency.evaluation_zone.only_getrix: false

  sapi.config:
    staging:
      services.getrix.getrix.annunci.immagini:
        baseUrl: "http://sapi-raw01-staging.vrt.imm.rm.ns.farm:9977/"
        rootPath: "/storage/immobiliare/images/"
        auth: "gtx-stag"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.getrix.annunci.planimetrie:
        baseUrl: "http://sapi-raw01-staging.vrt.imm.rm.ns.farm:9977/"
        rootPath: "/storage/immobiliare/plans/"
        auth: "gtx-stag"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.agenzie:
        baseUrl: "http://sapi-raw01-staging.vrt.imm.rm.ns.farm:9977/"
        rootPath: "/storage/immobiliare/images"
        auth: "gtx-stag"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.agenti:
        baseUrl: "http://sapi-raw01-staging.vrt.imm.rm.ns.farm:9977/"
        rootPath: "/storage/immobiliare/profiles/agents/images"
        auth: "gtx-stag"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.immobiliare.media:
        baseUrl: "http://sapi-raw01-staging.vrt.imm.rm.ns.farm:9977/"
        rootPath: "/storage/immobiliare/documents"
        auth: "gtx-stag"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
