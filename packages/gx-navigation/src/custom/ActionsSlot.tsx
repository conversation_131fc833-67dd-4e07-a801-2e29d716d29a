import { ComponentProps } from 'react';
import { CustomComponent } from './CustomComponent';

type ActionsSlotProps = {
  slots: ComponentProps<typeof CustomComponent>[];
};

export const ActionsSlot = (props: ActionsSlotProps) => {
  return (
    <div className='gx-navigation-header__actions'>
      {props.slots.map((action, index) => (
        <CustomComponent key={index} {...action} />
      ))}
    </div>
  );
};
