<?php

declare(strict_types=1);

namespace App\Controller\EventsTracker;

use App\Annotation\TrustedRequest;
use App\Handler\EventsTracker\LandingPageEventsTrackerHandler;
use App\Response\ErrorResponse;
use App\Response\SuccessResponse;
use App\Utils\SessionUtils;
use Ekbl\StatsBundle\Annotation\Stats;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * @Stats
 */
class LandingPageEventsTrackerController extends Controller
{
    use EventsTrackerTrait;

    /**
     * @var SessionUtils
     */
    private $sessionUtils;

    /**
     * @var LandingPageEventsTrackerHandler
     */
    private $landingPageEventsTrackerHandler;

    public function __construct(
        SessionUtils $sessionUtils,
        LandingPageEventsTrackerHandler $landingPageEventsTrackerHandler
    ) {
        $this->sessionUtils                    = $sessionUtils;
        $this->landingPageEventsTrackerHandler = $landingPageEventsTrackerHandler;
    }

    /**
     * @Route("/landing/{token}/track/",
     *     methods={"POST"},
     *     name="app_landing_page_events_tracker"
     * )
     * @TrustedRequest()
     */
    public function index(
        Request $request,
        string $token
    ): Response {
        try {
            $this->checkEventsTrackingToggle(
                $this->getParameter('app.user_events_tracking_toggle')
            );

            if ($this->getParameter('app.mixpanel_skip_backoffice') && $this->sessionUtils->getUser()->hasRole('ROLE_USER_BACKOFFICE')) {
                return SuccessResponse::ok(true);
            }

            $result = $this->landingPageEventsTrackerHandler->track(
                $token,
                $request->getClientIp(),
                json_decode(base64_decode($request->request->get('data')), true)
            );

            return new JsonResponse($result);
        } catch (\Exception $e) {
            return ErrorResponse::build($e->getMessage(), $e->getCode());
        }
    }
}
