<?php

declare(strict_types=1);

namespace App\Model\Property;

class CadastralData
{
    private ?int $id = null;
    private ?CadastralClass $cadastralClass = null;
    private ?CadastralType $cadastralType = null;
    private ?CadastralDataAddress $address = null;
    private ?string $section = null;
    private ?string $sheet = null;
    private ?string $parcel = null;
    private ?string $subordinate = null;
    private ?string $stapledSheet = null;
    private ?string $stapledParcel = null;
    private ?string $stapledSubordinate = null;
    private ?float $cadastralIncome = null;
    private ?string $otherCadastralData = null;
    private ?string $ownershipShares = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getCadastralClass(): ?CadastralClass
    {
        return $this->cadastralClass;
    }

    public function setCadastralClass(?CadastralClass $cadastralClass): void
    {
        $this->cadastralClass = $cadastralClass;
    }

    public function getCadastralType(): ?CadastralType
    {
        return $this->cadastralType;
    }

    public function setCadastralType(?CadastralType $cadastralType): void
    {
        $this->cadastralType = $cadastralType;
    }

    public function getAddress(): ?CadastralDataAddress
    {
        return $this->address;
    }

    public function setAddress(?CadastralDataAddress $address): void
    {
        $this->address = $address;
    }

    public function getSection(): ?string
    {
        return $this->section;
    }

    public function setSection(?string $section): void
    {
        $this->section = $section;
    }

    public function getSheet(): ?string
    {
        return $this->sheet;
    }

    public function setSheet(?string $sheet): void
    {
        $this->sheet = $sheet;
    }

    public function getParcel(): ?string
    {
        return $this->parcel;
    }

    public function setParcel(?string $parcel): void
    {
        $this->parcel = $parcel;
    }

    public function getSubordinate(): ?string
    {
        return $this->subordinate;
    }

    public function setSubordinate(?string $subordinate): void
    {
        $this->subordinate = $subordinate;
    }

    public function getStapledSheet(): ?string
    {
        return $this->stapledSheet;
    }

    public function setStapledSheet(?string $stapledSheet): void
    {
        $this->stapledSheet = $stapledSheet;
    }

    public function getStapledParcel(): ?string
    {
        return $this->stapledParcel;
    }

    public function setStapledParcel(?string $stapledParcel): void
    {
        $this->stapledParcel = $stapledParcel;
    }

    public function getStapledSubordinate(): ?string
    {
        return $this->stapledSubordinate;
    }

    public function setStapledSubordinate(?string $stapledSubordinate): void
    {
        $this->stapledSubordinate = $stapledSubordinate;
    }

    public function getCadastralIncome(): ?float
    {
        return $this->cadastralIncome;
    }

    public function setCadastralIncome(?float $cadastralIncome): void
    {
        $this->cadastralIncome = $cadastralIncome;
    }

    public function getOtherCadastralData(): ?string
    {
        return $this->otherCadastralData;
    }

    public function setOtherCadastralData(?string $otherCadastralData): void
    {
        $this->otherCadastralData = $otherCadastralData;
    }

    public function getOwnershipShares(): ?string
    {
        return $this->ownershipShares;
    }

    public function setOwnershipShares(?string $ownershipShares): void
    {
        $this->ownershipShares = $ownershipShares;
    }
}
