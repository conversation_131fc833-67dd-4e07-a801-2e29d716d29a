<?php

declare(strict_types=1);

namespace App\Handler\Property;

use App\Constants\Base\GtxConstants;
use App\DataProvider\Property\AuthenticatedPropertyInterface;
use App\Handler\Agency\AgencyHandler;
use App\Utils\TranslatorTrait;
use Symfony\Component\Translation\TranslatorInterface;

class ExportHandler
{
    use TranslatorTrait;

    private AgencyHandler $agencyHandler;
    private AuthenticatedPropertyInterface $authenticatedProperty;
    private TranslatorInterface $translator;

    public function __construct(
        AgencyHandler $agencyHandler,
        AuthenticatedPropertyInterface $authenticatedProperty,
        TranslatorInterface $translator
    ) {
        $this->agencyHandler = $agencyHandler;
        $this->authenticatedProperty = $authenticatedProperty;
        $this->translator = $translator;
    }

    public function exportAds(int $offset, int $limit): array
    {
        return $this->authenticatedProperty->exportAds([
            'adStatusIds' => \implode(',', $this->agencyHandler->isPro()
                ? GtxConstants::AD_STATUS_PRO
                : GtxConstants::AD_STATUS),
            'categories' => \implode(',', GtxConstants::CATEGORIES_USING_IMMOBILE),
            'typologyIds' => \implode(',', GtxConstants::TIPOLOGIES_ACTIVE_VERSION),
            'offset' => $offset,
            'limit' => $limit,
        ]);
    }

    public function exportAdsHeader(bool $isPro): array
    {
        $header = [
            $this->translateCms('label.code'),
            $this->translateCms('label.id'),
            $this->translateCms('label.category'),
            $this->translateCms('label.typology'),
            $this->translateCms('label.contract'),
            $this->translateCms('label.surface'),
            $this->translateCms('label.price_range'),
            $this->translateCms('label.selling_price'),
            $this->translateCms('label.rent_canon'),
            $this->translateCms('label.state_property'),
            $this->translateCms('label.rooms'),
            $this->translateCms('label.bathrooms'),
            $this->translateCms('label.kitchen'),
            $this->translateCms('label.floor'),
            $this->translateCms('label.heating'),
            $this->translateCms('label.terrace'),
            $this->translateCms('label.box_auto'),
            $this->translateCms('label.region'),
            $this->translateCms('label.province'),
            $this->translateCms('label.municipality'),
            $this->translateCms('label.macrozone'),
            $this->translateCms('label.microzone'),
            $this->translateCms('label.postal_code'),
            $this->translateCms('label.address'),
            $this->translateCms('label.street_number_1'),
            $this->translateCms('label.virtual_tour'),
            $this->translateCms('label.description'),
            $this->translateCms('label.note_plural'),
        ];

        if ($isPro) {
            $header = \array_merge($header, [
                $this->translateCms('label.visit_plural'),
                $this->translateCms('label.saved_plural'),
                $this->translateCms('label.hidden_plural'),
                $this->translateCms('label.contacts'),
                $this->translateCms('product.premium'),
                $this->translateCms('label.extra_visibility_vetrina'),
                $this->translateCms('label.extra_visibility_star'),
                $this->translateCms('label.extra_visibility_top'),
                $this->translateCms('label.guaranteed_property'),
                $this->translateCms('label.quality_index'),
            ]);
        }

        $header = \array_merge($header, [
            $this->translateCms('label.insertion_date'),
            $this->translateCms('label.last_modification_date'),
            $this->translateCms('label.type_of_business'),
            $this->translateCms('label.what_to_sell_or_rent'),
            $this->translateCms('label.building_use'),
            $this->translateCms('label.ad_status'),
        ]);

        if ($isPro) {
            $header = \array_merge($header, [
                $this->translateCms('label.position_search_ad'),
            ]);
        }

        return \array_merge($header, [
            $this->translateCms('label.agent'),
        ]);
    }
}
