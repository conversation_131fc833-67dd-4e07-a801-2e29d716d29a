<!doctype html>
<html lang="it">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1">
    <title>{% if title %}{{ title }}{% else %}{{ 'label.service_temporarily_unavailable'|trans({}, gtxConstants.DOMAIN_CMS) }}{% endif %}  - {{ gtxConstants.APP_TITLE }}</title>
    <link href="https://fonts.googleapis.com/css?family=Raleway" rel="stylesheet">
    <style>
        /* RESET */
        html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline; box-sizing: border-box;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block}body{line-height:1}ol,ul{list-style:none}blockquote,q{quotes:none}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none}table{border-collapse:collapse;border-spacing:0}
        /* SORRY */
        body {
            background: #ffffff;
            font-family: Arial, sans-serif;
        }

        .sorry-box {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            border-radius: 2px;
            text-align: center;
        }

        .logo {
            margin: 0 auto 30px;
        }

        .logo img {
            width: 240px;
            height: 42px;
            object-fit: contain;
            object-position: center top;
        }

        h1 {
            font-family: 'Raleway', Arial, sans-serif;
            font-weight: normal;
            font-size: 20px;
            line-height: 1.2;
            color: #A38300;
            padding: 20px;
            margin: 0 0 24px;
            background: #FBF3D3;
            border: 1px solid #EEE6C6;
            border-radius: 2px;
        }

        p {
            margin: 0 30px 32px;
            font-size: 16px;
            color: #666666;
            line-height: 24px;
        }

        .btn {
            width: 100%;
            display: inline-block;
            padding: 13px 20px;
            background: #e50013 ;
            border-radius: 2px;
            font-size: 14px;
            color: #FFFFFF;
            text-decoration: none;
        }

        @media screen and (min-width: 768px) {
            .sorry-box {
                margin: 0;
                width: 632px;
            }

            .logo img {
                width: 272px;
                height: 47px;
            }

            h1 {
                padding: 30px;
                font-size: 22px;
            }

            .btn {
                width: auto;
            }
        }

        @media screen and (max-height: 567px) {
            .sorry-box {
                position: static;
                top: 0;
                left: 0;
                transform: translate(0, 0);
                margin: 30px auto;
            }
        }
    </style>
</head>
<body>

<div class="sorry-box">
    <div class="logo">
        <img src="{{ asset('base/getrix/common/img/country/' ~ gtxConstants.COUNTRY_TAG ~ '/logo/logo-pro.svg') }}" />
    </div>
    <h1>
        {% if title %}
            {{ title }}
        {% else %}
            {{ 'label.service_temporarily_unavailable'|trans({}, gtxConstants.DOMAIN_CMS) }}
        {% endif %}
    </h1>
    <p>
        {% if message %}
            {{ message }}
        {% else %}
            {{ 'service_unavailable.retry_2'|trans({}, gtxConstants.DOMAIN_CMS) }}
        {% endif %}
    </p>
    <a class="btn" href="{{ targetUrl }}">{{ 'label.retry'|trans({}, gtxConstants.DOMAIN_CMS) }}</a>
</div>

</body>
</html>
