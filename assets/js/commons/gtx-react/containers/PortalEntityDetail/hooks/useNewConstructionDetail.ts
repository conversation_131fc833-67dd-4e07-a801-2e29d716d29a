import { trans } from '@pepita-i18n/babelfish';
import { PROPERTY_FIELDS_DATA } from 'constants/propertyFieldsData';

const sectionsData = [
    /**
     * Main info
     */
    {
        name: 'mainInfos',
        title: trans('label.main_infos'),
        data: [
            'projectName',
            'proposer',
            'residentialUnits',
            'commercialUnits',
            'constructionStartDate',
            'expectedDeliveryDate',
            'parkingSpaces',
            'parkingSpacesInGarage',
            'agentName',
            'collaboration',
        ],
    },
    /**
     * Features
     */
    {
        name: 'features',
        title: trans('label.property_features'),
        data: [
            'projectHeating',
            'projectHeatingType',
            'projectHeatingSupply',
            'projectAirConditioning',
            'projectAirConditioningType',
            'projectExternalFixtures',
            'projectDisabledAccess',
            'projectCommonGarden',
            'projectBikeParking',
        ],
    },
    /**
     * Sales office
     */
    {
        name: 'salesOffice',
        title: trans('label.sales_office'),
        data: [
            'salesOfficeRegion',
            'salesOfficeProvince',
            'salesOfficeCity',
            'salesOfficeAddress',
            'salesOfficePhone',
            'salesOfficeMobilePhone',
        ],
    },
    /**
     * Energy class
     */
    {
        name: 'projectEnergyClass',
        title: trans('label.energy_certification'),
        data: ['projectEnergyClass'],
    },
    /**
     * New construction properties
     */
    {
        name: 'newConstructionProperties',
        title: trans('label.estates'),
        data: ['newConstructionProperties'],
    },
];

const useNewConstructionDetail = () => {
    return {
        sections: sectionsData.map((section) => ({
            ...section,
            data: section.data.map((item) => {
                return PROPERTY_FIELDS_DATA.find((field) => field.name === item);
            }),
        })),
    };
};

export default useNewConstructionDetail;
