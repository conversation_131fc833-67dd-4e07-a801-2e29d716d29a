import { NotifyProvider } from '@gx-design/snackbar';
import { getDecade } from 'date-fns';
import { HttpResponse, http } from 'msw';
import { describe, expect, it } from 'vitest';

import { render, screen } from '#tests/react/testing-library-enhanced';
import { server } from '#tests/vitest/setup';

import { EditProfileForm } from './EditProfileForm';
import profileNotAdminProps from './dummies/profile-not-admin-props.json';
import profileAdminProps from './dummies/profile-admin-props.json';
import {
    CHANGE_EMAIL_USER_ENDPOINT,
    DELETE_USER_ENDPOINT,
    UPDATE_USER_ENDPOINT,
} from './web-api/endpoints';
import { parseDate } from '../DatePicker';

const renderEditProfileForm = (data: any) =>
    render(
        <NotifyProvider>
            <EditProfileForm {...data} />
        </NotifyProvider>
    );

describe('EditProfileForm', () => {
    it('should prefill all input fields', async () => {
        renderEditProfileForm(profileAdminProps);
        const { initialValues, isMainAgent } = profileAdminProps;

        const nameInput = screen.getByRole('textbox', { name: /label.name/ });
        expect(nameInput).toBeInTheDocument();
        expect(nameInput).toHaveValue(initialValues.nome);

        const surnameInput = screen.getByRole('textbox', {
            name: /label.surname/,
        });
        expect(surnameInput).toBeInTheDocument();
        expect(surnameInput).toHaveValue(initialValues.cognome);

        const mailInput = screen.getByRole('textbox', { name: /label.mail/ });
        expect(mailInput).toBeInTheDocument();
        expect(mailInput).toHaveValue(initialValues.email);
        if (isMainAgent) {
            expect(mailInput).toBeDisabled();
        }

        const dateInput = screen.getByRole('textbox', {
            name: /label.date_of_birth/,
        });
        expect(dateInput).toBeInTheDocument();
        expect(dateInput).toHaveValue(initialValues.dataNascita);

        const maleRadioInput = screen.getByRole('radio', {
            name: /label.male/,
        });
        const femaleRadioInput = screen.getByRole('radio', {
            name: /label.female/,
        });
        expect(maleRadioInput).toBeInTheDocument();
        expect(maleRadioInput).toBeChecked();
        expect(femaleRadioInput).toBeInTheDocument();
        expect(femaleRadioInput).not.toBeChecked();

        const fiscalCodeInput = screen.getByRole('textbox', {
            name: /label.fiscal_code/,
        });
        expect(fiscalCodeInput).toBeInTheDocument();
        expect(fiscalCodeInput).toHaveValue(initialValues.codiceFiscale);

        const municipalityInput = screen.getByTestId('input-municipality');
        expect(municipalityInput).toBeInTheDocument();
        expect(municipalityInput).toHaveValue(initialValues.comune.name);

        const residenceAddressInput = screen.getByRole('textbox', {
            name: /label.residence_address/,
        });
        expect(residenceAddressInput).toBeInTheDocument();
        expect(residenceAddressInput).toHaveValue(initialValues.indirizzo);

        const postalCodeInput = screen.getByRole('textbox', {
            name: /label.postal_code/,
        });
        expect(postalCodeInput).toBeInTheDocument();
        expect(postalCodeInput).toHaveValue(initialValues.cap);

        // phone inputs
        const phoneWrapperInputs = screen.getAllByTestId('wrapper-phone-input');
        expect(phoneWrapperInputs?.length).toBe(initialValues.contatti.length);
        for (let i = 0; i < initialValues.contatti.length; i++) {
            const phoneInput = screen.getByTestId(`input-phone-${i}`);
            expect(phoneInput).toHaveValue(
                initialValues.contatti[i].fNumeroSenzaPrefisso
            );
            const prefix = screen.getByTestId(`select-phone-prefix-${i}`);
            expect(prefix).toContainHTML(initialValues.contatti[i].prefisso);
            const visible = screen.getByTestId(`check-phone-visible-${i}`);
            if (initialValues.contatti[i].pubblico) {
                expect(visible).toBeChecked();
            } else {
                expect(visible).not.toBeChecked();
            }
        }

        const reaCodeInput = screen.getByRole('textbox', {
            name: /label.rea_code/,
        });
        expect(reaCodeInput).toBeInTheDocument();
        expect(reaCodeInput).toHaveValue(initialValues.codiceRea);

        const associationInput = screen.getByRole('combobox', {
            name: /label.association/,
        });
        expect(associationInput).toBeInTheDocument();
        expect(associationInput).toHaveValue(initialValues.associazione);

        const vatInput = screen.getByRole('textbox', {
            name: /label.vat/,
        });
        expect(vatInput).toBeInTheDocument();
        expect(vatInput).toHaveValue('');

        const descriptionInput = screen.getByRole('textbox', {
            name: /label.description/,
        });
        const selectedLang = descriptionInput
            .getAttribute('name')
            ?.split('_')[1];
        // const bio =
        //     profileAdmin.user.profilo.biografia[
        //         profileAdmin.user.profilo.biografia.findIndex(
        //             (bio) => bio.lingua === selectedLang
        //         )
        //     ]?.testo;
        const bio = initialValues[`bio_${selectedLang}`];
        expect(descriptionInput).toBeInTheDocument();
        expect(descriptionInput).toHaveValue(bio);
    });
    it('should open change email modal on email input click', async () => {
        const { user } = renderEditProfileForm(profileNotAdminProps);

        const mailInput = screen.getByRole('textbox', { name: /label.mail/ });
        expect(mailInput).toBeInTheDocument();
        expect(mailInput).toBeEnabled();
        await user.click(mailInput);

        const changeEmailModalTitle = await screen.findByRole('heading', {
            name: /label.edit_email/,
        });
        expect(changeEmailModalTitle).toBeInTheDocument();
        const newEmailInput = screen.getByRole('textbox', {
            name: /label.new_email/,
        });
        expect(newEmailInput).toBeInTheDocument();
        const confirmNewEmailInput = screen.getByRole('textbox', {
            name: /label.confirm_new_email/,
        });
        expect(confirmNewEmailInput).toBeInTheDocument();
        const yourPasswordInput =
            await screen.findByLabelText(/label.your_password/);
        expect(yourPasswordInput).toBeInTheDocument();
        const sendVerificationButton = screen.getByRole('button', {
            name: /label.send_verification/,
        });
        expect(sendVerificationButton).toBeInTheDocument();
    });
    it('should be able to change email through the modal', async () => {
        // email hardcoded in fake json response into mocks folder
        const newEmail = '<EMAIL>';
        const { user } = renderEditProfileForm(profileNotAdminProps);

        const mailInput = screen.getByRole('textbox', { name: /label.mail/ });
        await user.click(mailInput);

        const newEmailInput = screen.getByRole('textbox', {
            name: /label.new_email/,
        });
        const confirmNewEmailInput = screen.getByRole('textbox', {
            name: /label.confirm_new_email/,
        });
        const yourPasswordInput =
            await screen.findByLabelText(/label.your_password/);
        const sendVerificationButton = screen.getByRole('button', {
            name: /label.send_verification/,
        });

        await user.type(
            newEmailInput,
            '{BackSpace>20/}{d}{e}{v}{e}{l}{@}{m}{a}{i}{l}{.}{i}{t}'
        );
        await user.type(
            confirmNewEmailInput,
            '{BackSpace>20/}{d}{e}{v}{e}{l}{@}{m}{a}{i}{l}{.}{i}{t}'
        );
        await user.type(yourPasswordInput, '{BackSpace>20/}{a}{d}{m}{i}{n}');
        await user.click(sendVerificationButton);

        const successSnackbar = await screen.findByText(
            /userProfile.verifyEmailSent/
        );
        expect(successSnackbar).toBeInTheDocument();
        const updatedEmailInput = await screen.findByDisplayValue(newEmail);
        expect(updatedEmailInput).toBeInTheDocument();
    });
    it('should not be able to change email if password is wrong and see proper error message', async () => {
        const errorMessage = 'Password errata.';
        server.use(
            http.patch(CHANGE_EMAIL_USER_ENDPOINT, () =>
                HttpResponse.json({ code: 400, message: errorMessage })
            )
        );
        const { user } = renderEditProfileForm(profileNotAdminProps);

        const mailInput = screen.getByRole('textbox', { name: /label.mail/ });
        await user.click(mailInput);

        const newEmailInput = screen.getByRole('textbox', {
            name: /label.new_email/,
        });
        const confirmNewEmailInput = screen.getByRole('textbox', {
            name: /label.confirm_new_email/,
        });
        const yourPasswordInput =
            await screen.findByLabelText(/label.your_password/);
        const sendVerificationButton = screen.getByRole('button', {
            name: /label.send_verification/,
        });

        await user.type(
            newEmailInput,
            '{BackSpace>20/}{d}{e}{v}{e}{l}{@}{m}{a}{i}{l}{.}{i}{t}'
        );
        await user.type(
            confirmNewEmailInput,
            '{BackSpace>20/}{d}{e}{v}{e}{l}{@}{m}{a}{i}{l}{.}{i}{t}'
        );
        await user.type(yourPasswordInput, '{BackSpace>20/}{c}{i}{a}{o}');
        await user.click(sendVerificationButton);

        const errorSnackbar = await screen.findByText(errorMessage);
        expect(errorSnackbar).toBeInTheDocument();
    });
    it('should open calendar dropdown on date field focus/click', async () => {
        const { initialValues } = profileNotAdminProps;

        const { user } = renderEditProfileForm(profileNotAdminProps);
        const dateInput = screen.getByRole('textbox', {
            name: /label.date_of_birth/,
        });
        expect(dateInput).toBeInTheDocument();

        await user.click(dateInput);

        const birthDate = initialValues.dataNascita
            ? (parseDate(initialValues.dataNascita) as Date)
            : new Date();
        // const birthDate = profileNotAdmin.user.profilo.dataNascita
        //     ? new Date(profileNotAdmin.user.profilo.dataNascita)
        //     : new Date();
        const startRange = getDecade(birthDate) + 1;
        const endRange = getDecade(birthDate) + 10;
        const calendarDropdown = screen.getByRole('button', {
            name: `${startRange} – ${endRange}`,
        });
        expect(calendarDropdown).toBeInTheDocument();
        expect(calendarDropdown).toBeVisible();
    });
    it('should add a new numeric field on add number click', async () => {
        const { initialValues } = profileNotAdminProps;
        const { user } = renderEditProfileForm(profileNotAdminProps);
        const addContactButton = screen.getByRole('button', {
            name: /label.add_number/,
        });
        expect(addContactButton).toBeInTheDocument();
        await user.click(addContactButton);
        const phoneWrapperInputs = screen.getAllByTestId('wrapper-phone-input');
        expect(phoneWrapperInputs?.length).toBe(
            initialValues.contatti.length + 1
        );
    });
    it('should open an info modal when user tries to delete the main number, aborting the deletion', async () => {
        const { initialValues } = profileNotAdminProps;
        const { user } = renderEditProfileForm(profileNotAdminProps);
        const mainNumberIdx = initialValues.contatti.findIndex(
            (contact) => contact.preferito
        );
        const deleteButton = screen.getByTestId(
            `button-delete-contact-${mainNumberIdx}`
        );

        await user.click(deleteButton);
        const preventDeleteMainNumberModalTitle = await screen.findByRole(
            'heading',
            {
                name: /contacts.removeMainError.title/,
            }
        );
        expect(preventDeleteMainNumberModalTitle).toBeInTheDocument();
        const confirmErrorButton = await screen.findByRole('button', {
            name: /label.ok/,
        });
        await user.click(confirmErrorButton);
        const phoneWrapperInputs = screen.getAllByTestId('wrapper-phone-input');
        expect(phoneWrapperInputs?.length).toBe(initialValues.contatti.length);
    });
    it('should have a maximum of 1 main number when user sets to main another number', async () => {
        const { user } = renderEditProfileForm(profileNotAdminProps);
        const mainNumberButton = screen.getAllByTestId('button-contact-main');
        expect(mainNumberButton.length).toBe(1);

        const noMainUserButton = screen.getByTestId('button-contact-not-main');
        await user.click(noMainUserButton);
        const mainNumberButton2 = screen.getAllByTestId('button-contact-main');
        expect(mainNumberButton2.length).toBe(1);
    });
    it('should delete a number when user click on a non-main number and confirms it on modal', async () => {
        const { initialValues } = profileNotAdminProps;
        const { user } = renderEditProfileForm(profileNotAdminProps);
        const firstNotMainNumberIdx = initialValues.contatti.findIndex(
            (contact) => !contact.preferito
        );

        const deleteButton = screen.getByTestId(
            `button-delete-contact-${firstNotMainNumberIdx}`
        );
        await user.click(deleteButton);
        const confirmDeleteMainNumberModalTitle = await screen.findByRole(
            'heading',
            {
                name: /contacts.removeConfirm.title/,
            }
        );
        expect(confirmDeleteMainNumberModalTitle).toBeInTheDocument();
        const confirmDeleteButton = await screen.findByRole('button', {
            name: /label.confirm/,
        });
        expect(confirmDeleteButton).toBeInTheDocument();

        await user.click(confirmDeleteButton);

        const contactsRows = screen.getAllByTestId('wrapper-phone-input');
        expect(contactsRows.length).toBe(initialValues.contatti.length - 1);
    });
    it.todo(
        'should render into biography section a select when resolution is small'
    );
    it.todo(
        'should render into biography section a tab with flags only when resolution is medium'
    );
    it.todo(
        'should render into biography section a tab with flags and names when resolution is large'
    );
    it('should render delete-user button when !user.titolare', () => {
        renderEditProfileForm(profileNotAdminProps);
        const removeUserButton = screen.getByRole('button', {
            name: /label.remove/,
        });
        expect(removeUserButton).toBeInTheDocument();
        expect(removeUserButton).toBeEnabled();
    });
    it('should open delete-user modal when clicks to delete button (if exists) and successfully delete user', async () => {
        const { user } = renderEditProfileForm(profileNotAdminProps);

        const removeUserButton = screen.getByRole('button', {
            name: /label.remove/,
        });
        expect(removeUserButton).toBeInTheDocument();
        await user.click(removeUserButton);

        const deleteUserModalTitle = await screen.findByRole('heading', {
            name: /label.delete_user/,
        });
        expect(deleteUserModalTitle).toBeInTheDocument();
        const passwordInput = await screen.findByLabelText(/label.password/);
        expect(passwordInput).toBeInTheDocument();

        await user.type(passwordInput, '{BackSpace>20/}{t}{e}{s}{t}');
        const confirmDeleteButton = screen.getByRole('button', {
            name: /label.delete_profile/,
        });

        await user.click(confirmDeleteButton);

        const successSnackbar = await screen.findByText(
            /userProfile.removeSuccess/
        );
        expect(successSnackbar).toBeInTheDocument();
    });
    it('should not be able to delete user if password is wrong', async () => {
        const errorMessage = 'Password errata.';
        server.use(
            http.delete(DELETE_USER_ENDPOINT, () =>
                HttpResponse.json(
                    { code: 400, message: errorMessage },
                    { status: 400 }
                )
            )
        );
        const { user } = renderEditProfileForm(profileNotAdminProps);

        const removeUserButton = screen.getByRole('button', {
            name: /label.remove/,
        });
        expect(removeUserButton).toBeInTheDocument();
        await user.click(removeUserButton);

        const deleteUserModalTitle = await screen.findByRole('heading', {
            name: /label.delete_user/,
        });
        expect(deleteUserModalTitle).toBeInTheDocument();
        const passwordInput = await screen.findByLabelText(/label.password/);
        expect(passwordInput).toBeInTheDocument();

        await user.type(passwordInput, '{BackSpace>20/}{c}{i}{a}{o}');
        const confirmDeleteButton = screen.getByRole('button', {
            name: /label.delete_profile/,
        });

        await user.click(confirmDeleteButton);
        const errorSnackbar = await screen.findByText(errorMessage);
        expect(errorSnackbar).toBeInTheDocument();
    });
    it('should have submission button disabled if the form is pristine', () => {
        renderEditProfileForm(profileNotAdminProps);
        const saveButton = screen.getByRole('button', { name: /label.save/ });
        expect(saveButton).toBeInTheDocument();
        expect(saveButton).toBeDisabled();
    });
    it('should send form when everything is ok and show success snackbar with "Modifiche salvate con successo."', async () => {
        const { user } = renderEditProfileForm(profileNotAdminProps);

        await user.type(
            screen.getByRole('textbox', { name: /label.name/ }),
            '{BackSpace>20/}{V}{i}{t}{o}'
        );

        const saveButton = screen.getByRole('button', { name: /label.save/ });
        expect(saveButton).toBeInTheDocument();
        expect(saveButton).toBeEnabled();

        await user.click(saveButton);

        const successSnackbar = await screen.findByText(/generic.editSuccess/);
        expect(successSnackbar).toBeInTheDocument();
    });
    it('should abort submission and show error message under fields when validation fails', async () => {
        const { user } = renderEditProfileForm(profileNotAdminProps);

        await user.type(
            screen.getByRole('textbox', { name: /label.name/ }),
            '{BackSpace>20/}'
        );
        await user.type(
            screen.getByRole('textbox', { name: /label.surname/ }),
            '{BackSpace>20/}'
        );
        const saveButton = screen.getByRole('button', { name: /label.save/ });
        await user.click(saveButton);

        const validationMessages =
            await screen.findAllByText(/label.required_value/);

        expect(validationMessages.length).toBe(2);
    });
    it('should show an error snackbar if submission fails with specific message', async () => {
        server.use(
            http.put(UPDATE_USER_ENDPOINT, () =>
                HttpResponse.json({ ok: false })
            )
        );
        const { user } = renderEditProfileForm(profileNotAdminProps);

        await user.type(
            screen.getByRole('textbox', { name: /label.name/ }),
            '{BackSpace>20/}{V}{i}{t}{o}'
        );

        const saveButton = screen.getByRole('button', { name: /label.save/ });
        expect(saveButton).toBeInTheDocument();
        expect(saveButton).toBeEnabled();

        await user.click(saveButton);

        const errorSnackbar = await screen.findByText(/generic.editError/);
        expect(errorSnackbar).toBeInTheDocument();
    });
});
