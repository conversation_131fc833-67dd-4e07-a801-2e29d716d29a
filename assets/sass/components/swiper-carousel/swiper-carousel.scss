// new-carousel

@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$swiper-carousel: 'swiper-carousel';

.swiper-carousel {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-x: hidden;

  &__slideWrapper {
    display: flex;
    height: 100%;
  }
  &__slide {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    height: auto;
    position: relative;
  }

  &__card {
    height: 100%;
    cursor: default;
  }

  &--tabs {
    .#{$swiper-carousel}__slide {
      width: auto;
    }
  }

  &--gallery {
    height: 36rem;
    border-radius: radius(lg);
    overflow: hidden;

    .#{$swiper-carousel}__slide {
      width: 100%;
      overflow: hidden;

      img {
        height: 100%;
        width: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }

  &__pagination {
    padding: space(xs) space(sm);
    bottom: 0;
    margin: space(md);
    border-radius: radius(sm);
    position: absolute;
    color: color(content-reversed);
    background-color: color(background-reversed);
    @include typography(body-small);
    @include z-index(base);
  }

  &__button-container {
    height: 100%;
    position: absolute;
    top: 0;
    bottom: 0;

    &.fadeMargin::after {
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      @include z-index(base);
    }

    &:has(.disabled)::after {
      display: none;
    }
    &:has(.disabled) {
      pointer-events: none;
    }

    &_prev {
      padding-left: space(lg);
      left: 0;

      &.fadeMargin::after {
        left: 0;
        background: linear-gradient(-90deg, rgba(255, 255, 255, 0) 0%, white 100%, white 100%);
      }
    }

    &_next {
      right: 0;
      padding-right: space(lg);

      &.fadeMargin::after {
        right: 0;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, white 100%, white 100%);
      }
    }

    .#{$swiper-carousel}__button-next,
    .#{$swiper-carousel}__button-prev {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 3.2rem;
      height: 3.2rem;
      max-width: 100%;
      max-height: 100%;
      border: none;
      border-radius: radius(rounded);
      position: relative;
      top: 50%;
      transform: translateY(-50%);
      box-shadow: elevation(raised);
      color: color(content-high);
      background-color: color(background-action);
      user-select: none;
      appearance: none;
      cursor: pointer;
      @include z-index(base, 1);

      &.disabled {
        display: none;
        pointer-events: none;
      }
    }
  }
}
