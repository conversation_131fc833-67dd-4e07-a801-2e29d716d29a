{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended", "plugin:@typescript-eslint/recommended", "prettier"], "overrides": [], "parser": "@typescript-eslint/parser", "root": true, "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "react/prop-types": "off", "react/react-in-jsx-scope": "off", "@typescript-eslint/ban-ts-comment": "off", "spaced-comment": "error", "no-duplicate-imports": "error", "camelcase": "error", "eqeqeq": "off", "indent": "off", "new-cap": ["error", {"capIsNewExceptionPattern": "^(Immutable|chart|\\$).\\w"}], "quotes": ["error", "single", {"avoidEscape": true}], "no-console": "off", "curly": "error"}, "ignorePatterns": ["node_modules/", "webpack.*.js", "public/*.js"], "settings": {"react": {"version": "detect"}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended", "prettier"], "overrides": [], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "ignorePatterns": ["node_modules/", "webpack.*.js", "public/*.js"], "settings": {"react": {"version": "detect"}, "import/resolver": {"typescript": {}}}, "plugins": ["react-hooks", "@typescript-eslint", "prettier"]}}