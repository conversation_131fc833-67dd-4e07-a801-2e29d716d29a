<?php

declare(strict_types=1);

namespace App\DataProvider\Property\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Property\BrokerageData;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class BrokerageDataDataMapper extends AbstractDataMapper
{
    private AgentDataMapper $agentDataMapper;
    private CadastralClassDataMapper $cadastralClassDataMapper;
    private ClientDataMapper $clientDataMapper;
    private MandateTypeDataMapper $mandateTypeDataMapper;

    public function __construct(
        AgentDataMapper $agentDataMapper,
        CadastralClassDataMapper $cadastralClassDataMapper,
        ClientDataMapper $clientDataMapper,
        MandateTypeDataMapper $mandateTypeDataMapper
    ) {
        $this->agentDataMapper = $agentDataMapper;
        $this->cadastralClassDataMapper = $cadastralClassDataMapper;
        $this->clientDataMapper = $clientDataMapper;
        $this->mandateTypeDataMapper = $mandateTypeDataMapper;
    }

    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return BrokerageData::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var array $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('agent', function () use ($data) {
                return isset($data['agent']) ? $this->agentDataMapper->map($data['agent']) : null;
            })
            ->forMember('cadastralClass', function () use ($data) {
                return isset($data['cadastralClass']) ? $this->cadastralClassDataMapper->map($data['cadastralClass']) : null;
            })
            ->forMember('mandateType', function () use ($data) {
                return isset($data['mandateType']) ? $this->mandateTypeDataMapper->map($data['mandateType']) : null;
            })
            ->forMember('owner', function () use ($data) {
                return isset($data['owner']) ? $this->clientDataMapper->map($data['owner']) : null;
            })
        ;

        return $config;
    }
}
