import { createActions } from 'redux-actions';
import * as actions from '../../../../commons/gtx-react/actions/list';
import { IReduxRootState } from '../types/redux';
import { getAuctionsListApi } from '../web-api/api';
import { setSearchFiltersAction } from './searchFilters';
import { LIST_QS_FILTERS_KEYS, PROPERTY_STATUS_ACTIVE_LABEL } from '../constants';
import { BASE_PATH } from '../web-api/endpoints';
import { setQueryString } from 'lib/utility';
import { IPagination } from 'types/pagination';
import { IPortalAuctionsListFilters } from '../types/list';
import { getMatchesAndThreadsCounter } from '../web-api/api';
import gtxConstants from '@getrix/common/js/gtx-constants';
import { extractIdsFromArray } from 'lib/property';

export const { searchLoadingStart, searchLoadingEnd } = createActions('SEARCH_LOADING_START', 'SEARCH_LOADING_END');

function addFiltersToQueryString(payload) {
    let data = { ...payload };

    const payloadFields = Object.keys(data);

    payloadFields.forEach((field) => {
        if (
            LIST_QS_FILTERS_KEYS.indexOf(field) < 0 ||
            (LIST_QS_FILTERS_KEYS.indexOf(field) >= 0 && data[field] === '')
        ) {
            delete data[field];
        }
    });

    setQueryString(BASE_PATH, data);
}

export const listSearch = (pagination: Pick<IPagination, 'page' | 'results'>, searchParams: any) => {
    return (dispatch) => {
        if (searchParams.code === null) {
            dispatch(setSearchFiltersAction(searchParams));
        }
        dispatch(searchLoadingStart());
        dispatch(actions.paginationStart(pagination));

        getAuctionsListApi({ pagination, searchParams: searchParams })
            .then(async (result) => {
                let data: any;

                if (!result?.data?.properties || searchParams.status !== PROPERTY_STATUS_ACTIVE_LABEL) {
                    return result;
                }

                if (gtxConstants('PROPERTY_LIST_MATCH_COLUMN')) {
                    const { data: matchesAndThreadsCounters } = await getMatchesAndThreadsCounter(
                        extractIdsFromArray(result.data.properties)
                    );
                    data = { ...result.data, matchesAndThreadsCounters };
                } else {
                    data = { ...result };
                }

                return { ...result, data };
            })
            .then((result) => {
                dispatch(searchLoadingEnd());
                dispatch(actions.paginationEnd(result.data));
            })
            .catch(() => {
                dispatch(searchLoadingEnd());
                dispatch(actions.paginationError());
            });
    };
};

export const paginate = (data: { pagination: IPagination; searchFilters: IPortalAuctionsListFilters }) => {
    return (dispatch, getState) => {
        dispatch(searchLoadingStart());
        dispatch(actions.paginationStart(data));

        const state: IReduxRootState = getState();

        const payload = {
            pagination: data.pagination,
            searchParams: state.searchFilters,
        };

        getAuctionsListApi(payload)
            .then(async (result) => {
                let data: any;

                if (!result?.data?.properties || state.searchFilters.status !== PROPERTY_STATUS_ACTIVE_LABEL) {
                    return result;
                }

                if (gtxConstants('PROPERTY_LIST_MATCH_COLUMN')) {
                    const { data: matchesAndThreadsCounters } = await getMatchesAndThreadsCounter(
                        extractIdsFromArray(result.data.properties)
                    );
                    data = { ...result.data, matchesAndThreadsCounters };
                } else {
                    data = { ...result };
                }

                return { ...result, data };
            })
            .then((result) => {
                addFiltersToQueryString({
                    ...data.pagination,
                    ...state.searchFilters,
                });
                dispatch(searchLoadingEnd());
                dispatch(actions.paginationEnd(result.data));
                window.scrollTo(0, 0);
            })
            .catch(() => {
                dispatch(searchLoadingEnd());
                dispatch(actions.paginationError());
            });
    };
};
