@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

// mappa colori categorie
$category-map: (
  residential_ads: #1cbc9d,
  commercial_ads: #3798dc,
  room_ads: #9c5bb8,
  vacanze: #2c3e50,
  land_ads: #038387,
  land_virtual_ads: #ea4f3c,
  new_constructions_ads: #f1c40f,
  auctions_ads: #7e735f,
  buildings_ads: #ff8c00,
  warehouse_ads: #107c10,
  garage_ads: #95a5a6,
  office_ads: #0078d7,
  sheds_ads: #8e8cd8,
  shop_ads: #82c0ff,
);

$pie-chart-size: 28rem;

@include media('screen', '<#{breakpoint(sm)}') {
  .dashboard-page .gx-card-list {
    padding-bottom: (space(sm) * 2);
  }
}

.dashboard .gtx-ah-footer-spacing {
  background-color: color(background-alt);
}

// Property stats card
.property-stats {
  &__head {
    &Group {
      margin-top: space(lg);

      label {
        width: 50%;
      }
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    &__head {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: space(lg);

      > * + * {
        display: inline-flex;
        margin-left: space(xl);
      }

      &Group {
        margin-top: 0;

        label {
          width: auto;
        }
      }
    }

    &__content {
      display: flex;
      align-items: center;
    }
  }
}

.pie-chart {
  width: $pie-chart-size;
  margin: 0 auto 4rem;

  &__wrap {
    position: relative;
    width: $pie-chart-size;
    height: $pie-chart-size;
  }

  &__text {
    display: flex;
    position: absolute;
    top: -0.5rem;
    left: 0;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: $pie-chart-size;
    height: $pie-chart-size;
    padding: 5rem;
    font-size: 1.4rem;
    font-weight: 700;
    line-height: 1.2;
    text-align: center;
  }

  &__text b {
    color: color(content-action);
    font-size: 3.2rem;
    font-weight: 700;
  }

  @each $category, $color in $category-map {
    &__portion--#{$category} {
      fill: $color;
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}', '<#{breakpoint(md)}') {
    margin: 0;
    scale: 0.9;
    transform-origin: left;
    width: 25rem;
  }
  
  @include media('screen', '>=#{breakpoint(md)}') {
    margin: 0 space(3xl);
  }
 
  @include media('screen', '>=#{breakpoint(lg)}') {
    margin: 0 15.2rem;
  }
}

.pie-chart-legend {
  flex-grow: 1;

  &__table {
    width: 100%;

    thead {
      color: color(content-low);

      th {
        padding-bottom: 1rem;
        border-bottom: 0.1rem solid color(border-main);
        text-align: left;
      }
    }

    tr + tr {
      border-top: 0.1rem solid color(border-main);
    }

    td {
      padding: space(sm) 0;
      @include typography(body-small);

      .category {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        margin-right: 1.2rem;
        border-radius: radius(rounded);
        background: color(background-brand);
      }

      span {
        color: color(content-medium);
      }

      @each $category, $color in $category-map {
        .category-#{$category} {
          background: $color;
        }
      }

      .name {
        color: color(content-medium);
      }

      .link,
      .no-link {
        font-size: 1.4rem;
      }

      .no-link {
        color: color(content-low);
      }
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    margin-left: space(xl);
  }
}

// Appointments card
.appointments-card {
  &__head {
    display: flex;
    padding: space(md);
    flex-direction: column;
  }

  &__table {
    display: table;
    width: 100%;
  }

  .col-day {
    clear: both;
    border-bottom: 0.1rem solid color(border-main);
  }

  .col-day__head {
    width: 15rem;
    height: 6.2rem;
    padding: 0;
    float: left;
    font-weight: 400;
    line-height: 6.2rem;
    text-align: center;
    color: color(content-medium);

    .week-day {
      font-weight: 600;
    }
  }

  .col-day__head--today + .col-day__body {
    background-color: color(background-brand-alt);
  }

  .col-day__body {
    width: calc(100% - 15rem);
    height: ($gx-unit-size * 8);
    padding: space(sm) space(md);
    float: left;
    border-left: 0.1rem solid color(border-main);
    overflow-y: auto;

    ul {
      display: table;
      margin-bottom: 0;
      padding: 0;

      li {
        display: table-cell;

        & + li {
          padding-left: space(sm);
        }
      }
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    &__head {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    &__head {
      padding: space(lg) space(xl);
      border-bottom: 0.1rem solid color(border-main);
    }

    .col-day {
      display: table-cell;
      width: percentage(1/8);
      border-bottom: none;

      & + .col-day {
        border-left: 0.1rem solid color(border-main);
      }
    }

    .col-day__head {
      width: 100%;
      height: 4rem;
      float: none;
      border-bottom: 0.1rem solid color(border-main);
      line-height: 4rem;
      text-align: center;
    }

    .col-day__body {
      width: 100%;
      min-height: 25rem;
      padding: space(md);
      float: none;
      border-left: none;
      overflow-y: auto;

      ul {
        display: block;

        li {
          display: block;

          & + li {
            margin-top: space(sm);
            padding-left: 0;
          }
        }
      }
    }
  }
}

// properties card
.properties-card {
  display: flex;
  flex-direction: column;

  .gtx-panel {
    padding: 0 space(xl) space(sm);
    border: none;
    border-radius: 0;

    & + .gtx-panel {
      border-top: 0.1rem solid color(border-main);
    }

    &__head {
      padding: space(md) 0;
      border-bottom: 0.1rem solid color(border-main);

      &__action {
        display: block;
        margin-top: 1rem;
      }
    }

    ul {
      padding: 0;
      list-style: none;
    }

    @include media('screen', '>=#{breakpoint(sm)}') {
      &__head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: space(lg);
        padding-bottom: space(lg);

        &__action {
          margin-top: 0;
        }
      }
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    flex-direction: row;
    flex-wrap: wrap;

    .gtx-panel {
      width: 50%;
      flex-grow: 1;

      &:first-child {
        border-right: 0.1rem solid color(border-main);
      }

      &:not(:last-child) {
        border-top: none;
      }
    }
  }

  @include media('screen', '>=#{breakpoint(lg)}') {
    .gtx-panel {
      width: percentage(1/3);

      &:last-child {
        width: percentage(1/3);
        border-left: 0.1rem solid color(border-main);
        border-top: none;
      }
    }
  }

  .lista-annunci-panel li {
    display: block;
    position: relative;

    & + li {
      border-top: 0.1rem solid color(border-main);
    }
  }

  .lista-annunci-panel__pic {
    display: block;
    position: absolute;
    top: 50%;
    left: 0;
    width: 4.8rem;
    height: 3.8rem;
    transform: translateY(-50%);
  }

  .lista-annunci-panel__content {
    padding: 1rem 6rem 1rem 0;

    span {
      display: block;
    }

    .data {
      color: color(content-medium);
    }
  }

  .lista-annunci-panel__pic + .lista-annunci-panel__content {
    padding-left: 7rem;
  }

  .lista-annunci-panel__action {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }
}

// News section
.news-section {
  padding: space(md);

  &__head {
    display: flex;
    flex-direction: column;
    padding-bottom: space(md);
    margin-bottom: space(md);
    border-bottom: 0.1rem solid color(border-main);

    .gx-title-2 {
      margin-bottom: space(sm);
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    padding-left: space(xl);
    padding-right: space(xl);

    &__head {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .gx-title-2 {
        margin-bottom: none;
      }
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    padding: space(xl);
  }
}

#gtx-hp-news-container {
  position: relative; // Posizionamento dello spinner
  min-height: 13.4rem; // Altezza minima per contenere lo spinner

  .gtx-hp-news-pro {
    background-color: color(background-error);
  }
}

.news-preview-wrap--pro {
  .news-preview__pic {
    width: 100%;
    padding-bottom: 75%;
  }
}

// Agenda
.gtx-hp-appointments-list-item {
  a {
    text-decoration: none;
  }
}

.gtx-hp-appointments-item {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  color: white;
  border-radius: radius(sm);
}

.gtx-hp-appointments-item-type-1 {
  border-color: $appointment-type-1;
  background-color: $appointment-type-1;
}

.gtx-hp-appointments-item-type-2 {
  border-color: $appointment-type-2;
  background-color: $appointment-type-2;
}

.gtx-hp-appointments-item-type-3 {
  border-color: $appointment-type-3;
  background-color: $appointment-type-3;
}

.gtx-hp-appointments-item-type-4 {
  border-color: $appointment-type-4;
  background-color: $appointment-type-4;
}

.gtx-hp-appointments-item-type-5 {
  border-color: $appointment-type-5;
  background-color: $appointment-type-5;
}

.gtx-hp-appointments-item-type-6 {
  border-color: $appointment-type-6;
  background-color: $appointment-type-6;
}

.gtx-hp-appointments-item-type-7 {
  border-color: $appointment-type-7;
  background-color: $appointment-type-7;
}

.gtx-hp-appointments-item-type-8 {
  border-color: $appointment-type-8;
  background-color: $appointment-type-8;
}

.gtx-hp-appointments-item-type-9 {
  border-color: $appointment-type-9;
  background-color: $appointment-type-9;
}

.gtx-hp-appointments-item-type-10 {
  border-color: $appointment-type-10;
  background-color: $appointment-type-10;
}

.gtx-hp-appointments-item-type-11 {
  border-color: $appointment-type-11;
  background-color: $appointment-type-11;
}

.gtx-hp-appointments-item-type-12 {
  border-color: $appointment-type-12;
  background-color: $appointment-type-12;
}

.gtx-hp-appointments-item-type {
  font-size: smaller;
}

@include media('screen', '<#{breakpoint(sm)}') {
  .anteprima-header-actions {
    div[data-name='actions'] {
      display: none;
    }
  }

  .modal .detail-map {
    min-height: auto;
  }
}

.btn-input-group {
  label input {
    display: none;
  }
}

.skeleton-appointments {
  display: flex;
  flex-direction: column;

  &__col {
    width: 100%;
    height: ($gx-unit-size * 5);
    background-image: linear-gradient(
      90deg,
      color(background-alt) 0%,
      color(background-alt) 20%,
      color(background-main) 20%,
      color(background-main) calc(20% + 0.1rem),
      color(background-alt) calc(20% + 0.1rem),
      color(background-alt) 100%
    );

    & + .skeleton-appointments__col {
      margin-top: 0.1rem;
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    flex-direction: row;
    justify-content: space-between;

    &__col {
      width: calc(#{percentage(1/8)} - 0.1rem);
      height: 29rem;
      background-image: linear-gradient(
        180deg,
        color(background-alt) 0%,
        color(background-alt) percentage(1/6),
        color(background-main) percentage(1/6),
        color(background-alt) calc(#{percentage(1/6)} + 0.2rem),
        color(background-alt) percentage(2/6),
        color(background-main) percentage(2/6),
        color(background-alt) calc(#{percentage(2/6)} + 0.2rem),
        color(background-alt) percentage(3/6),
        color(background-main) percentage(3/6),
        color(background-alt) calc(#{percentage(3/6)} + 0.2rem),
        color(background-alt) percentage(4/6),
        color(background-main) percentage(4/6),
        color(background-alt) calc(#{percentage(4/6)} + 0.2rem),
        color(background-alt) percentage(5/6),
        color(background-main) percentage(5/6),
        color(background-alt) calc(#{percentage(5/6)} + 0.2rem),
        color(background-alt) 100%
      );
    }
  }
}

.skeleton-property-item {
  position: relative;
  padding: 1.4rem 6rem 1.4rem 7rem;

  & + & {
    border-top: 0.1rem solid color(border-main);
  }

  &__image {
    position: absolute;
    top: 50%;
    left: 0;
    width: 4.8rem;
    height: 3.8rem;
    transform: translateY(-50%);
  }

  &__text {
    width: 18rem;
    height: space(md);

    & + .skeleton-property-item__text {
      margin-top: space(sm);
    }

    &:last-child {
      width: 12rem;
    }
  }
}

.gtx-nw-modal-body {
  ul {
    list-style-type: disc;
    padding-left: space(2xl);
  }

  img {
    max-width: 100%;
  }
}

.modal-footer .dismiss-checkbox {
  display: inline-block;
  margin-right: 1.1rem;
}
