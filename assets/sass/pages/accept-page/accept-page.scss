@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.accept-page {
  max-width: 60rem;
  margin: 0 auto;
  padding: space(md) space(md) space(2xl);

  &--withFooter {
    padding-bottom: 15rem;
  }

  &__content {
    &__title {
      margin: 0 0 space(md) 0;
      color: color(content-medium);
      @include typography(title-1);
    }

    &__text {
      color: color(content-medium);
      @include typography(body);

      h3 {
        margin: 0 0 space(md) 0;
        color: color(content-medium);
        @include typography(title-2);

        & + &,
        & + p {
          margin-top: space(md);
        }
      }

      ul {
        margin-bottom: space(md);
        padding-left: space(lg);
      }

      ol {
        padding-left: space(lg);
      }

      h4 {
        margin: space(md) 0;
        color: color(content-medium);
        @include typography(title-2);
      }

      p {
        @include typography(body-small);

        & + &,
        & + h3 {
          margin-top: space(md);
        }
      }

      li {
        @include typography(body-small);

        ol {
          margin-top: space(sm);
          margin-bottom: space(sm);
        }

        & + li {
          margin-top: space(md);
        }
      }

      &--box {
        margin-top: space(md);
        padding: 2rem;
        border: 0.1rem solid color(border-main);
        @include typography(body-small);
      }
    }
  }

  &__contact {
    margin-top: space(xl);
    text-align: center;

    &__title {
      margin-bottom: space(md);
      color: color(content-medium);
      @include typography(title-2);
    }

    &__item {
      margin: 0 auto;
      color: color(content-medium);
      @include typography(body);

      a {
        color: color(content-medium);
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      svg {
        @include icon-size(md);
        margin-right: space(sm);
      }

      &--tel {
        margin-top: space(md);
      }
    }

    &__footer {
      margin-top: space(xl);
      padding-top: space(xl);
      border-top: 0.1rem solid color(border-main);
      color: color(content-medium);

      a {
        text-decoration: underline;

        &:hover {
          text-decoration: none;
        }
      }
    }

    .accept-page__content__text--noAdmin + & {
      padding-top: space(xl);
      border-top: 0.1rem solid color(border-main);
    }
  }

  &__footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: space(md);
    background-color: color(background-main);
    @include z-index(base, 2);
    box-shadow: elevation(fixed-bottom);

    &__content {
      max-width: 60rem;
      margin: 0 auto;
    }

    .styled-checkbox {
      margin-top: 0;
      margin-bottom: space(md);
    }

    .checkbox span {
      color: color(content-medium);
      @include typography(body-small);
    }

    .gx-button {
      width: 100%;
    }

    .parsley-errors-list.filled li,
    .parsley-custom-error-message {
      @include typography(body-tiny);
      line-height: 1.2;
    }
  }

  // TABLET
  @include media('screen', '>=#{breakpoint(sm)}') {
    padding-top: space(2xl);

    &__content {
      &__text {
        ul,
        ol {
          padding-left: space(2xl);
        }
      }
    }

    &__contact {
      margin-top: space(2xl);

      &__item {
        display: inline-block;

        &--tel {
          margin-top: 0;
          margin-left: space(2xl);
        }
      }

      .accept-page__content__text--noAdmin + & {
        padding-top: space(2xl);
      }
    }

    &__footer {
      padding: space(lg) 0;

      &__content {
        padding: 0 space(md);
      }

      .gx-button {
        width: auto;
      }

      .styled-checkbox {
        display: inline-block;
        margin-bottom: 0;
      }

      form {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }
}
