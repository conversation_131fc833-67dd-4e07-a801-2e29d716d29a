import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { useState } from 'react';
import { But<PERSON> } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { Badge } from '@gx-design/badge';
import { Checkbox } from '@gx-design/checkbox';
import { Tooltip } from '@gx-design/tooltip';
import { PaginationBar } from '@gx-design/pagination-bar';
import {
    ColumnPinningState,
    getCoreRowModel,
    PaginationState,
    SortingState,
    createColumnHelper,
} from '@tanstack/react-table';
import { Fragment } from 'react';

import {
    CrmTable,
    useReactTableV1,
} from '../../../js/commons/gtx-react/components/tanstack-table/v1';
import {
    useColumnOrderState,
    useColumnSizingState,
    useColumnVisibilityState,
    useDefaultOrderingColumns,
} from '../../../js/commons/gtx-react/components/tanstack-table/hooks';
import { getDefaultColumnOrder } from '../../../js/commons/gtx-react/components/tanstack-table/helpers';
import { SortableButton } from '../../../js/commons/gtx-react/components/tanstack-table/Sortable';
import { CrmSummary } from '../../../js/commons/gtx-react/components/CrmSummary';

/**
 * TanStack Table is a powerful, feature-rich table component built with TanStack Table v8.
 * It provides advanced functionality including sorting, filtering, pagination, column resizing,
 * drag & drop reordering, and row selection with a modern, responsive design.
 */
const meta: Meta = {
    title: 'CRM/TanstackTable',
    parameters: {
        componentSubtitle: 'Advanced Data Table',
        componentTitle: 'TanStack Table',
        componentSlug: 'tanstack-table',
        componentImport: 'CrmTable, useReactTableV1',
        packageName: 'gtx-react/components/tanstack-table/v1',
        layout: 'fullscreen',
        docs: {
            description: {
                component: `
The TanStack Table component is a comprehensive data table solution with the following features:

## Key Features:
- **Column Management**: Show/hide columns, resize, pin, and reorder columns via drag & drop
- **Row Selection**: Single and multi-row selection with checkboxes
- **Sorting**: Multi-column sorting with visual indicators
- **Pagination**: Configurable page sizes and navigation
- **Filtering**: Advanced filtering capabilities (when integrated)
- **Responsive Design**: Adapts to different screen sizes
- **Accessibility**: Full keyboard navigation and screen reader support
- **Customizable**: Highly customizable cells, headers, and styling

## Sub-components:
- **CrmTable**: Main table wrapper container
- **CrmTable.Thead**: Table header section
- **CrmTable.Tr**: Standard table row
- **CrmTable.TrSelectable**: Row with selection actions
- **CrmTable.Th**: Table header cell with advanced features
- **CrmTable.Td**: Table data cell
- **CrmTable.TableBody**: Table body wrapper
- **CrmTable.BodyRow**: Table body row
- **CrmTable.HorizontalDropping**: Drag & drop container for columns
- **CrmSummary**: Results summary and actions bar above table
- **CrmSummary.Results**: Display current page results information
- **CrmSummary.Actions**: Container for table action buttons

## Advanced Features:
- **Column Pinning**: Pin columns to left or right
- **Column Resizing**: Drag to resize column widths
- **Drag & Drop**: Reorder columns by dragging
- **Bulk Actions**: Perform actions on selected rows
- **Loading States**: Built-in skeleton loading
- **Empty States**: Customizable empty data displays

## Usage Pattern:
\`\`\`tsx
const table = useReactTableV1({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // ... other options
});

<CrmSummary>
    <CrmSummary.Results 
        itemsLength={table.getRowModel().rows.length}
        pageSize={pagination.pageSize}
        pageIndex={pagination.pageIndex}
        pageCount={data.length}
    />
    <CrmSummary.Actions>
        {/* Export, column visibility, etc. */}
    </CrmSummary.Actions>
</CrmSummary>

<CrmTable table={table}>
    <CrmTable.Thead>
        {/* Header content */}
    </CrmTable.Thead>
    <CrmTable.TableBody>
        {/* Body rows */}
    </CrmTable.TableBody>
</CrmTable>
\`\`\`
                `,
            },
        },
    },
    tags: ['autodocs'],
};

export default meta;

type Story = StoryObj;

// Sample data for the stories
type PropertyData = {
    id: string;
    code: string;
    title: string;
    price: string;
    location: string;
    image: string;
    status: 'active' | 'pending' | 'sold';
    bedrooms: number;
    bathrooms: number;
    squareMeters: number;
    type: 'apartment' | 'house' | 'office';
    createdAt: string;
};

const mockData: PropertyData[] = [
    {
        id: '1',
        code: 'APP001',
        title: 'Elegant Apartment in City Center',
        price: '€450,000',
        location: 'Rome, Trastevere',
        image: 'https://via.placeholder.com/100x80/4f46e5/white?text=IMG',
        status: 'active',
        bedrooms: 3,
        bathrooms: 2,
        squareMeters: 120,
        type: 'apartment',
        createdAt: '2024-01-15',
    },
    {
        id: '2',
        code: 'HOU002',
        title: 'Modern Villa with Garden',
        price: '€750,000',
        location: 'Milan, Brera',
        image: 'https://via.placeholder.com/100x80/059669/white?text=IMG',
        status: 'pending',
        bedrooms: 4,
        bathrooms: 3,
        squareMeters: 200,
        type: 'house',
        createdAt: '2024-01-20',
    },
    {
        id: '3',
        code: 'OFF003',
        title: 'Premium Office Space',
        price: '€320,000',
        location: 'Naples, Centro',
        image: 'https://via.placeholder.com/100x80/dc2626/white?text=IMG',
        status: 'sold',
        bedrooms: 0,
        bathrooms: 2,
        squareMeters: 80,
        type: 'office',
        createdAt: '2024-01-25',
    },
    {
        id: '4',
        code: 'APP004',
        title: 'Cozy Studio Apartment',
        price: '€180,000',
        location: 'Florence, San Lorenzo',
        image: 'https://via.placeholder.com/100x80/7c3aed/white?text=IMG',
        status: 'active',
        bedrooms: 1,
        bathrooms: 1,
        squareMeters: 45,
        type: 'apartment',
        createdAt: '2024-02-01',
    },
    {
        id: '5',
        code: 'HOU005',
        title: 'Luxury Penthouse',
        price: '€1,200,000',
        location: 'Venice, San Marco',
        image: 'https://via.placeholder.com/100x80/ea580c/white?text=IMG',
        status: 'active',
        bedrooms: 5,
        bathrooms: 4,
        squareMeters: 300,
        type: 'house',
        createdAt: '2024-02-10',
    },
];

// Define table columns
const useTableColumns = () => {
    const columnHelper = createColumnHelper<PropertyData>();

    return [
        columnHelper.accessor('id', {
            maxSize: 10,
            enableSorting: false,
            enableResizing: false,
            id: 'select',
            header: ({ table }) => (
                <Checkbox
                    aria-label="Select all"
                    indeterminate={
                        table.getIsSomeRowsSelected() &&
                        !table.getIsAllRowsSelected()
                    }
                    checked={table.getSelectedRowModel().rows.length > 0}
                    onChange={() => table.toggleAllRowsSelected()}
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    aria-label={`Select ${row.original.code}`}
                    checked={row.getIsSelected()}
                    onChange={() => row.toggleSelected()}
                />
            ),
            enablePinning: true,
        }),
        columnHelper.accessor('code', {
            header: 'Code',
            cell: ({ getValue }) => (
                <span className="font-mono text-sm text-blue-600">
                    {getValue()}
                </span>
            ),
            enablePinning: true,
            minSize: 80,
            size: 100,
            id: 'code',
        }),
        columnHelper.accessor('title', {
            header: 'Property',
            cell: ({ row }) => (
                <div className="crm-property-item">
                    <div className="crm-property-item__content">
                        <div className="crm-property-item__title">
                            {row.original.title}
                        </div>
                        <div className="crm-property-item__location">
                            {row.original.location}
                        </div>
                    </div>
                </div>
            ),
            minSize: 200,
            size: 350,
            id: 'title',
        }),
        columnHelper.accessor('status', {
            header: 'Status',
            cell: ({ getValue }) => {
                const status = getValue();
                const statusConfig = {
                    active: { icon: 'check' as const, text: 'Active' },
                    pending: { icon: 'clock' as const, text: 'Pending' },
                    sold: { icon: 'check' as const, text: 'Sold' },
                };
                const config = statusConfig[status];
                return <Badge icon={config.icon} text={config.text} />;
            },
            minSize: 130,
            size: 130,
        }),
        columnHelper.accessor('price', {
            header: 'Price',
            cell: ({ getValue }) => (
                <span className="font-semibold text-green-600">
                    {getValue()}
                </span>
            ),
            minSize: 100,
            size: 120,
        }),
        columnHelper.accessor('type', {
            header: 'Type',
            cell: ({ getValue }) => {
                const type = getValue();
                return <span className="capitalize text-gray-600">{type}</span>;
            },
            minSize: 80,
            size: 100,
        }),
        columnHelper.accessor('squareMeters', {
            header: 'Area',
            cell: ({ getValue }) => `${getValue()} m²`,
            minSize: 80,
            size: 100,
        }),
        columnHelper.accessor('bedrooms', {
            header: 'Beds',
            cell: ({ getValue }) => getValue() || '-',
            minSize: 60,
            size: 80,
        }),
        columnHelper.accessor('bathrooms', {
            header: 'Baths',
            cell: ({ getValue }) => getValue(),
            minSize: 60,
            size: 80,
        }),
        columnHelper.display({
            id: 'actions',
            header: 'Actions',
            maxSize: 120,
            enableResizing: false,
            cell: ({ row }) => (
                <div className="crm-table__rowActions">
                    <Tooltip position="top" text="View Details">
                        <Button iconOnly size="small" variant="ghost">
                            <Icon name="eye" />
                        </Button>
                    </Tooltip>
                    <Tooltip position="top" text="Edit">
                        <Button iconOnly size="small" variant="ghost">
                            <Icon name="pencil" />
                        </Button>
                    </Tooltip>
                    <Tooltip position="top" text="More Options">
                        <Button iconOnly size="small" variant="ghost">
                            <Icon name="ellipsis" />
                        </Button>
                    </Tooltip>
                </div>
            ),
            enablePinning: true,
        }),
    ];
};

// Main table component for stories
const TableStory = ({
    data = mockData,
    enableRowSelection = true,
    showPagination = true,
    showColumnVisibility = true,
}: {
    data?: PropertyData[];
    enableRowSelection?: boolean;
    showPagination?: boolean;
    showColumnVisibility?: boolean;
}) => {
    const columns = useTableColumns();
    const defaultColumnIds = useDefaultOrderingColumns(columns);

    // State management
    const [columnOrder, setColumnOrder] = useColumnOrderState(
        getDefaultColumnOrder([], defaultColumnIds)
    );
    const [columnSizing, setColumnSizing] = useColumnSizingState({});
    const [columnVisibility, setColumnVisibility] = useColumnVisibilityState(
        {}
    );
    const [columnPinning, setColumnPinning] = useState<ColumnPinningState>({
        left: ['select'],
        right: ['actions'],
    });
    const [pagination, setPagination] = useState<PaginationState>({
        pageIndex: 0,
        pageSize: 5,
    });
    const [sorting, setSorting] = useState<SortingState>([]);

    // Initialize table
    const table = useReactTableV1({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        enableRowSelection,
        enableColumnResizing: true,
        columnResizeMode: 'onChange', // Real-time resizing while dragging
        state: {
            columnOrder,
            columnSizing,
            columnVisibility,
            columnPinning,
            pagination,
            sorting,
            dnd: {
                select: false,
                actions: false,
            },
        },
        onColumnOrderChange: setColumnOrder,
        onColumnSizingChange: setColumnSizing,
        onColumnVisibilityChange: setColumnVisibility,
        onColumnPinningChange: setColumnPinning,
        onPaginationChange: setPagination,
        onSortingChange: setSorting,
        manualPagination: false,
        pageCount: Math.ceil(data.length / pagination.pageSize),
    });

    return (
        <div className="space-y-4">
            {/* CRM Summary with Results and Actions */}
            <CrmSummary>
                <CrmSummary.Results
                    itemsLength={table.getRowModel().rows.length}
                    pageSize={pagination.pageSize}
                    pageIndex={pagination.pageIndex}
                    pageCount={data.length}
                />

                <CrmSummary.Actions>
                    <Tooltip text="Export CSV">
                        <Button iconOnly size="small">
                            <Icon name="download" />
                        </Button>
                    </Tooltip>

                    {showColumnVisibility && (
                        <SortableButton
                            defaultItems={defaultColumnIds}
                            table={table}
                            items={columnOrder}
                            onChange={setColumnOrder}
                            onReset={(items) => setColumnOrder(items)}
                            renderItem={(item) => (
                                <SortableButton.Item id={item}>
                                    <SortableButton.Checkbox
                                        label={
                                            table.getColumn(item)?.columnDef
                                                .meta?.label || item
                                        }
                                    />
                                </SortableButton.Item>
                            )}
                        />
                    )}
                </CrmSummary.Actions>
            </CrmSummary>

            {/* Table */}
            <div className="crm-section__contentList">
                <CrmTable table={table}>
                    <CrmTable.Thead>
                        {table.getHeaderGroups().map(({ id, headers }) => (
                            <Fragment key={id}>
                                <CrmTable.Tr>
                                    {headers.map((header) => (
                                        <CrmTable.HorizontalDropping
                                            key={header.id}
                                            items={columnOrder}
                                        >
                                            <CrmTable.Th header={header}>
                                                <CrmTable.Th.PinningControls />
                                                <CrmTable.Th.ResizebleControls />
                                                <CrmTable.Th.DraggableButton />
                                            </CrmTable.Th>
                                        </CrmTable.HorizontalDropping>
                                    ))}
                                </CrmTable.Tr>
                                {enableRowSelection && (
                                    <CrmTable.TrSelectable>
                                        <CrmTable.TrSelectable.Actions>
                                            <Tooltip
                                                position="top"
                                                text="Archive"
                                            >
                                                <Button
                                                    iconOnly
                                                    size="small"
                                                    variant="ghost"
                                                >
                                                    <Icon name="inbox" />
                                                </Button>
                                            </Tooltip>
                                            <Tooltip
                                                position="top"
                                                text="Delete"
                                            >
                                                <Button
                                                    iconOnly
                                                    size="small"
                                                    variant="ghost"
                                                >
                                                    <Icon name="bin" />
                                                </Button>
                                            </Tooltip>
                                        </CrmTable.TrSelectable.Actions>
                                    </CrmTable.TrSelectable>
                                )}
                            </Fragment>
                        ))}
                    </CrmTable.Thead>
                    <CrmTable.TableBody>
                        {table.getRowModel().rows.map((row) => (
                            <CrmTable.BodyRow row={row} key={row.id}>
                                {row.getVisibleCells().map((cell) => (
                                    <CrmTable.HorizontalDropping
                                        key={cell.id}
                                        items={columnOrder}
                                    >
                                        <CrmTable.Td cell={cell}>
                                            <CrmTable.Td.ResizebleControls
                                                column={cell.column}
                                            />
                                        </CrmTable.Td>
                                    </CrmTable.HorizontalDropping>
                                ))}
                            </CrmTable.BodyRow>
                        ))}
                    </CrmTable.TableBody>
                </CrmTable>

                {/* Pagination */}
                {showPagination && (
                    <PaginationBar
                        separatorString="of"
                        resultString="results"
                        currentResults={pagination.pageSize}
                        totalResults={Math.ceil(
                            data.length / pagination.pageSize
                        )}
                    >
                        <PaginationBar.DropDown
                            onResultsChange={(value) => {
                                table.resetRowSelection();
                                setPagination({
                                    pageIndex: 0,
                                    pageSize: value,
                                });
                            }}
                            options={[5, 10, 20]}
                            value={pagination.pageSize}
                            resultString="results"
                        />
                        <PaginationBar.Pager
                            activePage={pagination.pageIndex + 1}
                            maxPagesToShow={3}
                            totalPages={Math.ceil(
                                data.length / pagination.pageSize
                            )}
                            onPageClick={(page) =>
                                setPagination({
                                    ...pagination,
                                    pageIndex: page - 1,
                                })
                            }
                        />
                    </PaginationBar>
                )}
            </div>
        </div>
    );
};

// Story definitions
export const DefaultTable: Story = {
    render: () => <TableStory />,
    parameters: {
        docs: {
            description: {
                story: 'The default table with all features enabled including row selection, sorting, column management, and pagination.',
            },
        },
    },
};

export const WithoutRowSelection: Story = {
    render: () => <TableStory enableRowSelection={false} />,
    parameters: {
        docs: {
            description: {
                story: 'Table without row selection functionality.',
            },
        },
    },
};

export const WithoutPagination: Story = {
    render: () => <TableStory showPagination={false} />,
    parameters: {
        docs: {
            description: {
                story: 'Table without pagination, showing all rows at once.',
            },
        },
    },
};

export const SimpleTable: Story = {
    render: () => (
        <TableStory
            enableRowSelection={false}
            showPagination={false}
            showColumnVisibility={false}
        />
    ),
    parameters: {
        docs: {
            description: {
                story: 'A simple table without advanced features, showing just the basic data display.',
            },
        },
    },
};

export const LargeDataset: Story = {
    render: () => {
        const largeData: PropertyData[] = Array.from(
            { length: 100 },
            (_, i) => {
                const baseItem = mockData[i % mockData.length];
                if (!baseItem) {
                    throw new Error('Missing base item');
                }
                return {
                    id: `${i + 1}`,
                    code: `PROP${String(i + 1).padStart(3, '0')}`,
                    title: baseItem.title,
                    price: baseItem.price,
                    location: baseItem.location,
                    image: baseItem.image,
                    status: baseItem.status,
                    bedrooms: baseItem.bedrooms,
                    bathrooms: baseItem.bathrooms,
                    squareMeters: baseItem.squareMeters,
                    type: baseItem.type,
                    createdAt: baseItem.createdAt,
                };
            }
        );
        return <TableStory data={largeData} />;
    },
    parameters: {
        docs: {
            description: {
                story: 'Table with a larger dataset to demonstrate pagination and performance.',
            },
        },
    },
};

export const EmptyState: Story = {
    render: () => <TableStory data={[]} showPagination={false} />,
    parameters: {
        docs: {
            description: {
                story: 'Table in empty state when no data is available.',
            },
        },
    },
};
