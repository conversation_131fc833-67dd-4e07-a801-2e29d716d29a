import gtxConstants from '@getrix/common/js/gtx-constants';
import { Icon, IconProps } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import { FC } from 'react';

import { SwiperCarousel } from 'gtx-react/components/SwiperCarousel';
import { RealEstateDetailProps } from 'gtx-react/containers/RealEstateDetail/RealEstateDetail';
import { RealEstateModalTab } from 'gtx-react/containers/RealEstateDetail/types';
import { prefetchImages } from 'lib/images';
import { DetailPropertyType } from 'lib/REST/types/detail-property';
import { getValueByPath } from './utils';

type MediaButtonDef = {
    name: RealEstateModalTab;
    icon: IconProps['name'];
    title: string;
    path?: Array<string | number>;
    showCount?: Boolean;
    onClick?: React.MouseEventHandler<HTMLButtonElement>;
};

const AD_IMAGE_FIELD: MediaButtonDef[] = [
    {
        name: 'photos',
        icon: 'image',
        title: trans('label.photo'),
        showCount: true,
        path: ['properties', 0, 'images'],
    },
];
const NEW_CONSTRUCTIONS_IMAGE_FIELD: MediaButtonDef[] = [
    {
        name: 'photos',
        icon: 'image',
        title: trans('label.photo'),
        showCount: true,
        path: ['images'],
    },
];
const COMMON_MEDIA_FIELDS: MediaButtonDef[] = [
    {
        name: 'plans',
        icon: 'planimetry',
        title: trans('label.plan'),
        path: ['properties', 0, 'plans'],
    },
    {
        name: 'photoplan',
        icon: 'planimetry-cursor',
        title: trans('label.fotoplan'),
        path: ['externalMedia', 'hasPhotoPlan'],
    },
    {
        name: 'virtualtour',
        icon: 'tour',
        title: trans('label.virtual_tour'),
        path: ['externalMedia', 'virtualTourUrl'],
    },
    {
        name: 'video',
        icon: 'video',
        title: trans('label.video'),
        path: ['externalMedia', 'videoUrl'],
    },
];

export const AD_MEDIA_FIELDS = [...AD_IMAGE_FIELD, ...COMMON_MEDIA_FIELDS];

export const NEW_CONSTRUCTION_MEDIA_FIELDS = [
    ...NEW_CONSTRUCTIONS_IMAGE_FIELD,
    ...COMMON_MEDIA_FIELDS,
];

type MediaButtonProps = MediaButtonDef & {
    data: DetailPropertyType;
    onClick?: React.MouseEventHandler<HTMLButtonElement>;
};
const MediaButton: FC<MediaButtonProps> = ({
    data,
    icon,
    onClick,
    title,
    path,
    showCount,
}) => {
    const value = getValueByPath(data, path);

    if (!value) {
        return null;
    }

    return (
        <button className="media-button" onClick={onClick}>
            <Icon name={icon} />
            <span>{`${showCount ? `${value.length} ` : ''}${title}`}</span>
        </button>
    );
};

type MediaSectionProps = { realEstate: DetailPropertyType } & Pick<
    RealEstateDetailProps,
    'onTabChange' | 'category'
>;

export const MediaSection: FC<MediaSectionProps> = ({
    realEstate,
    onTabChange,
    category,
}) => {
    const property = realEstate.properties?.length
        ? realEstate.properties[0]
        : null;

    let list;

    if (category === 'newConstruction') {
        list = realEstate?.images?.map(({ id }) => ({
            src: `${gtxConstants('GETRIX_MEDIA_URL')}/${gtxConstants(
                'AD_IMAGE_ENDPOINT'
            )}/${id}/print.jpg`,
        }));
    } else {
        list = property?.images?.map(({ url }) => ({ src: url }));
    }

    if (property?.plans?.length) {
        prefetchImages(property.plans.map((plan) => plan.url));
    }

    return (
        <>
            <div className="carousel-container carousel-feature">
                {list?.length ? (
                    <SwiperCarousel items={list} variant="gallery" />
                ) : (
                    <div className="carousel-container__noPhoto">
                        <Icon name="image-off" />
                        <span>{trans('alert.no_photo')}</span>
                    </div>
                )}
            </div>
            <div className="propertyDetail__nav">
                {(category === 'newConstruction'
                    ? [...NEW_CONSTRUCTION_MEDIA_FIELDS]
                    : [...AD_MEDIA_FIELDS]
                ).map((x) => (
                    <MediaButton
                        key={`media-button-${x.name}`}
                        data={realEstate}
                        {...x}
                        onClick={onTabChange(x.name)}
                    />
                ))}
            </div>
        </>
    );
};
