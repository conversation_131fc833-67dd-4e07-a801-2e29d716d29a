{% extends 'base.html.twig' %}

{% block header %}
<div id="header"></div>
{% endblock %}

{% block content %}
<div id="content" class="section-property-billboard"></div>
{% endblock %}

{% block reactApp %}
<div id="root"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script id="initial-state" type="application/json">{{ initialState|json_encode|raw }}</script>
    <script id="agency-info" type="application/json">{{ agency|json_encode|raw }}</script>
    <script id="header-config" type="application/json">{{ headerConfig|json_encode|raw }}</script>
    <script id="site-section" type="application/json">{{ site_section }}</script>
    <script id="site-section-title" type="application/json">{{ title }}</script>
    <script id="site-subsection" type="application/json">{{ site_subsection }}</script>
    <script id="agent-image-thumb" type="application/json">
        {% if logged_agent and logged_agent.urlImmagineAgenteThumb %}{{ logged_agent.urlImmagineAgenteThumb|raw }}{% endif %}
    </script>
    {% include 'commons/core/js-scripts.html.twig' %}
    {{ webpack_asset('base/property-billboard') }}
{% endblock %}
