import { Button } from '@gx-design/button';
import { Modal } from '@gx-design/modal';
import { trans } from '@pepita-i18n/babelfish';
import { Form, Formik, useFormikContext } from 'formik';
import { ucFirst } from 'lib/strings-formatter';
import GeographyFields from 'gtx-react/containers/GeographyFields';
import TypologyFields from 'gtx-react/containers/TypologyFields';
import * as Yup from 'yup';
import {
    GxFkCheckbox,
    GxFkInput,
    GxFkRangeInput,
    GxFkSelect,
} from 'gtx-react/components/gx-formik';
import { useQuery, useSuspenseQuery } from '@tanstack/react-query';
import {
    createCategoriesQueryOptions,
    createContentQueryOptions,
    createFloorTypesQueryOptions,
    createProvincesQueryOptions,
} from 'lib/REST/requests/acquisizione/annunci-privati/initial-data/query-factory';
import { lookupEndpoints } from '../../web-api/endpoints';
import { useMemo } from 'react';
import { useAppSearchParams } from '../../contexts/useAppSearchParams';
import { createPropertiesQueryOptions } from 'lib/REST/requests/acquisizione/annunci-privati/properties/query-factory';
import { useFiltersSearchParams } from '../../contexts/useFiltersSearchParams';

export const FiltersModalFooter = ({ onClose }: { onClose: () => void }) => {
    const { submitForm } = useFormikContext<FiltersSchemaType>();

    return (
        <>
            <Button onClick={onClose} variant="ghost">
                {trans('label.cancel')}
            </Button>
            <Button onClick={submitForm} variant="accent">
                {trans('label.apply_filters')}
            </Button>
        </>
    );
};

const FiltersForm = () => {
    const { data: provinces } = useSuspenseQuery({
        ...createProvincesQueryOptions(),
        select: (data) => data?.provinces?.data,
    });

    const { data: categories } = useSuspenseQuery({
        ...createCategoriesQueryOptions(),
        select: (data) => data?.categories?.data,
    });

    const { data: floorTypes } = useSuspenseQuery({
        ...createFloorTypesQueryOptions(),
        select: (data) => data?.floorTypes?.data,
    });

    const {
        data: {
            contracts: { data: contractsData },
            rooms,
            outcomes: outcomesList,
        },
    } = useSuspenseQuery({
        ...createContentQueryOptions(),
        select: (data) => data.content,
    });

    const outcomes = useMemo(
        () =>
            Object.values(outcomesList).map((item) => {
                return {
                    value: item.value,
                    label: item.label,
                };
            }),
        [outcomesList]
    );

    return (
        <Form className="modal-filters">
            <div className="filter-box__section">
                <div className="filter-box__section__item filter-box__section__item__code">
                    <GxFkInput
                        type="text"
                        id="code"
                        name="code"
                        placeholder={ucFirst(trans('label.code'))}
                        label={trans('label.insert_property_code')}
                    />
                </div>
            </div>
            <GeographyFields
                hasCountry={false}
                hasRegion={false}
                noPlaceholder={['province', 'city']}
                initialValuesMap={{
                    provinces,
                }}
                customEndpointsMap={{
                    cities: lookupEndpoints.GET_CITIES_LOOKUP,
                    zones: lookupEndpoints.GET_ZONES_LOCALITIES_LOOKUP,
                }}
                placeholders={{
                    city: trans('label.choose'),
                    zone: trans('label.choose'),
                }}
            />

            <div className="filter-box__section">
                <TypologyFields
                    placeholders={{
                        category: trans('label.any'),
                        contract: trans('label.any'),
                        typology: trans('label.any'),
                    }}
                    hasTypology={true}
                    initialValues={{
                        categories: categories,
                        contracts: contractsData,
                    }}
                    reqEndpointsMap={{
                        typology: lookupEndpoints.GET_TYPOLOGIES_LOOKUP,
                        contract: lookupEndpoints.GET_CONTRACTS_LOOKUP,
                    }}
                />
            </div>
            <div className="filter-box__section">
                <div className="filter-box__section__item">
                    <GxFkRangeInput
                        id="price"
                        name="price"
                        min={0}
                        minSuffix="_from"
                        maxSuffix="_to"
                        label={trans('label.price')}
                        type="number"
                        className="fromto gx-input"
                    />
                </div>
                <div className="filter-box__section__item">
                    <GxFkRangeInput
                        id="surface"
                        name="surface"
                        min={0}
                        minSuffix="_from"
                        maxSuffix="_to"
                        label={trans('label.surface')}
                        type="number"
                        className="fromto gx-input"
                    />
                </div>
                <div className="filter-box__section__item">
                    <GxFkSelect
                        id="rooms"
                        name="rooms"
                        label={trans('label.rooms_number')}
                        options={rooms}
                        placeholder={trans('label.any')}
                    />
                </div>
                <div className="filter-box__section__item">
                    <GxFkSelect
                        id="floor"
                        name="floor"
                        label={trans('label.floor')}
                        options={floorTypes}
                        placeholder={trans('label.any')}
                    />
                </div>
            </div>
            <div className="filter-box__section filter-boxEsito">
                <div className="filter-box__section__item">
                    <GxFkSelect
                        id="outcome"
                        name="outcome"
                        label={trans('label.outcome')}
                        options={outcomes}
                        placeholder={trans('label.any')}
                    />
                </div>
                <div className="filter-box__section__item">
                    <GxFkCheckbox
                        isFullWidth
                        variant="button"
                        isReversed
                        name="with_phone"
                        label={trans('label.with_phone')}
                    />
                </div>
                <div className="filter-box__section__item">
                    <GxFkCheckbox
                        isFullWidth
                        variant="button"
                        isReversed
                        name="with_matches"
                        label={trans('label.with_matches')}
                    />
                </div>
            </div>
            <div className="filter-box__section hidden">
                <div className="filter-box__section__item">
                    <GxFkCheckbox
                        isFullWidth
                        variant="button"
                        isReversed
                        name="no_agencies"
                        label={trans('label.exclude_agencies')}
                    />
                </div>
            </div>
        </Form>
    );
};

export const filtersSchema = Yup.object().shape({
    code: Yup.string()
        .min(3, trans('label.insert_at_least_n_chars', { N: '3' }))
        .optional(),
    city: Yup.string().required(trans('label.municipality_required')),
});

export type FiltersSchemaType = Yup.InferType<typeof filtersSchema>;

export default ({ isOpen, close }) => {
    const searchParams = useAppSearchParams();
    const { data } = useQuery(
        createPropertiesQueryOptions({ query: searchParams })
    );

    const { onChangeFilter } = useFiltersSearchParams();

    return (
        <Formik
            initialValues={{
                code: data?.filters.code ?? '',
                province: data?.filters.province ?? '',
                city: data?.filters.city ?? '',
                zones: data?.filters.zones ?? '',
                ['no_agencies']: data?.filters['no_agencies'] ?? '',
                ['with_matches']: data?.filters['with_matches'] ?? '',
                ['with_phone']: data?.filters['with_phone'] ?? '',
                outcome: data?.filters.outcome ?? '',
                floor: data?.filters.floor ?? '',
                ['surface_from']: data?.filters['surface_from'] ?? '',
                ['surface_to']: data?.filters['surface_to'] ?? '',
                ['price_from']: data?.filters['price_from'] ?? '',
                ['price_to']: data?.filters['price_to'] ?? '',
                rooms: data?.filters.rooms ?? '',
                category: data?.filters.category ?? '',
                typology: data?.filters.typology ?? '',
                contract: data?.filters.contract ?? '',
            }}
            validationSchema={filtersSchema}
            validateOnBlur={false}
            validateOnChange={false}
            onSubmit={(data) => {
                onChangeFilter(data);
                close();
            }}
        >
            <Modal
                title={trans('label.filter')}
                footer={<FiltersModalFooter onClose={close} />}
                onClose={close}
                isOpen={isOpen}
            >
                <FiltersForm />
            </Modal>
        </Formik>
    );
};
