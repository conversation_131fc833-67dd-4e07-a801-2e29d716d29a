import { createContext, memo, useContext, useMemo, useReducer } from 'react';
import { CustomMenuComponent, MenuConfig } from '../../types';
import { isMenuItem } from '../utils/type-guards';

type GxNavigationMenuContextProps = {
  openMenu: boolean;
  openSideMenu: boolean;
  openProfile: boolean;
  currentSecondaryMenu: MenuConfig | CustomMenuComponent;
};

type GxNavigationMenuControllerContextProps = {
  dispatch: React.Dispatch<ActionState>;
};
export const GxNavigationMenuContext = createContext<GxNavigationMenuContextProps | undefined>(
  undefined,
);

export const GxNavigationMenuDispatcher = createContext<
  GxNavigationMenuControllerContextProps | undefined
>(undefined);

type GxNavigationMenuState = {
  /**
   * Whether the main menu is open: set from the hamburger menu in mobile
   */
  openMenu: boolean;
  /**
   * Whether the side menu is open: set from the secondary menu
   */
  openSideMenu: boolean;
  /**
   * Whether the profile menu is open: set from the user icon
   */
  openProfile: boolean;
  /**
   * The current secondary menu item: set from the secondary menu
   */
  currentSecondaryMenu: MenuConfig | CustomMenuComponent;
};

const initialState: GxNavigationMenuState = {
  openMenu: false,
  openSideMenu: false,
  openProfile: false,
  currentSecondaryMenu: {
    item: undefined,
    subMenu: undefined,
  },
};

type ActionState =
  | { type: 'TOGGLE_MENU'; payload?: MenuConfig }
  | {
      type:
        | 'CLOSE_MENU' // <- closing stuff
        | 'CLOSE_SIDE_MENU'
        | 'CLOSE_PROFILE'
        | 'OPEN_MENU'
        | 'OPEN_SIDE_MENU'
        | 'OPEN_PROFILE'
        | 'OPEN_PROFILE_AS_SIDE_MENU'
        | 'OPEN_CUSTOMER_CARE_AS_SIDE_MENU';
    }
  | {
      type: 'SET_CURRENT_SECONDARY_MENU';
      payload: MenuConfig | CustomMenuComponent;
    };

const reducer = (state: GxNavigationMenuState, action: ActionState): GxNavigationMenuState => {
  // console.log('%cAction: ', 'color: red', action); // Uncomment for debugging
  switch (action.type) {
    case 'TOGGLE_MENU':
      if (state.openMenu) {
        return { ...state, openMenu: false, openSideMenu: false };
      }

      return {
        ...state,
        openMenu: true,
        openSideMenu: isMenuItem(action.payload) && !!action.payload.subMenu,
      };
    case 'CLOSE_MENU':
      if (state.openMenu === false && state.openSideMenu === false) {
        // Optimization to avoid unnecessary re-renders
        return state;
      }

      return { ...state, openMenu: false, openSideMenu: false };
    case 'CLOSE_SIDE_MENU':
      // Optimization to avoid unnecessary re-renders
      if (
        state.openSideMenu === false &&
        'item' in state.currentSecondaryMenu &&
        state.currentSecondaryMenu?.item === undefined
      ) {
        return state;
      }

      return { ...state, openSideMenu: false, currentSecondaryMenu: { item: undefined } };
    case 'CLOSE_PROFILE':
      return { ...state, openProfile: false };
    case 'OPEN_MENU':
      return { ...state, openMenu: true };
    case 'OPEN_SIDE_MENU':
      return { ...state, openMenu: true, openSideMenu: true };
    case 'OPEN_PROFILE':
      return { ...state, openProfile: true, openSideMenu: false };
    case 'OPEN_PROFILE_AS_SIDE_MENU':
      return {
        ...state,
        openSideMenu: true,
        currentSecondaryMenu: { customMenuComponent: 'profile' },
      };
    case 'OPEN_CUSTOMER_CARE_AS_SIDE_MENU':
      return {
        ...state,
        openSideMenu: true,
        currentSecondaryMenu: { customMenuComponent: 'customerCare' },
      };
    case 'SET_CURRENT_SECONDARY_MENU':
      return {
        ...state,
        openProfile: false,
        openSideMenu: true,
        currentSecondaryMenu: action.payload,
      };
    default:
      throw new Error('Invalid action type');
  }
};

const useMenuReducer = () => {
  return useReducer(reducer, initialState);
};

export const GxNavigationMenuProvider = memo(function GxNavigationMenuProvider(props: {
  children: React.ReactNode;
}) {
  const [state, dispatch] = useMenuReducer();

  const controllers = useMemo(() => ({ dispatch }), [dispatch]);

  return (
    <GxNavigationMenuContext.Provider value={state}>
      <GxNavigationMenuDispatcher.Provider value={controllers}>
        {props.children}
      </GxNavigationMenuDispatcher.Provider>
    </GxNavigationMenuContext.Provider>
  );
});

export const useGxNavigationMenuContext = () => {
  const context = useContext(GxNavigationMenuContext);

  if (!context) {
    throw new Error('useGxNavigationMenuContext must be used within a GxNavigationMenuProvider');
  }

  return context;
};

export const useGxNavigationMenuDispatcher = () => {
  const context = useContext(GxNavigationMenuDispatcher);

  if (!context) {
    throw new Error(
      'useGxNavigationMenuControllerContext must be used within a GxNavigationMenuControllerProvider',
    );
  }

  return context;
};
