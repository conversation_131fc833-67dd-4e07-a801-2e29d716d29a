import React, { useMemo, useState } from 'react';
import { Alert } from '@gx-design/alert';
import { Input } from '@gx-design/input';
import { Button } from '@gx-design/button';
import { trans } from '@pepita-i18n/babelfish';
import { Loader } from 'gtx-react/components';
import { Badge } from '@gx-design/badge';
import { useRankingDetailComparisonMutation } from './hooks/useRankingDetailComparisonMutation';

type RankingDetailModalComparisonType = {
    propertyId: string;
};

type comparisonScoreItem = {
    perk: string;
    scoreAd1: number;
    scoreAd2: number;
};

export const RankingDetailModalComparison: React.FC<
    RankingDetailModalComparisonType
> = ({ propertyId }) => {
    const [comparisonId, setComparisonId] = useState<string>('');

    const {
        mutate,
        isPending,
        data: mutationData,
    } = useRankingDetailComparisonMutation();

    const data = useMemo(() => {
        if (mutationData?.data) {
            const allTypes = new Set();

            mutationData.data[0]?.data.detail?.forEach((item) =>
                allTypes.add(item.Type)
            );
            mutationData.data[1]?.data.detail?.forEach((item) =>
                allTypes.add(item.Type)
            );

            const comparisonScores = Array.from(allTypes).map((type) => {
                const ad1Score =
                    mutationData.data[0]?.data.detail?.find(
                        (item) => item.Type === type
                    )?.Score || 0;
                const ad2Score =
                    mutationData.data[1]?.data.detail?.find(
                        (item) => item.Type === type
                    )?.Score || 0;

                return {
                    perk: type as string,
                    scoreAd1: ad1Score,
                    scoreAd2: ad2Score,
                };
            }) as comparisonScoreItem[];

            return {
                adRanking: mutationData?.data[0]?.data?.value || 0,
                adComparedRanking: mutationData?.data[1]?.data?.value || 0,
                comparisonScores,
            };
        }
    }, [mutationData]);

    return (
        <>
            {isPending && (
                <Loader loading={true} fixedOverlay={false} centered={false} />
            )}
            <Alert withMarginBottom style="info">
                {trans('ranking.insert_ad_is')}
            </Alert>
            <div className="gx-row gx-box-row property-ranking-details-modal__compare">
                <div className="gx-col-xs-5">
                    <strong>{trans('label.ad')}</strong>
                </div>
                <div className="gx-col-xs-2 no-padding">
                    <Input
                        disabled
                        label={trans('label.current_ad')}
                        isLabelVisible={false}
                        value={propertyId}
                    />
                </div>
                <div className="gx-col-xs-1 no-padding">
                    <span>vs</span>
                </div>
                <div className="gx-col-xs-2 no-padding">
                    <Input
                        onChange={(e) => setComparisonId(e.target.value)}
                        label={trans('label.compare_with')}
                        isLabelVisible={false}
                        placeholder={trans('label.compare_with')}
                    />
                </div>
                <div className="gx-col-xs-2">
                    <Button
                        onClick={() => mutate({ propertyId, comparisonId })}
                        variant="accent"
                        disabled={isPending || !comparisonId}
                    >
                        {trans('label.compare')}
                    </Button>
                </div>
            </div>
            {data && (
                <div className="property-ranking-details-modal__comparisonData">
                    {data.comparisonScores.map((scoreRow, index) => {
                        return (
                            <div
                                key={`${scoreRow.perk}-${index}`}
                                className="gx-row"
                            >
                                <div className="gx-col-xs-5">
                                    <strong>{scoreRow.perk}</strong>
                                </div>
                                <div className="gx-col-xs-2 no-padding">
                                    <Badge
                                        style={
                                            scoreRow.scoreAd1 >
                                            scoreRow.scoreAd2
                                                ? 'success'
                                                : undefined
                                        }
                                        text={
                                            scoreRow.scoreAd1
                                                ? scoreRow.scoreAd1.toString()
                                                : ''
                                        }
                                    />
                                </div>
                                <div className="gx-col-xs-2 gx-col-xs-offset-1 no-padding">
                                    <Badge
                                        style={
                                            scoreRow.scoreAd2 >
                                            scoreRow.scoreAd1
                                                ? 'success'
                                                : undefined
                                        }
                                        text={
                                            scoreRow.scoreAd2
                                                ? scoreRow.scoreAd2.toString()
                                                : ''
                                        }
                                    />
                                </div>
                            </div>
                        );
                    })}
                    <div className="gx-row">
                        <div className="gx-col-xs-5">
                            <strong>{trans('label.ranking')}</strong>
                        </div>
                        <div className="gx-col-xs-2 no-padding">
                            <Badge
                                style={
                                    data.adRanking > data.adComparedRanking
                                        ? 'success'
                                        : undefined
                                }
                                text={
                                    data.adRanking
                                        ? data.adRanking.toString()
                                        : ''
                                }
                            />
                        </div>
                        <div className="gx-col-xs-2 gx-col-xs-offset-1 no-padding">
                            <Badge
                                style={
                                    data.adComparedRanking > data.adRanking
                                        ? 'success'
                                        : undefined
                                }
                                text={
                                    data.adComparedRanking
                                        ? data.adComparedRanking.toString()
                                        : ''
                                }
                            />
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};
