import { useRef } from 'react'
import { IToursApiResponse } from 'src/types/api'
import { IAppTour, IAppTourStep, OnBoardDataAttrItem, OnBoardHTMLElement } from 'src/types/tour'
/**
 *
 * @param tourListFromApi la lista dei tour proveniente dall'api
 * @returns getValidToursFromDom una funzione che dapprima fa il retrieve di tutti gli elementi nel dom con l'attributo
 * [data-onboarding] tornerà un array di appTours contente solo i tour per i quali sono stati trovati tutti gli elementi
 * nel DOM, cioè se un tour dall'API ha 3 step e nel DOM vengono trovati 2 elementi, il tour non viene tornato tra i tour per l'app
 *
 */
export const useBuildTourAppArrayFromDom = (tourListFromApi: IToursApiResponse['data']) => {
    const onBoardHtmlElArrayRef = useRef<NodeListOf<OnBoardHTMLElement> | null>(null)

    const buildTourAppArrayFromDom = (): IAppTour[] => {
        if (!tourListFromApi || tourListFromApi.length === 0) return []

        onBoardHtmlElArrayRef.current = document.querySelectorAll('[data-onboarding]')

        const appTourArray: IAppTour[] = []

        if (onBoardHtmlElArrayRef.current.length > 0) {

            for (const [, onBoardEl] of onBoardHtmlElArrayRef.current.entries()) {
                const elementToursSteps: OnBoardDataAttrItem[] = JSON.parse(onBoardEl.dataset.onboarding)

                for (const elementTourStep of elementToursSteps) {

                    const foundTourInApi = tourListFromApi.find(apiTourItm => apiTourItm.name === elementTourStep.tour)

                    /**
                     * Se trovo il tour tra quelli da mostrare tornati dalle api allora procedo ad
                     * includerlo nell'array dell'app
                     */

                    if (foundTourInApi) {

                        const alreadyExistingAppTour = appTourArray.find(tourItem =>
                            tourItem.name === elementTourStep.tour)
                        const foundStepInTourApi = foundTourInApi.steps
                            .find(apiTourStep => apiTourStep.order === elementTourStep.step)


                        if (foundStepInTourApi) {

                            const stepObj: IAppTourStep = {
                                id: foundStepInTourApi.id,
                                el: onBoardEl,
                                title: foundStepInTourApi.title,
                                body: foundStepInTourApi.body,
                                order: foundStepInTourApi.order,
                                imageUrl: foundStepInTourApi.imageUrl,
                                indicatorStyle: elementTourStep.indicatorStyle,
                                position: elementTourStep.position

                            }
                            if (alreadyExistingAppTour) {
                                alreadyExistingAppTour.steps.push(stepObj)
                            } else {
                                appTourArray.push({
                                    id: foundTourInApi.id,
                                    order: foundTourInApi.order,
                                    name: foundTourInApi.name,
                                    steps: [stepObj]
                                })
                            }
                        }


                    }

                }


            }
        }

        return appTourArray
    }

    //la lista derivata dei tour validi in prospettiva dell'aggiornamento in place di appTours
    const getValidToursFromDom = () => {
        const validTours = []

        const appTours = buildTourAppArrayFromDom()

        for (const appTourItem of appTours) {
            const foundTourInList = tourListFromApi.find(apiItm => apiItm.name === appTourItem.name)
            if (foundTourInList && appTourItem.steps.length === foundTourInList.steps.length) {
                validTours.push(appTourItem)
            }
        }

        validTours.sort((a, b) => a.order - b.order)

        return validTours
    }

    return { getValidToursFromDom }

}
