{"compilerOptions": {"pretty": true, "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": false, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "downlevelIteration": true, "noUnusedLocals": true, "strictNullChecks": false, "noImplicitAny": false, "baseUrl": "./src", "outDir": "./dist", "declaration": true}, "exclude": ["dist", "node_modules"], "include": ["src/**/*.ts", "src/**/*.tsx"]}