@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;

$ah-std-spacing: 1.5rem;
$ah-opacity-transition: opacity 0.2s linear;
$ah-activation-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
$ah-button-size: 5.6rem;
$ah-button-position: 2rem;
$ask-bkg: #fda932;

.gtx-ask-help {
  position: fixed;
  bottom: $ah-button-position;
  right: $ah-button-position;
  //Over the top
  @include z-index(9999);

  -webkit-transition: $ah-opacity-transition;
  transition: $ah-opacity-transition;

  .gx-icon {
    @include icon-size(sm);
  }

  &.scrolling {
    opacity: 0.8;
  }

  &Button {
    height: $ah-button-size;
    width: $ah-button-size;
    border-radius: radius(rounded);
    box-shadow: elevation(fixed-bottom);
    font-size: 2.1rem;
    color: #fff;
    background-color: $ask-bkg;
    border: none;
    cursor: pointer;

    .opened-icon {
      opacity: 0;
      visibility: hidden;
    }

    .closed-icon,
    .opened-icon {
      -webkit-transition: $ah-activation-transition;
      transition: $ah-activation-transition;

      position: absolute;
      top: 0;
      left: 0;
      margin-left: 1.3rem;
      margin-top: 1.3rem;
      width: 3rem;
      height: 3rem;
      text-align: center;
      font-size: 2.4rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &.open .dropdown-toggle {
    .closed-icon,
    .opened-icon {
      transform: rotate(90deg);
    }
    .closed-icon {
      opacity: 0;
      visibility: hidden;
    }
    .opened-icon {
      opacity: 1;
      visibility: visible;
    }
  }

  &Menu {
    left: auto;
    right: 0;
    bottom: 7.2rem;
    min-width: 24rem;
    -webkit-transition: $ah-activation-transition;
    transition: $ah-activation-transition;
    display: block;
    visibility: hidden;
    opacity: 0;
    bottom: 70%;

    padding: $ah-std-spacing 0;
    margin-bottom: $ah-std-spacing;
    border: none;
    box-shadow: elevation(raised);

    * {
      white-space: nowrap;
    }

    .gx-button {
      height: ($gx-unit-size * 3);
      padding: 0;
      font-weight: inherit;

      .gx-icon {
        @include icon-size(sm);
      }
    }
  }

  &.open .dropdown-menu {
    visibility: visible;
    opacity: 1;
    bottom: 100%;
    margin-bottom: space(md);
  }

  &Avatar {
    width: ($gx-unit-size * 9);
    height: ($gx-unit-size * 9);
    margin-right: space(sm);
    overflow: hidden;
    border-radius: radius(rounded);
    border: 0.2rem solid color(border-main);

    img {
      max-width: 100%;
    }
  }

  &Contacts {
    display: flex;
    padding: space(md);
    margin-bottom: space(md);
    border-bottom: 0.1rem solid color(border-main);
  }

  &Comment {
    margin: space(sm) space(md) 0;
  }

  .gx-button--ghost {
    padding: 0;
    font-weight: normal;
  }

  .gx-title-2 {
    margin: 0 space(md);
  }
}

body #__toctoc-live-box-container {
  padding: 2rem;
  @include z-index(modal, 3);

  & > div.iconized-button.icon-chat {
    background-size: 50% 50% !important;
    background-color: #fda932 !important;
    border-color: #ca8728 !important;
  }

  & > div.iconized-button.icon-ring {
    background-size: 50% 50% !important;
    background-color: #fda932 !important;
    border-color: #ca8728 !important;

    &.enter {
      background-size: 50% 50% !important;
    }
  }

  #__toctoc-action-hangup {
    background-color: rgba(0, 0, 0, 0.1) !important;
  }

  div.button.info {
    background-color: color(background-brand) !important;
    color: color(content-accent) !important;
    border-radius: radius(sm) !important;

    &:hover {
      background-color: color(background-brand) !important;
    }
  }

  #__toctoc-close {
    background-color: rgba(0, 0, 0, 0.1) !important;
    background-image: none !important;
    left: 26.5rem !important;
    top: 1.5rem !important;
    height: 2rem !important;
    width: 2rem !important;
    border-radius: radius(md) !important;

    &:before {
      content: '';
      position: absolute;
      height: 0.2rem;
      width: 50%;
      top: 50%;
      left: 27%;
      margin-top: -0.1rem;
      background-color: color(background-main);
      transform: rotate(45deg);
    }

    &:after {
      content: '';
      position: absolute;
      height: 0.2rem;
      width: 50%;
      top: 50%;
      left: 27%;
      margin-top: -0.1rem;
      background-color: color(background-main);
      transform: rotate(-45deg);
    }
  }
}

body #__toctoc-modal-overlay {
  & > #__toctoc-modal-container {
    & > #__toctoc-modal-footer {
      background-color: color(background-main) !important;

      #__toctoc-modal-accept {
        background-color: color(background-brand) !important;
        border-color: color(border-selected) !important;
      }

      #__toctoc-modal-close {
        background-color: rgba(0, 0, 0, 0) !important;
        border-color: rgba(0, 0, 0, 0) !important;
        color: color(content-action) !important;
      }
    }
  }
}

.gtx-ah-footer-spacing {
  height: $ah-button-size + ($ah-button-position * 2);
}

// Salesforce Customer care
body {
  .embeddedMessagingConversationButtonWrapper,
  .embeddedMessagingFrame {
    @include z-index(modal, -1);
  }
}

// Hides chat on some sections
.body-request-messages,
.body-active-searches {
  .embedded-messaging {
    display: none;
  }
}
