const path = require('path')
const sharedConfig = require('./webpack.shared')

module.exports = {
  ...sharedConfig,
  devServer: {
    static: {
      directory: path.join(__dirname, 'public'),
    },
    compress: true,
    port: 9000,
    devMiddleware: {
      writeToDisk: (filePath) => {
        return /^(?!.*(hot)).*/.test(filePath)
      },
    },
  },
  mode: 'development',
  devtool: 'inline-source-map',
}
