@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.notification-settings-container {
  max-width: 1024px;

  &__main-descr {
    margin-bottom: space(xl);
  }
  .package-itm-row {
    &__package-icon {
      max-width: 6.4rem;
    }
  }

  .notification-settings-zones-table {
    th {
      text-align: left;
    }
    td:last-child {
      text-align: right;
    }
    .package-itm-row {
      &__package-icon {
        margin-right: space(md);
        img {
          max-width: 2.4rem;
        }
      }
    }
  }
}
.notification-settings-category-counter {
  color: color(content-medium);
  margin-top: space(xs);
  @include typography(body-small);

  &.disabled {
    color: color(content-low);
  }

  a {
    cursor: pointer;
  }
}

.notification-settings-enable-modal__zone-detail {
  padding: space(md);
  background-color: color(background-alt);
  margin-bottom: 1rem;
}

.notification-settings-categories-table {
  th {
    text-align: left;

    .gx-checkbox {
      &__text {
        padding-top: space(xs);
      }
    }
  }
}
