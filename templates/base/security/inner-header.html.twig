<div class="login-header">
	<a href="{{ globalConfigs.immobiliare.baseUrl }}" class="login-header__logo">
		<img class="login-header__logo-default" src="/bundles/base/getrix/common/img/country/{{ gtxConstants.COUNTRY_TAG }}/logo/logo.svg" alt="logo" />
		<img class="login-header__logo-reversed" class="" src="/bundles/base/getrix/common/img/country/{{ gtxConstants.COUNTRY_TAG }}/logo/logo_reversed.svg" alt="logo" />
	</a>
	<div class="login-header__action">
		{% if customer is defined and customer %}
			<span class="pubblica-annunci gx-body-small">{{ "login.header.customer"|trans({}, gtxConstants.DOMAIN_CMS) }}</span>
			<a class="gx-button" href="{{ path('app_login') }}">{{ "btn.label.login"|trans({}, gtxConstants.DOMAIN_CMS) }}</a>
		{% else %}
			<span class="pubblica-annunci gx-body-small">{{ "login.header.not_customer"|trans({}, gtxConstants.DOMAIN_CMS) }}</span>
			<a class="gx-button" href="{{ path('app_register') }}">{{ "btn.label.sign_in"|trans({}, gtxConstants.DOMAIN_CMS) }}</a>
		{% endif %}
	</div>
</div>
