import { VisitType } from "../../../../../base/pages/messaging/types";

export interface IApiRoom {
    id?: number;
    agentId: number;
    propertyId: number;
    scheduledTime: string;
    note: string;
    guests: IApiPostRoomGuest[];
}

export interface IApiPostRoomGuest {
    id?: number;
    email: string;
}

export interface IApiRoomResponse {
    id: number;
    // agentId: null;
    agent: { id: number; firstname: string; lastname: string };
    startTime: null | string;
    endTime: null | string;
    scheduledTime: string;
    externalId: string;
    created: string;
    note: string;
    modified: string;
    guests: IGuests[];
    property: IProperty;
}

export interface  IOnSubmitContextCallbackReturn extends IApiRoomResponse {
    visitType: VisitType
}
interface IProperty {
    id: number;
    properties: IProperties[];
    prices: IPrices[];
    mainImage: number;
    created: string;
    modified: string;
    propertiesDetailUrl: string;
    printableSurface: string;
    printableTypology: string;
    printablePrice: string;
    mainThumbUrl: string;
    geographyInformations: IGeographyInformations;
}

interface IProperties {
    id: number;
    reference: string;
    typologyV2: ITypologyV2;
    geographyInformation: IGeographyInformation;
    isMain: boolean;
    surface: number;
    rooms: number;
    bathrooms: number;
    floor: null;
}

interface ITypologyV2 {
    id: number;
    name: string;
    parent: IParent;
}

interface IParent {
    id: number;
    name: string;
    parent: IParent | null;
}

interface IGeographyInformation {
    city: ICity;
    macrozone: IMacrozone;
    address: IAddress;
    coordinates: ICoordinates;
    showAddress: boolean;
}

interface ICity {
    id: number;
    name: string;
    province: IProvince;
    cityMacroZoneType: null;
}

interface IProvince {
    id: string;
    name: string;
}

interface IMacrozone {
    id: number;
    name: string;
    nameSn: null;
    keyUrl: null;
    city: null;
}

interface IAddress {
    street: string;
    number: null;
}

interface ICoordinates {
    latitude: number;
    longitude: number;
}

interface IPrices {
    price: string;
    isVisible: boolean;
    contractId: number;
    printablePrice: string;
}

interface IGeographyInformations {
    city: ICity;
    macrozone: IMacrozone;
    address: IAddress;
    coordinates: ICoordinates;
    showAddress: boolean;
    printableCity: string;
    printableAddress: string;
}

interface IGuests {
    id: number;
    roomId: number;
    firstname: string;
    lastname: string;
    phone: string;
    email: string;
}

interface IPagination {
    start: number;
    results: number;
    total: number;
    page: number;
}
