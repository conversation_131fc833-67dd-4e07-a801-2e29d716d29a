import { Fragment, useState, useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import clsx from 'clsx';
import produce from 'immer';
import { format } from 'date-fns';
import { Formik, Form, useFormikContext } from 'formik';
import * as Yup from 'yup';
import { Loader } from 'gtx-react/components';
import { useFormikError } from 'gtx-react/hooks/useFormikError';
import { validPhoneNumberNotStrict } from '../utils/utility';
import { getLookupTypesApi, getLookupSourcesApi, getLookupDocumentTypesApi } from '../web-api';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { Alert } from '@gx-design/alert';
import { Badge } from '@gx-design/badge';
import { Tooltip } from '@gx-design/tooltip';
import { trans } from '@pepita-i18n/babelfish';
import { GxFkCheckbox, GxFkSelect, GxFkAddonInput } from 'gtx-react/components/gx-formik';
import { useFormikGeoFields } from 'gtx-react/hooks/useFormikGeoFields';
import { Checkbox } from '@gx-design/checkbox';
import { GxFkInput } from 'gtx-react/components/gx-formik';

const queryConfig = { refetchOnWindowFocus: false, staleTime: 1000 * 60 * 30 };
const FORM_ID = 'customerForm';

const Note = ({ isChecked, note, onClick }) => (
    <tr className={clsx('gx-table__row', { selected: isChecked })}>
        <td className="gx-table__field">
            <Checkbox onChange={onClick} checked={isChecked} />
        </td>
        <td className="gx-table__field">
            <Tooltip text={note.popoverDate}>
                <span>{note.created}</span>
            </Tooltip>
        </td>
        <td>{note.note}</td>
    </tr>
);

const NotesList = ({ notes, handleDelete }) => {
    const [selected, setSelected] = useState([]);
    const [bulk, setBulk] = useState(false);

    const handleClick = (index) => {
        let nextState;

        if (selected.indexOf(index) === -1) {
            nextState = produce(selected, (draftState) => {
                draftState.push(index);
            });
        } else {
            nextState = selected.filter((item) => item !== index);
        }

        setSelected(nextState);
    };

    const handleBulkSelect = () => {
        let selected = [];

        if (!bulk) {
            for (let index in notes) {
                index in notes ? selected.push(parseInt(index)) : null;
            }
        }

        setSelected(selected);
        setBulk(!bulk);
    };

    const onDeleteSelected = (evt) => {
        evt.preventDefault();

        handleDelete(selected);
        setSelected([]);
    };

    const isChecked = (index) => selected.indexOf(index) !== -1;

    return (
        <div className="gtx-list gtx-list-sm gtx-cst-note-container">
            <div className="gtx-list-heading gtx-cst-note-header">
                <Checkbox
                    onChange={handleBulkSelect}
                    checked={selected.length === notes.length}
                    indeterminate={selected.length && selected.length !== notes.length}
                />
                <Button
                    disabled={selected.length === 0}
                    className="gtx-cst-remove-notes pull-right"
                    onClick={onDeleteSelected}
                >
                    <Icon name="bin" />
                    <span>{trans('label.delete_selected')}</span>
                    <Badge text={selected.length} className="gtx-notes-selected" />
                </Button>
            </div>
            <div className="gtx-cst-note-list">
                <table className="gx-table">
                    <tbody className="gx-table__body">
                        {notes.map((item, index) => (
                            <Note
                                note={item}
                                key={`${item.timestamp}${index}`}
                                isChecked={isChecked(index)}
                                onClick={() => handleClick(index)}
                            />
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

const WrapperElement = ({ children }) => <div>{children}</div>;

const FormComponent = (props) => {
    const { customer, handleCancel, inModal, modalSubmitLabel, showContactsAlert, lookupEndpoints, setInitialValues } =
        props;
    const formik = useFormikContext();
    const [notes, setNotes] = useState([]);
    const noteInputRef = useRef();

    const getFormikGeoFieldProps = useFormikGeoFields([
        {
            type: 'country',
            placeholder: trans('label.any'),
            customEndpoint: lookupEndpoints.GET_CUSTOMER_GEO_NATION_LOOKUP,
        },
        {
            type: 'region',
            placeholder: trans('label.any'),
            customEndpoint: { url: lookupEndpoints.GET_CUSTOMER_GEO_REGION_LOOKUP, searchParam: 'value' },
        },
        {
            type: 'province',
            placeholder: trans('label.any'),
            customEndpoint: { url: lookupEndpoints.GET_CUSTOMER_GEO_PROVINCE_LOOKUP, searchParam: 'value' },
        },
        {
            type: 'city',
            placeholder: trans('label.any'),
            customEndpoint: { url: lookupEndpoints.GET_CUSTOMER_GEO_CITY_LOOKUP, searchParam: 'value' },
        },
    ]);

    const { isFetching: fetchingTypes, data: types } = useQuery({
        queryKey: ['types'],
        queryFn: () => getLookupTypesApi().then((res) => res),
        ...queryConfig,
    });
    const { isFetching: fetchingSources, data: sources } = useQuery({
        queryKey: ['sources'],
        queryFn: () => getLookupSourcesApi().then((res) => res),
        ...queryConfig,
    });
    const { isFetching: fetchingDocumentTypes, data: documentTypes } = useQuery({
        queryKey: ['documentTypes'],
        queryFn: () => getLookupDocumentTypesApi().then((res) => res),
        ...queryConfig,
    });

    const addNote = (e) => {
        e.preventDefault();

        const text = noteInputRef.current.value.trim();

        if (text) {
            const now = new Date();
            const nextState = produce(notes, (draftState) => {
                draftState.push({
                    note: text,
                    created: format(now, 'dd/MM/yyyy'),
                    popoverDate: format(now, 'dd/MM/yyyy HH:mm'),
                    timestamp: now.getTime(),
                });
            });

            if (typeof window !== 'undefined') {
                localStorage.setItem('notes', JSON.stringify(nextState));
            }

            formik.setFieldValue('noteText', '');

            setNotes(nextState);
        }
    };

    const deleteNotes = (selected) => {
        if (!selected.length) {
            return;
        }

        let nextNotes = notes.filter((_, index) => selected.indexOf(index) === -1);

        if (typeof window !== 'undefined') {
            localStorage.setItem('notes', JSON.stringify(nextNotes));
        }

        setNotes(nextNotes);
    };

    useFormikError({
        formik,
        selector: '.gx-helper--error',
    });

    useEffect(() => {
        if (!customer || 'customer' in customer) {
            return;
        }

        let data = {};
        for (const key in customer) {
            data[key] = customer[key] ?? '';
        }

        setInitialValues((values) => ({ ...values, ...data }));

        if (customer.notes && customer.notes.length) {
            let notes = [...customer.notes].map((item) => {
                const createdAt = new Date(item.created);
                return {
                    id: item.id,
                    note: item.note,
                    created: format(createdAt, 'dd/MM/yyyy'),
                    popoverDate: format(createdAt, 'dd/MM/yyyy HH:mm'),
                    timestamp: createdAt.getTime(),
                };
            });

            localStorage.setItem('notes', JSON.stringify(notes));
            return setNotes(notes);
        }
    }, [customer]);

    useEffect(() => {
        if (typeof window !== 'undefined') {
            const notes = localStorage.getItem('notes');

            if (customer && notes !== '' && notes !== null) {
                setNotes(JSON.parse(notes));
            } else {
                localStorage.setItem('notes', []);
                setNotes([]);
            }
        }
    }, []);

    return (
        <Form id={FORM_ID}>
            {showContactsAlert ? (
                <Alert style="error" withMarginBottom>
                    <Fragment>
                        <b>{trans('label.customer_error_title')}</b>
                        <br />
                        {trans('label.customer_error_message')}
                    </Fragment>
                </Alert>
            ) : null}
            <div className="gx-section">
                <div className="gx-row">
                    <div className="gx-col-xs-12 gx-col-sm-6">
                        <div className="gx-box-row">
                            <GxFkInput
                                id="firstName"
                                name="firstName"
                                type="text"
                                placeholder={trans('label.insert_name_2')}
                                label={trans('label.name')}
                            />
                        </div>
                    </div>
                    <div className="gx-col-xs-12 gx-col-sm-6">
                        <div className="gx-box-row">
                            <GxFkInput
                                id="lastName"
                                name="lastName"
                                type="text"
                                placeholder={trans('label.insert_surname_2')}
                                label={trans('label.surname')}
                            />
                        </div>
                    </div>
                </div>
                <div className="gx-row">
                    <div className="gx-col-xs-12 gx-col-sm-6">
                        <div className="gx-box-row">
                            <GxFkAddonInput
                                placeholder={trans('label.example_user_mail')}
                                name="email"
                                type="text"
                                id="email"
                                label={trans('label.mail')}
                                className={clsx('gx-input', 'gx-input--withAddon', {
                                    'gx-input-withAddon--negative': showContactsAlert,
                                })}
                                addon={{
                                    position: 'left',
                                    type: 'icon',
                                    value: 'mail',
                                }}
                            />
                        </div>
                    </div>
                    <div className="gx-col-xs-12 gx-col-sm-6">
                        <div className="gx-box-row">
                            <GxFkAddonInput
                                type="text"
                                label={trans('label.mobile_phone')}
                                id="mobilePhone"
                                name="mobilePhone"
                                placeholder={trans('label.insert_mobile_phone')}
                                className={clsx('gx-input', 'gx-input--withAddon', {
                                    'gx-input-withAddon--negative': showContactsAlert,
                                })}
                                addon={{ position: 'left', type: 'icon', value: 'mobile' }}
                            />
                        </div>
                    </div>
                </div>
                <div className="gx-row">
                    <div className="gx-col-xs-12 gx-col-sm-6">
                        <div className="gx-box-row">
                            <GxFkAddonInput
                                type="text"
                                label={trans('label.phone')}
                                id="phone"
                                name="phone"
                                placeholder={trans('label.insert_phone')}
                                className={clsx('gx-input', 'gx-input--withAddon', {
                                    'gx-input-withAddon--negative': showContactsAlert,
                                })}
                                addon={{ position: 'left', type: 'icon', value: 'phone' }}
                            />
                        </div>
                    </div>
                    <div className="gx-col-xs-12 gx-col-sm-6">
                        <div className="gx-box-row">
                            <GxFkAddonInput
                                type="text"
                                label={trans('label.fax')}
                                id="fax"
                                name="fax"
                                placeholder={trans('label.insert_fax')}
                                className={clsx('gx-input', 'gx-input--withAddon', {
                                    'gx-input-withAddon--negative': showContactsAlert,
                                })}
                                addon={{ position: 'left', type: 'icon', value: 'print' }}
                            />
                        </div>
                    </div>
                </div>
                <div className="gx-row">
                    <div className="gx-col-xs-12 gx-col-sm-6">
                        <div className="gx-box-row">
                            <GxFkSelect
                                id="documentTypeId"
                                name="documentTypeId"
                                label={trans('label.document')}
                                className={fetchingDocumentTypes ? 'ss-loading-options' : null}
                                options={documentTypes ?? []}
                            />
                        </div>
                    </div>
                    <div className="gx-col-xs-12 gx-col-sm-6">
                        <div className="gx-box-row">
                            <GxFkInput
                                id="documentNumber"
                                name="documentNumber"
                                type="text"
                                placeholder={trans('label.insert_document_number')}
                                label={trans('label.document_number')}
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div className="gx-section">
                <div className="gx-row">
                    <div className="gx-col-xs-12">
                        <div className="form-group-col2">
                            <div className="filter-box__section__item">
                                <GxFkSelect {...getFormikGeoFieldProps('country')} />
                            </div>
                            <div className="filter-box__section__item">
                                <GxFkSelect {...getFormikGeoFieldProps('region')} />
                            </div>
                            <div className="filter-box__section__item">
                                <GxFkSelect {...getFormikGeoFieldProps('province')} />
                            </div>
                            <div className="filter-box__section__item">
                                <GxFkSelect {...getFormikGeoFieldProps('city')} />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="gx-row">
                    <div className="gx-col-xs-12 gx-col-sm-6">
                        <div className="gx-box-row">
                            <GxFkInput
                                id="address"
                                name="address"
                                type="text"
                                placeholder={trans('label.insert_address')}
                                label={trans('label.address')}
                            />
                        </div>
                    </div>
                    <div className="gx-col-xs-6 gx-col-sm-3">
                        <div className="gx-box-row">
                            <GxFkInput
                                id="addressNumber"
                                name="addressNumber"
                                type="text"
                                label={trans('label.number')}
                            />
                        </div>
                    </div>
                    <div className="gx-col-xs-6 gx-col-sm-3">
                        <div className="gx-box-row">
                            <GxFkInput
                                id="postalCode"
                                name="postalCode"
                                type="text"
                                label={trans('label.postal_code')}
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div className="gx-section">
                <div className="gx-row">
                    <div className="gx-col-xs-12 gx-col-sm-6">
                        <div className="gx-box-row">
                            <GxFkSelect
                                id="typeId"
                                name="typeId"
                                label={trans('label.customer_type')}
                                className={fetchingTypes ? 'ss-loading-options' : null}
                                options={types ?? []}
                                placeholder={trans('label.any')}
                            />
                        </div>
                    </div>
                    <div className="gx-col-xs-6 gx-col-sm-6">
                        <div className="gx-box-row">
                            <GxFkSelect
                                placeholder={trans('label.any')}
                                id="sourceId"
                                name="sourceId"
                                label={trans('label.customer_source')}
                                className={fetchingSources ? 'ss-loading-options' : null}
                                options={sources ?? []}
                            />
                        </div>
                    </div>
                </div>
                <div className="gx-row">
                    <div className="gx-col-xs-12 gx-col-sm-6">
                        <div className="gx-box-row">
                            <label className="gx-label">{trans('label.agency_privacy_sheet')}</label>
                            <GxFkCheckbox
                                name="privacy"
                                variant="button"
                                isReversed
                                isFullWidth
                                label={trans('label.purchase')}
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div className="gx-section">
                <div className="gx-row">
                    <div className="gx-col-xs-12">
                        <div className="gx-card gx-box-row">
                            <div className="report-customer-form-notes">
                                <div className="gx-input-master-wrapper">
                                    <GxFkAddonInput
                                        ref={noteInputRef}
                                        id="noteText"
                                        type="text"
                                        name="noteText"
                                        placeholder={trans('label.note_placeholder')}
                                        addon={{ position: 'left', type: 'icon', value: 'note' }}
                                        isLabelVisible={false}
                                    />
                                </div>
                                <Button variant="accent" onClick={addNote}>
                                    <span>{trans('label.insert')}</span>
                                </Button>
                            </div>
                            {notes && notes.length > 0 ? (
                                <NotesList notes={notes} handleDelete={deleteNotes} />
                            ) : (
                                <span className="report-customer-no-notes">{trans('label.empty_notes')}</span>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            {inModal ? (
                <div className="gx-section">
                    <div className="gx-end-xs">
                        <Button title={trans('label.cancel')} onClick={handleCancel} variant="ghost">
                            {trans('label.cancel')}
                        </Button>
                        <Button variant="accent" title={modalSubmitLabel} type="submit">
                            {modalSubmitLabel}
                        </Button>
                    </div>
                </div>
            ) : (
                <div className="gx-section">
                    <div className="pull-right">
                        <Button variant="accent" title={trans('label.add')} type="submit">
                            {trans('label.add')}
                        </Button>
                    </div>
                </div>
            )}
        </Form>
    );
};

export const CustomerForm = ({ customer, handleSubmit, loading, ...props }) => {
    const [showContactsAlert, setShowContactsAlert] = useState(false);
    const [initialValues, setInitialValues] = useState({
        firstName: '',
        lastName: '',
        email: '',
        mobilePhone: '',
        phone: '',
        fax: '',
        documentTypeId: '',
        documentNumber: '',
        address: '',
        addressNumber: '',
        postalCode: '',
        typeId: '',
        sourceId: '',
        noteText: '',
        privacy: false,
        country: '',
        region: '',
        province: '',
        city: '',
    });
    const scrolModalUp = () => {
        const modalContainer = document.querySelector('.gx-modal');
        modalContainer ? document.querySelector('.gx-modal').scrollTo(0, 0) : window.scrollTo(0, 0);
    };

    const onSubmit = (data) => {
        if (!data.email && !data.phone && !data.mobilePhone) {
            setShowContactsAlert(true);
            scrolModalUp();
            return;
        }

        let savedNotes = localStorage.getItem('notes');
        let parsedNotes = savedNotes ? JSON.parse(savedNotes) : null;
        let notes = {};

        if (parsedNotes) {
            parsedNotes.forEach((item) => {
                const timestamp = new Date(item.timestamp);
                notes = {
                    ...notes,
                    ...{
                        [item.timestamp]: {
                            id: item.id,
                            note: item.note,
                            date: format(timestamp, 'yyyy-MM-dd HH:mm:ss'),
                            idFake: item.timestamp,
                        },
                    },
                };
            });
        }

        handleSubmit({ ...data, ...{ notes: parsedNotes } });
    };

    return (
        <>
            <Formik
                enableReinitialize
                initialValues={initialValues}
                validationSchema={Yup.object().shape({
                    firstName: Yup.string().required(trans('label.insert_name_2')),
                    lastName: Yup.string().required(trans('label.insert_surname_2')),
                    email: Yup.string().email(trans('label.insert_valid_mail_3')).nullable(),
                    mobilePhone: Yup.string()
                        .test('is-valid-mobile-phone', trans('phone_verification.valid_number.error'), function (text) {
                            return text ? validPhoneNumberNotStrict(text) : true;
                        })
                        .nullable(),
                    fax: Yup.string()
                        .test('is-valid-fax', trans('phone_verification.valid_number.error'), function (text) {
                            return text ? validPhoneNumberNotStrict(text) : true;
                        })
                        .nullable(),
                    phone: Yup.string()
                        .test('is-valid-phone', trans('phone_verification.valid_number.error'), function (text) {
                            return text ? validPhoneNumberNotStrict(text) : true;
                        })
                        .nullable(),
                })}
                validateOnChange={false}
                validateOnBlur={false}
                validateOnMount={false}
                onSubmit={(data) => onSubmit(data)}
            >
                <FormComponent
                    setInitialValues={setInitialValues}
                    customer={customer}
                    showContactsAlert={showContactsAlert}
                    {...props}
                />
            </Formik>
            <Loader loading={loading} fixedOverlay={false} centered={false} />
        </>
    );
};
