import { PerformanceRankingIndicator } from '../../../js/commons/gtx-react/components/PerformanceRankingIndicator/PerformanceRankingIndicator';
import type { Meta, StoryObj } from '@storybook/react';
import { NoPerformanceReason } from '../../../js/commons/lib/propertyPerformances';
import { IPortalPropertiesListItem } from '../../../js/commons/types/api/property';

/**
 * PerformanceRankingIndicator displays property performance ranking information with variation indicators.
 * It shows ranking position, performance badges, and historical ranking changes.
 */
const meta: Meta<typeof PerformanceRankingIndicator> = {
    title: 'Components/PerformanceRankingIndicator',
    component: PerformanceRankingIndicator,
    parameters: {
        componentSubtitle: 'Display Component',
        componentTitle: 'PerformanceRankingIndicator',
        componentSlug: 'PerformanceRankingIndicator',
        componentImport: 'PerformanceRankingIndicator',
        packageName:
            'gtx-react/components/PerformanceRankingIndicator/PerformanceRankingIndicator',
        docs: {
            description: {
                component: `
PerformanceRankingIndicator is a sophisticated component for displaying property performance metrics with the following features:

## Key Features:
- **Performance Ranking**: Shows current position relative to similar properties
- **Variation Indicators**: Displays changes from previous rankings with visual cues
- **Multiple States**: Handles various scenarios (no data, few comparisons, etc.)
- **Performance Badges**: Color-coded performance indicators (low, medium, high)
- **Tooltips**: Contextual information about ranking variations

## Usage Patterns:
- Property listing tables showing performance metrics
- Dashboard widgets for property performance tracking
- Detailed property views with ranking information
- Performance comparison interfaces

## States:
- **Active Ranking**: Shows current position with variation indicators
- **Not Residential**: Message for non-residential properties
- **Available in X Days**: Countdown for upcoming performance data
- **Few Similar Ads**: Special popover for unique properties
- **Unavailable**: Fallback state for missing or invalid data

## Props:
- **response**: Boolean indicating if performance data is available
- **reason**: Reason code when performance data is unavailable
- **extra**: Additional data like countdown days
- **data**: Property data containing ranking and performance information
                `,
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        response: {
            description: 'Whether performance data is available',
            control: { type: 'boolean' },
        },
        reason: {
            description: 'Reason why performance data is unavailable',
            control: { type: 'select' },
            options: Object.values(NoPerformanceReason),
        },
    },
};

export default meta;

type Story = StoryObj<typeof PerformanceRankingIndicator>;

// Mock property data for different scenarios
const mockPropertyData: IPortalPropertiesListItem = {
    id: 'property-123',
    code: 'ABC123',
    contract: 'rent',
    price: '350000',
    priceByRequest: false,
    mainImageUrl: 'https://example.com/property-image.jpg',
    mainImageThumbUrl: 'https://example.com/property-thumb.jpg',
    created: '2024-01-15T10:00:00Z',
    modified: '2024-08-07T15:30:00Z',
    portalUrl: 'https://portal.example.com/property/property-123',
    category: 'Residential',
    typology: 'Apartment',
    subTypology: 1,
    categoryId: 1,
    typologyId: 1,
    subTypologyId: 1,
    favourite: false,
    properties: [],
    usersStats: {
        views: 245,
        hidden: 3,
        saves: 12,
    },
    searchPosition: 15,
    ranking: 8.5,
    integrationFlag: false,
    statusId: 1,
    contractId: 1,
    extraVisibilities: {
        searchable: true,
        star: false,
        starPremium: false,
        starExpiration: '',
        top: false,
        topPremium: false,
        topExpiration: '',
        showcase: false,
        showcasePremium: false,
        showcaseExpiration: '',
    },
    externalMedia: {},
    performanceRelativeIndex: { id: 7, name: 'High' }, // High performance
    zoneSimilarPropertiesPosition: 3,
    zoneSimilarProperties: 25,
    positionDiff: 2, // Moved up 2 positions
} as IPortalPropertiesListItem;

const highPerformanceData: IPortalPropertiesListItem = {
    ...mockPropertyData,
    zoneSimilarPropertiesPosition: 1,
    performanceRelativeIndex: { id: 9, name: 'Very High' }, // Very high performance
    positionDiff: 1, // Moved up 1 position
};

const lowPerformanceData: IPortalPropertiesListItem = {
    ...mockPropertyData,
    zoneSimilarPropertiesPosition: 20,
    performanceRelativeIndex: { id: 1, name: 'Very Low' }, // Very low performance
    positionDiff: -3, // Moved down 3 positions
};

const stablePerformanceData: IPortalPropertiesListItem = {
    ...mockPropertyData,
    zoneSimilarPropertiesPosition: 8,
    performanceRelativeIndex: { id: 5, name: 'Average' }, // Average performance
    positionDiff: 0, // No change
};

/**
 * High performance property with positive ranking change
 */
export const HighPerformance: Story = {
    args: {
        response: true,
        data: highPerformanceData,
    },
};

/**
 * Average performance property with stable ranking
 */
export const AveragePerformance: Story = {
    args: {
        response: true,
        data: stablePerformanceData,
    },
};

/**
 * Low performance property with negative ranking change
 */
export const LowPerformance: Story = {
    args: {
        response: true,
        data: lowPerformanceData,
    },
};

/**
 * Property with no position difference data
 */
export const NoVariationData: Story = {
    args: {
        response: true,
        data: {
            ...mockPropertyData,
            positionDiff: undefined,
        },
    },
};

/**
 * Property that is not residential
 */
export const NotResidential: Story = {
    args: {
        response: false,
        reason: NoPerformanceReason.NotResidential,
        data: mockPropertyData,
    },
};

/**
 * Property with performance data available in 1 day
 */
export const AvailableInOneDay: Story = {
    args: {
        response: false,
        reason: NoPerformanceReason.AvailableInXDays,
        extra: { days: 1 },
        data: mockPropertyData,
    },
};

/**
 * Property with performance data available in multiple days
 */
export const AvailableInMultipleDays: Story = {
    args: {
        response: false,
        reason: NoPerformanceReason.AvailableInXDays,
        extra: { days: 5 },
        data: mockPropertyData,
    },
};

/**
 * Property with few similar ads for comparison
 */
export const FewSimilarAds: Story = {
    args: {
        response: false,
        reason: NoPerformanceReason.FewSimilarAds,
        data: {
            ...mockPropertyData,
            price: '2500000', // Expensive property
        },
    },
};

/**
 * Property that is not active
 */
export const NotActive: Story = {
    args: {
        response: false,
        reason: NoPerformanceReason.NotActive,
        data: mockPropertyData,
    },
};

/**
 * Property that is tourist category
 */
export const IsTourist: Story = {
    args: {
        response: false,
        reason: NoPerformanceReason.IsTourist,
        data: mockPropertyData,
    },
};

/**
 * Property with invalid data
 */
export const InvalidData: Story = {
    args: {
        response: false,
        reason: NoPerformanceReason.InvalidData,
        data: mockPropertyData,
    },
};

/**
 * Property with unavailable performance data
 */
export const Unavailable: Story = {
    args: {
        response: false,
        reason: NoPerformanceReason.Unavailable,
        data: mockPropertyData,
    },
};

/**
 * Property with missing ranking position data
 */
export const MissingRankingData: Story = {
    args: {
        response: true,
        data: {
            ...mockPropertyData,
            zoneSimilarPropertiesPosition: undefined,
        },
    },
};

/**
 * Interactive comparison showing different performance levels
 */
export const PerformanceComparison: Story = {
    render: () => (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div
                style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(3, 1fr)',
                    gap: '16px',
                    alignItems: 'start',
                }}
            >
                <div
                    style={{
                        padding: '16px',
                        border: '1px solid #e0e0e0',
                        borderRadius: '8px',
                    }}
                >
                    <h4 style={{ margin: '0 0 12px 0', color: '#28a745' }}>
                        High Performance
                    </h4>
                    <PerformanceRankingIndicator
                        response={true}
                        data={highPerformanceData}
                    />
                </div>

                <div
                    style={{
                        padding: '16px',
                        border: '1px solid #e0e0e0',
                        borderRadius: '8px',
                    }}
                >
                    <h4 style={{ margin: '0 0 12px 0', color: '#ffc107' }}>
                        Average Performance
                    </h4>
                    <PerformanceRankingIndicator
                        response={true}
                        data={stablePerformanceData}
                    />
                </div>

                <div
                    style={{
                        padding: '16px',
                        border: '1px solid #e0e0e0',
                        borderRadius: '8px',
                    }}
                >
                    <h4 style={{ margin: '0 0 12px 0', color: '#dc3545' }}>
                        Low Performance
                    </h4>
                    <PerformanceRankingIndicator
                        response={true}
                        data={lowPerformanceData}
                    />
                </div>
            </div>

            <div
                style={{
                    padding: '16px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                }}
            >
                <h4 style={{ margin: '0 0 12px 0' }}>Error States</h4>
                <div
                    style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(2, 1fr)',
                        gap: '12px',
                    }}
                >
                    <div>
                        <strong>Not Residential:</strong>
                        <PerformanceRankingIndicator
                            response={false}
                            reason={NoPerformanceReason.NotResidential}
                            data={mockPropertyData}
                        />
                    </div>
                    <div>
                        <strong>Few Similar Ads:</strong>
                        <PerformanceRankingIndicator
                            response={false}
                            reason={NoPerformanceReason.FewSimilarAds}
                            data={mockPropertyData}
                        />
                    </div>
                    <div>
                        <strong>Available in 3 Days:</strong>
                        <PerformanceRankingIndicator
                            response={false}
                            reason={NoPerformanceReason.AvailableInXDays}
                            extra={{ days: 3 }}
                            data={mockPropertyData}
                        />
                    </div>
                    <div>
                        <strong>Unavailable:</strong>
                        <PerformanceRankingIndicator
                            response={false}
                            reason={NoPerformanceReason.Unavailable}
                            data={mockPropertyData}
                        />
                    </div>
                </div>
            </div>
        </div>
    ),
};

/**
 * Table view simulation showing multiple properties
 */
export const TableView: Story = {
    render: () => (
        <div
            style={{
                border: '1px solid #ddd',
                borderRadius: '8px',
                overflow: 'hidden',
            }}
        >
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead style={{ backgroundColor: '#f8f9fa' }}>
                    <tr>
                        <th
                            style={{
                                padding: '12px',
                                textAlign: 'left',
                                borderBottom: '1px solid #ddd',
                            }}
                        >
                            Property
                        </th>
                        <th
                            style={{
                                padding: '12px',
                                textAlign: 'left',
                                borderBottom: '1px solid #ddd',
                            }}
                        >
                            Performance Ranking
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td
                            style={{
                                padding: '12px',
                                borderBottom: '1px solid #eee',
                            }}
                        >
                            <div>
                                <strong>Via Roma 123</strong>
                                <div
                                    style={{ fontSize: '14px', color: '#666' }}
                                >
                                    3 rooms, 85m² - €350,000
                                </div>
                            </div>
                        </td>
                        <td
                            style={{
                                padding: '12px',
                                borderBottom: '1px solid #eee',
                            }}
                        >
                            <PerformanceRankingIndicator
                                response={true}
                                data={highPerformanceData}
                            />
                        </td>
                    </tr>
                    <tr>
                        <td
                            style={{
                                padding: '12px',
                                borderBottom: '1px solid #eee',
                            }}
                        >
                            <div>
                                <strong>Corso Italia 45</strong>
                                <div
                                    style={{ fontSize: '14px', color: '#666' }}
                                >
                                    2 rooms, 65m² - €280,000
                                </div>
                            </div>
                        </td>
                        <td
                            style={{
                                padding: '12px',
                                borderBottom: '1px solid #eee',
                            }}
                        >
                            <PerformanceRankingIndicator
                                response={true}
                                data={stablePerformanceData}
                            />
                        </td>
                    </tr>
                    <tr>
                        <td
                            style={{
                                padding: '12px',
                                borderBottom: '1px solid #eee',
                            }}
                        >
                            <div>
                                <strong>Via Nazionale 67</strong>
                                <div
                                    style={{ fontSize: '14px', color: '#666' }}
                                >
                                    4 rooms, 120m² - €450,000
                                </div>
                            </div>
                        </td>
                        <td
                            style={{
                                padding: '12px',
                                borderBottom: '1px solid #eee',
                            }}
                        >
                            <PerformanceRankingIndicator
                                response={true}
                                data={lowPerformanceData}
                            />
                        </td>
                    </tr>
                    <tr>
                        <td style={{ padding: '12px' }}>
                            <div>
                                <strong>Villa Exclusive</strong>
                                <div
                                    style={{ fontSize: '14px', color: '#666' }}
                                >
                                    8 rooms, 450m² - €2,500,000
                                </div>
                            </div>
                        </td>
                        <td style={{ padding: '12px' }}>
                            <PerformanceRankingIndicator
                                response={false}
                                reason={NoPerformanceReason.FewSimilarAds}
                                data={{
                                    ...mockPropertyData,
                                    price: '2500000',
                                }}
                            />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    ),
};
