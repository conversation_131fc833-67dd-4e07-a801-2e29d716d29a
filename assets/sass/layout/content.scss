@use '../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

#content-wrapper {
  position: relative;
  box-sizing: content-box;
  margin-top: $gx-new-menu-header-height;
  min-height: calc(100vh - #{$gx-new-menu-header-height});

  .multi-agency--switch & {
    margin-top: $gx-multi-agency-banner-height + $gx-new-menu-header-height;
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    margin-left: $gx-new-menu-width;
  }

  @include media('screen', '>=#{breakpoint(xl)}') {
    margin-left: $gx-new-menu-width;
  }

  .has-menu-fixed & {
    margin-left: calc(26rem + #{$gx-new-menu-width});
  }
}
