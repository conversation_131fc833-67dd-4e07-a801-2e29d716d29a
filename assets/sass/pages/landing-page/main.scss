@use '@gx-design/theme/pro-tokens' as proTokens;
@use '@gx-design/theme/tokens' with (
  $tokens: proTokens.$tokens
);
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

@forward 'telefono-smart';
@forward 'messaging';

/*------------------------------------*  STYLE.css
\*------------------------------------*/
/*-----------------------------------------------*  $RESET
  	http://meyerweb.com/eric/tools/css/reset/
  	v2.0 | 20110126
  	License: none (public domain)
\*-----------------------------------------------*/
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after {
  content: '';
  content: none;
}

q:before,
q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

select {
  border: none;
  background: none;
  -webkit-appearance: none;
}

/* BOX MODEL */
*,
:before,
:after {
  box-sizing: border-box;
}

/* CLEARFIX */
.cf,
.gc,
.nav,
.media {
  zoom: 1;
}

.cf:before,
.gc:before,
.nav:before,
.media:before,
.cf:after,
.gc:after,
.nav:after,
.media:after {
  display: table;
  content: ' ';
}

.cf:after,
.gc:after,
.nav:after,
.media:after {
  clear: both;
}

body {
  color: color(content-medium);
  font-family: Arial, sans-serif;
}

strong {
  font-weight: bold;
}

.landing-page {
  &__contact {
    margin-top: 3.2rem;
    padding: 6rem 0;
    background: color(background-alt);
    text-align: center;

    &__title {
      margin-bottom: 1.6rem;
      color: color(content-medium);
      font-size: 1.8rem;
      font-weight: 600;
    }

    &__item {
      margin: 0 auto;
      color: color(content-low);
      font-family: 'Raleway', sans-serif;
      font-size: 1.8rem;

      a {
        color: color(content-low);
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      svg {
        max-width: 2rem;
        margin-right: 0.8rem;
        vertical-align: middle;
      }

      &--tel {
        margin-top: 1.6rem;
      }
    }
  }

  // TABLET
  @include media('screen', '>=#{breakpoint(sm)}') {
    &__contact {
      &__title {
        margin-bottom: 3.2rem;
      }
    }

    &--withFooter {
      padding-bottom: 10rem;
    }

    &__contact {
      margin-top: 4rem;

      &__item {
        display: inline-block;

        &--tel {
          margin-top: 0;
          margin-left: 4rem;
        }
      }
    }

    &__footer {
      padding: 2.4rem 0;

      .checkbox {
        display: inline-block;
      }

      .btn {
        width: auto;
        float: right;
      }
    }
  }
}

//BOX MESSAGGIO
.box-msg {
  position: relative;
  width: 90%;
  max-width: 41rem;
  margin: 5.6rem auto 0;
  padding: 4rem 3rem 3rem;
  border-radius: radius(md);
  background-color: color(background-main);

  &__icon {
    display: flex;
    position: absolute;
    top: 0;
    left: 50%;
    align-items: center;
    justify-content: center;
    width: 5.2rem;
    height: 5.2rem;
    transform: translate(-50%, -50%);
    border-radius: radius(rounded);
    background-color: color(background-accent);
  }

  &__title {
    color: color(content-medium);
    font-family: 'Raleway', sans-serif;
    font-size: 2.4rem;
    font-weight: 500;
  }

  &__text {
    margin-top: 2rem;
    color: color(content-medium);
    font-size: 1.6rem;
    line-height: 1.5;
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    margin-top: 7.6rem;

    &__title {
      font-size: 2.9rem;
    }
  }
}

.go-app-section {
  width: 90%;
  max-width: 37.2rem;
  margin: 0 auto;
}

button {
  -webkit-appearance: none;
}

.btn {
  display: block;
  width: 100%;
  height: 4rem;
  margin-top: 2rem;
  border: none;
  border-radius: radius(sm);
  color: color(content-accent);
  font-size: 1.3rem;
  line-height: 4rem;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
}

.btn-secondary {
  background-color: color(background-accent);
}
