import { Icon } from '@gx-design/icon';
import React, { Fragment, useRef } from 'react';
import { useGxNavigationMenuDispatcher } from '../../shared/components/GxNavigationMenuContext';
import { trans } from '../../shared/translations';
import { mapMenuConfig } from '../../shared/utils';
import { BaseMenuConfig, CustomMenuComponent, MenuConfig, Pathname } from '../../types';
import { FeaturesMenuAdapter } from '../FeaturesProviders';
import { MenuCustomSubMenu } from './MenuCustomSubMenu';
import { MenuSecondaryMenuItem } from './MenuSecondaryMenuItem';
import {
  MenuCurrentOpenedSectionProvider,
  useMenuCurrentOpenedSectionContext,
} from './MenuCurrentOpenedSection';
import { isCustomMenuComponent, isMenuItem } from '../../shared/utils/type-guards';

type MenuSubmenuProps = {
  activePath?: Pathname;
  item: BaseMenuConfig | CustomMenuComponent | undefined;
};

const hasSomePath = (activeMenu?: string, menuConfig?: MenuConfig) => {
  return (
    menuConfig?.subMenu &&
    Object.keys(menuConfig.subMenu).some((value) => {
      return value === activeMenu;
    })
  );
};

export const MenuSecondaryMenu: React.FC<MenuSubmenuProps> = React.memo(
  ({ activePath, item }: MenuSubmenuProps) => {
    const { dispatch } = useGxNavigationMenuDispatcher();

    return (
      <>
        <div
          onClick={() => dispatch({ type: 'CLOSE_SIDE_MENU' })}
          className='gx-navigation-sidemenu__menuBack gx-is-hidden-md-up'
        >
          <Icon name='arrow-left' />
          <span>{trans('label.main_menu')}</span>
        </div>
        {isMenuItem(item) && item.subMenu ? (
          <>
            <h3 className='gx-navigation-sidemenu__menuTitle'>{item.item?.label}</h3>
            <ul className='gx-navigation-sidemenu__menuItems'>
              <MenuCurrentOpenedSectionProvider menuItem={item.subMenu}>
                <CollapsableMenu activePath={activePath} item={item.subMenu} />
              </MenuCurrentOpenedSectionProvider>
            </ul>
          </>
        ) : isCustomMenuComponent(item) ? (
          <MenuCustomSubMenu customMenuComponent={item.customMenuComponent} />
        ) : null}
      </>
    );
  },
);

MenuSecondaryMenu.displayName = 'MenuSecondaryMenu';

const CollapsableMenu = ({ item, activePath }: { item: MenuConfig; activePath?: Pathname }) => {
  const previousGroup = useRef<string | null | undefined>(null);
  const { pathRecord } = useMenuCurrentOpenedSectionContext();

  return (
    <>
      {mapMenuConfig(item, ([key, value]: [Pathname, MenuConfig]) => {
        const group = value.item?.group;
        const shouldRenderDivider =
          group !== previousGroup.current && previousGroup.current !== null;
        previousGroup.current = group;

        return (
          <Fragment key={key}>
            <FeaturesMenuAdapter menu={value} contexts={value?.item?.contexts} level={2}>
              {(secondLevelMenu) => (
                <>
                  {shouldRenderDivider && (
                    <div className='gx-navigation-sidemenu__menuItemsDivider'></div>
                  )}

                  <MenuSecondaryMenuItem
                    isOpen={pathRecord[key]}
                    activePath={activePath}
                    isActive={hasSomePath(activePath, value) || activePath === key}
                    key={key}
                    href={key}
                    item={secondLevelMenu}
                  />
                </>
              )}
            </FeaturesMenuAdapter>
          </Fragment>
        );
      })}
    </>
  );
};
