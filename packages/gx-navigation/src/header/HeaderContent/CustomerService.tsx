import { ActionList, ActionListItem } from '@gx-design/action-list';
import { Avatar } from '@gx-design/avatar';
import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { Tooltip } from '@gx-design/tooltip';
import { trans } from '@pepita-i18n/babelfish';
import React, { FC, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

import { useGxNavigationContext } from '../../shared/components/GxNavigationContext';
import { useEnsureGxNavigationContext } from '../../shared/components/GxNavigationContext';
import { useGxNavigationMenuDispatcher } from '../../shared/components/GxNavigationMenuContext';
import { useOnClickOutside } from '@gx-design/use-on-click-outside';
import { useGxNavigationFeatureFlagsContext } from '../../shared/components/GxNavigationFeatureFlagsContext';

const GX_SPACING = 4;

type ContentProps = {
  onClose?: () => void;
};
export const CustomerServiceContent: FC<ContentProps> = ({ onClose }) => {
  const context = useGxNavigationContext();

  const { responsibleAvatar, responsibleName, responsiblePhone } = useEnsureGxNavigationContext();
  const { helpJuiceEnabled } = useGxNavigationFeatureFlagsContext();

  return (
    <div className='customer-service'>
      <div className='customer-service__consultant'>
        <div className='gx-title-2'>{trans('label.call_your_dedicated_consultant')}</div>
        <div className='customer-service__consultantContacts'>
          <Avatar avatarImage={responsibleAvatar} altText={responsibleName} />
          <div className='customer-service__consultantCard'>
            <h6 className='customer-service__consultantName'>{responsibleName}</h6>
            <div className='customer-service__consultantContactRow'>
              <Icon name='phone' />
              <span className='gx-body-small'>{responsiblePhone}</span>
            </div>
          </div>
        </div>
      </div>
      <ActionList>
        <ActionListItem
          startElement={<Icon name='chat-question-mark' />}
          text={trans('label.live_chat_support')}
          onClick={typeof onClose === 'function' ? onClose : undefined}
        />
        {helpJuiceEnabled ? (
          <ActionListItem
            startElement={<Icon name='sparkle' />}
            text={trans('label.ask_to_ai')}
            href={context?.helpJuiceAccountUrl}
            target='__blank'
          />
        ) : (
          <></>
        )}
      </ActionList>
    </div>
  );
};

export const CustomerServiceMenuContent = () => {
  const { dispatch } = useGxNavigationMenuDispatcher();

  const onClose = () => {
    dispatch({ type: 'CLOSE_MENU' });
  };

  return (
    <>
      <h3 className='gx-navigation-sidemenu__menuTitle'>{trans('label.customer_service')}</h3>
      <CustomerServiceContent onClose={onClose} />
    </>
  );
};

type ButtonProps = {
  position?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
};

export const CustomerServiceButton: FC<ButtonProps> = ({ position = 'bottom-left' }) => {
  const buttonRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);

  const [coords, setCoords] = useState<React.CSSProperties>({
    top: 0,
    left: 0,
    display: 'block',
  });

  const refs = useMemo(() => [dropdownRef, buttonRef], [dropdownRef, buttonRef]);

  const toggle = () => setIsDropdownOpen((prev) => !prev);

  const onClose = () => setIsDropdownOpen(false);

  useOnClickOutside({
    refs,
    handler: onClose,
    shouldListen: isDropdownOpen,
  });

  useLayoutEffect(() => {
    const dropdownPositionHandler = () => {
      if (!isDropdownOpen || !buttonRef?.current || !dropdownRef?.current) {
        return;
      }

      const buttonRect = buttonRef?.current?.getBoundingClientRect();
      const dropdownRect = dropdownRef?.current?.getBoundingClientRect();

      const [xAxis, yAxis] = position.split('-');
      const newCoords: React.CSSProperties = {
        display: 'block',
        left:
          yAxis === 'left' ? buttonRect.x : buttonRect.x + buttonRect.width - dropdownRect.width,
        top:
          xAxis === 'top'
            ? buttonRect.y - dropdownRect.height - GX_SPACING + window.scrollY
            : buttonRect.y + buttonRect.height + GX_SPACING + window.scrollY,
      };

      setCoords(newCoords);
    };

    dropdownPositionHandler();

    const forceClose = () => {
      if (isDropdownOpen) {
        onClose();
        buttonRef.current?.blur();
      }
    };

    window.addEventListener('resize', forceClose);
    window.addEventListener('scroll', forceClose);

    return () => {
      window.removeEventListener('resize', forceClose);
      window.removeEventListener('scroll', forceClose);
    };
  }, [isDropdownOpen, position]);

  return (
    <div className='gx-is-hidden-sm-down'>
      <div ref={buttonRef}>
        <Tooltip text={trans('label.customer_service')} position='bottom'>
          <Button variant='ghost' iconOnly onClick={toggle}>
            <Icon name='question-mark-circle' />
          </Button>
        </Tooltip>
      </div>
      {isDropdownOpen &&
        createPortal(
          <div className='gx-navigation-header__dropdown' style={coords} ref={dropdownRef}>
            <CustomerServiceContent onClose={onClose} />
          </div>,
          document.body,
        )}
    </div>
  );
};
