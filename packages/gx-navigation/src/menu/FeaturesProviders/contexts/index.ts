import { ActiveSearchesCounterAdapter } from './ActiveSearchesCounterContext';
import { CustomersCounterAdapter } from './CustomersCounterContext';
import { MatchesCounterAdapter } from './MatchesCounterContext';
import { MessageCounterAdapter } from './MessageCounterContext';

export const adapterMap = new Map([
  ['message-counter' as const, MessageCounterAdapter],
  ['matches-counter' as const, MatchesCounterAdapter],
  ['active-searches-counter' as const, ActiveSearchesCounterAdapter],
  ['customers-counter' as const, CustomersCounterAdapter],
]);

const keys = [...adapterMap.keys()].map((key) => key);

export type ContextMapKey = (typeof keys)[number];
