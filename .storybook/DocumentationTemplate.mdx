import {
  Meta,
  Title,
  Subtitle,
  Description,
  Primary,
  ArgTypes,
  Stories,
  Unstyled,
} from "@storybook/blocks";
import { ComponentStart } from "./blocks/ComponentStart";

<Meta isTemplate />
<Unstyled>
  <div className="source-styled">
    <ComponentStart />
    <h2 className="gx-title-1 component-doc-heading">Example</h2>
    <Primary />
    <h2 className="gx-title-1 component-doc-heading">Props</h2>
    <ArgTypes />
    <h2 className="gx-title-1 component-doc-heading">Variations</h2>
    <div className="doc-stories-wrapper">
      <Stories title="" />
    </div>
  </div>
</Unstyled>

export const tags = ["autodocs"];
