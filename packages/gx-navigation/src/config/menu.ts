import { trans } from '../shared/translations';
import { MenuConfig } from '../types';

/**
 * @deprecated
 * This is not used anymore, but it's still here for reference.
 * Use `fetchMenuConfig` instead.
 */
export const mainMenuConfig: MenuConfig = {
  '/': {
    item: { label: trans('label.summary'), icon: 'thumb-list' },
  },
  '#annunci': {
    item: { label: trans('label.ads'), icon: 'home', href: '/inserimento_annuncio.php' },
    subMenu: {
      '#immobili': {
        item: { label: trans('label.estates'), icon: 'home', group: '1', expanded: true },
        subMenu: {
          '/inserimento_annuncio.php': {
            item: { label: trans('label.add') },
          },
          '/annunci_agenzia.php': {
            item: { label: trans('label.list') },
            subMenu: {
              '/immobili/{id}/cartellone': {
                item: { hidden: true },
              },
            },
          },
          '/immobili/portale/lista': {
            item: { label: trans('label.on_ad_portal') },
            subMenu: {
              '/immobili/portale/{id}/prestazioni': {
                item: { hidden: true },
              },
            },
          },
          '/annunci_venduti_affittati.php': {
            item: { label: `${trans('label.sold')} / ${trans('label.rented')}` },
          },
        },
      },
      '#nuove-costruzioni': {
        item: {
          label: trans('label.new_buildings'),
          icon: 'helmet',
          group: '1',
        },
        subMenu: {
          '/v2/nuove-costruzioni': {
            item: { label: trans('label.add') },
          },
          '/v2/nuove-costruzioni/lista': {
            item: { label: trans('label.list') },
          },
          '/nuove-costruzioni/portale/lista': {
            item: { label: trans('label.on_ad_portal') },
          },
        },
      },
      '#aste': {
        item: { label: trans('label.auctions'), icon: 'hammer', group: '1', expanded: false },
        subMenu: {
          '/inserimento_asta.php': {
            item: { label: trans('label.add') },
          },
          '/aste.php': {
            item: { label: trans('label.list') },
          },
          '/immobili/aste/portale/lista': {
            item: { label: trans('label.on_ad_portal') },
          },
          '/immobili/aste/catalogo/': {
            item: { label: trans('label.catalogue') },
          },
        },
      },
      '#multinvio': {
        item: {
          label: trans('label.multisend'),
          icon: 'shuffle',
          group: '1',
        },
        subMenu: {
          '/multinvio/annunci': {
            item: { label: trans('label.ads') },
          },
          '/multinvio/nuove-costruzioni': {
            item: { label: trans('label.new_buildings') },
          },
          '/multinvio/esportazioni/attive': {
            item: { label: trans('label.active_exports') },
          },
          '/multinvio/esportazioni': {
            item: { label: trans('label.exports_management') },
          },
        },
      },
      '#virtual-tour': {
        item: {
          label: trans('label.virtual_tour_3d'),
          icon: 'tour',
          group: '2',
        },
        subMenu: {
          '/servizi_extra.php?multimedia=vt': {
            item: { label: trans('label.ads') },
            params: { multimedia: 'vt' },
          },
          '/v2/virtual-tour-nuove-costruzioni': {
            item: { label: trans('label.new_buildings') },
          },
        },
      },
      '#immovisita': {
        item: { label: trans('label.immovisita'), icon: 'video', group: '2', expanded: false },
        subMenu: {
          '/annunci_immovisita.php': {
            item: { label: trans('label.ads'), href: '/annunci_immovisita.php' },
          },
          '/immovisita/scheduled-visits': {
            item: {
              label: trans('label.immovisita.scheduled_visits'),
              href: '/annunci_immovisita.php',
            },
          },
        },
      },
      '/servizi_extra.php?multimedia=pl': {
        params: { multimedia: 'pl' },
        item: {
          label: trans('label.plans'),
          icon: 'planimetry',
          group: '2',
        },
      },
    },
  },
  '#messaggi': {
    item: {
      label: trans('label.messages'),
      icon: 'chat',
      href: '/messaggi/lista',
      contexts: ['message-counter'],
    },
    subMenu: {
      '/messaggi/lista': {
        item: { label: trans('label.messages'), icon: 'chat', contexts: ['message-counter'] },
        subMenu: {
          '/messaggi/{id}': {
            item: { hidden: true },
          },
        },
      },
      '/v2/telefonate': {
        item: { label: trans('label.phone_calls'), icon: 'phone' },
      },
    },
  },
  '#clienti': {
    item: {
      label: trans('label.customers'),
      icon: 'user-group',
      href: '/ricerche/lista',
    },
    subMenu: {
      '#ricerche': {
        item: { label: trans('label.searches'), icon: 'search', expanded: true },
        subMenu: {
          '/ricerche/lista': {
            item: {
              label: trans('label.active_plural_female'),
              tooltip: trans('label.research_active_info'),
            },

            subMenu: {
              '/ricerche/{id}': {
                item: { hidden: true },
              },
            },
          },
          '/richieste.php?id=spec': {
            item: {
              label: trans('label.specifics'),
              href: '/richieste.php?id=spec',
              tooltip: trans('label.research_specific_info'),
            },
            params: { id: 'spec' },
          },
          '/v2/richieste_utente.php': {
            item: {
              label: trans('label.received'),
              tooltip: trans('label.research_received_info'),
            },
          },
        },
      },
      '/clienti': {
        item: {
          label: trans('label.customers_portfolio_2'),
          icon: 'user-group',
        },
      },
    },
  },
  '#acquisizione': {
    item: { label: trans('label.acquisition'), icon: 'pen', href: '/acquisizione/annunci-privati' },
    subMenu: {
      '/acquisizione/annunci-privati': {
        item: { label: trans('label.private_ads'), icon: 'chat' },
      },
      '/acquisizione/valutazioni': {
        item: { label: trans('label.property_valuations'), icon: 'libra' },
      },
      '/acquisizione/richieste-vendita': {
        item: { label: trans('label.sales_requests'), icon: 'euro-house' },
      },
    },
  },
  '/agenda.php': {
    item: {
      label: trans('label.agenda'),
      icon: 'calendar',
    },
  },
  '#servizi': {
    item: { label: trans('label.youdomus.services'), icon: 'layers', href: '/youdomus' },
    subMenu: {
      '#youdomus': {
        item: { label: trans('label.service_youdomus'), icon: 'youdomus' },
        subMenu: {
          '/youdomus': {
            item: { label: trans('label.youdomus.services') },
          },
          '/youdomus/Archivio': {
            item: { label: trans('label.youdomus.documents') },
          },
          '/youdomus/Monitoraggio/IndexImmobile': {
            item: { label: trans('label.youdomus.monitoring') },
          },
          '/youdomus/Mappa/CatastoSuMappa': {
            item: {
              label: trans('label.youdomus.cadastre_on_map'),
            },
          },
          '/youdomus/Utente/Profilo': {
            item: {
              label: trans('label.youdomus.customers_area'),
            },
          },
        },
      },
      '/report': {
        item: { label: trans('label.real_estate_report'), icon: 'chart' },
      },
      '/v2/servizi-web-marketing': {
        item: { label: trans('label.web_services'), icon: 'scope' },
      },
      '/sitoagenzia.php': {
        item: {
          label: trans('label.website'),
          icon: 'monitor',
        },
      },
      '/consulenza-mutuo/richiesta': {
        item: {
          label: trans('label.mortgage_advice'),
          icon: 'calculator',
        },
      },
    },
  },
};

/**
 * @deprecated
 * This is not used anymore, but it's still here for reference.
 * Use `fetchMenuConfig` instead.
 */
export const secondaryMenuConfig: MenuConfig = {
  '#impostazioni': {
    item: { label: trans('label.settings'), icon: 'sliders', href: '/impostazioni/generale' },
    subMenu: {
      '/impostazioni/generale': {
        item: { label: trans('label.general'), group: '1' },
      },
      '/impostazioni/sede': {
        item: { label: trans('label.head_office'), group: '1' },
      },
      '/impostazioni/media': {
        item: { label: trans('label.images_and_videos'), group: '1' },
      },
      '/impostazioni/utenti': {
        item: { label: trans('label.users'), group: '1' },
        subMenu: {
          '/impostazioni/utenti/{id}': {
            item: { hidden: true },
          },
          '/impostazioni/utenti/{id}/edit': {
            item: { hidden: true },
          },
          '/impostazioni/utenti/{id}/activation': {
            item: { hidden: true },
          },
        },
      },
      '/impostazioni/visite-a-distanza': {
        item: { label: trans('label.remote_visits'), group: '1' },
      },
      '/amministrazione/contratto': {
        item: { label: trans('label.contract'), group: '2' },
      },
      '/amministrazione/dati-fatturazione': {
        item: { label: trans('label.billing_information'), group: '2' },
      },
      '/amministrazione/fatture': {
        item: { label: trans('label.invoice_plural'), group: '2' },
      },
    },
  },
};
