import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { trans } from '../../shared/translations';
import { Children, cloneElement, isValidElement, useCallback, useRef, useState } from 'react';
import { LOGOUT_URL, MULTI_AGENCY_URL } from '../../shared/constants';
import { useOnClickOutside } from '@gx-design/use-on-click-outside';
import { useAgencyContext } from '../../shared/components/AgencyContext';

const ddStyle = {
  display: 'block',
  top: '100%',
  right: '0',
  width: '280px',
  borderRadius: 0,
};

type ProfileAvatarProps = {
  avatarUrl: string;
  avatarAlt?: string;
};

export const ProfileAvatar = (props: ProfileAvatarProps) => {
  return (
    <>
      <div className='gx-navigation-header__profileImage'>
        <img src={props.avatarUrl} alt={props.avatarAlt || ''} />
      </div>
      <Icon name='arrow-down' />
    </>
  );
};

type ProfileDropdownProps = {
  isDropdownOpen?: boolean;
  agentName: string;
  agentEmail: string;
};

export const ProfileDropdown = ({ isDropdownOpen = false, ...props }: ProfileDropdownProps) => {
  const { profileUrl } = useAgencyContext();

  if (!isDropdownOpen) {
    return null;
  }
  return (
    <div className='gx-dropdown' style={ddStyle}>
      <div className='gx-navigation-header__profileBox'>
        <div className='gx-title-small'>{props.agentName}</div>
        <div className='gx-navigation-header__profileMail'>{props.agentEmail}</div>
        <div className='gx-navigation-header__buttonGroup'>
          <Button as='a' href={profileUrl} size='fullWidth'>
            <Icon name='user-round' />
            <span>{trans('label.my_profile')}</span>
          </Button>
          <MasterAccountButton />
        </div>
      </div>
      <Button
        as='a'
        href={LOGOUT_URL}
        size='fullWidth'
        className='gx-navigation-header__profileLogout'
        variant='ghost'
      >
        {trans('label.exit')}
      </Button>
    </div>
  );
};

export const MasterAccountButton = () => {
  const { isMasterAccount } = useAgencyContext();

  if (!isMasterAccount) {
    return null;
  }

  return (
    <Button as='a' href={MULTI_AGENCY_URL} size='fullWidth'>
      <Icon name='circular-arrows' />
      <span>{trans('label.switch_agency')}</span>
    </Button>
  );
};

type ProfileProps = {
  children: React.ReactNode;
};

export const Profile = (props: ProfileProps) => {
  const [isDropdownOpen, setDropdownOpen] = useState(false);
  const toggle = useCallback(() => setDropdownOpen((prevState) => !prevState), []);
  const dropdownRef = useRef<HTMLDivElement | null>(null);

  useOnClickOutside({
    refs: dropdownRef,
    handler: () => {
      setDropdownOpen(false);
    },
  });

  return (
    <div ref={dropdownRef} role='button' onClick={toggle} className='gx-navigation-header__profile'>
      {Children.map(props.children, (child) => {
        if (isValidElement<{ isDropdownOpen: boolean }>(child) && child.type === ProfileDropdown) {
          return cloneElement(child, { isDropdownOpen });
        }
        return child;
      })}
    </div>
  );
};
