// ==========================================================================
// Nav Slider - Components
// ==========================================================================
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.gx-nav-slider {
  position: relative;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  border-bottom: 0.1rem solid color(border-main);

  &Sticky {
    background: color(background-main);
    position: fixed;
    height: 5.7rem;
    top: 0;
    left: 0;
    width: 100%;
    @include z-index(base, 4);
    border-bottom: 0.1rem solid color(border-main);
    box-shadow: elevation(fixed-top);
    overflow: hidden;
    white-space: nowrap;
    animation: moveDown 0.5s ease-in-out;
  }

  &__wrap {
    margin: 0 space(sm);
    overflow: hidden;
  }

  &__content {
    display: inline-block;
    transition: margin-left 0.15s ease-in;

    ul {
      display: inline-flex;
      list-style: none;
      margin-bottom: 0;
      padding-left: 0;

      li {
        display: inline-flex;
        align-items: center;
        height: ($gx-unit-size * 7);
        padding: 0 space(sm);
        text-decoration: none;
        color: color(content-high);

        &.is-selected {
          color: color(content-action);
          border-bottom: 0.2rem solid color(border-selected);
        }

        &:hover {
          color: color(content-action);
          cursor: pointer;
        }
      }
    }
  }

  &__btn-wrap {
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    width: ($gx-unit-size * 8);
    height: ($gx-unit-size * 7);

    &:empty {
      display: none;
    }

    &--prev {
      padding-left: space(sm);
      left: 0;
      background: linear-gradient(90deg, rgba(255, 255, 255, 1) 80%, rgba(255, 255, 255, 0) 100%);
    }

    &--next {
      justify-content: flex-end;
      padding-right: space(sm);
      right: 0;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 20%);
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    &__wrap {
      margin: 0 space(md);
    }

    &__btn-wrap {
      width: ($gx-unit-size * 16);

      &--prev {
        padding-left: space(md);
        background: linear-gradient(90deg, rgba(255, 255, 255, 1) 20%, rgba(255, 255, 255, 0) 100%);
      }

      &--next {
        padding-right: space(md);
        background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 80%);
      }
    }
  }

  @include media('>=#{breakpoint(md)}') {
    &Sticky {
      left: 5.5rem;
      width: calc(100% - 5.5rem);
    }

    &__wrap {
      margin: 0 space(xl);
    }
  }

  @include media('>=#{breakpoint(xl)}') {
    &Sticky {
      left: 24rem;
      width: calc(100% - 24rem);
    }
  }
}
