@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$list-item-size: space(xl);

.gx-autocomplete {
  position: relative;

  &__inputContainer {
    position: relative;

    .gx-input-clear {
      display: flex;
    }
  }

  &__list {
    position: absolute;
    width: 100%;
    overflow-y: auto;
    max-height: ($list-item-size * 6 - $list-item-size / 2);
    padding: space(xs) 0;
    margin-top: space(xs);
    list-style: none;
    background-color: color(background-main);
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);
    @include z-index(dropdown);

    &Item {
      display: flex;
      align-items: center;
      height: $list-item-size;
      padding: space(sm) space(md);
      cursor: pointer;

      &:hover,
      &.is-focused {
        background-color: color(background-alt);
        color: color(content-high);
        text-decoration: none;
      }
    }
  }
}
