import { trans } from '@pepita-i18n/babelfish';
import { List, ListItem } from '@gx-design/list';
import { Popover } from '@gx-design/popover';
import { Icon } from '@gx-design/icon';
import { IPortalNewConstructionsListItem } from '../types/list';
import { IPortalNewConstructionsImproveQualityApiResponse } from '../types/apiResponse';
import gtxConstants from '@getrix/common/js/gtx-constants';
import { PropertyActiveVisibilities } from 'gtx-react/containers/PropertyActiveVisibilities';
import { useModalContext } from './useModalContext';
import { useConfirmDialogContext } from './useConfirmDialogContext';
import useNewConstructionActions from './useNewConstructionActions';
import { IReduxRootState } from '../types/redux';
import { useSelector } from 'react-redux';
import { ListCounter } from 'gtx-react/components/ListCounter/ListCounter';
import { Counter } from 'types/counters';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { ucFirst } from 'lib/strings-formatter';

const isValidKeyForProperty = (key: string) => {
    if (key === 'energyClass' || key === 'virtualTour' || key === 'plans') {
        return false;
    }

    return true;
};

const areAllOutcomesChecked = (
    apiData: IPortalNewConstructionsImproveQualityApiResponse
) => {
    let result = true;
    const qualityKeys = Object.keys(apiData);

    qualityKeys
        .filter((key) => isValidKeyForProperty(key))
        .forEach((key) => {
            if (!apiData[key]['outcome']) {
                result = false;
            }
        });

    return result;
};

const useListField = () => {
    const { showModal } = useModalContext();
    const { showConfirmDialog } = useConfirmDialogContext();
    const {
        deleteNewConstructionAction,
        editNewConstructionAction,
        printNewConstructionDetailAction,
    } = useNewConstructionActions();

    const counters = useSelector(
        (data: IReduxRootState) => data.matchesAndThreadsCounters
    );

    const searchFilters = useSelector(
        (data: IReduxRootState) => data.searchFilters
    );
    const { trackEvent } = useMixpanelContext();

    const renderMapping = {
        place: (data: IPortalNewConstructionsListItem) => {
            if (!data?.project || !data?.project?.geographyInformation?.city) {
                return null;
            }

            const geographyInformation = data.project.geographyInformation;

            return (
                <List>
                    <Popover
                        title={''}
                        onEdge={false}
                        large={false}
                        content={
                            <>
                                <div>{`${
                                    geographyInformation.city.name || ''
                                } (${geographyInformation.city.province
                                    ?.name})`}</div>
                                <div>
                                    {geographyInformation?.macroZone?.name}
                                </div>
                                <div>
                                    {geographyInformation.address?.street}
                                </div>
                            </>
                        }
                    >
                        <div>
                            <ListItem
                                content={`${
                                    geographyInformation.city.name || ''
                                } (${geographyInformation.city.province
                                    ?.name})`}
                            />
                            <ListItem
                                content={
                                    geographyInformation.macroZone?.name || ''
                                }
                            />
                            <ListItem
                                content={geographyInformation.address?.street}
                            />
                        </div>
                    </Popover>
                </List>
            );
        },
        reference: (data: IPortalNewConstructionsListItem) => {
            if (!data) {
                return null;
            }

            return (
                <div
                    className="gx-property-item gx-property-item--clickable"
                    onClick={() => {
                        trackEvent({
                            event: 'properties_open_details_listing',
                            extra: {
                                ['property_id']: data.id,
                                ['list_tab']: ucFirst(
                                    `${searchFilters.status}`
                                ),
                            },
                        });
                        showModal({
                            id: data.id,
                            isOpen: true,
                            title: trans('label.ad_details'),
                            type: 'realEstate-detail',
                            footerActions: {
                                delete: (onDelete) =>
                                    showConfirmDialog({
                                        isOpen: true,
                                        title: trans('ad.delete'),
                                        type: 'deleteProperty',
                                        hasSubmitControl: true,
                                        submitControlLabel:
                                            trans('ad.delete_confirm'),
                                        submitLabel: trans('label.remove'),
                                        onSubmit: () => (
                                            deleteNewConstructionAction(
                                                data.id
                                            ),
                                            onDelete()
                                        ),
                                    }),
                                edit: () =>
                                    editNewConstructionAction(data.id, 6, true),
                                print: () =>
                                    printNewConstructionDetailAction(data.id),
                            },
                        });
                    }}
                >
                    <div className="gx-property-item__pic">
                        <img src={data.mainImageThumbUrl} loading="lazy" />
                    </div>
                    <div className="gx-property-item__desc">
                        <List>
                            <ListItem content={data.id} />
                            <ListItem content={data?.code || ''} />
                        </List>
                    </div>
                </div>
            );
        },
        date: (data: IPortalNewConstructionsListItem) => {
            if (!data || !data.modified) {
                return;
            }

            return data.modified;
        },
        visibility: (data: IPortalNewConstructionsListItem) => {
            if (
                data.statusId !== gtxConstants('PROPERTY_ACTIVE_PORTAL_STATUS')
            ) {
                return null;
            }

            return (
                <div className="gx-table-new__contentFixedHeight">
                    <PropertyActiveVisibilities property={data} />
                </div>
            );
        },
        match: (data: IPortalNewConstructionsListItem) => {
            if (counters && Array.isArray(counters)) {
                const found = counters.find(
                    (counter: Counter) => counter.propertyId === data.id
                );
                return (
                    <ListCounter
                        data={found}
                        type="matches"
                        href={`/clienti/match?search=${found?.propertyId}`}
                    />
                );
            }
            return <ListCounter disabled />;
        },
        threads: (data: IPortalNewConstructionsListItem) => {
            if (counters && Array.isArray(counters)) {
                const found = counters.find(
                    (counter: Counter) => counter.propertyId === data.id
                );
                return (
                    <ListCounter
                        data={found}
                        type="threads"
                        href={`/messaggi/lista?code=${found?.propertyId}`}
                    />
                );
            }
            return <ListCounter disabled />;
        },
        quality: (
            data: IPortalNewConstructionsListItem,
            openRankingDetailModal: () => void,
            qualityDetails: IPortalNewConstructionsImproveQualityApiResponse,
            isFromBackoffice: boolean
        ) => {
            if (!data.ranking) {
                return (
                    <span className="gx-table-new__contentFixedHeight">
                        {'0%'}
                    </span>
                );
            }

            const qualityOutcomesStatus = areAllOutcomesChecked(qualityDetails)
                ? 'positive'
                : 'warning';

            return (
                <span
                    className="gx-table-new__quality gx-table-new__contentFixedHeight"
                    onClick={
                        isFromBackoffice ? openRankingDetailModal : undefined
                    }
                >
                    <span>{data.ranking}%</span>
                    <Icon
                        className={`gx-icon gx-text-${qualityOutcomesStatus}`}
                        name={
                            qualityOutcomesStatus === 'positive'
                                ? 'check-circle--active'
                                : 'exclamation-mark-circle--active'
                        }
                    />
                </span>
            );
        },
        statistics: (data: IPortalNewConstructionsListItem) => {
            if (!data?.usersStats) {
                return;
            }
            return (
                <List>
                    <ListItem
                        content={`${data.usersStats.views} ${trans(
                            'label.visit_plural'
                        )} ${trans('label.detail')}`}
                    />
                    <ListItem
                        content={`${data.usersStats.saves} ${trans(
                            'label.saved_plural'
                        )}`}
                    />
                    <ListItem
                        content={`${data.usersStats.hidden} ${trans(
                            'label.hidden_plural'
                        )}`}
                    />
                </List>
            );
        },
    };

    return renderMapping;
};

export default useListField;
