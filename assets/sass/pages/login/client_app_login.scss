@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.client-app-login-box {
  position: absolute;
  top: 2rem;
  left: 50%;
  width: 100%;
  max-width: 48rem;
  padding: space(md);
  transform: translateX(-50%);

  strong {
    color: color(content-high);
    font-weight: 900;
  }

  &__logo {
    img {
      width: 24rem;
      height: 4rem;
      margin: 0 auto;
      object-fit: contain;
      object-position: center bottom;
    }
  }

  &__title {
    margin: 5rem 0 space(sm);
    @include typography(title-1);
    color: color(content-medium);
  }

  &__list {
    @include typography(title-2);
    color: color(content-medium);
    list-style: disc;

    li {
      margin-left: space(lg);
      margin-bottom: space(sm);
    }
  }

  &__text {
    @include typography(title-2);
    color: color(content-medium);
  }
}
