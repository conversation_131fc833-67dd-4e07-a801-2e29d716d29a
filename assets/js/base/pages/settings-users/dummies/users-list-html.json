{"agency": {"idAgenzia": 93434, "idOriginale": null, "idFatturazione": null, "idFatturazioneGtx": null, "descrizione": "Nulla ipsam integer mi! Repellat animi placeat vero! Phasellus pariatur. Maxime ornare! Expedita est, odio voluptatibus ligula enim, quidem, lectus exercitation non, donec auctor etiam perferendis? Temporibus! Pede? Etiam ipsam. Sint ultricies aptent consequatur purus vel, labore pariatur voluptas dolores feugiat conubia! Parturient?\r\n\r\nNulla ipsam integer mi! Repellat animi placeat vero! Phasellus pariatur. Maxime ornare! xpedita est, odio voluptatibus ligula enim, quidem, lectus exercitation non, doec auctor etiam perferendis? Temporibus! Pede? Etiam ipsam. Sint ultricies aptent consequatur purus vel, laore pariatur voluptas dolores feugiat conubia! Parturient", "nome": "Casatua", "indirizzo": "Via San Rocco", "numeroCivico": "21", "cap": "18025", "contatti": null, "username": "<EMAIL>", "password": "$V2_fe83fa8ebcd7f536751d46d818d7d548b98496a0430b5ce270247d41cbe9aa28", "passwordMD5": null, "lastLogin": null, "lastLoginIp": null, "email": "<EMAIL>", "email2": "<EMAIL>", "web": "www.indomio.es", "note": "<PERSON><PERSON><PERSON> immobil<PERSON>i", "fkLogo": 1323055021, "fkLogoBig": 1323055023, "fkImmagine": 1323026119, "telefono1": "+3906945010211", "telefono2": "+3906945010211", "cellulare": "+************", "fax": "+3933319111", "nomeTitolare": "<PERSON>", "cognomeTitolare": "Bian<PERSON>o", "telefonoTitolare": "+************", "cellulareTitolare": "+************", "emailTitolare": "<EMAIL>", "alboTitolare": null, "cittaAlboTitolare": null, "indirizzoSedeLegale": "Via roma, 333", "capSedeLegale": "00145", "piva": "08435221000", "codice_fiscale": null, "socioUnico": true, "numeroREA": "324423423", "capitaleSociale": 1000000, "comunicazioni": true, "dataAttivazione": null, "dataInizioValidita": null, "dataFineValidita": null, "fkCartina": null, "responsabile": {"idResponsabile": 141, "codice": "zzz_test", "nome": "Anagrafica di Test", "telefono": "02 871 074 32", "telefono_assistenza": "02 87 11 87", "visibile": true, "avatar": "avatar_141.jpg", "email": "<EMAIL>", "dipartimento": "Park", "password": "3b9c1275f33b9ea5", "chatbot": false}, "ranking": 6, "reputazione": null, "visiteTelefono": null, "latitudine": 44.0816, "longitudine": 7.74805, "zoom": 17, "keyurl": null, "aperturaLunVenMatt": null, "chiusuraLunVenMatt": null, "aperturaLunVenPom": null, "chiusuraLunVenPom": null, "aperturaSabMatt": null, "chiusuraSabMatt": null, "aperturaSabPom": null, "chiusuraSabPom": null, "segreteria": null, "url_valutamutuo": null, "url_consulenzaMutuo": null, "nascondiMutuo": null, "nascondiConsulenzaMutuo": null, "nascondiMutuoContatti": null, "memorizzaLuogo": null, "flagCostruttore": 0, "visibilitaProgetti": true, "dataInizioCost": null, "dataFineCost": null, "flatEstero": null, "dataInizioEstero": null, "dataFineEstero": null, "opzionePremium": 0, "testoPremium1": "casatua", "testoPremium2": null, "urlPremium": "", "numeroPrimoPianoPremium": null, "primoPianoPremiumAttivi": null, "primoPianoPremiumRand": null, "dataInizioPremium": null, "dataInizioOpzionePremium": null, "dataTermineOpzionePremium": null, "dataTerminePremium": null, "dataFineLusso": null, "dataInizioLusso": null, "statoLusso": null, "numeroAnnunciH24": null, "flagCommerciale": null, "numeroAnnunciRepubblica": null, "emailAmministrazione": null, "utilizzo": null, "dataFineConcorrenza": null, "paginaFacebook": null, "dataCreazioneAgenzia": null, "dataModificaAgenzia": "2024-01-05 12:02:38", "comune": {"nome": "Briga Alta", "sigla_provincia": "CN", "idComune": 9408}, "macrozona": {"nome": "Piaggia", "idMacrozona": 20617}, "comuneSedeLegale": null, "franchising": {"nome": "Casapoint", "idFranchising": 124}, "tipologia": {"nome": "<PERSON><PERSON><PERSON>", "idTipologiaAgenzia": 2}, "sottoTipologiaAgenzia": {"nome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "idSottoTipologia": 20}, "associazione": {"nome": "<PERSON><PERSON><PERSON>", "idAssociazione": 3}, "associazioneProvincia": {"idProvincia": "AT", "nome": "<PERSON><PERSON>"}, "comuneRif": null, "provinciaRif": null, "tipoContrattoAgenzia": {"nome": "BASIC", "idContratto": 1}, "macrozonaRif": null, "aperturaDomMatt": null, "chiusuraDomMatt": null, "aperturaDomPom": null, "chiusuraDomPom": null, "disponibilitaAppuntamento": null, "dataAggiornamentoAgenzia": null, "luogoRepertorioEconomicoAmministrativo": {"nome": "Roma", "sigla_provincia": "RM", "idComune": 6737}, "registroImprese": {"nome": "Bracciano", "sigla_provincia": "RM", "idComune": 6655}, "ragionesociale": "casamia", "responsabileGetrix": null, "fkLogoOrig": 1323055025, "descrizioniMultilingua": {"en-GB": {"lingua": "en-GB"}, "fr-FR": {"lingua": "fr-FR"}, "pt-PT": {"lingua": "pt-PT"}, "de-DE": {"lingua": "de-DE", "testo": "a s<PERSON>da das das das das dasd as das ds das"}, "ru-RU": {"lingua": "ru-RU"}, "el-GR": {"lingua": "el-GR"}, "es-ES": {"lingua": "es-ES"}, "it-IT": {"lingua": "it-IT", "testo": "Nulla ipsam integer mi! Repellat animi placeat vero! Phasellus pariatur. Maxime ornare! Expedita est, odio voluptatibus ligula enim, quidem, lectus exercitation non, donec auctor etiam perferendis? Temporibus! Pede? Etiam ipsam. Sint ultricies aptent consequatur purus vel, labore pariatur voluptas dolores feugiat conubia! Parturient?\r\n\r\nNulla ipsam integer mi! Repellat animi placeat vero! Phasellus pariatur. Maxime ornare! xpedita est, odio voluptatibus ligula enim, quidem, lectus exercitation non, doec auctor etiam perferendis? Temporibus! Pede? Etiam ipsam. Sint ultricies aptent consequatur purus vel, laore pariatur voluptas dolores feugiat conubia! Parturient"}}, "orariAgenzia": [[], [{"idOrariAgenzia": 247555, "giorno": 1, "inizio": "17:25", "fine": "18:30", "dataModifica": 1702483390}], [{"idOrariAgenzia": 247567, "giorno": 2, "inizio": "16:10", "fine": "16:30", "dataModifica": 1702483390}], [], [], [], []], "extensions": [{"id": 1, "nome": "Nuove Costruzioni", "stato": true, "scadenza": "2028-11-30", "maxAnnunci": 1}, {"id": 3, "nome": "House 24", "stato": false}, {"id": 5, "nome": "<PERSON>", "stato": false}, {"id": 7, "nome": "Repubblica", "stato": false}, {"id": 9, "nome": "Estero", "stato": true, "scadenza": "2024-06-02", "maxAnnunci": 100}, {"id": 11, "nome": "<PERSON><PERSON>", "stato": false, "scadenza": "2017-05-31"}, {"id": 13, "nome": "virtual tour", "stato": false, "scadenza": "2015-09-30"}, {"id": 15, "nome": "video tour", "stato": false}, {"id": 17, "nome": "planimetria", "stato": false, "extraInfo": {"projects": 0}}, {"id": 19, "nome": "getrix", "stato": true, "scadenza": "2017-12-31", "extraInfo": {"profile": 2}}, {"id": 21, "nome": "new video tour", "stato": false, "scadenza": "2011-09-20"}, {"id": 22, "nome": "new agenda", "stato": true}, {"id": 26, "nome": "<PERSON><PERSON> agenzia", "stato": true, "scadenza": "2011-09-05"}, {"id": 30, "nome": "Profilo facebook", "stato": false}, {"id": 34, "nome": "Getrix Social", "stato": true, "scadenza": "2013-04-02"}, {"id": 35, "nome": "Subito.it", "stato": false, "scadenza": "2014-05-28"}, {"id": 36, "nome": "Youdomus.it", "stato": true, "extraInfo": {"queries": 300, "standalone": false}}, {"id": 37, "nome": "VirtualTour 360", "stato": true, "scadenza": "2022-10-02"}, {"id": 38, "nome": "ADV", "stato": true, "scadenza": "2017-05-10", "extraInfo": {"notes": "pubblicità"}}, {"id": 39, "nome": "ImmoVox", "stato": true, "scadenza": "2024-03-22", "extraInfo": {"voxNumber": "+390685874464", "destinationNumber": "+393333319155", "status": 1, "notifications": null}}, {"id": 40, "nome": "Multinvio", "stato": true, "scadenza": "2024-03-29"}, {"id": 41, "nome": "Acquisizione privati", "stato": true, "scadenza": "2026-08-30"}, {"id": 42, "nome": "Valutazioni immobiliari", "stato": true, "extraInfo": {"zones": [{"id": 130921, "city": {"id": 6627, "name": "Riet<PERSON>", "province": {"id": "RI", "name": "Riet<PERSON>", "region": {"id": "laz", "name": "Lazio", "country": {"id": "IT", "name": "Italia"}}}, "cityMacroZoneType": null, "chiefTown": null, "istatCode": null, "coordinates": null}, "macroZone": null, "type": {"id": 1, "name": "<PERSON><PERSON>"}, "locked": false, "from": "2023-10-01", "to": "2023-10-31"}]}}, {"id": 43, "nome": "Report immobiliare", "stato": true, "scadenza": "2022-08-04", "extraInfo": {"dailyLimit": 15, "monthlyLimit": 300}}, {"id": 44, "nome": "Agenda", "stato": true, "scadenza": "2017-12-31"}, {"id": 45, "nome": "Clienti", "stato": true, "scadenza": "2017-12-31"}, {"id": 46, "nome": "<PERSON><PERSON>", "stato": true, "scadenza": "2017-12-31"}, {"id": 47, "nome": "Lista annunci", "stato": true, "scadenza": "2017-12-31"}, {"id": 48, "nome": "Richiesta consulenza mutuo", "stato": true, "scadenza": "2022-03-31"}, {"id": 49, "nome": "Catalogo aste", "stato": true, "scadenza": "2026-03-31"}, {"id": 50, "nome": "Immov<PERSON><PERSON>", "stato": true}, {"id": 51, "nome": "Messaggistica Gestionale", "stato": true, "scadenza": "2022-03-31"}, {"id": 52, "nome": "Richieste vendita", "stato": true}, {"id": 53, "nome": "Reportistica", "stato": true}, {"id": 60, "nome": "<PERSON><PERSON><PERSON><PERSON> Visi<PERSON>", "stato": true}], "video": null, "nazione": {"id": "IT", "description": "Italia"}, "nazioneSedeLegale": {"id": "IT"}, "nazioneREA": {"id": "IT", "description": "Italia"}, "nazioneRegistroImprese": {"id": "IT", "description": "Italia"}, "regione": {"id": "pie", "description": "Piemonte"}, "responsabileVisibility": null, "responsabileAvatarToView": null, "responsabileNomeToView": null, "responsabileTelefonoToView": null, "responsabileTelefonoAssistenzaToView": null, "hasSubitoIt": null, "hasSocial": null, "hasSitoWeb": null, "hasChatBot": null, "logoUrl": "http://media.getrix.daniele.irsuti.dev.loc/imagenoresize/1323055023.jpg", "imageUrl": "http://media.getrix.daniele.irsuti.dev.loc/imagenoresize/1323026119.jpg", "videoHtml": null, "videoUrl": null, "emailPec": "<EMAIL>", "codiceSdi": "", "opzionePremiumLabel": "label.standard", "opzionePremiumFormattedUrl": "https://immobiliare-it-reference.kube-dev.it3.ns.farm/agenzie-immobiliari/93434", "contractExtensions": {"foreignAds": {"id": 9, "nome": "Estero", "stato": true, "scadenza": "02/06/2024", "maxAnnunci": 100}, "immoVox": {"id": 39, "nome": "ImmoVox", "stato": true, "scadenza": "22/03/2024", "extraInfo": {"voxNumber": "+390685874464", "destinationNumber": "+393333319155", "status": 1, "notifications": null}}, "interactivePlans": {"id": 17, "nome": "planimetria", "stato": false, "extraInfo": {"projects": 0}}, "multisend": {"id": 40, "nome": "Multinvio", "stato": true, "scadenza": "29/03/2024"}, "prestige": {"id": 3, "nome": "House 24", "stato": false}, "privateProperties": {"id": 41, "nome": "Acquisizione privati", "stato": true, "scadenza": "30/08/2026"}, "propertyValuations": {"id": 42, "nome": "Valutazioni immobiliari", "stato": true, "extraInfo": {"zones": [{"id": 130921, "city": {"id": 6627, "name": "Riet<PERSON>", "province": {"id": "RI", "name": "Riet<PERSON>", "region": {"id": "laz", "name": "Lazio", "country": {"id": "IT", "name": "Italia"}}}, "cityMacroZoneType": null, "chiefTown": null, "istatCode": null, "coordinates": null}, "macroZone": null, "type": {"id": 1, "name": "<PERSON><PERSON>"}, "locked": false, "from": "2023-10-01", "to": "2023-10-31"}]}}, "realEstateReport": {"id": 43, "nome": "Report immobiliare", "stato": true, "scadenza": "04/08/2022", "extraInfo": {"dailyLimit": 15, "monthlyLimit": 300}}, "virtualTour3d": {"id": 37, "nome": "VirtualTour 360", "stato": true, "scadenza": "02/10/2022"}, "website": {"id": 26, "nome": "<PERSON><PERSON> agenzia", "stato": true, "scadenza": "05/09/2011"}, "youdomus": {"id": 36, "nome": "Youdomus.it", "stato": true, "extraInfo": {"queries": 300, "standalone": false}}, "salesRequests": {"id": 52, "nome": "Richieste vendita", "stato": true}}, "hasConsulenzaMutuo": null, "hasImmovisita": null, "languageId": "it-IT", "languageCode": "it_IT", "modules": {}, "contract": {"validFrom": {"date": "2016-07-04 00:00:00.000000", "timezone_type": 3, "timezone": "UTC"}, "validTo": {"date": "2033-04-30 00:00:00.000000", "timezone_type": 3, "timezone": "UTC"}, "expiring": false, "plafond": 318, "status": 1, "accountLocked": false, "getrix": 2, "getrixVersion": 10, "adsLoadingGroup": "getrix_ok", "plans": 0, "guaranteedAds": false, "sellAdsCount": 200, "rentAdsCount": 200, "projectsCount": 1}, "address": {}, "language": null, "agencyFlags": [{}], "getrixVersion": 10, "webagencyInfo": null, "getrix": 2, "uuid": "5fc5117c-ae36-589b-aabe-d58ef63f50f2", "soloAppuntamento": null}, "users": [{"idAgente": 205908, "cognome": "Bian<PERSON>o", "nome": "<PERSON>", "email": "<EMAIL>", "chat": false, "fkImmagineAgente": **********, "titolare": true, "emailVerificata": true, "status": 2, "profilo": {"fkAgente": 205908, "dataNascita": "1985-07-11", "sesso": 1, "codiceFiscale": "12wedfr56ytghju8", "comune": {"nome": "Cerignola", "sigla_provincia": "FG", "idComune": 10281}, "indirizzo": "Via valentino banal 891", "cap": "00174", "codiceREA": "kaaaabvd", "partitaIVA": "**********", "associazioneDiCategoria": {"idAssociazioneDiCategoria": 2, "nome": "<PERSON><PERSON><PERSON>"}, "contatti": [{"fkAgente": 205908, "numero": "+************", "tipo": "2", "preferito": true, "idContattoAgente": 719727, "pubblico": false, "fNumero": "+39 333 3333 333", "prefisso": "+39", "fNumeroSenzaPrefisso": "3333333333", "shortCode": "it"}], "biografia": [{"fkAgente": 205908, "lingua": "it", "testo": "<PERSON><PERSON> (Siena, 1257 circa – Siena, 1313 circa) è stato uno scrittore e poeta italiano, contemrporaneo di <PERSON> e appartenente alla storica casata degli Angiolieri.\nIl celebre sonetto S'i' fosse foco, arderei 'l mondo appartiene a unsecolare tradizione letteraria goliardica improntata all'improperio e alla dissacrazione dell", "idBiografiaAgente": 1206771}, {"fkAgente": 205908, "lingua": "en", "testo": "Mi laoreet augue maecenas consectetuer proin aptent occaecat odit mattis! Class sollicitudin, porro nemo penatibus, totam quod porro, minus condimentum lobortis sapiente dictum est, veniam natoque, numquam doloribus exercitationem dis! Autem facilis quo? Officia pellentesque mattis? Eu dolores? Alias. Praesentium, nobis expedita, eveniet quas reiciendis nihil volutpat, anim, sint venenatis.\n\nMolestiae nesciunt ex vulputate facilisis eaque pulvinar fusce, vel, aute, libero? Nunc scelerisque fusce molestias iure vulputate? Morbi quisquam congue corrupti dis! Diam, beatae velit ratione iste? Natus tempore duis, senectus nonummy! Varius imperdiet cursus illum, iusto deleniti iure, mollis! Adipisicing iure, mollis amet eu blandit? Fusce ultricies minima aliquet.\n\nFermentum scelerisque soluta primis tempora rem lobortis mollitia porttitor debitis netus laboriosam saepe magnis dicta distinctio quos leo excepturi architecto corrupti feugiat velit. Proin libero laboriosam. Repudiandae rerum quaerat fermentum? Mollit ac, praesentium pharetra malesuada torquent quae posuere ornare, sollicitudin excepturi nec aliquid dicta! Gravida aspernatur minus adipiscing gravida ex.Mi laoreet augue maecenas consectetuer proin aptent occaecat odit mattis! Class sollicitudin, porro nemo penatibus, totam quod porro, minus condimentum lobortis sapiente dictum est, veniam natoque, numquam doloribus exercitationem dis! Autem facilis quo? Officia pellentesque mattis? Eu dolores? Alias. Praesentium, nobis expedita, eveniet quas reiciendis nihil volutpat, anim, sint venenatis.\n\nMolestiae nesciunt ex vulputate facilisis eaque pulvinar fusce, vel, aute, libero? Nunc scelerisque fusce molestias iure vulputate? Morbi quisquam congue corrupti dis! Diam, beatae velit ratione iste? Natus tempore duis, senectus nonummy! Varius imperdiet cursus illum, iusto deleniti iure, mollis! Adipisicing iure, mollis amet eu blandit? Fusce ultricies minima aliquet.\n\nFermentum scelerisque soluta primis tempora rem lobortis mollitia porttitor debitis netus laboriosam saepe magnis dicta distinctio quos leo excepturi architecto corrupti feugiat velit. Proin libero laboriosam. Repudiandae rerum quaerat fermentum? Mollit ac, praesentium pharetra malesuada torquent quae posuere ornare, sollicitudin excepturi nec aliquid dicta! Gravida aspernatur minus adipiscing gravida ex.Mi laoreet augue maecenas consectetuer proin aptent occaecat odit mattis! Class sollicitudin, porro nemo penatibus, totam quod porro, minus condimentum lobortis sapiente dictum est, veniam natoque, numquam doloribus exercitationem dis! Autem facilis quo? Officia pellentesque mattis? Eu dolores? Alias. Praesentium, nobis expedita, eveniet quas reiciendis nihil volutpat, anim, sint venenatis.\nMolestiae nesciunt ex vulputate facilisis eaque pulvinar fusce, vel, aute, libero? Nunc scelerisque fusce molestias iure vulputate? Morbi quisquam congue corrupti dis! Diam, beatae velit ratione iste? Natus tempore duis, senectus nonummy! Varius imperi", "idBiografiaAgente": 1206773}, {"fkAgente": 205908, "lingua": "de", "testo": "Wer reitet so spät durch Nacht und Wind?\nEs ist der Vater mit seinem Kind;\nEr hat den Knaben wohl in dem Arm,\nEr faßt ihn sicher, er hält ihn warm.\n\n<PERSON><PERSON>, was birgst du so bang' dein Gesicht?\n<PERSON><PERSON><PERSON>, Vater, du den Erlkönig nicht?\nDen Erlenkönig mit Kron' und Schweif?\n<PERSON><PERSON>, es ist ein Nebelschweif.", "idBiografiaAgente": 1206775}, {"fkAgente": 205908, "lingua": "fr", "testo": "Le 30 mars 1844, naissance à Metz de <PERSON>, fils <PERSON>, militaire de carrière, et d' <PERSON><PERSON><PERSON>-Sté<PERSON>nie <PERSON>. C'est une famille bourgeoise aisée. Ils ont recueilli leur nièce Elisa Moncomble en 1836 et elle jouera auprès de <PERSON> à la fois le rôle de grande soeur et de cousine. Enfant unique longtemps désiré, il est choyé par sa mère et sa cousine qui lui passent tous ses caprices. En 1849, le père de Verlaine démissionne de l'armée après de nombreuses mutations et la famille s'installe à Paris. De 1851 à 1861 : V<PERSON><PERSON>e fait toutes ses études à Paris, en temps qu'interne à l'institution Landry, rue Chaptal, puis au lycée Bonaparte (Condorcet aujourd'hui).", "idBiografiaAgente": 1206777}, {"fkAgente": 205908, "lingua": "es", "testo": "<PERSON> (Sevilla, 26 de julio de 1875 – Collioure, Francia, 22 de febrero de 1939) fue un poeta español, miembro tardío de la Generación del 98 y uno de sus miembros más representativos. Su obra inicial suele inscribirse en el movimiento literario denominado Modernismo. Hola.", "idBiografiaAgente": 1206779}, {"fkAgente": 205908, "lingua": "pt", "testo": "PORTOGHESE", "idBiografiaAgente": 1206781}, {"fkAgente": 205908, "lingua": "ru", "testo": "RUSSO", "idBiografiaAgente": 1206783}, {"fkAgente": 205908, "lingua": "gr", "testo": "GRECO2", "idBiografiaAgente": 1206785}]}, "ruolo": {"idRuoloAgente": 1, "ruolo": "Amministratore"}, "uuid": "93f00def-d326-5704-a24c-10636d1ab490", "fkAgenzia": 93434, "mostraInPubblicita": false, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/205908/**********/80.jpg"}, {"idAgente": 235278, "cognome": "<PERSON>", "nome": "<PERSON><PERSON>", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": true, "status": 2, "profilo": {"fkAgente": 235278, "dataNascita": "2020-02-05", "sesso": 0, "contatti": [{"fkAgente": 235278, "numero": "+39234324234", "tipo": "1", "preferito": true, "idContattoAgente": 463012, "pubblico": true, "fNumero": "+39 23432 4234", "prefisso": "+39", "fNumeroSenzaPrefisso": "234324234", "shortCode": "it"}, {"fkAgente": 235278, "numero": "+3943434343434", "tipo": "1", "preferito": false, "idContattoAgente": 463014, "pubblico": true, "fNumero": "+39 434343 43434", "prefisso": "+39", "fNumeroSenzaPrefisso": "43434343434", "shortCode": "it"}], "biografia": [{"fkAgente": 235278, "lingua": "it", "testo": "", "idBiografiaAgente": 450038}, {"fkAgente": 235278, "lingua": "en", "testo": "", "idBiografiaAgente": 450040}, {"fkAgente": 235278, "lingua": "de", "testo": "", "idBiografiaAgente": 450042}, {"fkAgente": 235278, "lingua": "fr", "testo": "", "idBiografiaAgente": 450044}, {"fkAgente": 235278, "lingua": "es", "testo": "", "idBiografiaAgente": 450046}, {"fkAgente": 235278, "lingua": "pt", "testo": "", "idBiografiaAgente": 450048}, {"fkAgente": 235278, "lingua": "ru", "testo": "", "idBiografiaAgente": 450050}, {"fkAgente": 235278, "lingua": "gr", "testo": "", "idBiografiaAgente": 450052}]}, "nuovaEmail": "<EMAIL>", "ruolo": {"idRuoloAgente": 1, "ruolo": "Amministratore"}, "uuid": "e9332cc4-fab2-5179-8ce4-3e13be5e136e", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/235278/7168/80.jpg"}, {"idAgente": 235374, "cognome": "Napoli", "nome": "<PERSON>", "email": "<EMAIL>", "chat": false, "fkImmagineAgente": 1117142660, "titolare": false, "emailVerificata": true, "status": 2, "profilo": {"fkAgente": 235374, "dataNascita": "1986-07-11", "sesso": 0, "contatti": [{"fkAgente": 235374, "numero": "+39044555555", "tipo": "1", "preferito": true, "idContattoAgente": 575110, "pubblico": true, "fNumero": "+39 0445 555 55", "prefisso": "+39", "fNumeroSenzaPrefisso": "044555555", "shortCode": "it"}, {"fkAgente": 235374, "numero": "+3965765765", "tipo": "1", "preferito": false, "idContattoAgente": 575112, "pubblico": true, "fNumero": "+39 6576 5765", "prefisso": "+39", "fNumeroSenzaPrefisso": "65765765", "shortCode": "it"}], "biografia": [{"fkAgente": 235374, "lingua": "it", "testo": "", "idBiografiaAgente": 748866}, {"fkAgente": 235374, "lingua": "en", "testo": "", "idBiografiaAgente": 748868}, {"fkAgente": 235374, "lingua": "de", "testo": "", "idBiografiaAgente": 748870}, {"fkAgente": 235374, "lingua": "fr", "testo": "", "idBiografiaAgente": 748872}, {"fkAgente": 235374, "lingua": "es", "testo": "", "idBiografiaAgente": 748874}, {"fkAgente": 235374, "lingua": "pt", "testo": "", "idBiografiaAgente": 748876}, {"fkAgente": 235374, "lingua": "ru", "testo": "", "idBiografiaAgente": 748878}, {"fkAgente": 235374, "lingua": "gr", "testo": "", "idBiografiaAgente": 748880}]}, "ruolo": {"idRuoloAgente": 9, "ruolo": "Responsabile Commercializzazione"}, "uuid": "17d6634f-a08e-59ce-8600-aef40778f5f0", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/235374/1117142660/80.jpg"}, {"idAgente": 243824, "cognome": "Spaziali", "nome": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": true, "status": 3, "profilo": {"fkAgente": 243824, "dataNascita": "1984-07-06", "sesso": 1, "codiceFiscale": "****************", "indirizzo": "Piazza del Popolo", "cap": "00187", "codiceREA": "3123", "partitaIVA": "12412341234", "associazioneDiCategoria": {"idAssociazioneDiCategoria": 3, "nome": "<PERSON><PERSON><PERSON>"}, "contatti": [{"fkAgente": 243824, "numero": "+39123456789", "tipo": "1", "preferito": true, "idContattoAgente": 529271, "pubblico": true, "fNumero": "+39 12345 6789", "prefisso": "+39", "fNumeroSenzaPrefisso": "123456789", "shortCode": "it"}, {"fkAgente": 243824, "numero": "+3944444433", "tipo": "1", "preferito": false, "idContattoAgente": 529273, "pubblico": true, "fNumero": "+39 4444 4433", "prefisso": "+39", "fNumeroSenzaPrefisso": "44444433", "shortCode": "it"}], "biografia": [{"fkAgente": 243824, "lingua": "it", "testo": "I dati spaziali sono caratterizzati da una tale quantità di formati che il problema dell'interoperabilità è sempre stato in primo pianofdfdhgghffhfgghfhf", "idBiografiaAgente": 672659}, {"fkAgente": 243824, "lingua": "en", "testo": "", "idBiografiaAgente": 672661}, {"fkAgente": 243824, "lingua": "de", "testo": "", "idBiografiaAgente": 672663}, {"fkAgente": 243824, "lingua": "fr", "testo": "", "idBiografiaAgente": 672665}, {"fkAgente": 243824, "lingua": "es", "testo": "", "idBiografiaAgente": 672667}, {"fkAgente": 243824, "lingua": "pt", "testo": "", "idBiografiaAgente": 672669}, {"fkAgente": 243824, "lingua": "ru", "testo": "", "idBiografiaAgente": 672671}, {"fkAgente": 243824, "lingua": "gr", "testo": "", "idBiografiaAgente": 672673}]}, "ruolo": {"idRuoloAgente": 9, "ruolo": "Responsabile Commercializzazione"}, "uuid": "6f04c868-9306-5b72-90be-391da1904af5", "fkAgenzia": 93434, "mostraInPubblicita": false, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/243824/8383/80.jpg"}, {"idAgente": 248454, "cognome": "<PERSON><PERSON><PERSON>", "nome": "Antonio", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": false, "status": 1, "profilo": {"fkAgente": 248454, "codiceFiscale": "", "indirizzo": "", "cap": "", "codiceREA": "", "partitaIVA": ""}, "ruolo": {"idRuoloAgente": 5, "ruolo": "<PERSON><PERSON>"}, "uuid": "34bc34f5-e47c-54c5-b2a6-804338d059cc", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/248454/6583/80.jpg"}, {"idAgente": 252945, "cognome": "<PERSON>", "nome": "Rambo", "email": "<EMAIL>", "chat": false, "fkImmagineAgente": 669938699, "titolare": false, "emailVerificata": true, "status": 2, "profilo": {"fkAgente": 252945, "sesso": 0, "codiceFiscale": "", "indirizzo": "", "cap": "", "codiceREA": "", "partitaIVA": "", "contatti": [{"fkAgente": 252945, "numero": "+************", "tipo": "2", "preferito": true, "idContattoAgente": 282607, "pubblico": true, "fNumero": "+39 335 1231 234", "prefisso": "+39", "fNumeroSenzaPrefisso": "3351231234", "shortCode": "it"}], "biografia": [{"fkAgente": 252945, "lingua": "it", "testo": "", "idBiografiaAgente": 200223}, {"fkAgente": 252945, "lingua": "en", "testo": "", "idBiografiaAgente": 200225}, {"fkAgente": 252945, "lingua": "de", "testo": "", "idBiografiaAgente": 200227}, {"fkAgente": 252945, "lingua": "fr", "testo": "", "idBiografiaAgente": 200229}, {"fkAgente": 252945, "lingua": "es", "testo": "", "idBiografiaAgente": 200231}, {"fkAgente": 252945, "lingua": "pt", "testo": "", "idBiografiaAgente": 200233}, {"fkAgente": 252945, "lingua": "ru", "testo": "", "idBiografiaAgente": 200235}, {"fkAgente": 252945, "lingua": "gr", "testo": "", "idBiografiaAgente": 200237}]}, "ruolo": {"idRuoloAgente": 9, "ruolo": "Responsabile Commercializzazione"}, "uuid": "1ad6060b-c80d-5731-a0e5-d4c1efa28407", "fkAgenzia": 93434, "mostraInPubblicita": false, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/252945/669938699/80.jpg"}, {"idAgente": 288639, "cognome": "Men", "nome": "Car", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": false, "status": 0, "profilo": {"fkAgente": 288639, "codiceFiscale": "", "indirizzo": "", "cap": "", "codiceREA": "", "partitaIVA": ""}, "ruolo": {"idRuoloAgente": 5, "ruolo": "<PERSON><PERSON>"}, "uuid": "7f45a8d0-3c92-5d27-b005-edf12944ab45", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/288639/99109/80.jpg"}, {"idAgente": 425746, "cognome": "Gironella", "nome": "<PERSON>", "email": "<EMAIL>", "chat": false, "fkImmagineAgente": 1148719496, "titolare": false, "emailVerificata": true, "status": 2, "profilo": {"fkAgente": 425746, "dataNascita": "1950-01-01", "sesso": 0, "codiceFiscale": "aaaaaa50r12h501b", "indirizzo": "piazza della radio, 1", "cap": "00146", "codiceREA": "RA-123", "partitaIVA": "12345", "associazioneDiCategoria": {"idAssociazioneDiCategoria": 1, "nome": "<PERSON><PERSON>"}}, "nuovaEmail": "<EMAIL>", "ruolo": {"idRuoloAgente": 5, "ruolo": "<PERSON><PERSON>"}, "uuid": "4e0855ad-ef96-51df-a73b-d437b836ea3a", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/425746/1148719496/80.jpg"}, {"idAgente": 428380, "cognome": "<PERSON><PERSON><PERSON>", "nome": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": true, "status": 2, "profilo": {"fkAgente": 428380, "contatti": [{"fkAgente": 428380, "numero": "+3952435234523452", "tipo": "1", "preferito": true, "idContattoAgente": 683836, "pubblico": true, "fNumero": "+39 5243523 4523452", "prefisso": "+39", "fNumeroSenzaPrefisso": "52435234523452", "shortCode": "it"}], "biografia": [{"fkAgente": 428380, "lingua": "it", "testo": "", "idBiografiaAgente": 1110978}, {"fkAgente": 428380, "lingua": "en", "testo": "", "idBiografiaAgente": 1110980}, {"fkAgente": 428380, "lingua": "de", "testo": "", "idBiografiaAgente": 1110982}, {"fkAgente": 428380, "lingua": "fr", "testo": "", "idBiografiaAgente": 1110984}, {"fkAgente": 428380, "lingua": "es", "testo": "", "idBiografiaAgente": 1110986}, {"fkAgente": 428380, "lingua": "pt", "testo": "", "idBiografiaAgente": 1110988}, {"fkAgente": 428380, "lingua": "ru", "testo": "", "idBiografiaAgente": 1110990}, {"fkAgente": 428380, "lingua": "gr", "testo": "", "idBiografiaAgente": 1110992}]}, "ruolo": {"idRuoloAgente": 5, "ruolo": "<PERSON><PERSON>"}, "uuid": "fee6b30f-9b5e-5bdd-969b-ab3779996beb", "fkAgenzia": 93434, "mostraInPubblicita": false, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/428380/6973/80.jpg"}, {"idAgente": 432405, "cognome": "Pi", "nome": "Gi", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": true, "status": 2, "ruolo": {"idRuoloAgente": 5, "ruolo": "<PERSON><PERSON>"}, "uuid": "01ee7298-8198-5590-9ecb-9f3a75d50e2a", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/432405/7180/80.jpg"}, {"idAgente": 437280, "cognome": "Non Validato", "nome": "<PERSON><PERSON>", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": false, "status": 1, "ruolo": {"idRuoloAgente": 5, "ruolo": "<PERSON><PERSON>"}, "uuid": "21db21a4-951a-5647-8853-81721fdb2110", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/437280/6578/80.jpg"}, {"idAgente": 445668, "cognome": "Mari", "nome": "<PERSON>", "email": "<EMAIL>", "chat": false, "fkImmagineAgente": 1180200462, "titolare": false, "emailVerificata": true, "status": 2, "profilo": {"fkAgente": 445668, "contatti": [{"fkAgente": 445668, "numero": "+************", "tipo": "2", "preferito": true, "idContattoAgente": 588902, "pubblico": true, "fNumero": "+39 335 1234 567", "prefisso": "+39", "fNumeroSenzaPrefisso": "3351234567", "shortCode": "it"}], "biografia": [{"fkAgente": 445668, "lingua": "it", "testo": "", "idBiografiaAgente": 794642}, {"fkAgente": 445668, "lingua": "en", "testo": "", "idBiografiaAgente": 794644}, {"fkAgente": 445668, "lingua": "de", "testo": "", "idBiografiaAgente": 794646}, {"fkAgente": 445668, "lingua": "fr", "testo": "", "idBiografiaAgente": 794648}, {"fkAgente": 445668, "lingua": "es", "testo": "", "idBiografiaAgente": 794650}, {"fkAgente": 445668, "lingua": "pt", "testo": "", "idBiografiaAgente": 794652}, {"fkAgente": 445668, "lingua": "ru", "testo": "", "idBiografiaAgente": 794654}, {"fkAgente": 445668, "lingua": "gr", "testo": "", "idBiografiaAgente": 794656}]}, "ruolo": {"idRuoloAgente": 1, "ruolo": "Amministratore"}, "uuid": "d739d220-e4b6-5bad-98b8-91dac6eece8a", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/445668/1180200462/80.jpg"}, {"idAgente": 458944, "cognome": "<PERSON>", "nome": "<PERSON>", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": false, "status": 1, "ruolo": {"idRuoloAgente": 1, "ruolo": "Amministratore"}, "uuid": "b46f6a1c-2623-5df3-ad21-b81ca5f322e6", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/458944/7777/80.jpg"}, {"idAgente": 477556, "cognome": "<PERSON>soni", "nome": "<PERSON>", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": true, "status": 2, "ruolo": {"idRuoloAgente": 5, "ruolo": "<PERSON><PERSON>"}, "uuid": "fe0bb0c4-88ad-5e82-a1b1-e67d23878204", "fkAgenzia": 93434, "mostraInPubblicita": false, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/477556/8380/80.jpg"}, {"idAgente": 494933, "cognome": "<PERSON><PERSON>", "nome": "<PERSON><PERSON>", "email": "<EMAIL>", "chat": false, "fkImmagineAgente": 1323027375, "titolare": false, "emailVerificata": true, "status": 2, "profilo": {"fkAgente": 494933, "dataNascita": "1990-10-16", "sesso": 0, "codiceFiscale": "****************", "comune": {"nome": "Foggia", "sigla_provincia": "FG", "idComune": 10285}, "indirizzo": "<PERSON>, 3", "cap": "71122", "contatti": [{"fkAgente": 494933, "numero": "+39166180180", "tipo": "1", "preferito": true, "idContattoAgente": 719731, "pubblico": true, "fNumero": "+39 16618 0180", "prefisso": "+39", "fNumeroSenzaPrefisso": "166180180", "shortCode": "it"}], "biografia": [{"fkAgente": 494933, "lingua": "it", "testo": "giggino di mayo e il reddido di giddadinanza che non c'è più", "idBiografiaAgente": 1206803}, {"fkAgente": 494933, "lingua": "en", "testo": "mayonnaise luigi hello hi", "idBiografiaAgente": 1206805}, {"fkAgente": 494933, "lingua": "de", "testo": "", "idBiografiaAgente": 1206807}, {"fkAgente": 494933, "lingua": "fr", "testo": "je m'appelle baguette", "idBiografiaAgente": 1206809}, {"fkAgente": 494933, "lingua": "es", "testo": "", "idBiografiaAgente": 1206811}, {"fkAgente": 494933, "lingua": "pt", "testo": "", "idBiografiaAgente": 1206813}, {"fkAgente": 494933, "lingua": "ru", "testo": "", "idBiografiaAgente": 1206815}, {"fkAgente": 494933, "lingua": "gr", "testo": "", "idBiografiaAgente": 1206817}]}, "ruolo": {"idRuoloAgente": 1, "ruolo": "Amministratore"}, "uuid": "e31b35c3-5228-54e5-b826-e1a9e9a12268", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/494933/1323027375/80.jpg"}, {"idAgente": 494935, "cognome": "<PERSON>", "nome": "<PERSON>", "telefono": "+39061234567", "cellulare": "+************", "email": "<EMAIL>", "chat": true, "titolare": false, "emailVerificata": false, "status": 1, "profilo": {"fkAgente": 494935, "dataNascita": "1985-07-11", "sesso": 0, "codiceFiscale": "****************", "comune": {"nome": "Roma", "sigla_provincia": "RM", "idComune": 6737}, "indirizzo": "Via roma, 1", "cap": "00177", "codiceREA": "678JFDSF7", "partitaIVA": "0123YYYY", "associazioneDiCategoria": {"idAssociazioneDiCategoria": 2, "nome": "<PERSON><PERSON><PERSON>"}, "contatti": [{"fkAgente": 494935, "numero": "+************", "tipo": "2", "preferito": true, "idContattoAgente": 719497, "pubblico": true, "fNumero": "+39 391 3559 147", "prefisso": "+39", "fNumeroSenzaPrefisso": "3913559147", "shortCode": "it"}], "biografia": [{"fkAgente": 494935, "lingua": "en", "testo": "This is my biography", "idBiografiaAgente": 1206127}]}, "ruolo": {"idRuoloAgente": 5, "ruolo": "<PERSON><PERSON>"}, "uuid": "d15a62e9-fdd9-527f-b8e0-0094e995a920", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/494935/7782/80.jpg"}, {"idAgente": 494957, "cognome": "Di <PERSON>", "nome": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": false, "status": 1, "ruolo": {"idRuoloAgente": 1, "ruolo": "Amministratore"}, "uuid": "177f7de2-90e1-5253-ba30-c6fe8025838b", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/494957/7168/80.jpg"}, {"idAgente": 494969, "cognome": "Oreficetest", "nome": "Francescotest", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": false, "status": 1, "ruolo": {"idRuoloAgente": 3, "ruolo": "Segreteria"}, "uuid": "cc7bf2a4-f9eb-5836-ba0b-cacba31d190c", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/494969/7079/80.jpg"}, {"idAgente": 494971, "cognome": "Ore", "nome": "<PERSON>a", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": true, "status": 2, "ruolo": {"idRuoloAgente": 3, "ruolo": "Segreteria"}, "uuid": "9d96b891-25d4-5169-b112-e21d140af469", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/494971/7079/80.jpg"}, {"idAgente": 495039, "cognome": "<PERSON><PERSON><PERSON>", "nome": "<PERSON><PERSON>", "email": "<EMAIL>", "chat": false, "titolare": false, "emailVerificata": false, "status": 1, "ruolo": {"idRuoloAgente": 1, "ruolo": "Amministratore"}, "uuid": "33440a8f-ad83-5f32-8c7e-038ba934f3b8", "fkAgenzia": 93434, "mostraInPubblicita": true, "urlImmagineAgenteThumb": "http://media.getrix.daniele.irsuti.dev.loc/agenti/495039/6873/80.jpg"}], "usersFormData": {"ruolo": {"1": "db_agent_role.id_1", "3": "db_agent_role.id_3", "5": "db_agent_role.id_5", "9": "db_agent_role.id_9"}, "status": {"2": {"static": false, "text": "Attivo", "transKey": "label.active", "dropdownClass": "status-active btn-colortext-success", "labelClass": "gx-tag--positiveFilled"}, "3": {"static": false, "text": "Disattivo", "transKey": "label.not_active", "dropdownClass": "status-not-active btn-colortext-error", "labelClass": "gx-tag--negativeFilled"}, "1": {"static": true, "text": "In attesa di attivazione", "transKey": "label.wait_activation", "buttonClass": "status-verifying", "labelClass": "gx-tag--warningFilled"}, "0": {"static": true, "text": "Attiva utente", "transKey": "label.activate_user", "buttonClass": "status-to-verify", "labelClass": "gx-tag--primaryFilled"}}, "contatto": {"1": "<PERSON><PERSON>", "2": "Cellulare", "3": "Fax", "4": "Altro recapito"}}, "usersCounts": {"total": 20, "active": 11}, "isAdmin": false, "isSuperAdmin": true}