import { BaseMenuConfig, MenuConfig, Pathname } from '../../types';

export const hasMenuItem = (value: MenuConfig) => {
  return 'item' in value && typeof value.item !== 'string';
};

const safeMap = (props: [string, MenuConfig]): props is [string, BaseMenuConfig] => {
  const [, value] = props;
  const isSafe = hasMenuItem(value);

  return isSafe;
};

/**
 *  A function that maps the menu configuration.
 * @example
  mapMenuConfig(x, (props) => {
    const [, value] = props

    return value.item?.group
})
 */
export const mapMenuConfig = <T>(
  menuConfig: MenuConfig,
  callback: (value: [Pathname, BaseMenuConfig]) => T,
) => {
  return Object.entries(menuConfig).filter(safeMap).map(callback);
};
