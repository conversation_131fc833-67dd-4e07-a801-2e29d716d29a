<?php

declare(strict_types=1);

namespace App\DataProvider\Messaging\ApiGtx;

use App\Builder\Model\ScheduledVisit\ScheduledVisitBuilder;
use App\Constants\Base\ImmovisitaCostants;
use App\Constants\Base\MessagingConstants;
use App\Constants\Base\ScheduledVisitCostants;
use App\Constants\PerformanceProfiler;
use App\DataMapper\ApiResponseToDto\Messaging\ThreadDataMapper;
use App\DataMapper\ApiResponseToDto\Messaging\MessageDataMapper;
use App\DataMapper\EntityToResponse\Messaging\Thread\ParametricStatsDataMapper;
use App\DataProvider\AbstractApiGtxDataProvider;
use App\DataProvider\Messaging\MessagingInterface;
use App\DTO\Messaging\CreateMessage;
use App\DTO\Messaging\MessagePaginationRequest;
use App\DTO\Messaging\SetReadMultipleThreads;
use App\DTO\Messaging\ThreadPaginationRequest;
use App\DTO\Messaging\ThreadsFilters;
use App\DTO\Messaging\ThreadsParametricStatsFilters;
use App\Exception\ApiException;
use App\Exception\ImmovisitaException;
use App\Exception\SessionException;
use App\Formatter\Messaging\ListThreadFormatter;
use App\Formatter\Messaging\ThreadFormatter;
use App\Helper\ApiWsseHeaderGenerator;
use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;
use App\Model\Messaging\V2\PlannedVisitRequest;
use App\Model\Messaging\V2\Thread;
use App\Model\ScheduledVisit\ScheduledVisit;
use App\Performance\ProfilerInterface;
use App\Utils\SessionUtils;
use Carbon\Carbon;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\HttpFoundation\Response;

class Messaging extends AbstractApiGtxDataProvider implements MessagingInterface
{
    const HTTP_GET = 'GET';
    const HTTP_POST = 'POST';
    const HTTP_DELETE = 'DELETE';
    const HTTP_PATCH = 'PATCH';

    const RESOURCE_AGENCIES = 'agencies';
    const RESOURCE_AGENTS = 'agents';
    const RESOURCE_THREADS = 'threads';
    const RESOURCE_MESSAGES = 'messages';
    const RESOURCE_ATTACHMENTS = 'attachments';

    const LEADS_SOCIALS_PREFIX = 'leads/socials';
    const COUNT = 'count';
    const IN_BOX = 'inbox';
    const ADDITIONAL_INFO = 'additional-info';
    const STATS = 'stats';
    const PARAMETRIC_STATS = 'parametric-stats';
    const PLANNED_VISIT = 'planned-visit';

    const STATUS_FAVOURITE = 'favourite';
    const STATUS_NOT_FAVOURITE = 'not-favourite';
    const STATUS_READ = 'read';
    const STATUS_READ_ALL = 'read-all';
    const STATUS_ARCHIVE = 'archive';
    const STATUS_ACTIVATE = 'activate';

    private float $countersTimeout;
    private string $endpoint;
    private ThreadDataMapper $threadDataMapper;
    private MessageDataMapper $messageDataMapper;
    private ParametricStatsDataMapper $parametricStatsDataMapper;
    private ListThreadFormatter $listThreadFormatter;
    private ThreadFormatter $threadFormatter;

    public function __construct(
        ApiWsseHeaderGenerator $apiWsseHeaderGenerator,
        HttpClientHelper $client,
        ListThreadFormatter $listThreadFormatter,
        ThreadDataMapper $threadDataMapper,
        MessageDataMapper $messageDataMapper,
        ParametersBag $parametersBag,
        ParametricStatsDataMapper $parametricStatsDataMapper,
        ProfilerInterface $performanceProfiler,
        SessionUtils $sessionUtils,
        ThreadFormatter $threadFormatter
    ) {
        parent::__construct($apiWsseHeaderGenerator, $client, $parametersBag, $performanceProfiler, $sessionUtils);

        $this->endpoint = $this->getBaseUri();
        $this->listThreadFormatter = $listThreadFormatter;
        $this->threadDataMapper = $threadDataMapper;
        $this->messageDataMapper = $messageDataMapper;
        $this->parametricStatsDataMapper = $parametricStatsDataMapper;
        $this->threadFormatter = $threadFormatter;
        $this->countersTimeout = (float) $parametersBag->get('getrix.api_counters_timeout');
    }

    /**
     * @throws ApiException
     * @throws SessionException
     */
    public function getThread(int $agencyId, int $agentId, string $threadId, MessagePaginationRequest $pagination): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $getThreadUrl = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $threadId
        );

        $getThreadResponse = $this->client->execRequestWithResponse(
            self::HTTP_GET,
            $getThreadUrl,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $getThreadResponse->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $getThreadResponse->getStatusCode());
        }

        $getThreadDecoded = \json_decode($getThreadResponse->getBody()->getContents(), true);

        $getThreadDecoded['data']['agencyId'] = $this->sessionUtils->getAgencyId();
        $getThreadDecoded['data']['agentId'] = $this->sessionUtils->getAgentId();

        $getThreadDecoded['data']['participants'] = [
            $getThreadDecoded['data']['agency'],
            $getThreadDecoded['data']['customer'],
        ];

        foreach ($getThreadDecoded['data']['agents'] as $agent) {
            $getThreadDecoded['data']['participants'][] = $agent;
        }

        /** @var Thread $thread */
        $thread = $this->threadDataMapper->map($getThreadDecoded['data']);

        $getMessagesUrl = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/%s?%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $threadId,
            self::RESOURCE_MESSAGES,
            \http_build_query($pagination->toArray())
        );

        $getMessagesResponse = $this->client->execRequestWithResponse(
            self::HTTP_GET,
            $getMessagesUrl,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $getMessagesResponse->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $getMessagesResponse->getStatusCode());
        }

        $getMessagesDecoded = \json_decode($getMessagesResponse->getBody()->getContents(), true);

        $messages = \array_map(function (array $message) {
            return $this->messageDataMapper->map($message);
        }, $getMessagesDecoded['data']);

        $thread->setMessages($messages);

        $response['thread'] = $this->threadFormatter->format($thread);

        $this->performanceProfiler->stop(__METHOD__);

        return $response;
    }

    /**
     * @throws ApiException
     * @throws SessionException
     */
    public function listThreads(int $agencyId, int $agentId, ThreadsFilters $filters, ThreadPaginationRequest $pagination): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $parameters = \array_merge(
            $pagination->toArray(),
            $filters->toArray()
        );

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s?%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $this->sessionUtils->getAgencyId(),
            self::RESOURCE_AGENTS,
            $this->sessionUtils->getAgentId(),
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            \http_build_query($parameters)
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_GET,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (null === $response || Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $data = \json_decode($response->getBody()->getContents(), true);

        $threads = \array_map(function ($thread) {
            $thread['participants'] = [
                $thread['agency'],
                $thread['customer'],
            ];

            foreach ($thread['agents'] as $agent) {
                $thread['participants'][] = $agent;
            }

            return $thread;
        }, $data['data']['threads']);

        $result['filters'] = $filters->toArrayResult();

        $result['pagination'] = [
            'moreResults' => ThreadPaginationRequest::LIMIT_DEFAULT === \count($data['data']['threads']) || $response->getHeaderLine('X-Invalid-Count') > 0,
            'results' => \count($data['data']['threads']),
            'page' => $pagination->getPage(),
        ];

        $result['threads'] = \array_map(function (array $thread) {
            $thread['agencyId'] = $this->sessionUtils->getAgencyId();
            $thread['agentId'] = $this->sessionUtils->getAgentId();

            return $this->listThreadFormatter->format(
                $this->threadDataMapper->map($thread)
            );
        }, $threads);

        $this->performanceProfiler->stop(__METHOD__);

        return $result;
    }

    public function countThreads(int $agencyId, int $agentId, ThreadsFilters $filters): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s?%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            self::COUNT,
            \http_build_query($filters->toArray())
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_GET,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);
        $count = $decoded['data'];

        $this->performanceProfiler->stop(__METHOD__);

        return ['data' => $count];
    }

    public function getInBoxThreads(int $agencyId, int $agentId): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            self::IN_BOX
        );

        $this->client->setTimeout($this->countersTimeout);
        $response = $this->client->execRequestWithResponse(
            self::HTTP_GET,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);
        $inbox = $decoded['data'];

        $this->performanceProfiler->stop(__METHOD__);

        return ['inbox' => $inbox];
    }

    /**
     * @throws ApiException
     */
    public function deleteThread(int $agencyId, int $agentId, string $threadId): string
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $threadId
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_DELETE,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $this->performanceProfiler->stop(__METHOD__);

        return $response->getBody()->getContents();
    }

    public function getThreadsStats(int $agencyId, int $agentId): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            self::STATS
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_GET,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return $decoded['data'];
    }

    public function getThreadParametricStats(int $agencyId, int $agentId, ThreadsParametricStatsFilters $filters): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s?%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            self::PARAMETRIC_STATS,
            \http_build_query($filters->toArray())
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_GET,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return $this->parametricStatsDataMapper->map($decoded['data']);
    }

    public function setAdditionalInfo(int $agencyId, int $agentId, string $threadId): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $threadId,
            self::ADDITIONAL_INFO
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_PATCH,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $answeredByPhone = $decoded['data']['answeredByPhone'];

        $this->performanceProfiler->stop(__METHOD__);

        return ['answeredByPhone' => Carbon::parse($answeredByPhone)];
    }

    public function archiveThread(int $agencyId, int $agentId, string $threadId): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $threadId,
            self::STATUS_ARCHIVE
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_PATCH,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return $decoded;
    }

    public function activateThread(int $agencyId, int $agentId, string $threadId): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $threadId,
            self::STATUS_ACTIVATE
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_PATCH,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return $decoded;
    }

    public function setFavouriteThread(int $agencyId, int $agentId, string $threadId)
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $threadId,
            self::STATUS_FAVOURITE
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_PATCH,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $this->performanceProfiler->stop(__METHOD__);
    }

    public function setNotFavouriteThread(int $agencyId, int $agentId, string $threadId)
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $threadId,
            self::STATUS_NOT_FAVOURITE
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_PATCH,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $this->performanceProfiler->stop(__METHOD__);
    }

    public function setReadThread(int $agencyId, int $agentId, string $threadId): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $threadId,
            self::STATUS_READ
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_PATCH,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return $decoded;
    }

    public function setReadMultipleThreads(int $agencyId, int $agentId, SetReadMultipleThreads $setReadMultipleThreadsDto): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            self::STATUS_READ
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_PATCH,
            $url,
            [
                SetReadMultipleThreads::REQUEST_PARAMETER_KEY => $setReadMultipleThreadsDto->getThreadIds(),
            ],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return $decoded;
    }

    public function setReadAllThreads(int $agencyId, int $agentId): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            self::STATUS_READ_ALL
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_PATCH,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return $decoded;
    }

    public function createMessage(int $agencyId, int $agentId, string $threadId, CreateMessage $createMessageDto): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $threadId,
            self::RESOURCE_MESSAGES
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_POST,
            $url,
            $createMessageDto->getPayload(),
            $this->getDefaultMultipartHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $messageId = $decoded['data']['externalId'];
        $textPlain = $decoded['data']['text'];

        $generateDownloadAttachmentUrl = function ($attachmentId) use ($agencyId, $agentId, $threadId, $messageId) {
            return \sprintf(
                '%s/%s/%d/%s/%d/%s/%s/%s/%s/%s/%s/%s',
                $this->endpoint,
                self::RESOURCE_AGENCIES,
                $agencyId,
                self::RESOURCE_AGENTS,
                $agentId,
                self::LEADS_SOCIALS_PREFIX,
                self::RESOURCE_THREADS,
                $threadId,
                self::RESOURCE_MESSAGES,
                $messageId,
                self::RESOURCE_ATTACHMENTS,
                $attachmentId
            );
        };

        $attachments = \array_map(function (array $attachment) use ($generateDownloadAttachmentUrl) {
            $attachment['id'] = $attachment['externalId'];
            $attachment['url'] = $generateDownloadAttachmentUrl($attachment['id']);

            return $attachment;
        }, $decoded['data']['attachments']);

        $decoded['data']['id'] = $messageId;
        $decoded['data']['textPlain'] = $textPlain;
        $decoded['data']['status'] = 'unread';
        $decoded['data']['attachments'] = $attachments;

        $this->performanceProfiler->stop(__METHOD__);

        return $decoded;
    }

    public function downloadAttachment(int $agencyId, int $agentId, string $messageId, string $attachmentId): ResponseInterface
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            self::RESOURCE_MESSAGES,
            $messageId,
            self::RESOURCE_ATTACHMENTS,
            $attachmentId
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_GET,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $this->performanceProfiler->stop(__METHOD__);

        return $response;
    }

    public function getMessage(int $agencyId, int $agentId, string $messageId, string $threadId): array
    {
        // TODO: Implement getMessage() method.
    }

    public function listMessages(int $agencyId, int $agentId, string $threadId, ThreadPaginationRequest $pagination): array
    {
        // TODO: Implement listMessages() method.
    }

    public function createPlannedVisit(int $agencyId, int $agentId, string $visitType, PlannedVisitRequest $plannedVisitRequest): ScheduledVisit
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $plannedVisitRequest->setVisitType(\array_search($visitType, ScheduledVisitCostants::VISIT_TYPES_ID_SLUG_MAPPING, true));

        if (true === $plannedVisitRequest->hasXThreadId()) {
            $plannedVisitRequest->setAgentSenderId($agentId);
        }

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            self::PLANNED_VISIT,
            $visitType
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_POST,
            $url,
            $plannedVisitRequest,
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_CREATED !== $response->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $scheduledVisit = ScheduledVisitBuilder::newBuilder()
            ->fromApiResponse($decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $scheduledVisit;
    }

    public function getDetailedThreadStatsController(int $agencyId, int $agentId, string $threadId): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%s/%s',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $threadId,
            self::STATS
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_GET,
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = \json_decode($response->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return $decoded['data'];
    }

    public function getThreadIdBySocialsThreadId(int $agencyId, int $agentId, string $socialsThreadId): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $getThreadUrl = sprintf(
            '%s/%s/%d/%s/%d/%s/%s/%d',
            $this->endpoint,
            self::RESOURCE_AGENCIES,
            $agencyId,
            self::RESOURCE_AGENTS,
            $agentId,
            self::LEADS_SOCIALS_PREFIX,
            self::RESOURCE_THREADS,
            $socialsThreadId
        );

        $response = $this->client->execRequestWithResponse(
            self::HTTP_GET,
            $getThreadUrl,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new ApiException(MessagingConstants::EXCEPTION_MESSAGES['generic'], $response->getStatusCode());
        }

        $decoded = json_decode($response->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return $decoded['data'];
    }
}
