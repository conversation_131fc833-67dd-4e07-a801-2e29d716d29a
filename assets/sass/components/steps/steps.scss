// ==========================================================================
// Steps - Components
// ==========================================================================
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$max-width-element: 22.4rem !default;
$step-graph-size: ($gx-unit-size * 4) !default;
$bar-thickness: ($gx-unit-size / 4) !default;
$customer-care-width: 10.6rem;

$item-colors: (
  default: (
    color: color(content-low),
    background-color: color(background-alt),
    background-color-bar: color(border-main),
  ),
  completed: (
    color: color(content-action),
    background-color: color(background-action),
    background-color-bar: color(border-selected),
  ),
  active: (
    color: color(content-selected-high),
    background-color: color(background-selected-high),
    background-color-bar: color(border-selected),
  ),
);

$steps-bar-root: 'gx-stepsBar';

.gx-steps {
  display: flex;
  flex-direction: column;

  &__footer {
    position: fixed;
    bottom: 0;
    @include z-index(base, 1);
    background: color(background-main);
    width: 100%;
    margin: 0 (-(space(md)));
    border-top: 0.1rem solid color(border-main);

    &Container {
      display: flex;
      justify-content: space-between;
      width: 100%;
      max-width: 128rem;
      padding: space(sm) $customer-care-width space(sm) space(sm);
    }

    .gx-button:only-child {
      margin-left: auto;
    }
  }

  &Content {
    width: 100%;
    max-width: 96rem;

    &__section {
      & + & {
        padding-top: space(xl);
        margin-top: space(sm);
        border-top: 0.1rem solid color(border-main);
      }

      .gx-loader-overlay .gx-loader-overlay--inline {
        @include z-index(0);
      }
    }

    .gx-title {
      margin-bottom: space(lg);
    }
  }

  &Bar {
    display: flex;
    justify-content: center;
    margin-bottom: space(xl);

    &__label {
      @include typography(body-small);
    }

    &__item {
      position: relative;
      display: flex;
      align-items: center;

      a {
        display: flex;
        align-items: center;

        &:hover,
        &:focus {
          text-decoration: none;
        }
      }

      &:not(:first-child) {
        flex-grow: 1;
        max-width: ($gx-unit-size * 9);

        &::before {
          content: '';
        }
      }

      &::before {
        display: block;
        flex-grow: 1;
        min-width: $gx-unit-size;
        max-width: ($gx-unit-size * 4);
        height: $bar-thickness;
        margin: 0 space(xs);
      }

      &:first-child {
        counter-reset: counter-step;
      }

      @each $item-typology, $colors in $item-colors {
        @if ($item-typology == 'default') {
          a {
            color: map-get($colors, 'color');
          }

          &::before {
            background-color: map-get($colors, 'background-color-bar');
          }

          .#{$steps-bar-root}__graph {
            background-color: map-get($colors, 'background-color');
            color: map-get($colors, 'color');
          }
        } @else {
          &--#{$item-typology} {
            a {
              color: color(content-high);

              .#{$steps-bar-root}__graph {
                color: map-get($colors, 'color');
                background-color: map-get($colors, 'background-color');

                @if ($item-typology == 'completed') {
                  border: 0.2rem solid map-get($colors, 'color');

                  &::before {
                    content: '';
                  }

                  .gx-icon {
                    display: block;
                    fill: currentColor;
                  }
                }
              }
            }

            &::before {
              background-color: map-get($colors, 'background-color-bar');
            }
          }
        }
      }
    }

    &__graph {
      display: flex;
      flex-shrink: 0;
      justify-content: center;
      align-items: center;
      width: $step-graph-size;
      height: $step-graph-size;
      border-radius: radius(rounded);

      &::before {
        counter-increment: counter-step;
        content: counter(counter-step);
      }

      .gx-icon {
        display: none;
      }
    }
  }
  @include media('screen', '>=#{breakpoint(sm)}') {
    &Windows {
      ul {
        display: flex;
        flex-wrap: wrap;
        margin: 0 (-(space(md)));
      }

      li {
        width: calc(#{percentage(1/3)} - #{$gx-unit-size * 4});
        margin-left: space(md);
        margin-right: space(md);
      }
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    flex-direction: row;

    &__footer {
      margin: 0;
      left: $gx-new-menu-width;
      width: calc(100% - #{$gx-new-menu-width});

      &Container {
        padding: space(md) $customer-care-width space(md) space(xl);

        @include media('screen', '>=#{breakpoint(xl)}') {
          padding-right: space(xl);
        }

        .has-menu-fixed & {
          padding-left: calc(#{$gx-side-menu-width} + 5.2rem);
        }
      }
    }

    &Bar {
      width: 100%;
      max-width: $max-width-element;
      margin-right: space(xl);
      flex-direction: column;
      justify-content: flex-start;
      margin-bottom: 0;

      &__item {
        align-items: flex-start;
        align-items: center;

        &:not(:first-child) {
          max-width: none;
          padding-top: space(xl);
          flex-grow: 0;

          &::before {
            position: absolute;
            top: space(xs);
            left: ($step-graph-size / 2);
            transform: translateX(-50%);
            width: $bar-thickness;
            max-width: none;
            min-width: auto;
            height: ($gx-unit-size * 3);
            margin: 0;
          }
        }
      }

      &__graph {
        margin-right: space(sm);
      }
    }
  }
}
