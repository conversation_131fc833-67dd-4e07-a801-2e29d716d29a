@use '~@nugget/carousel/variables' as carousel;

/// @access public
/// @param { string } $arrow-size [$arrow-size] - the carousel arrow size
/// @param { string } $spaces [$nd-carousel-spaces] - the carousel spaces
/// @param { string } $item-spaces - the carousel item spaces [0]
/// @output change the style of the carousel to have arrows outside the content
@mixin in-carousel-outside-arrow(
    $arrow-size: carousel.$arrow-size,
    $spaces: carousel.$space,
    $item-spaces: 0
) {
    $arrow-area: $arrow-size + ($spaces * 2);
    $outside-spacing: (($arrow-area + $spaces - $item-spaces) / 2);

    @media screen and (hover: hover),
        all and (-ms-high-contrast: none),
        (-ms-high-contrast: active) {
        padding-right: $outside-spacing;
        padding-left: $outside-spacing;

        [class*='nd-carousel__content'] {
            padding-right: 0;
            padding-left: 0;
        }
    }

    @if ($arrow-size != carousel.$arrow-size) {
        [class*='nd-carousel__arrowIcon'] {
            width: $arrow-size;
            height: $arrow-size;
        }
    }
}

/// @access public
/// @param { string } $margins [$space] - the carousel item spaces
/// @output sets carousel item spacings and applies negative margins to the container
@mixin in-carousel-item-spaces(
    $item-width: carousel.$item-width,
    $spaces: carousel.$space
) {
    &[class*='isStarting'] {
        padding-left: ($spaces / 2);
    }

    [class*='nd-carousel__item'] {
        // width: calc(#{$item-width + $spaces});
        // must necessarily be padding to be included in the calculation of single item scroll by the carousel
        padding-right: ($spaces / 2);
        padding-left: ($spaces / 2);
    }
}
