parameters:
  thrifter_rpc_services: 'config/%env(COUNTRY_TAG)%/sdk/rpc_services_dev.yml'
  log_stream_format: "%%datetime%% - %%channel%%.%%level_name%% - %%extra.appId%% - %%extra.token%% - %%extra.sessionId%% - %%extra.requestId%% - %%extra.serviceCanonicalName%% - %%extra.methodName%% - %%extra.methodArguments%% - %%message%% - %%extra%%\n"
  app.logs_dir: '%kernel.logs_dir%'
  maps.consumerKey: 'immobiliarepro'
  immobiliare.responsabile_avatar_sapi_url: '%env(GETRIX_MEDIA_URL)%/image'

  app.agency.evaluation_zone.only_getrix: false

  sapi.config:
    dev:
      services.getrix.getrix.annunci.immagini:
        baseUrl: "http://sapi.ns.imm.re:9977/"
        rootPath: "/storage/immobiliare-dev/images/"
        auth: "im-dev"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.getrix.annunci.planimetrie:
        baseUrl: "http://sapi.ns.imm.re:9977/"
        rootPath: "/storage/immobiliare-dev/plans/"
        auth: "im-dev"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.agenzie:
        baseUrl: "http://sapi.ns.imm.re:9977/"
        rootPath: "/storage/immobiliare-dev/images"
        auth: "im-dev"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.getrix.agenti:
        baseUrl: "http://sapi.ns.imm.re:9977/"
        rootPath: "/storage/immobiliare-dev/profiles/agents/images"
        auth: "im-dev"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
      services.immobiliare.media:
        baseUrl: "http://sapi.ns.imm.re:9977/"
        rootPath: "/storage/immobiliare-dev/documents"
        auth: "im-dev"
        timeout: 20000
        connectTimeout: 3000
        verbose: false
        retry: 3
        persistent: false
