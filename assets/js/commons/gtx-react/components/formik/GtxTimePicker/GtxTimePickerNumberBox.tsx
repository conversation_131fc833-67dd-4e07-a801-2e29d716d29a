import React, { <PERSON> } from 'react';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';

interface IGtxTimePickerNumberBoxProps {
    defaultValue: number;
    onChange: Function;
}

export const GtxTimePickerNumberBox: FC<IGtxTimePickerNumberBoxProps> = ({
    defaultValue,
    onChange,
}) => {
    return (
        <div className="gx-time_picker__number-box">
            <Button variant="ghost" iconOnly onClick={onChange('up')}>
                <Icon name="arrow-top" />
            </Button>
            <div className="gx-time_picker__digit">
                {defaultValue.toString().padStart(2, '0')}
            </div>
            <Button variant="ghost" iconOnly onClick={onChange('down')}>
                <Icon name="arrow-down" />
            </Button>
        </div>
    );
};
