import { CustomMenuComponent, MenuConfig } from '../../types';

/* Menu typeguards */
export const isMenuItem = (menu: MenuConfig | undefined): menu is MenuConfig => {
  return menu !== undefined && 'item' in menu;
};

export const isCustomMenuComponent = (
  item: MenuConfig | CustomMenuComponent | undefined,
): item is CustomMenuComponent => {
  return (
    item !== undefined && 'customMenuComponent' in item && item.customMenuComponent !== undefined
  );
};

export const isMenuItemDefinedOrCustomMenuComponent = (
  item: MenuConfig | CustomMenuComponent | undefined,
): item is MenuConfig | CustomMenuComponent => {
  return (
    (isMenuItem(item) && Object.values(item).every((f) => f !== undefined)) ||
    isCustomMenuComponent(item)
  );
};

export const isSubmenuHidden = (subMenu: MenuConfig) => {
  return Object.values(subMenu).every((value: MenuConfig) => {
    return value?.item?.hidden === true;
  });
};
