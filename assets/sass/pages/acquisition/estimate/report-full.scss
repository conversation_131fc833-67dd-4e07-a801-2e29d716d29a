@use '../../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$report-full-doc-width: 90rem;
$report-background-section: #eee;

$colors-report: (
  'blue-lagoon': #037e8c,
  'calypso': #0074c1,
  'danube': #548fcc,
  'green': #2cbb61,
  'downy': #54cccb,
  'mandy': #ec575c,
  'orange': #f88450,
  'shakespeare': #449ecf,
  'yellow': #fcca6c,
  'violet': #7560b4,
);

// classes for charts' colors
@each $name, $color in $colors-report {
  .report-#{$name} {
    background-color: $color !important;
  }
}

@each $name, $color in $colors-report {
  .stroke-#{$name} {
    stroke: $color;
  }
}

@each $name, $color in $colors-report {
  .line-#{$name} {
    color: $color !important;
  }
}

@mixin border-chart {
  box-shadow:
    0.1rem 0.1rem 0 inset rgba(#000000, 0.1),
    -0.1rem -0.1rem 0 inset rgba(#000000, 0.1);
}

.report-full-doc {
  max-width: $report-full-doc-width;

  &__head {
    display: flex;
  }
}

.report-full-page {
  @include typography(body-small);
  background-color: color(background-main);
  box-shadow: elevation(raised);

  @include media('screen', '<#{breakpoint(sm)}') {
    margin: 0 -#{space(sm)};
  }

  & + & {
    margin-top: space(md);

    @include media('screen', '>=#{breakpoint(sm)}') {
      margin-top: space(xl);
    }
  }

  &__show {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: space(md);
    border-bottom: 0.1rem solid color(border-main);

    &Check {
      label {
        display: inline-flex;
        align-items: center;
      }

      .styled-check + span {
        margin-left: space(xs);
      }
    }

    @include media('screen', '>=#{breakpoint(sm)}') {
      padding-left: space(xl);
      padding-right: space(xl);
    }
  }

  &__content {
    position: relative;
  }

  &__section {
    padding: space(lg) space(md);

    &Title,
    &Text {
      margin-bottom: space(md);
    }

    &--fullWidth {
      background-color: $report-background-section;
    }

    @include media('screen', '>=#{breakpoint(sm)}') {
      padding: space(lg);

      &Title {
        margin-bottom: space(lg);
      }
    }

    .estimate-box {
      margin-top: space(2xl);
    }
  }

  // Page 1 - Cover
  &__cover {
    position: relative;
    width: 100%;
    padding-bottom: 140%;
    overflow: hidden;

    img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      min-width: 100%;
      min-height: 100%;
    }

    &--vertical {
      img {
        max-width: 100%;
      }
    }

    &--horizontal {
      img {
        height: 100%;
      }
    }

    &Box-title {
      position: absolute;
      left: 50%;
      width: 75%;
      padding: space(xl) space(md);
      background-color: color(background-main);
      text-align: center;
      line-height: 1.2;

      &::before {
        content: '';
        position: absolute;
        top: (-(space(sm)));
        left: (-(space(sm)));
        right: (-(space(sm)));
        bottom: (-(space(sm)));
        @include z-index(-1);
        border: 0.2rem solid color(border-reversed);
      }

      &--center {
        top: 50%;
        transform: translate(-50%, -50%);
      }

      &--top {
        top: 10%;
        transform: translateX(-50%);
      }

      &--bottom {
        bottom: 10%;
        transform: translateX(-50%);
      }
    }

    &Title {
      font-size: 4.6vw;
      color: color(content-high);
    }

    &Address {
      font-size: 2.8vw;
      margin-top: 2vw;
    }

    &Last-update {
      position: relative;
      font-size: 2vw;
      border-top: 0.1rem solid #000000;
      margin-top: 2vw;

      span {
        position: absolute;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: color(background-main);
        display: block;
        width: 42%;
      }
    }

    @include media('screen', '>=#{breakpoint(sm)}') {
      &Box-title {
        padding: space(2xl) space(xl) space(xl);
      }
    }

    @include media('screen', '>=#{breakpoint(md)}') {
      &Box-title {
        padding: space(2xl);
      }

      &Title {
        font-size: space(2xl);
      }

      &Address {
        font-size: space(lg);
        margin-top: space(lg);
      }

      &Last-update {
        font-size: space(md);
        margin-top: space(xl);
      }
    }
  }

  // Page 2 - Property data
  &--propertyData {
    @include media('>=#{breakpoint(sm)}') {
      .estimate-property-info__item {
        width: percentage(1/3);
      }
    }
  }
}

// Pie chart
.report-pie-charts {
  @include media('>=#{breakpoint(sm)}') {
    display: flex;
    justify-content: space-between;
  }
}

$pie-chart: '.report-pie-chart';

#{$pie-chart} {
  display: flex;

  & + & {
    margin-top: space(xl);
  }

  &__wrap {
    position: relative;
    flex-shrink: 0;
    margin-right: space(xl);

    svg {
      transform: rotate(-90deg);
    }
  }

  &__content {
    &Title {
      width: 100%;
      margin-bottom: space(sm);
    }

    &Row {
      display: flex;
      align-items: center;
      white-space: nowrap;

      & + & {
        margin-top: space(sm);
      }

      span {
        display: inline-block;
        width: space(md);
        height: space(md);
        margin-right: space(sm);
        border-radius: radius(rounded);
        background-color: color(content-action);
        @include border-chart();
      }
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    width: calc(50% - #{space(md)});

    & + & {
      margin-top: 0;
    }

    &__two-columns {
      column-count: 2;
    }
  }

  &--nationality {
    width: auto;

    @include media('<#{breakpoint(sm)}') {
      flex-direction: column;

      #{$pie-chart} {
        &__wrap {
          margin: 0 auto;
        }

        &__content {
          display: flex;
          margin-bottom: space(2xl);

          &Row {
            & + #{$pie-chart}__contentRow {
              margin-top: 0;
              margin-left: space(md);
            }
          }
        }
      }
    }

    #{$pie-chart} {
      &__wrap {
        flex-wrap: wrap;

        &Legend {
          position: absolute;
          display: flex;
          justify-content: center;
          align-items: center;
          width: ($gx-unit-size * 5);
          height: ($gx-unit-size * 5);
          border-radius: radius(rounded);
          border: 0.2rem solid color(border-reversed);
          color: color(content-accent);
          font-weight: 600;

          &--italians {
            right: 0;
            bottom: 0;
          }

          &--foreigners {
            top: 0;
            left: 0;
          }
        }
      }
    }

    @include media('>=#{breakpoint(sm)}') {
      #{$pie-chart} {
        &__content {
          justify-content: center;
          display: flex;
          flex-direction: column;
          margin-right: space(2xl);
        }
      }
    }
  }

  &--family {
    @include media('<#{breakpoint(sm)}') {
      flex-direction: column;

      #{$pie-chart} {
        &__wrap {
          margin-bottom: space(lg);
        }
      }
    }

    @include media('>=#{breakpoint(sm)}') {
      align-items: flex-start;
      width: 48rem;
      margin-left: space(2xl);

      #{$pie-chart} {
        &__content {
          display: flex;
          flex-wrap: wrap;

          &Row {
            margin-bottom: space(sm);

            & + #{$pie-chart}__contentRow {
              margin-top: 0;
            }
          }
        }
      }
    }
  }
}

// Surface analysys block (page 2)
.surface-analysis-block {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -#{space(xs)};

  &__item {
    width: calc(50% - #{space(sm)});
    padding: space(sm) space(md);
    margin: 0 space(xs) space(sm);
    background-color: color(background-main);

    &Value {
      display: block;
    }

    &--total {
      display: flex;
      justify-content: space-between;
      width: 100%;
    }
  }

  &__totWrap {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    &__item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: calc(#{percentage(1/3)} - #{space(sm)});

      &--total {
        width: calc(50% - #{space(sm)});
      }
    }
  }
}

// Reference area box (page 2)
$reference-area-box: 'reference-area-box';

.#{$reference-area-box} {
  &__list {
    &Title {
      display: flex;
      align-items: center;
      margin-bottom: space(sm);

      svg {
        @include icon-size(md);
        margin-right: space(sm);
      }
    }

    ul {
      padding: 0;
      margin: 0 0 space(md) space(lg);
      list-style-position: inside;
    }
  }

  &__map {
    img {
      max-width: 100%;
    }

    .gx-loader-overlay {
      padding-top: 50%;
    }
  }

  &--horizontal {
    display: block;

    .#{$reference-area-box} {
      &__map {
        width: 100%;
        margin-bottom: space(lg);
      }

      &__lists {
        display: flex;

        > div {
          width: 50%;
        }
      }
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    display: flex;
    justify-content: space-between;

    &__content {
      width: calc(50% - #{$gx-unit-size * 16});
    }

    &__map {
      width: 50%;
    }

    &--horizontal {
      display: block;

      .#{$reference-area-box}__map {
        margin-bottom: space(md);
      }
    }
  }
}

// Market analysis (page 3)
.market-analysis {
  display: flex;
  flex-wrap: wrap;
  border-width: 0 0.1rem 0.1rem 0;
  border-style: solid;
  background-color: color(background-main);
  border-color: color(border-main);

  &__item {
    display: flex;
    align-items: center;
    width: 100%;
    padding: space(md);
    border-top: 0.1rem solid color(border-main);
    border-left: 0.1rem solid color(border-main);

    &Icon {
      margin-right: space(md);
      font-size: space(xl);
      color: color(content-action);
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    &__item {
      flex-direction: column;
      width: 25%;
      padding: space(md) space(lg);
      text-align: center;

      &Icon {
        font-size: space(2xl);
        margin-right: 0;
        margin-bottom: space(sm);
      }

      div:first-child {
        height: space(xl);
      }
    }
  }
}

// Market stats (page 3)
.market-stats {
  margin-bottom: space(xl);

  &-list {
    & + & {
      margin-top: space(xl);
    }

    &__title {
      margin-bottom: space(sm);
    }

    &:first-child {
      .market-stats-list__itemLabel {
        width: 5rem;
      }
    }

    &__item {
      display: flex;
      align-items: center;

      & + & {
        margin-top: space(xs);
      }

      &Label {
        width: space(4xl);
        margin-right: space(sm);
        text-align: right;
      }

      &Data {
        display: flex;
        align-items: center;
        flex-grow: 1;
        padding: space(xs);
        background-color: color(background-main);

        div {
          height: space(lg);
          border-top-right-radius: radius(sm);
          border-bottom-right-radius: radius(sm);
          background-color: #f88450;
          @include border-chart();
        }

        span {
          margin-left: space(sm);
          font-weight: 600;
          color: color(content-action);
        }
      }
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    display: flex;
    justify-content: space-between;

    &-list {
      width: calc(50% - #{space(md)});

      & + & {
        margin-top: 0;
      }
    }
  }
}

// Attractiveness (page 3)
.attractiveness {
  display: flex;
  flex-direction: column;
  align-items: center;

  &__status {
    color: color(content-action);
    text-transform: uppercase;
  }

  &__stars {
    color: color(content-action);

    svg {
      font-size: space(xl);

      & + svg {
        margin-left: space(xs);
      }
    }

    @for $i from 0 to 5 {
      &--#{$i} {
        svg:nth-last-child(-n + #{5 - $i}) {
          opacity: 0.4;
        }
      }
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    position: absolute;
    top: 0;
    right: 0;
    width: 22rem;
    margin-top: -(space(md));

    &Wrap {
      position: relative;
      padding-right: 34rem;
    }
  }
}

// Services (page 4)
.services {
  &__map {
    position: relative;
    margin-bottom: space(2xl);

    img {
      max-width: 100%;
    }

    .gx-loader-overlay {
      padding-top: 50%;
    }

    > div {
      position: absolute;
      right: space(sm);
      top: space(sm);
      padding: space(sm);
      background: rgba(color(background-main, true), 0.8);
    }
  }

  &__list {
    margin-bottom: space(lg);

    &Title {
      display: flex;
      align-items: center;
      font-weight: 600;
      margin-bottom: space(sm);

      span {
        display: inline-flex;
        flex-shrink: 0;
        justify-content: center;
        align-items: center;
        width: space(lg);
        height: space(lg);
        border-radius: radius(rounded);
        margin-right: space(sm);
        @include border-chart();
        color: color(content-accent);
      }
    }

    &Info {
      margin-bottom: space(xs);
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    li {
      display: block;
      width: 100%;

      & + li {
        margin-top: space(xs);
      }

      &::before {
        content: '';
        flex-shrink: 0;
        display: inline-block;
        width: space(xs);
        height: space(xs);
        margin-right: space(xs);
        border-radius: radius(rounded);
        background-color: currentColor;
        vertical-align: middle;
      }
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    &__lists {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    &__list {
      width: calc(33% - #{$gx-unit-size * 9});
      margin: 0 ($gx-unit-size * 9) space(lg) 0;
    }
  }
}

// Demographic box (page 5)
.demographic {
  &-box {
    padding: space(xl) space(md);
    border: 0.1rem solid color(border-main);

    & + & {
      margin-top: space(xl);
    }

    &__head {
      margin-bottom: space(xl);

      &--family {
        justify-content: space-between;
        margin-bottom: 0;
      }
    }
  }

  &-tot {
    margin-bottom: space(xl);

    &__icon {
      font-size: ($gx-unit-size * 5);
      color: color(content-action);
      margin-bottom: space(md);
    }

    .gx-title-1 {
      color: color(content-action) !important;
      font-weight: 600;
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    &-box {
      padding: space(xl);

      &__head {
        display: flex;

        &--population {
          justify-content: space-between;
        }

        &--family {
          .demographic-tot {
            width: auto;

            &--fullWidth {
              width: auto;
            }
          }
        }
      }
    }
  }
}

// Foreign origin (page 5)
%graph-bar-wrap {
  display: flex;
  align-items: center;
  flex-grow: 1;
  background-color: color(background-main) !important;
}

%graph-bar {
  width: calc(var(--percentage) * 1%);
  height: space(lg);
  border-top-right-radius: radius(sm);
  border-bottom-right-radius: radius(sm);
}

%graph-bar-label {
  margin-left: space(sm);
  font-weight: 600;
  color: color(content-action) !important;
}

.foreign-origin {
  margin-top: space(xl);
  margin-bottom: space(xl);

  &__title {
    margin-bottom: space(sm);
  }

  ul {
    display: block;
    padding: 0;
    margin: 0 0 0 space(3xl);
    list-style: none;
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);
  }

  li {
    position: relative;
    padding: space(xs);

    & + li {
      border-top: 0.1rem solid color(border-main);
    }

    > span {
      position: absolute;
      left: -(space(3xl));
      top: 50%;
      transform: translateY(-50%);
      width: space(2xl);
      text-align: right;
    }
  }

  &__bar {
    @extend %graph-bar-wrap;

    div {
      @extend %graph-bar;
      @include border-chart();
      background-color: map-get($colors-report, downy);
    }

    span {
      @extend %graph-bar-label;
    }
  }
}

// Population age (page 5)
.population-age {
  &__title {
    margin-bottom: space(sm);
  }

  ul {
    display: block;
    padding: 0;
    margin: 0 0 0 space(3xl);
    list-style: none;
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);
  }

  li {
    position: relative;
    padding: space(xs);

    & + li {
      border-top: 0.1rem solid color(border-main);
    }

    > span {
      position: absolute;
      left: -(space(3xl));
      top: 50%;
      transform: translateY(-50%);
      width: space(2xl);
      text-align: right;
    }
  }

  &__bar {
    @extend %graph-bar-wrap;

    > div {
      @extend %graph-bar;
      border-right: 0.3rem solid color(border-selected);
      background-color: color(background-brand-alt);
    }

    span {
      @extend %graph-bar-label;
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    ul {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 100%;
      margin: 0;
      border: none;
    }

    li {
      display: flex;
      flex-direction: column;
      width: calc(#{percentage(1 / 6)} - #{$gx-unit-size * 4});
      padding: 0;

      & + li {
        border-top: none;
      }

      > span {
        position: static;
        left: 0;
        top: auto;
        bottom: 0;
        transform: none;
        width: auto;
        margin-top: space(sm);
        text-align: center;
        order: 3;
      }
    }

    &__bar {
      height: 11rem;
      flex-direction: column;

      > div {
        width: 100%;
        // ratio between highest percentage, maximum height and current percentage
        height: calc(#{$gx-unit-size * 9} * (var(--percentage) / var(--higher-percentage)));
        border-radius: radius(sm) radius(sm) 0 0;
        border-top: 0.3rem solid color(background-brand);
        border-right: none;
        order: 2;
      }

      span {
        display: flex;
        align-items: flex-end;
        width: 100%;
        margin-left: 0;
        flex-grow: 1;
      }

      &Balloon {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: space(xl);
        margin-bottom: space(sm);
        border: 0.2rem solid color(background-brand);
        border-radius: radius(sm);

        &:after,
        &:before {
          top: 100%;
          left: 50%;
          border: solid transparent;
          content: ' ';
          height: 0;
          width: 0;
          position: absolute;
          pointer-events: none;
        }

        &:after {
          border-top-color: color(background-main);
          border-width: 0.5rem;
          margin-left: -0.5rem;
        }
        &:before {
          border-top-color: color(background-brand);
          border-width: 0.8rem;
          margin-left: -0.8rem;
        }
      }

      &--higher {
        > div {
          height: ($gx-unit-size * 9);
        }
      }
    }
  }
}

// Similar propertis (page 6)
.similar-properties {
  &__map {
    margin-bottom: space(2xl);

    img {
      max-width: 100%;
    }

    .gx-loader-overlay {
      padding-top: 50%;
    }
  }

  &__list {
    padding: 0;
    margin: 0;
    list-style: none;
  }

  @include media('>=#{breakpoint(sm)}') {
    &__list {
      display: flex;
      flex-wrap: wrap;
    }
  }
}

// Chart (page 6)
.price-chart {
  margin-bottom: space(lg);

  .nd-chart {
    padding-left: 6ch;
    padding-bottom: 0;
    padding-right: 0;
    display: flex;

    &__container {
      font-size: 1rem;
      color: color(content-low) !important;

      &__container__grid {
        display: flex;
      }
    }

    &__groupItem {
      margin-top: 0;

      &::before,
      &::after {
        position: relative;
        top: -50%;
        transform: translateY(50%);
      }
    }

    svg {
      .area {
        fill: color(content-action);
      }

      .line {
        stroke: color(content-action);
      }
    }
  }

  &__average {
    display: none;
  }

  @include media('>=#{breakpoint(sm)}') {
    display: flex;
    margin-bottom: space(2xl);

    .nd-chart {
      flex-grow: 1;
      padding-right: 14.8rem;
    }

    &__average {
      display: block;
      position: absolute;
      right: -14.8rem;
      height: 100%;

      &Balloon {
        position: relative;
        width: ($gx-unit-size * 16);
        padding: space(sm) space(md);
        background-color: color(background-brand);
        border-radius: radius(sm);
        transform: translateY(-50%);
        color: color(content-accent);
        text-align: center;

        b {
          font-size: space(md);
        }

        span {
          display: block;
        }

        &::before {
          position: absolute;
          left: -0.4rem;
          top: 50%;
          transform: translateY(-50%);
          content: '';
          width: 0;
          height: 0;
          border-style: solid;
          border-width: space(xs) space(xs) space(xs) 0;
          border-color: transparent color(background-brand) transparent transparent;
        }
      }
    }
  }
}

// Properties list item (page 6)
.properties-list-item {
  padding: space(md) 0 space(md) space(md);

  & + & {
    border-top: 0.1rem solid color(border-main);
  }

  &__content {
    display: flex;

    &Pic {
      position: relative;
      overflow: hidden;
      flex-shrink: 0;
      width: ($gx-unit-size * 16);
      height: ($gx-unit-size * 12);
      margin-right: space(md);
      border: 0.1rem solid color(border-main);

      img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 100%;
      }
    }

    &Data {
      &Title,
      &Price {
        margin-bottom: space(xs);
      }

      &Surface {
        display: flex;

        span {
          display: inline-flex;
          align-items: center;

          svg {
            margin-right: space(xs);
          }

          & + span {
            border-left: 0.1rem solid color(border-main);
            padding-left: space(xs);
            margin-left: space(xs);
          }
        }
      }
    }
  }

  &__info {
    > div {
      align-items: center;
    }

    &ViewPoint {
      display: inline-flex;
      flex-shrink: 0;
      justify-content: center;
      align-items: center;
      width: space(md);
      height: space(md);
      margin-right: space(sm);
      background-color: color(content-action) !important;
      color: color(content-accent) !important;
      @include border-chart();
      border-radius: radius(rounded);
      font-size: 1rem;
    }

    &Distance {
      display: inline-flex;
      margin-top: space(md);
      margin-bottom: space(sm);
    }

    &Date {
      svg {
        margin-right: space(sm);
        @include icon-size(sm);
      }
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    flex-grow: 1;
    width: 50%;
    padding: space(lg);
    border-top: 0.1rem solid color(border-main);

    &:nth-child(-n + 2) {
      border-top: 0;
    }

    &__info {
      display: flex;
      margin-top: space(md);

      &Distance {
        width: ($gx-unit-size * 13);
        margin-top: 0;
        margin-bottom: 0;
      }

      &Date {
        margin-left: space(md);
      }
    }
  }
}

// Properties research (page 7)
.property-research {
  padding: space(xl) space(md);
  border: 0.1rem solid color(border-main);

  p {
    margin-bottom: 0;
  }

  &__chart {
    position: relative;
    width: ($gx-unit-size * 17);
    height: ($gx-unit-size * 17);
    margin: 0 auto space(lg);

    span {
      position: absolute;
      top: 50%;
      display: flex;
      align-items: center;
      height: space(xl);
      padding: 0 space(sm);
      border-radius: radius(sm);
      color: color(content-accent);
      font-weight: 600;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        width: 0;
        height: 0;
        border-style: solid;
      }
    }

    &Interested {
      position: relative;
      width: 100%;
      height: 100%;
      border-radius: radius(rounded);
      @include border-chart();

      span {
        right: 0;
        transform: translate(calc((100% + #{space(md)})), -50%);

        &::before {
          left: 0;
          transform: translate(-100%, -50%);
          border-width: 0.8rem 0.8rem 0.8rem 0;
          border-color: transparent map-get($colors-report, calypso) transparent transparent;
        }
      }
    }

    &Buyers {
      position: absolute;
      left: -0.2rem;
      top: 50%;
      transform: translateY(-50%);
      min-width: space(md);
      min-height: space(md);
      width: calc((var(--buyers) / var(--interested) * 100%));
      height: calc((var(--buyers) / var(--interested) * 100%));
      border-radius: radius(rounded);
      border: 0.2rem solid color(border-reversed);

      span {
        left: 0;
        transform: translate(calc((100% + #{space(md)}) * -1), -50%);

        &::before {
          right: 0;
          transform: translate(100%, -50%);
          border-width: 0.8rem 0 0.8rem 0.8rem;
          border-color: transparent transparent transparent map-get($colors-report, danube);
        }
      }
    }
  }

  &__legend {
    &Row {
      display: flex;

      & + & {
        margin-top: space(sm);
      }

      span {
        display: inline-block;
        flex-shrink: 0;
        width: space(md);
        height: space(md);
        margin-right: space(sm);
        border-radius: radius(rounded);
        background-color: color(content-action);
        @include border-chart();
      }
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    display: flex;
    padding: space(lg) space(xl);

    &__chart-wrap {
      width: 36rem;
      flex-shrink: 0;
      margin-right: space(md);
    }

    &__chart {
      flex-shrink: 0;
      margin-bottom: 0;
    }
  }
}

// Everage prices box (page 7)
.everage-prices-box {
  &__item {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    padding: space(md);
    border: 0.1rem solid color(border-main);

    & + & {
      margin-top: space(md);
    }

    > div {
      width: 50%;
    }

    &Title {
      font-weight: normal;
    }

    &Price {
      height: 100%;
      display: flex;
      flex-direction: column;
      text-align: right;

      svg {
        @include icon-size(md);
        margin-right: space(xs);
      }

      &Value {
        color: color(content-action);
      }

      &--increase {
        svg {
          color: color(content-success);
        }
      }

      &--decrease {
        svg {
          color: color(content-error);
        }
      }
    }

    &Time {
      margin-top: space(md);
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    display: flex;
    justify-content: space-between;

    &__item {
      width: calc(50% - #{space(sm)});

      & + & {
        margin-top: 0;
      }
    }
  }
}

// Quality index (page 8)
.quality-index-box {
  border: 0.1rem solid color(border-main);

  &__content {
    padding: space(xl) space(lg);
  }

  &__item {
    display: flex;

    & + & {
      margin-top: space(xl);
    }

    > div {
      margin-left: space(md);
    }

    &Stars {
      display: inline-flex;
      color: color(content-action);
      margin-top: space(sm);

      span {
        position: relative;
        display: block;
        width: ($gx-unit-size * 3);
        height: ($gx-unit-size * 3);

        & + span {
          margin-left: space(sm);
        }
      }

      svg {
        position: absolute;
        font-size: space(lg);
        opacity: 0.4;
        left: 0;
      }

      .gx-icon__half {
        opacity: 1;
      }

      @for $i from 0 to 10 {
        &--#{$i} {
          // It makes the stars transparent
          span:nth-last-child(n + #{5 - floor($i / 2)}) {
            svg:not(.gx-icon__full) {
              opacity: 1;
            }
          }
        }
      }
    }
  }

  &__icon {
    font-size: space(xl);
    color: color(content-action);
  }

  &__tot {
    position: relative;
    border-top: 0.1rem solid color(border-main);
    padding: space(xl);
    text-align: center;

    &:after,
    &:before {
      top: -0.1rem;
      left: 50%;
      border: solid transparent;
      content: ' ';
      height: 0;
      width: 0;
      position: absolute;
      pointer-events: none;
    }

    &:after {
      border-top-color: #fff;
      border-width: space(md);
      margin-left: -(space(md));
    }
    &:before {
      border-top-color: color(border-main);
      border-width: (space(md) + 1);
      margin-left: (-(space(md)) - 1);
    }

    &Value {
      font-size: typography-scale(size-40);
      color: color(content-action);

      span {
        @include typography(display-2);
      }
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    display: flex;

    &__content {
      display: flex;
      flex-wrap: wrap;
      flex-grow: 1;
      padding: 0;
    }

    &__icon {
      margin-bottom: space(sm);
    }

    &__item {
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 50%;
      padding: space(lg) 0;
      text-align: center;

      & + & {
        margin-top: 0;
      }

      > div {
        margin-left: 0;
      }
    }

    &__icon {
      font-size: space(2xl);
    }

    &__tot {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-top: 0;
      border-left: 0.1rem solid color(border-main);

      .gx-title-2 {
        white-space: nowrap;
      }

      &:after,
      &:before {
        top: 50%;
        left: -0.1rem;
      }

      &:after {
        border-width: space(md);
        margin-top: -(space(md));
        margin-left: auto;
        border-left-color: #fff;
        border-top-color: transparent;
      }
      &:before {
        border-width: (space(md) + 0.1);
        margin-top: (-(space(md)) - 0.1);
        margin-left: auto;
        border-left-color: color(border-main);
        border-top-color: transparent;
      }
    }
  }
}

//Placement chart (page 8)
.placement-chart {
  margin-bottom: space(2xl);

  .nd-chart {
    height: 26rem !important;
    padding-right: 0;
    padding-top: 2rem;
    padding-left: 4rem;

    &__grid {
      margin-bottom: 0;
      border-left: none;

      &Item {
        &--horizontal {
          font-size: 1.2rem;
          color: color(content-low) !important;

          span {
            position: absolute;
            width: calc(33% - #{space(lg)});
            padding: space(xs) 0;
            background-color: color(content-accent) !important;
            border: 0.2rem solid color(border-selected);
            font-size: 1.2rem;
            text-align: center;
            font-weight: 600;
          }
        }
      }
    }

    &__unit {
      font-size: 1.2rem;
      position: absolute;
      left: -3rem;
      top: -2rem;
    }

    &__price-box {
      position: absolute;
      @include z-index(base, 1);
      transform: translateY(50%);
      width: calc(33% - #{space(lg)});
      padding: space(xs) 0;
      background-color: color(content-accent) !important;
      border: 0.2rem solid color(border-selected);
      font-size: 1.2rem;
      text-align: center;
      font-weight: 600;

      @each $name, $color in $colors-report {
        &.line-#{$name} {
          @if $name == 'shakespeare' {
          } @else {
            border-color: $color;
            color: $color !important;

            sup {
              color: $color !important;
            }

            @if $name == 'violet' {
              left: 0;
            }
            @if $name == 'green' {
              right: space(md);
            }
            @if $name == 'yellow' {
              left: 33%;
            }
          }
        }
      }
    }

    @each $name, $color in $colors-report {
      .line-#{$name} {
        &::before {
          height: 0.2rem;

          @if $name == 'shakespeare' {
            background-color: $color !important;
          } @else {
            background-image: linear-gradient(to right, #{$color} 50%, white 50%) !important;
            background-size: space(sm) 0.1rem !important;
          }
        }

        @if $name == 'shakespeare' {
          @include z-index(base, 1);

          span {
            right: 0;
            display: block !important;
            width: ($gx-unit-size * 18);
            padding: space(sm);
            background-color: $color !important;
            border-radius: radius(sm);
            color: color(content-accent) !important;
            border: none;
            font-size: 1.4rem;

            &::before {
              content: '';
              position: absolute;
              left: (-(space(sm)));
              top: 50%;
              transform: translateY(-50%);
              width: 0;
              height: 0;
              border-style: solid;
              border-width: space(sm) space(sm) space(sm) 0;
              border-color: transparent $color transparent transparent;
            }

            &::after {
              content: 'Prezzo stimato';
              display: block;
              margin-top: space(xs);
              font-size: 1rem;
              color: color(content-accent) !important;
            }

            sup {
              color: color(content-accent) !important;
            }
          }
        }
      }
    }

    &-legend {
      display: flex;
      flex-direction: column;
      font-size: 1.2rem;

      > div {
        display: inline-flex;
        align-items: center;

        & + div {
          margin-top: space(sm);
        }

        &::before {
          content: '';
          display: none;
          width: space(md);
          height: space(md);
          margin-right: space(sm);
          border-radius: radius(rounded);
          border: 0.2rem solid color(border-selected);
        }
      }

      span {
        display: inline-block;
        padding: space(xs) space(sm);
        margin-right: space(sm);
        border: 0.2rem solid color(border-selected);
        border-radius: radius(sm);
        font-weight: 600;
      }

      @each $name, $color in $colors-report {
        .line-#{$name} {
          color: color(content-medium) !important;

          span {
            border-color: $color;
          }

          &::before {
            border-color: $color;
          }
        }
      }
    }

    @include media('>=#{breakpoint(sm)}') {
      width: calc(100% - #{$gx-unit-size * 18});

      .line-shakespeare {
        span {
          left: calc(100% + #{space(sm) - 1});
          right: auto;
        }
      }

      &-legend {
        flex-direction: row;
        justify-content: flex-end;

        > div {
          & + div {
            margin-top: 0;
            margin-left: space(lg);
          }

          &::before {
            display: inline-block;
          }
        }
      }
    }
  }
}

// Omi box (page 8)
.omi-box {
  .gx-summary-list {
    padding: space(md) space(md) 0;
    margin-bottom: space(lg);
    border: 0.1rem solid color(border-main);
  }

  &__logo {
    width: 15.5rem;
    height: 4rem;
    margin-bottom: space(md);

    img {
      width: 100%;
    }
  }

  &__prices {
    display: flex;
    border: 0.1rem solid color(border-main);

    @include media('<#{breakpoint(sm)}') {
      flex-wrap: wrap;
    }
  }

  &__price {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    padding: space(md) 0;
    flex-grow: 1;
    border-left: 0.1rem solid color(border-main);

    &--min {
      border-left: 0;
    }

    .medium-price {
      font-size: space(md);
    }

    @include media('<#{breakpoint(sm)}') {
      &--medium {
        order: -1;
        width: 100%;
        border-bottom: 0.1rem solid color(border-main);
        border-left: 0;
      }
    }
  }

  &__no-quotation {
    padding: space(md);
    background-color: color(background-alt);
    border: 0.1rem solid color(border-main);
  }

  @include media('>=#{breakpoint(sm)}') {
    display: flex;

    &__content {
      width: 24rem;
      margin-right: space(3xl);
    }

    &__prices {
      position: relative;
      justify-content: space-between;
    }

    &__price {
      width: 33%;
      flex-grow: 0;
      justify-content: space-between;
      border-left: none;

      &--medium {
        position: absolute;
        width: 33%;
        left: 50%;
        padding: space(lg) 0;
        transform: translateX(-50%);
        border: 0.1rem solid color(border-main);
        top: (-(space(sm)));
        background-color: color(background-main) !important;
      }
    }
  }
}

// Agent evaluation box (page 9)
.agent-evaluation-box {
  padding: space(lg);
  margin-bottom: space(2xl);
  border: 0.1rem solid color(border-main);

  &__content {
    margin-bottom: space(md);

    div {
      margin-bottom: space(md);
    }
  }

  &__price {
    font-size: space(lg);
    color: color(content-action);
  }

  @include media('>=#{breakpoint(sm)}') {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &__content {
      max-width: 40rem;
      margin-bottom: 0;
    }

    &__price {
      font-size: space(2xl);
    }
  }
}

// Note agent (page 10)
.note-agent-box {
  padding: space(md);
  border: 0.1rem solid color(border-main);
  font-size: space(md);
  line-height: 1.5;
  word-wrap: break-word;

  @include media('>=#{breakpoint(sm)}') {
    padding: space(lg);
  }
}

//Agency box (page 10)
.report-full-page .data-agency-box {
  &__pic {
    &--agency {
      border: 0.1rem solid color(border-main);
    }
  }

  &__agency {
    &--noAgent {
      margin-top: 0;
    }
  }

  &__agencyContacts {
    &--noAgent {
      flex-direction: column;

      .data-agency-box__agencyPhone {
        margin-top: space(md);
      }
    }
  }
}

// Report pic (page 11 and 12)
.report-pic {
  &__item {
    margin-bottom: space(xs);
    border: 0.1rem solid color(border-main);
    text-align: center;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 0 -(space(xs));

    &__item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-grow: 1;
      width: calc(50% - #{space(sm)});
      padding-bottom: 70%;
      margin: 0 space(xs) space(sm);

      img {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }

      &:last-child:nth-child(2) {
        width: 100%;
      }

      &:only-child {
        padding-bottom: 120%;
      }
    }
  }
}

// PRINT
.print-mode {
  background-color: color(content-accent) !important;

  .nd-chart__gridItem::before {
    background-color: #d1d1d1 !important;
  }

  .report-full-doc {
    counter-reset: page;
  }

  .report-full-page--hidden {
    display: none !important;
  }

  .estimate-box {
    &__priceEst {
      &__pricePrimary,
      &__priceSecondary {
        color: color(content-action) !important;
      }
    }
  }

  .gx-slider {
    display: none;
  }

  .report-full-page {
    display: flex;
    height: 29.5cm;
    flex-direction: column;
    page-break-before: always;
    box-shadow: none;

    & + .report-full-page {
      margin-top: 0;
    }

    &__section {
      &Title {
        font-size: 2.2rem;
        margin-bottom: space(md);
      }
    }

    &__show {
      display: none;
    }

    &--propertyData {
      .report-full-page__head {
        display: flex;
        justify-content: space-between;
        padding-bottom: 0.25cm;
        margin: 0.5cm 2cm 0.5cm;
        font-size: 1rem;

        &::before {
          content: var(--agencyName);
          font-weight: 600;
        }

        &::after {
          content: var(--agencyContacts);
        }
      }

      .report-full-page__content {
        height: 29cm;

        &--full-width {
          position: relative;
        }
      }

      .report-full-page__foot {
        display: flex;
        justify-content: space-between;
        padding-top: 0.25cm;
        padding-bottom: 0.25cm;
        margin: 0.5cm 2cm 0;
        border-top: 0.1rem solid color(border-main);
        font-size: 1rem;

        &::before {
          content: 'Report del ' var(--lastUpdate);
        }

        &::after {
          counter-increment: page;
          content: counter(page);
        }
      }
    }

    &__cover {
      height: 29.7cm;
      padding-bottom: 0;

      &Box-title {
        background-color: color(content-accent) !important;
      }

      &Last-update {
        margin-top: space(xl);

        span {
          background-color: color(content-accent) !important;
        }
      }
    }

    &--propertyData {
      .estimate-property-info__item {
        width: percentage(1/3);
      }
    }

    &__section {
      padding-left: 2cm;
      padding-right: 2cm;

      &--fullWidth {
        background-color: $report-background-section !important;
      }

      &:first-child {
        padding-top: 0;
      }

      &:last-child {
        padding-bottom: 0;
      }
    }
  }

  .estimate-property-info__item {
    .gx-body-small {
      font-size: 1rem;
    }

    .gx-title-2 {
      font-size: 1.4rem !important;
    }

    .gx-icon {
      fill: color(content-action);
      font-size: ($gx-unit-size * 5);
      margin-right: space(sm);
    }
  }

  // Pie chart
  .report-pie-charts {
    display: flex;
    justify-content: space-between;

    &--auto {
      .report-pie-chart__contentTitle {
        font-size: 1.4rem;
      }
    }
  }

  #{$pie-chart} {
    width: calc(50% - #{space(md)});

    & + #{$pie-chart} {
      margin-top: 0;
    }

    &__wrap {
      &Legend {
        color: color(content-accent) !important;
      }
    }

    &__two-columns {
      column-count: 2;
    }

    &--nationality {
      #{$pie-chart} {
        &__content {
          justify-content: center;
          display: flex;
          flex-direction: column;
          margin-right: space(2xl);
          height: 13.6rem;
          margin-bottom: 0;
        }

        &__wrap {
          position: absolute;
          top: 0;
          right: 0;
        }

        &__contentRow + #{$pie-chart}__contentRow {
          margin-top: space(sm);
          margin-left: 0;
        }
      }
    }

    &--family {
      position: absolute;
      right: 0;
      align-items: flex-start;
      width: 42rem;
      margin-left: space(2xl);

      #{$pie-chart} {
        &__content {
          display: flex;
          flex-wrap: wrap;
          position: absolute;
          right: 0;
          width: calc(100% - 12.8rem);

          &Row {
            margin-bottom: space(sm);

            & + #{$pie-chart}__contentRow {
              margin-top: 0;
            }
          }
        }
      }
    }
  }

  //Print page 2
  .surface-analysis-block {
    &__item {
      display: flex;
      justify-content: space-between;
      width: calc(#{percentage(1/3)} - #{space(sm)});
      background-color: color(content-accent) !important;

      &--total {
        width: calc(50% - #{space(sm)});
      }
    }
  }

  .reference-area-box {
    display: flex;
    justify-content: space-between;

    &__content {
      width: calc(50% - #{$gx-unit-size * 4});
    }

    &__map {
      width: 50%;
    }

    &--horizontal {
      display: block;

      .#{$reference-area-box} {
        &__map {
          width: 100%;
          margin-bottom: space(lg);
        }

        &__lists {
          display: flex;

          > div {
            width: 50%;
          }
        }
      }
    }
  }

  //Print page 3
  .market-analysis {
    background-color: color(content-accent) !important;

    &__item {
      flex-direction: column;
      width: 25%;
      padding: space(md) space(lg);
      text-align: center;

      &Icon {
        font-size: space(2xl);
        margin-right: 0;
        margin-bottom: space(sm);
        fill: color(content-action) !important;

        use {
          fill: color(content-action) !important;
        }
      }

      .gx-copy-label-light {
        font-size: 1rem;
      }

      .gx-title-2 {
        font-size: 1.6rem;
      }

      div:first-child {
        height: space(xl);
      }

      &Content {
        .gx-copy-label-light {
          color: color(content-low) !important;
        }
      }
    }
  }

  // Market stats
  .market-stats {
    margin-bottom: space(xl);
    display: flex;
    justify-content: space-between;

    &-list {
      width: calc(50% - #{space(md)});

      & + .market-stats-list {
        margin-top: 0;
      }

      &__title {
        font-size: 1.4rem;
      }

      &__item {
        &Data {
          background-color: color(background-main) !important;

          span {
            color: color(content-action) !important;
          }
        }
      }
    }
  }

  .attractiveness {
    position: absolute;
    top: -1.6rem;
    right: 0;
    width: 22rem;

    &Wrap {
      position: relative;
      padding-right: 34rem;
    }

    &__status {
      color: color(content-action) !important;
    }

    &__stars {
      color: color(content-action) !important;
    }

    svg {
      fill: color(content-action);
    }
  }

  //Print page 4
  .services {
    margin-bottom: -(space(md));

    &__lists {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }

    &__list {
      width: calc(33% - #{space(lg)});
      margin: 0 space(lg) space(lg) 0;

      svg {
        fill: color(content-accent);
      }
    }
  }

  //Print page 5
  .demographic-tot {
    width: 15rem;

    &--fullWidth {
      width: auto;
    }

    .gx-icon {
      fill: color(content-action);
    }
  }

  .demographic {
    &-box {
      padding: space(xl);

      &__head {
        display: flex;
        position: relative;

        &--population {
          justify-content: space-between;

          .report-pie-chart {
            &__content {
              &Row {
                & + .report-pie-chart__contentRow {
                  margin-top: space(sm);
                  margin-left: 0;
                }
              }
            }
            &__wrap {
              position: absolute;
              top: 0;
              right: 0;
            }
          }
        }

        &--family {
          margin-bottom: 0;

          .demographic-tot {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .population-age {
    ul {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 100%;
      margin: 0;
      border: none;
    }

    li {
      display: flex;
      flex-direction: column;
      width: calc(#{percentage(1 / 6)} - #{$gx-unit-size * 4});
      padding: 0;

      & + li {
        border-top: none;
      }

      > span {
        position: static;
        left: 0;
        top: auto;
        bottom: 0;
        transform: none;
        width: auto;
        margin-top: space(sm);
        text-align: center;
        order: 3;
      }
    }

    &__bar {
      height: 11rem;
      flex-direction: column;

      > div {
        width: 100%;
        height: calc(#{$gx-unit-size * 9} * (var(--percentage) / var(--higher-percentage)));
        border-radius: radius(sm) radius(sm) 0 0;
        border-top: 0.3rem solid color(background-brand);
        border-right: none;
        order: 2;
      }

      span {
        display: flex;
        align-items: flex-end;
        width: 100%;
        margin-left: 0;
        flex-grow: 1;
      }

      &Balloon {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: space(xl);
        margin-bottom: space(sm);
        border: 0.2rem solid color(background-brand);
        border-radius: radius(sm);

        &:after,
        &:before {
          top: 100%;
          left: 50%;
          border: solid transparent;
          content: ' ';
          height: 0;
          width: 0;
          position: absolute;
          pointer-events: none;
        }

        &:after {
          border-top-color: color(content-accent);
          border-width: 0.5rem;
          margin-left: -0.5rem;
        }
        &:before {
          border-top-color: color(content-action);
          border-width: 0.8rem;
          margin-left: -0.8rem;
        }
      }

      &--higher {
        > div {
          height: ($gx-unit-size * 9);
        }
      }
    }
  }

  //Print page 5
  .foreign-origin {
    &__bar {
      div {
        background-color: map-get($colors-report, downy) !important;
      }
    }
  }

  .population-age {
    &__bar {
      > div {
        background-color: color(background-brand-alt) !important;
      }
    }

    .gx-body-small {
      font-size: 1rem;

      span {
        display: inline-block !important;
      }
    }

    ul {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      width: 100%;
      margin: 0;
      border: none;
    }

    li {
      display: flex;
      flex-direction: column;
      width: calc(#{percentage(1 / 6)} - #{$gx-unit-size * 4});
      padding: 0;

      & + li {
        border-top: none;
      }

      > span {
        position: static;
        left: 0;
        top: auto;
        bottom: 0;
        transform: none;
        width: auto;
        margin-top: space(sm);
        text-align: center;
        order: 3;
      }
    }

    &__bar {
      height: 11rem;
      flex-direction: column;

      > div {
        width: 100%;
        // ratio between highest percentage, maximum height and current percentage
        height: calc(#{$gx-unit-size * 9} * (var(--percentage) / var(--higher-percentage)));
        border-radius: radius(sm) radius(sm) 0 0;
        border-top: 0.3rem solid color(background-brand);
        border-right: none;
        order: 2;
      }

      span {
        display: flex;
        align-items: flex-end;
        width: 100%;
        margin-left: 0;
        flex-grow: 1;
      }

      &Balloon {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: space(xl);
        margin-bottom: space(sm);
        border: 0.2rem solid color(background-brand);
        border-radius: radius(sm);
        color: color(content-action) !important;

        &:after,
        &:before {
          top: 100%;
          left: 50%;
          border: solid transparent;
          content: ' ';
          height: 0;
          width: 0;
          position: absolute;
          pointer-events: none;
        }

        &:after {
          border-top-color: color(background-brand);
          border-width: 0.5rem;
          margin-left: -0.5rem;
        }
        &:before {
          border-top-color: color(background-brand);
          border-width: 0.8rem;
          margin-left: -0.8rem;
        }
      }

      &--higher {
        > div {
          height: ($gx-unit-size * 9);
        }
      }
    }
  }

  //Page 6
  .price-chart {
    display: flex;
    margin-bottom: space(2xl);

    .nd-chart {
      height: 36rem !important;
      flex-grow: 1;
      padding-right: 14.8rem;
    }

    &__average {
      display: block;
      position: absolute;
      right: -14.8rem;
      height: 100%;

      &Balloon {
        position: relative;
        width: ($gx-unit-size * 16);
        padding: space(sm) space(md);
        background-color: color(content-action);
        border-radius: radius(sm);
        transform: translateY(-50%);
        color: color(content-accent);
        text-align: center;

        b {
          font-size: space(md);
        }

        span {
          display: block;
        }

        &::before {
          position: absolute;
          left: -0.4rem;
          top: 50%;
          transform: translateY(-50%);
          content: '';
          width: 0;
          height: 0;
          border-style: solid;
          border-width: space(xs) space(xs) space(xs) 0;
          border-color: transparent color(border-selected) transparent transparent;
        }
      }
    }
  }

  .similar-properties {
    &__list {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .properties-list-item {
    flex-grow: 1;
    width: 50%;
    padding: space(lg);
    border-top: 0.1rem solid color(border-main);

    &:nth-child(-n + 2) {
      border-top: 0;
    }

    &__info {
      display: flex;
      margin-top: space(md);

      &Distance {
        width: ($gx-unit-size * 13);
        margin-top: 0;
        margin-bottom: 0;
      }

      &Date {
        margin-left: space(md);
      }
    }
  }

  //Page 7
  .property-research {
    display: flex;
    align-items: center;
    padding: space(lg) space(xl);

    &__chart-wrap {
      width: 30rem;
      flex-shrink: 0;
      margin-right: space(xl);
    }

    &__chart {
      flex-shrink: 0;
      margin-bottom: 0;

      &Interested,
      &Buyers {
        span {
          color: color(content-accent) !important;
        }
      }
    }

    &__value {
      span {
        color: color(content-accent) !important;
      }
    }

    p {
      font-size: 1.2rem;
    }
  }

  .everage-prices-box {
    display: flex;
    justify-content: space-between;

    &__item {
      width: calc(50% - #{space(sm)});

      & + .everage-prices-box__item {
        margin-top: 0;
      }

      &Price {
        sup {
          color: color(content-action) !important;
        }

        &Value {
          color: color(content-action) !important;
        }

        &--increase {
          svg {
            fill: color(content-success) !important;
          }
        }

        &--decrease {
          svg {
            fill: color(content-error) !important;
          }
        }
      }
    }
  }

  //Page 8
  .quality-index-box {
    display: flex;

    &__content {
      display: flex;
      flex-wrap: wrap;
      flex-grow: 1;
      padding: 0;
    }

    &__icon {
      margin-bottom: space(sm);
      fill: color(content-action) !important;
    }

    &__item {
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 50%;
      padding: space(lg) 0;
      text-align: center;

      & + .quality-index-box__item {
        margin-top: 0;
      }

      > div {
        margin-left: 0;
      }

      &Stars {
        svg {
          fill: color(content-action) !important;
        }
      }
    }

    &__icon {
      font-size: space(2xl);

      use {
        fill: color(content-action);
      }
    }

    &__tot {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border-top: 0;
      border-left: 0.1rem solid color(border-main);

      .gx-title-2 {
        white-space: nowrap;
      }

      &:after,
      &:before {
        top: 50%;
        left: -0.1rem;
      }

      &:after {
        border-width: space(md);
        margin-top: -(space(md));
        margin-left: auto;
        border-left-color: #fff;
        border-top-color: transparent;
      }
      &:before {
        border-width: (space(md) + 1);
        margin-top: (-(space(md)) - 1);
        margin-left: auto;
        border-left-color: color(border-main);
        border-top-color: transparent;
      }

      &Value {
        color: color(content-action) !important;

        span {
          color: color(content-action) !important;
        }
      }
    }
  }

  .omi-box {
    display: flex;

    &__content {
      width: 24rem;
      margin-right: space(3xl);
    }

    &__prices {
      position: relative;
      justify-content: space-between;
    }

    &__price {
      width: 33%;
      flex-grow: 0;
      justify-content: space-between;
      border-left: none;

      &--medium {
        position: absolute;
        width: 33%;
        left: 50%;
        padding: space(lg) 0;
        transform: translateX(-50%);
        border: 0.1rem solid color(border-main);
        top: (-(space(sm)));
        background-color: color(background-main) !important;

        .medium-price {
          font-size: space(md);
        }
      }
    }
  }

  //Page 9
  .estimate-box__priceEst .estimate-box__pricePrimary {
    color: color(content-action) !important;
  }

  .placement-chart {
    margin-bottom: space(2xl);

    .nd-chart {
      width: calc(100% - #{$gx-unit-size * 18});

      .line-shakespeare {
        span {
          left: calc(100% + #{space(sm) - 1});
          right: auto;
        }
      }

      &__price-box {
        display: block !important;
      }

      &-legend {
        flex-direction: row;
        justify-content: flex-end;

        > div {
          & + div {
            margin-top: 0;
            margin-left: space(lg);
          }

          &::before {
            display: inline-block;
          }
        }

        span {
          display: none;
        }
      }
    }
  }

  .agent-evaluation-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: space(md);

    &__content {
      max-width: 40rem;
      margin-bottom: 0;
    }

    &__price {
      font-size: space(xl);
      font-weight: normal;
      color: color(content-action) !important;
    }
  }

  .report-alert {
    position: absolute;
    bottom: 0;
    font-size: 1rem;
  }

  //Page 10
  .#{data-agency-box} {
    display: flex;

    use {
      fill: color(content-action) !important;
    }

    &__agencyLogo {
      border: none;
    }

    &__pic {
      width: $gx-data-agency-box-pic-width;
      margin-right: space(xl);
    }

    &__info {
      flex-grow: 1;

      svg {
        fill: color(content-action) !important;
      }
    }

    &__agent {
      &Show {
        padding: space(md) space(xl);
      }

      &Detail {
        padding: space(md) space(xl) space(sm);
      }
    }

    &__agency {
      display: flex;

      &Logo {
        margin-right: space(md);
        margin-bottom: 0;
      }

      &Contacts {
        display: flex;
      }

      &Mail {
        margin-right: space(md);
        margin-bottom: 0;
      }
    }
  }

  .note-agent-box {
    padding: space(lg);
  }

  //Page 11 and 12
  .report-pic {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 0 -(space(xs));

    &__item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-grow: 1;
      width: calc(50% - #{space(sm)});
      padding-bottom: 57%;
      margin: 0 space(xs) space(sm);

      img {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
      }

      &:last-child:nth-child(2) {
        width: 100%;
      }

      &:only-child {
        padding-bottom: 120%;
      }
    }
  }
}
