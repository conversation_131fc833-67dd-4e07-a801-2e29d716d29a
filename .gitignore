###> symfony/framework-bundle ###
/docker/dev/certs
.env
.env.*
/public/bundles/*
!/public/bundles/.gitkeep
/var/*
!/var/.gitkeep
/vendor/
###< symfony/framework-bundle ###
._*
.buildpath
.idea/
.project
.settings/org.eclipse.wst.common.project.facet.core.xml
.vagrant
.DS_Store
assets_version.json
build/
composer.phar
mycontainer.json
node_modules
npm-debug.log
templates/base/Vendor/
yarn-error.log
translations/*.po
translations/cache/*/

!/assets/js/base/vendor
.vscode/launch.json

.php_cs.cache
report*.json
.pnp.*
.yarn/*
.yarn/cache
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
coverage/*
reports/
stats.json
junit.xml
*storybook.log
storybook-static/

###> symfony/phpunit-bridge ###
.phpunit
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###


