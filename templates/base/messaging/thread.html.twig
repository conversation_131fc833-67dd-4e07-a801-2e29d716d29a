{% extends 'base.html.twig' %}

{% block header %}
    <div id="header"></div>
{% endblock %}

{% block content %}
    <div id="content"></div>
{% endblock %}

{% block reactApp %}
    <div id="root"></div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script id="agency-info" type="application/json">{{ agency|json_encode|raw }}</script>
    <script id="header-config" type="application/json">{{ headerConfig|json_encode|raw }}</script>
    <script id="site-section" type="application/json">{{ site_section }}</script>
    <script id="site-subsection" type="application/json">{{ site_subsection }}</script>
    <script id="agent-image-thumb" type="application/json">
        {% if logged_agent and logged_agent.urlImmagineAgenteThumb %}{{ logged_agent.urlImmagineAgenteThumb|raw }}{% endif %}
    </script>
    {% if gtxConstants.MESSAGING_V2_ENABLED %}
        {% set messagingV2 = 0 %}
        {% growthbook_feature 'messaging_v2' %}
            {% set messagingV2 = 1 %}
        {% endgrowthbook_feature %}
        {% if messagingV2 == 1 %}
            {{ webpack_asset('base/messaging') }}
        {% else %}
            {{ webpack_asset('base/messaging-old') }}
        {% endif %}
    {% endif %}
{% endblock %}
