export const getLogo = (url: string, holidayDaysCheck: boolean) => {
  let logoUrl = url;
  if (!holidayDaysCheck) {
    return logoUrl;
  }

  if (isChristmasTime()) {
    logoUrl = url.replace('/loghi/', '/loghi/christmas/');
  }
  return logoUrl;
};

export const isChristmasTime = () => {
  // Get the current date
  const currentDate = new Date();

  // Get the current year
  const currentYear = currentDate.getFullYear();

  // Create a date object for 8th December of the current year
  const start = new Date(currentYear, 11, 8); // In JavaScript, months are 0-indexed (0 for January, 11 for December)

  // Create a date object for 7th January of the next year
  const end = new Date(currentYear + 1, 0, 7); // January of next year

  // If the current date is in January, we need to check against the previous December
  if (currentDate.getMonth() === 0) {
    start.setFullYear(currentYear - 1); // Set start year to the previous year
    end.setFullYear(currentYear); // Set end year to this year
  }

  // Check if the current date is between the start and end dates
  return currentDate >= start && currentDate < end;
};
