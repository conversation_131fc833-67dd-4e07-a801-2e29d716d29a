import { CustomerServiceMenuContent } from '../../header/HeaderContent/CustomerService';
import { CustomMenuComponent } from '../../types';
import { MenuProfile } from './MenuProfile';

type MenuCustomSubMenuProps = {
  customMenuComponent: CustomMenuComponent['customMenuComponent'];
};

export const MenuCustomSubMenu = ({ customMenuComponent }: MenuCustomSubMenuProps) => {
  switch (customMenuComponent) {
    case 'profile':
      return <MenuProfile />;
    case 'customerCare':
      return <CustomerServiceMenuContent />;
    default:
      return null;
  }
};
