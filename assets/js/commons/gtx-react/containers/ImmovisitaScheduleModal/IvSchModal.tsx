import { FC, ReactNode, useEffect, useState } from 'react';
import { IIvSchModalFormPropertyProps, IvSchModalForm } from './IvSchModalForm';
import { getPropertyById } from './web-api';

import { ImmovisitaScheduleModalFormFields, ISelectOption } from './types/form';
import { useFormikContext } from 'formik';
import { ChangeRealEstate } from './ChangeRealEstate/ChangeRealEstate';
import { IApiRealEstateListItem } from './ChangeRealEstate/types';
import { IvSchModalFooter } from './IvSchModalFooter';
import { http } from '@pepita/http';
import { endpoints } from './web-api/endpoints';
import { IRealEstateObj, VisitType } from './types';
import { Button } from '@gx-design/button';
import { Modal } from '@gx-design/modal';
import { trans } from '@pepita-i18n/babelfish';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

enum ModalViews {
    FORM = 'FORM',
    LIST = 'LIST',
}

const queryClient = new QueryClient();

interface IImmovisitaScheduleModal {
    type?: VisitType;
    isOpen: boolean;
    onClose: () => void;
    initialFormState: Partial<ImmovisitaScheduleModalFormFields>;
    roomId?: number;
    initialRealEstateObj?: IRealEstateObj;
    propertyBoxComponent?: FC<IIvSchModalFormPropertyProps>;
}

export const IvSchModal: FC<IImmovisitaScheduleModal> = ({
    isOpen,
    onClose,
    initialFormState,
    initialRealEstateObj,
    propertyBoxComponent,
    type,
}) => {
    const {
        setErrors,
        isSubmitting,
        values,
        setFieldValue,
        validateField,
        resetForm,
    } = useFormikContext();

    const [currentView, setCurrentView] = useState(ModalViews.FORM);

    const [loadingRealEstate, setLoadingrealEstate] = useState(false);
    const [loadingError, setLoadingError] = useState(false);
    const [realEstateObj, setRealEstateObj] = useState<IRealEstateObj | null>(
        null
    );

    const [agents, setAgents] = useState<ISelectOption[]>([]);
    const [isLoadingAgents, setIsLoadingAgents] = useState(false);

    const [modalTitle, setModalTitle] = useState(
        trans('label.immovisita_scheduled.modal_title')
    );

    const [selectedTempRealEstate, setSelectedTempRealEstate] =
        useState<IApiRealEstateListItem | null>(null);

    const setTypeBasedModalTitle = (typein: VisitType) => {
        switch (typein) {
            case VisitType.REAL: {
                setModalTitle(
                    trans('label.scheduled_visit.in_person_modal_title')
                );
                break;
            }
            case VisitType.VIRTUAL: {
                setModalTitle(trans('label.immovisita_scheduled.modal_title'));
                break;
            }
        }
    };

    let currentViewComponent: ReactNode = null;
    let modalFooterComponent: ReactNode = null;

    useEffect(() => {
        if (type) {
            setTypeBasedModalTitle(type);
        }
    }, [type]);

    useEffect(() => {
        if ((values as ImmovisitaScheduleModalFormFields).propertyId) {
            validateField('propertyId');
        }
    }, [(values as ImmovisitaScheduleModalFormFields).propertyId]);

    useEffect(() => {
        if (isOpen) {
            // in messaging, fields comes from different components
            // here a trick to not loose some value
            const newValues =
                typeof values === 'object'
                    ? { ...values, ...initialFormState }
                    : { ...initialFormState };
            resetForm({ values: newValues });
        } else {
            // these were not reset on modal close
            setSelectedTempRealEstate(null);
            setRealEstateObj(null);
        }
    }, [initialFormState, isOpen]);

    useEffect(() => {
        setIsLoadingAgents(true);
        http.get(endpoints.GET_AGENTS, {
            searchParams: {
                status: 'active',
            },
        })
            .json()
            .then((res) => {
                setAgents(
                    res.data.map(
                        (agent: {
                            id: number;
                            firstname: string;
                            lastname: string;
                        }) => {
                            return {
                                value: agent.id,
                                label: `${agent.firstname || ''} ${
                                    agent.lastname || ''
                                }`,
                            };
                        }
                    )
                );
                setIsLoadingAgents(false);
            })
            .catch(() => setIsLoadingAgents(false));
    }, []);

    useEffect(() => {
        setErrors({});
        setCurrentView(ModalViews.FORM);
    }, [isOpen]);

    useEffect(() => {
        if (initialRealEstateObj) {
            setLoadingError(false);
            setRealEstateObj(initialRealEstateObj);
        }

        if (initialFormState.propertyId && !initialRealEstateObj && isOpen) {
            setLoadingrealEstate(true);
            setLoadingError(false);
            getPropertyById(parseInt(initialFormState.propertyId.toString()))
                .json()
                .then((data) => {
                    setLoadingrealEstate(false);
                    setRealEstateObj(data);
                })
                .catch(() => {
                    setLoadingError(true);
                    setLoadingrealEstate(false);
                });
        }

        if (!initialRealEstateObj && !initialFormState?.propertyId) {
            setLoadingError(false);
            setRealEstateObj(null);
        }
    }, [initialFormState, initialRealEstateObj]);

    useEffect(() => {
        if (realEstateObj) {
            setFieldValue('propertyId', realEstateObj.id);
        }
    }, [realEstateObj]);

    switch (currentView) {
        case ModalViews.FORM: {
            currentViewComponent = (
                <IvSchModalForm
                    loadingError={loadingError}
                    loading={loadingRealEstate}
                    realEstateObj={realEstateObj}
                    onChangeRefClick={() => {
                        setCurrentView(ModalViews.LIST);
                        setModalTitle(trans('label.choose_property'));
                    }}
                    agents={agents}
                    isLoadingAgents={isLoadingAgents}
                    propertyBoxComponent={propertyBoxComponent}
                />
            );

            const submitEnabled =
                !loadingError && !loadingRealEstate && !isSubmitting;

            modalFooterComponent = (
                <IvSchModalFooter
                    submitEnabled={submitEnabled}
                    onClose={onClose}
                    isSubmitting={isSubmitting}
                />
            );

            break;
        }
        case ModalViews.LIST: {
            currentViewComponent = (
                <QueryClientProvider client={queryClient}>
                    <ChangeRealEstate
                        currentRealEstateItemId={
                            selectedTempRealEstate?.id ?? realEstateObj?.id
                        }
                        setSelectedTempRealEstate={setSelectedTempRealEstate}
                    />
                </QueryClientProvider>
            );

            modalFooterComponent = (
                <div>
                    <Button
                        variant="ghost"
                        onClick={() => {
                            setCurrentView(ModalViews.FORM);
                            if (type) {
                                setTypeBasedModalTitle(type);
                            }
                        }}
                    >
                        {trans('label.cancel')}
                    </Button>
                    <Button
                        variant="accent"
                        disabled={!selectedTempRealEstate}
                        onClick={() => {
                            setCurrentView(ModalViews.FORM);
                            if (type) {
                                setTypeBasedModalTitle(type);
                            }
                            if (selectedTempRealEstate) {
                                setRealEstateObj({
                                    id: selectedTempRealEstate.id,
                                    mainThumbUrl:
                                        selectedTempRealEstate.mainImage,
                                    mainImage: selectedTempRealEstate.mainImage,
                                    properties: [
                                        {
                                            geographyInformation:
                                                selectedTempRealEstate.geographyInformation,
                                            surface:
                                                selectedTempRealEstate.surface,

                                            reference:
                                                selectedTempRealEstate.reference,
                                        },
                                    ],
                                    prices: [
                                        {
                                            price: selectedTempRealEstate.price?.toString(),
                                            contractId:
                                                selectedTempRealEstate.contract
                                                    ?.id,
                                            isVisible: true,
                                        },
                                    ],
                                });
                            }
                        }}
                    >
                        {trans('label.choose')}
                    </Button>
                </div>
            );
            break;
        }
    }

    let modalBodyComponent: ReactNode = currentViewComponent;

    return (
        <Modal
            isOpen={isOpen}
            className="ivsch-modal"
            onClose={() => {
                onClose();
                setCurrentView(ModalViews.FORM);
            }}
            footer={modalFooterComponent}
            title={modalTitle}
        >
            {modalBodyComponent}
        </Modal>
    );
};
