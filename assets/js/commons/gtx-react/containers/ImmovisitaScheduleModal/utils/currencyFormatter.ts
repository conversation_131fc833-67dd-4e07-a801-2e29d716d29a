// export const currencyFormatter = (value: number) => {
//     return new Intl.NumberFormat(getLang(), {
//         style: 'currency',
//         currency: 'EUR',
//         maximumFractionDigits: 0,
//     }).format(value);
// };

export const currencyFormatter = (value: number) => {
    return value ? `€ ${value}` : '';
};

const normalizeLanguage = language => language.substring(0, 2).toLowerCase();

function getLang() {
    return normalizeLanguage(document.documentElement.lang) || 'it';
}
