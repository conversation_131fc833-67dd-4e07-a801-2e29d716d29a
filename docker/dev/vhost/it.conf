upstream nodejs {
    server localhost:3000;
}

server {
    listen 80;
    listen 443 ssl;
    ssl_certificate     /application/docker/dev/certs/localhost.cert;
    ssl_certificate_key /application/docker/dev/certs/localhost.key;
    root /application/public;
    server_name mls.it.localhost gestionale.it.localhost;

    location / {
        try_files $uri /index.php$is_args$args;
    }


    location ~ ^/fapi {
        rewrite ^/fapi/(.*)$ /$1 break;
        proxy_pass http://nodejs;

        proxy_http_version 1.1;
        proxy_pass_header Set-Cookie;


        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $http_host;
        proxy_cache_bypass $http_upgrade;

        proxy_buffer_size       128k;
        proxy_buffers           4 256k;
        proxy_busy_buffers_size 256k;
    }

    # DEV
    # This rule should only be placed on your development environment
    # In production, don't include this and don't deploy app_dev.php or config.php
    location ~ ^/index\.php(/|$) {
        fastcgi_pass unix:/run/php/php7.4-fpm.sock;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        # When you are using symlinks to link the document root to the
        # current version of your application, you should pass the real
        # application path instead of the path to the symlink to PHP
        # FPM.
        # Otherwise, PHP's OPcache may not properly detect changes to
        # your PHP files (see https://github.com/zendtech/ZendOptimizerPlus/issues/126
        # for more information).
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        # CUSTOM ENVS
        fastcgi_param COUNTRY_TAG "it";
    }

    # return 404 for all other php files not matching the front controller
    # this prevents access to other php files you don't want to be accessible.
    location ~ \.php$ {
        return 404;
    }

    access_log /var/log/nginx/gestionale.it.localhost.access.log;
    error_log  /var/log/nginx/gestionale.it.localhost.error.log;
}

