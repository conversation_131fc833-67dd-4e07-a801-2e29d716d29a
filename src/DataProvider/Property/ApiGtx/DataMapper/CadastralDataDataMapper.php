<?php

declare(strict_types=1);

namespace App\DataProvider\Property\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Property\CadastralData;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class CadastralDataDataMapper extends AbstractDataMapper
{
    private CadastralClassDataMapper $cadastralClassDataMapper;
    private CadastralTypeDataMapper $cadastralTypeDataMapper;
    private CadastralDataAddressDataMapper $cadastralDataAddressDataMapper;

    public function __construct(
        CadastralClassDataMapper $cadastralClassDataMapper,
        CadastralTypeDataMapper $cadastralTypeDataMapper,
        CadastralDataAddressDataMapper $cadastralDataAddressDataMapper
    ) {
        $this->cadastralClassDataMapper = $cadastralClassDataMapper;
        $this->cadastralTypeDataMapper = $cadastralTypeDataMapper;
        $this->cadastralDataAddressDataMapper = $cadastralDataAddressDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return CadastralData::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        /**
         * @var array $data
         */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('cadastralClass', function () use ($data) {
                return !empty($data['cadastralClass']) ?
                    $this->cadastralClassDataMapper->map($data['cadastralClass']) :
                    null;
            })
            ->forMember('cadastralType', function () use ($data) {
                return !empty($data['cadastralType']) ?
                    $this->cadastralTypeDataMapper->map($data['cadastralType']) :
                    null;
            })
            ->forMember('address', function () use ($data) {
                return !empty($data['address']) ?
                    $this->cadastralDataAddressDataMapper->map($data['address']) :
                    null;
            })
        ;

        return $config;
    }
}
