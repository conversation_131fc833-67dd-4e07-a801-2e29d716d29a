<?php

namespace App\Helper;

use App\Constants\Base\DashboardConstants;
use App\Constants\Base\PropertyConstants;
use App\Constants\PerformanceProfiler;
use App\Exception\ApiException;
use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;
use App\Model\Dashboard\AdsByCategoryCount;
use App\Model\Dashboard\AdsByCategoryGroups;
use App\Model\Property\ListingAd;
use App\Model\Property\SearchFilters\PropertiesSearchFilter;
use App\Model\Shared\Order;
use App\Model\Shared\PaginationRequest;
use App\Performance\ProfilerInterface;
use GuzzleHttp\Promise\Utils;
use Symfony\Component\HttpFoundation\Response;

class AdApiClientHelper extends ApiClientHelper
{
    const RESOURCE = 'ads';

    protected string $endpoint;

    public function __construct(
        HttpClientHelper $httpClient,
        ParametersBag $parametersBag,
        ApiWsseHeaderGenerator $apiWsseHeaderGenerator,
        ProfilerInterface $performanceProfiler
    ) {
        parent::__construct($httpClient, $performanceProfiler, $parametersBag, $apiWsseHeaderGenerator);

        $this->endpoint = \sprintf('%s/api/%s/agency', $this->apiBaseurl, parent::VERSION_2);
    }

    /**
     * @throws ApiException
     */
    public function getAdsByCategoryGroups(
        int $agencyId,
        array $activeStatusIds = [],
        array $archivedStatusIds = []
    ): AdsByCategoryGroups {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $urls = [
            DashboardConstants::CATEGORY_GROUP_ACTIVE => \sprintf(
                '%s/%s/%s/count?status=%s',
                $this->endpoint,
                $agencyId,
                self::RESOURCE,
                \implode(',', $activeStatusIds)
            ),
            DashboardConstants::CATEGORY_GROUP_PUBLISHED => \sprintf(
                '%s/%s/%s/count?status=%s&searchable=1',
                $this->endpoint,
                $agencyId,
                self::RESOURCE,
                \implode(',', $activeStatusIds)
            ),
            DashboardConstants::CATEGORY_GROUP_ARCHIVED => \sprintf(
                '%s/%s/%s/count?status=%s',
                $this->endpoint,
                $agencyId,
                self::RESOURCE,
                \implode(',', $archivedStatusIds)
            ),
        ];

        $client = new \GuzzleHttp\Client();

        $promises = [];
        foreach ($urls as $key => $url) {
            $promises[$key] = $client->getAsync($url, [
                'headers' => $this->getDefaultJsonHeaders(),
            ]);
        }

        // Wait for the requests to complete, even if some of them fail
        $responses = Utils::settle($promises)->wait();

        $decoded = [];
        foreach ($urls as $key => $url) {
            $response = $responses[$key]['value'] ?? null;

            $decoded[$key] = $response instanceof \GuzzleHttp\Psr7\Response ?
                \json_decode($response->getBody()->getContents(), true) :
                [];
        }

        $ads = \App\Builder\Model\Dashboard\AdsByCategoryGroupsBuilder::newBuilder()
            ->withActive(\array_map(static function (array $item) {
                return \App\Builder\Model\Dashboard\AdsByCategoryCountBuilder::newBuilder()
                    ->fromApiResponse($item)
                    ->build();
            }, $decoded[DashboardConstants::CATEGORY_GROUP_ACTIVE]['data'] ?? []))
            ->withPublished(\array_map(static function (array $item) {
                return \App\Builder\Model\Dashboard\AdsByCategoryCountBuilder::newBuilder()
                    ->fromApiResponse($item)
                    ->build();
            }, $decoded[DashboardConstants::CATEGORY_GROUP_PUBLISHED]['data'] ?? []))
            ->withArchived(\array_map(static function (array $item) {
                return \App\Builder\Model\Dashboard\AdsByCategoryCountBuilder::newBuilder()
                    ->fromApiResponse($item)
                    ->build();
            }, $decoded[DashboardConstants::CATEGORY_GROUP_ARCHIVED]['data'] ?? []))
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $ads;
    }

    /**
     * @throws ApiException
     * @return AdsByCategoryCount[]
     */
    public function getAdsByCategoryCount(
        int $agencyId,
        array $adStatusIds,
        bool $searchable = null
    ): array {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s/count?status=%s',
            $this->endpoint,
            $agencyId,
            self::RESOURCE,
            \implode(',', $adStatusIds)
        );

        if (null !== $searchable) {
            $url .= \sprintf('&searchable=%s', $searchable);
        }

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ApiException(PropertyConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $data = \array_map(static function (array $item) {
            return \App\Builder\Model\Dashboard\AdsByCategoryCountBuilder::newBuilder()
                ->fromApiResponse($item)
                ->build();
        }, $decoded['data'] ?? []);

        $this->performanceProfiler->stop(__METHOD__);

        return $data;
    }

    /**
     * @throws ApiException
     * @return ListingAd[]
     */
    public function listListingAds(int $agencyId, PropertiesSearchFilter $filter, Order $order, PaginationRequest $pagination): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $params = [
            'offset' => $pagination->getStart(),
            'limit' => $pagination->getResults(),
            'sort' => \sprintf('%s%s', $order->getDirection(), $order->getField()),
        ];

        if (!empty($filter->getAdStatusIds())) {
            $params['status'] = \implode(',', $filter->getAdStatusIds());
        }

        $url = \sprintf(
            '%s/%s/%s/listing?%s',
            $this->endpoint,
            $agencyId,
            self::RESOURCE,
            \http_build_query($params)
        );

        $result = $this->httpClient->execRequestWithResponse(
            'GET',
            $url,
            [],
            $this->getDefaultJsonHeaders()
        );

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ApiException(PropertyConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $data = \array_map(static function (array $item) {
            return \App\Builder\Model\Property\ListingAdBuilder::newBuilder()
                ->fromApiResponse($item)
                ->build();
        }, $decoded['data'] ?? []);

        $this->performanceProfiler->stop(__METHOD__);

        return $data;
    }
}
