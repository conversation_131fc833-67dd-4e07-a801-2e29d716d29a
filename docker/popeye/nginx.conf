server {
    server_name _;
    listen 5000;
    root /application/public;

    rewrite ^(/logs)$ https://logs.kube-dev.it3.ns.farm/explore?orgId=1&left=%7B%22datasource%22:%22Loki%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22editorMode%22:%22builder%22,%22expr%22:%22%7Bnamespace%3D%5C%22dynamic_namespace%5C%22%7D%22,%22queryType%22:%22range%22%7D%5D,%22range%22:%7B%22from%22:%22now-1h%22,%22to%22:%22now%22%7D%7D permanent;

    location / {
        rewrite ^/assets/.*?/bundles/(.*)$ /bundles/$1 last;
        rewrite ^/img/getrix/common/(.+)$ /bundles/base/getrix/common/img/$1 last;
        rewrite ^/(css|js|img|fonts)/(.+)$ /bundles/base/$1/$2 last;
        try_files $uri /index.php$is_args$args;
    }

    # DEV
    # This rule should only be placed on your development environment
    # In production, don't include this and don't deploy app_dev.php or config.php
    location ~ ^/index\.php(/|$) {
        fastcgi_pass unix:/run/php/php7.0-fpm.sock;
        fastcgi_split_path_info ^(.+\.php)(/.*)$;
        include fastcgi_params;
        # When you are using symlinks to link the document root to the
        # current version of your application, you should pass the real
        # application path instead of the path to the symlink to PHP
        # FPM.
        # Otherwise, PHP's OPcache may not properly detect changes to
        # your PHP files (see https://github.com/zendtech/ZendOptimizerPlus/issues/126
        # for more information).
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_param DOCUMENT_ROOT $realpath_root;
        # CUSTOM ENVS
        fastcgi_param COUNTRY_TAG "it";
    }

    # return 404 for all other php files not matching the front controller
    # this prevents access to other php files you don't want to be accessible.
    location ~ \.php$ {
        return 404;
    }

    access_log  /dev/stdout ekbl;
    error_log  /dev/stderr;
}
