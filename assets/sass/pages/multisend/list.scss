@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.list__item.list__item--no-results {
  .list__field {
    text-align: center;
  }

  .list__field:hover {
    background-color: color(background-main);
  }
}

.portal-card-linked-portal-logo {
  .table-wrap:nth-child(odd) {
    margin-left: space(sm);
  }
}

.results-bar__action {
  @include media('screen', '<#{breakpoint(sm)}') {
    & {
      flex-grow: 1;
      text-align: right;
    }

    .btn-icon-mobile {
      width: 4rem;
      padding: 0;

      &.btn--chiudi {
        width: 8rem;
      }
    }
  }

  .btn-group.properties-ordering {
    ul>li {
      width: 23rem;
    }

    ul>li>span {
      display: block;
      padding: 1rem 2rem;
      clear: both;
      font-weight: 600;
      line-height: 1.428571429;
    }

    >.btn {
      float: none;
    }
  }
}

.modal-body>.portal-properties-container~.portal-properties-container {
  .portal-properties-container__logo {
    display: none;
  }
}

.btn-group.disabled {
  .ge-checkbox {
    cursor: not-allowed !important;
  }
}

// LIST
@include media('screen', '<#{breakpoint(md)}') {

  .list,
  .list__body {
    display: block;
  }

  .list__item {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .list__field {
    display: block;
  }
}

.list {
  width: 100%;
}

.list__item {
  padding: space(md);

  &+.list__item {
    border-top: space(md) solid color(border-main);
  }
}

.list__field {
  width: 100%;

  &__thumb {
    position: relative;
    width: ($gx-unit-size * 11);
    height: space(3xl);

    img {
      position: absolute;
      object-fit: cover;
      width: 100%;
      height: 100%;

      &[src$='placeholder.png'] {
        object-fit: unset;
      }
    }
  }

  .th-title {
    color: color(content-low);
    font-size: 1.2rem;
    text-transform: uppercase;

    &--sortable {
      color: color(content-action);
      cursor: pointer;
    }

    .gx-icon {
      fill: currentColor;
      @include icon-size(sm);
    }
  }
}

th.list__field {
  font-weight: normal;
}

.list__field--property {
  margin-bottom: space(sm);
  padding: space(md) 0;
  border-bottom: 0.1rem solid color(border-main);
}

.results-bar__action,
.list__head--portals,
.list__field--portal,
.modal-body {
  .btn-group>.dropdown-menu>li>a {
    cursor: pointer;
  }
}

.modal-body {
  .dropdown-toggle {
    min-width: 3.2rem;
  }
}

.list__head--portals {
  .dropdown-toggle {
    min-width: 3.2rem;
  }
}

.list__field--portal {
  position: relative;
  padding: space(sm) 0;
  border-bottom: 0.1rem solid white;

  .dropdown-toggle {
    min-width: 3.2rem;
  }

  .btn-group>.dropdown-menu>li>a {
    cursor: pointer;
  }

  &:empty {
    display: none;
  }

  .dropdown-menu {
    .with-badge {
      position: relative;
      padding-right: 4.4rem;

      .ge-badge {
        position: absolute;
        top: 50%;
        right: 2rem;
        transform: translateY(-50%);
      }
    }
  }
}

.list__field--actions {
  display: flex;
  justify-content: space-between;
  padding: space(md) 0 space(sm);

  .btn {
    width: calc(50% - #{space(sm)});
  }

  .preview {
    span {
      margin-left: 1rem;
    }
  }
}

.list-footer {
  padding: space(md);

  @include media('screen', '<#{breakpoint(sm)}') {
    border-top: 0.8rem solid color(border-reversed);
    background: color(background-alt);
  }
}

.counter-properties {
  width: 7.1rem;
  height: 3.2rem;
  float: left;
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);
  background-color: color(background-main);
  color: color(content-medium);
  font-size: 1.3rem;
  font-weight: normal;
  line-height: 3.2rem;
  text-align: center;
  text-overflow: ellipsis;
  overflow: hidden;
}

.portal-activation-row__actions__item .counter-properties {
  position: relative;
  right: 0;
}

// LIST | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .list__field--portal {
    width: calc(50% - (#{space(sm)} * 4));
  }

  .list__field--actions {
    display: block;
    justify-content: space-between;
    text-align: right;

    .btn {
      display: inline;
      width: auto;

      &+.btn {
        margin-left: space(md);
      }
    }
  }

  .list-footer {
    display: flex;
    justify-content: space-between;
    padding: 3rem;
    border-top: 0.1rem solid color(border-main);

    &--noBorder {
      border-top: none;
    }
  }

  .list-footer__results {
    min-width: 19rem;
  }
}

@include media('screen', '>=#{breakpoint(sm)}') {
  .list__head {
    border-bottom: 0.1rem solid color(border-main);
    background: color(background-alt);
  }

  .list_head--sticky {
    position: fixed;
    top: $gx-new-menu-header-height;
    right: 0;
    margin: 0;
    @include z-index(base, 2);

    .logo-square {
      margin-right: 3.3rem;
    }

    .other-portal-action__action {
      display: none;
    }

    .btn-group,
    .ge-checkbox {
      display: none;
    }

    .portal-publish-all-info div {
      display: none;
    }

    .counter-properties {
      border-bottom: 0.1rem solid color(border-main) !important;
    }
  }
}

// LIST | DESKTOP
@include media('screen', '>=#{breakpoint(md)}') {
  .list__body tr:hover {
    background: rgba(66, 140, 198, 0.05);
  }

  .list__item {
    &+.list__item {
      border-top: 0.1rem solid color(border-main);
    }
  }

  .list__field {
    width: auto;
    padding: space(md) 1rem;

    &:first-child {
      padding-left: 3rem;
    }
  }

  .list__field--property {
    width: 30rem;
    border-bottom: none;

    .property-block {
      width: 30rem;
    }

    .property-block__desc__field {
      width: 100%;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  .list__field--portal {
    padding: space(md) 1rem;
    border-bottom: none;

    .logo {
      display: none;
    }

    .btn-group {
      margin-right: 0.8rem;
    }
  }

  .list__field--portal:empty {
    display: table-cell;
  }

  .list__field--actions {
    display: table-cell;
    min-width: 20rem;

    .btn {
      width: auto;
    }

    .preview {
      span {
        display: none;
      }
    }
  }

  .list__head--portals {
    .portal-publish-all {
      width: 14rem;
      padding-right: 1.8rem;
      text-align: right;

      .counter-properties {
        position: static;
        width: 100%;
        float: none;
        margin-bottom: 0.5rem;
        transform: translateY(0);

        span {
          font-size: ($gx-unit-size * 2);
        }
      }

      .btn-group {
        >.btn:first-child {
          border-top-left-radius: 0;
        }

        .dropdown-toggle {
          border-top-right-radius: 0;
        }
      }
    }

    .portal-publish-all-info {
      font-weight: normal;
      line-height: 3.6rem;
      vertical-align: bottom;
    }

    .portal-publish-all__action {
      display: inline-block;

      .logo-square {
        position: relative;
        width: 4.2rem;
        height: 4.2rem;
        margin-bottom: 1.1rem;
        border: 0.1rem solid color(border-main);
        border-radius: radius(sm);
        cursor: pointer;

        img {
          position: absolute;
          top: 50%;
          left: 50%;
          width: auto;
          max-width: 100%;
          height: auto;
          max-height: 100%;
          transform: translate(-50%, -50%);
        }
      }
    }
  }

  .list__field:last-child.other-portal-action {
    padding-right: 8.5rem;
    text-align: right;
  }

  .other-portal-action__action {
    display: inline-block;
    padding-top: 8.5rem;

    .ge-checkbox {
      margin-top: space(md);
      margin-left: 50%;
      transform: translateX(-50%);
    }
  }
}

// RESULTS-BAR
.results-bar {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 1rem space(md);
  border-bottom: 0.1rem solid color(border-main);
}

.results-bar__number {
  height: 4rem;
  color: color(content-medium);
  line-height: 1.2;
  display: inline-flex;
  align-items: center;
}

.results-bar__action_right {
  .btn+.btn {
    margin-left: space(sm);
  }
}

.results-bar--ad {
  .results-bar__number {
    display: none;
  }

  .results-bar__action_right .btn--filtra {
    width: calc(100% - 4.8rem);
  }

  .results-bar__number {
    .btn--rimuovi--filtri {
      margin-left: 1.1rem;
    }
  }

  .btn--ordina {
    width: 4rem;
    padding: 0;

    span {
      display: none;
    }
  }
}

.results-bar__mobile {
  width: 100%;

  >span {
    line-height: 4rem;
  }

  .btn--rimuovi--filtri {
    float: right;
  }
}

.filter-box {
  padding: 0 space(md);
  border-bottom: 0.1rem solid color(border-main);

  select[disabled] {
    cursor: not-allowed;

    &+div {
      background-color: rgb(248, 248, 248);
      opacity: 0.65;
    }
  }

  .styled-checkbox--label-space {
    margin-top: 2.5rem;
  }
}

.filter-box__section {
  padding: space(lg) 0;

  &+.filter-box__section {
    border-top: 0.1rem solid color(border-main);
  }
}

.filter-box__section__item {
  &+.filter-box__section__item {
    margin-top: space(lg);
  }
}

.filter-box__section__item__range {
  display: flex;
  justify-content: space-between;

  .fromto {
    width: calc(50% - #{space(sm)});
  }
}

.filter-box__range {
  display: flex;
  justify-content: space-between;

  >div {
    width: calc(50% - #{space(sm)});
  }
}

.filter-box__actions {
  display: flex;
  justify-content: space-between;
  padding-bottom: space(lg);

  .btn--cerca {
    width: 50%;
  }

  .btn--half {
    width: calc(50% - 0.4rem);
  }
}

// RESULTS-BAR | TABLET
@include media('screen', '>=#{breakpoint(md)}') {
  .results-bar {
    padding-right: space(xl);
    padding-left: space(xl);
  }

  .results-bar--ad {
    .results-bar__number {
      display: inline-flex;
    }

    .btn--publish-all {
      display: none;
    }

    .results-bar__action_right .btn+.btn {
      margin-left: space(md);
    }

    .results-bar__action_right,
    .results-bar__action_right .btn--filtra {
      width: auto;
    }

    .btn--ordina {
      width: auto;
      padding-right: 1.6rem;
      padding-left: 1.6rem;

      i {
        display: none;
      }

      span {
        display: inline;
      }
    }
  }

  .filter-box {
    padding: 0 space(xl);
  }

  .filter-box__section {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .filter-box__section__item {
    width: calc(50% - #{space(sm)} * 2);
    margin-top: 0;
    margin-bottom: space(lg);

    &+.filter-box__section__item {
      margin-top: 0;
    }
  }

  .filter-box__actions {
    justify-content: flex-end;
    width: 100%;

    .btn+.btn {
      margin-left: space(sm);
    }

    .btn--cerca,
    .btn--half {
      width: auto;
    }
  }
}

// RESULTS-BAR | DESKTOP
@include media('screen', '>=#{breakpoint(md)}') {
  .filter-box__section {
    justify-content: flex-start;
    padding-bottom: 0;
  }

  .filter-box__section__item {
    width: calc(25% - #{space(lg)});

    &+.filter-box__section__item {
      margin-left: space(xl);
    }

    &:nth-child(5n) {
      margin-left: 0;
    }
  }
}

// PORTAL ACTIVATION ROW
.portal-activation-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: space(md) 0;

  &+.portal-activation-row {
    border-top: 0.1rem solid color(border-main);
  }

  .logo {
    max-width: 13rem;
    height: 4rem;
    margin-right: auto;

    img {
      display: block;
      position: relative;
      top: 50%;
      max-width: 100%;
      max-height: 100%;
      transform: translateY(-50%);
    }
  }
}

.modal-body {
  .portal-activation-row {
    position: relative;
    margin: 0 -1.8rem;
    padding-right: 1.8rem;
    padding-left: 1.8rem;

    @include media('screen', '<#{emConverter(360)}') {
      .logo {
        max-width: 10rem;
      }
    }

    .counter-properties {
      right: 11.8rem;
    }

    &:empty {
      display: none;
    }
  }

  ul.error-list {
    padding-left: 0;
    list-style: none;

    li {
      margin-bottom: space(sm);
    }
  }
}

// PORTAL ACTIVATION ROW | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .portal-activation-row {
    .logo+.ge-badge {
      display: inline-block;
      position: relative;
      top: 1rem;
      margin-left: 2rem;
    }

    .send-ad {
      width: 12rem;
      justify-content: space-between;
      align-items: center;
      padding: 0 space(sm);

      .styled-check {
        width: 2rem;
        height: 2rem;
      }

      .send-ad__text {
        color: color(content-medium);
        font-weight: normal;
        text-transform: none;
      }
    }
  }

  .modal-body {
    .portal-activation-row {
      .counter-properties {
        right: 0;
      }
    }
  }

  .portal-activation-row__actions {
    display: flex;
    justify-content: flex-end;
    width: calc(100% - 20rem);
    float: right;
    align-items: center;

    .styled-select {
      width: 20rem;
    }
  }

  .portal-activation-row__actions__item {
    &+.portal-activation-row__actions__item {
      margin-left: space(md);
    }
  }

  .portal-activation-row--all {
    display: flex;

    span {
      margin-left: 1rem;
      color: color(content-medium);
    }
  }

  .modal-body {
    .portal-activation-row {
      margin: 0 -3rem;
      padding-right: 3rem;
      padding-left: 3rem;
    }
  }
}

.ge-checkbox {
  width: 2rem;
  height: 2rem;
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);
  background-color: color(background-main);
  line-height: 1.8rem;
  text-align: center;
  cursor: pointer;

  i {
    font-size: 1.4rem;
  }
}

.ge-checkbox--checked {
  border-color: color(content-action);
  background-color: color(background-brand);
  color: color(content-accent);
}

.ge-badge {
  width: 2rem;
  height: 2rem;
  border-radius: radius(rounded);
  background: color(background-reversed);
  color: color(content-accent);
  font-size: 1.1rem;
  font-weight: bold;
  line-height: 2rem;
  text-align: center;
  @include z-index(base, 1);

  &::before {
    content: '!';
  }
}

.ge-badge--success {
  background: color(content-success);

  &::before {
    content: '✓';
  }
}

//TO-DO: sostituire con le icone
.ge-badge--warning {
  background: color(content-warning);
}

.ge-badge--danger {
  background: color(content-error);
}

.ge-badge--info {
  background-color: color(background-brand);

  &::before {
    content: '?';
  }
}

.ge-badge__container {
  position: relative;
  overflow: visible;
}

.ge-badge--top-right {
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
  transform: translate(50%, -50%) !important;
}

.property-block {
  display: flex;

  .property-block__photo {
    width: auto;
    max-width: 8rem;
    height: auto;
    max-height: 6rem;
    margin-right: 1.6rem;
    float: left;
  }

  .property-block__desc {
    width: calc(100% - 9.6rem);
    float: left;
    color: color(content-medium);
  }

  .property-block__field {
    width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}

.noVerticalPadding {
  margin-top: -2rem;
  margin-bottom: -2rem;

  @include media('screen', '>=#{breakpoint(sm)}') {
    margin-top: -3rem;
    margin-bottom: -3rem;
  }
}

// PORTAL PROPERTIES CONTAINER
.portal-properties-container {
  margin: 0 -1.8rem;
  padding-top: space(md);
  border-top: 0.1rem solid color(border-main);

  p+& {
    margin-top: space(lg);
  }

  .portal-properties-container__logo {
    max-width: 13rem;
    height: 4rem;
    margin: 0 1.8rem;

    img {
      display: block;
      position: relative;
      top: 50%;
      max-width: 100%;
      max-height: 100%;
      transform: translateY(-50%);
    }
  }

  .modal-body & {
    .portal-activation-row {
      margin: 0 1.8rem;
      padding-right: 0;
      padding-left: 0;
    }
  }

  .portal-activation-row__actions__item {
    margin-top: space(md);
  }
}

// PORTAL PROPERTIES CONTAINER | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .portal-properties-container {
    position: relative;
    margin: 0 -3rem;

    .portal-properties-container__logo {
      position: absolute;
      top: 3.4rem;
      left: 3rem;
      margin: 0;
    }
  }

  .modal-body .portal-properties-container {
    .portal-activation-row {
      display: flex;
      justify-content: space-between;
      width: calc(100% - 21rem);
      margin: 0 0 0 18rem;
      padding-right: 0;
      padding-left: 0;
    }

    .portal-activation-row__actions__item {
      width: 17.6rem;
      margin-top: 0;
      margin-left: space(lg);
    }

    .property-block {
      width: calc(100% - 20rem);
    }

    .property-block__desc__field {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}

.styled-select.error .styled-select-placeholder {
  border: 0.1rem solid color(border-error);
}

// PAGER
.pager {
  position: relative;
  flex-grow: 1;
  text-align: center;
}

.pager__item {
  &+.pager__item {
    margin-left: 1rem;
  }
}

.pager__item--link,
.pager__item--ellipsis {
  display: none;
}

.btn.pager__item--current {
  background: color(border-main);
  color: color(content-medium);
  cursor: default;

  &:hover {
    background: color(border-main);
    color: color(content-medium);
    text-decoration: none;
  }
}

.pager__item--ellipsis {
  color: color(content-medium);

  &:hover {
    color: color(content-medium);
    text-decoration: none;
  }
}

.pager__item--prev,
.pager__item--next {
  position: absolute;
  top: 0;
}

.pager__item--prev {
  left: 0;
}

.pager__item--next {
  right: 0;
}

// PAGER | TABLET
@include media('screen', '>=#{breakpoint(sm)}') {
  .pager {
    text-align: right;
  }

  .pager__item--link,
  .pager__item--ellipsis {
    display: inline-block;
  }

  .pager__item--prev,
  .pager__item--next {
    position: static;
    top: 1.6rem;
  }

  .pager__item--prev {
    left: 1.6rem;
  }

  .pager__item--next {
    right: 1.6rem;
  }
}

//////////////////////////////////////////
// FINE elementi da spostare

// NEW MULTISEND
.multisend-item__actions,
.multisend-headActions {
  position: relative;
  display: flex;

  .gx-checkbox {
    padding: 0 space(sm);
    min-width: ($gx-unit-size * 5);
    justify-content: center;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;

    &__control {
      margin-right: 0;
    }
  }

  .gx-button {
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .gx-dropupButton {
    margin-left: -0.1rem;
  }

  .styled-checkbox .styled-check {
    width: 2rem;
    height: 2rem;
  }

  .gx-button--dropdownCaret {
    min-width: space(xl);
    padding: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

.counter-properties+.multisend-headActions {
  >.gx-button {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .gx-button--dropdownCaret {
    border-top-right-radius: 0;
  }
}

.multisend-item {
  &__field {
    &Property {
      width: 30rem;
    }

    &Actions {
      min-width: 20rem;

      .gx-table__cell {
        justify-content: end;
      }
    }
  }

  &__status {
    font-size: 20px;
    position: absolute;
    top: -#{space(sm)};
    right: -#{space(sm)};
    background-color: color(background-main);
    border-radius: radius(rounded);
  }

  @include media('screen', '<gx-medium') {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    &+& {
      border-top-width: ($gx-unit-size * 2);
    }

    .emptyPortal {
      display: none;
    }

    &__actions {
      margin-left: space(md);
    }

    &__field {
      display: flex;
      width: 100%;
      padding: space(md) 0;

      &:first-child {
        padding-left: 0;
      }

      &Property {
        width: 100%;
        border-bottom: 0.1rem solid color(border-main);
      }

      &Portal {
        width: calc(50% - #{space(lg)});
        padding-left: 0;
        padding-right: 0;

        .gx-table__cell {
          width: 100%;
          justify-content: space-between;
        }
      }

      &Actions {
        min-width: auto;

        .gx-table__cell {
          width: 100%;
        }

        &:last-child {
          padding-right: 0;
        }

        .gx-button--iconOnly {
          padding: space(sm) space(md);
        }
      }
    }
  }

  @include media('screen', '<#{breakpoint(sm)}') {
    &__field {
      &Portal {
        width: 100%;
      }

      &Actions {
        .gx-table__cell {
          justify-content: space-between;
        }

        .gx-button {
          width: 50%;
        }
      }
    }
  }

  .logo {
    max-width: 10rem;
    height: 4rem;
    margin-right: auto;

    @include media('screen', '>=#{emConverter(360)}') {
      max-width: 13rem;
    }

    img {
      display: block;
      position: relative;
      top: 50%;
      max-width: 100%;
      max-height: 100%;
      transform: translateY(-50%);
    }

    @include media('screen', '>=#{breakpoint(md)}') {
      display: none;
    }
  }
}

.other-portal-action {
  .gx-table__cell {
    justify-content: flex-end;
    padding-right: 5.6rem;
  }
}