{% extends 'mail/base.html.twig' %}

{% block body %}

	{{ 'label.new_registration_request'|trans({}, gtxConstants.DOMAIN_CMS) }} {{ gtxConstants.APP_NAME }}
	<br/><br/><br/>
	{{ 'label.agency_id'|trans({}, gtxConstants.DOMAIN_CMS) }}: {{ id }}
	<br/><br/>
	{{ 'label.reference_name'|trans({}, gtxConstants.DOMAIN_CMS) }}: {{ firstName }} {{ lastName }}
	<br/>
	{{ 'label.company_type_2'|trans({}, gtxConstants.DOMAIN_CMS) }}: {% if companyType %}{{ 'label.building_company'|trans({}, gtxConstants.DOMAIN_CMS) }}{% else %}{{ 'label.estate_agency'|trans({}, gtxConstants.DOMAIN_CMS) }}{% endif %}
	<br/>
	{{ 'label.company_name'|trans({}, gtxConstants.DOMAIN_CMS) }}: {{ companyName }}
	<br/>
	{{ 'label.mail'|trans({}, gtxConstants.DOMAIN_CMS) }}: <a href="mailto:{{ email }}">{{ email }}</a>
	<br/>
	{{ 'label.phone'|trans({}, gtxConstants.DOMAIN_CMS) }}: {{ phonePrefix }}{{ phoneNumber }}
	<br/>
	{{ 'label.municipality'|trans({}, gtxConstants.DOMAIN_CMS) }}: {{ cityName }}
	<br/>
	{{ 'label.vat'|trans({}, gtxConstants.DOMAIN_CMS) }}: {{ vatNumber }}
	<br/><br/><br/>
	--<br/>
	{{ 'mail.sent_by_form'|trans({}, gtxConstants.DOMAIN_CMS) }} {{ gtxConstants.APP_NAME }} <a href="{{ globalConfigs.getrix.baseurl }}{{ path('app_register') }}" target="_blank">{{ globalConfigs.getrix.baseurl }}{{ path('app_register') }}</a>
	<br/><br/>

{% endblock %}
