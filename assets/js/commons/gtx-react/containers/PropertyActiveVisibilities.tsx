import { FC } from 'react';

import { Tooltip } from '@gx-design/tooltip';
import { Badge } from '@gx-design/badge';
import { trans } from '@pepita-i18n/babelfish';
import {
    PREMIUM_VISIBILITY_KEY,
    SKY_VISIBILITY_NAME,
} from 'constants/propertyVisibilities';
import gtxConstants from '@getrix/common/js/gtx-constants';
import {
    getActiveVisibilities,
    getPropertyAllowedExtraVisibilities,
} from 'lib/propertyVisibilities';
import { IPropertyDetail } from 'types/api/property';
import { SECRET_PROPERTY_STATUS_ID } from 'constants/property';
import { useFeatureIsOn } from '@growthbook/growthbook-react';

type PropertyActiveVisibilitiesProps = {
    property: IPropertyDetail;
};

export const PropertyActiveVisibilities: FC<
    PropertyActiveVisibilitiesProps
> = ({ property }: PropertyActiveVisibilitiesProps) => {
    const activeVisibilities = getActiveVisibilities(property);
    const extraVisibilities = activeVisibilities.filter(
        (visibility) => visibility.value !== PREMIUM_VISIBILITY_KEY
    );
    const isSecretPropertyEnabled = useFeatureIsOn('crm_secret_property');

    if (
        property.statusId === SECRET_PROPERTY_STATUS_ID &&
        isSecretPropertyEnabled
    ) {
        return (
            <Tooltip
                position="top"
                text={trans('label.secret_property_tooltip')}
            >
                <Badge
                    icon="eye-off"
                    style="reversed"
                    text={trans('label.secret')}
                />
            </Tooltip>
        );
    }

    if (!activeVisibilities.length) {
        return <>{trans('label.agency_page_short')}</>;
    }

    if (activeVisibilities.length === 1 || extraVisibilities.length === 1) {
        return (
            <Badge
                text={activeVisibilities[0].label}
                className={`visibility-${activeVisibilities[0].icon}`}
            />
        );
    }

    const allowedExtraVisibilities = getPropertyAllowedExtraVisibilities();

    if (
        gtxConstants('VISIBILITIES_ALL_EXTRA_VISIBILITIES_PRODUCT') &&
        extraVisibilities.length === allowedExtraVisibilities.length
    ) {
        return (
            <Tooltip
                text={extraVisibilities
                    .map((visibility) => visibility.label)
                    .join(' + ')}
                position="top"
            >
                <Badge text={SKY_VISIBILITY_NAME} className="visibility-sky" />
            </Tooltip>
        );
    }

    return (
        <>
            {activeVisibilities.map(
                (visibility) =>
                    visibility.shortLabel && (
                        <Tooltip
                            key={`visibility_tag_${visibility.shortLabel}`}
                            text={visibility.label}
                            position="top"
                        >
                            <Badge
                                className={`visibility-${visibility.icon}`}
                                text={visibility.shortLabel}
                            />
                        </Tooltip>
                    )
            )}
        </>
    );
};
