import React from 'react';
import { useQuery, QueryClientProvider, useQueryClient, keepPreviousData } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { REACT_QUERY_DEV_TOOLS_CLOSE_BTN, REACT_QUERY_DEV_TOOLS_TOGGLE_BTN } from 'gtx-react/constants';
import { Modal } from '@gx-design/modal';
import { CustomerForm } from './CustomerForm';
import { trans } from '@pepita-i18n/babelfish';

export const CustomerModal = ({
    id,
    isOpen,
    loading,
    getCustomerApi,
    lookupEndpoints,
    onCloseHandler,
    onSubmitHandler,
    className,
}) => {
    const queryClient = useQueryClient();
    const { isFetching: fetchingCustomer, data: customer } = useQuery({
        queryKey: ['customer', id],
        queryFn: () => {
            return getCustomerApi({ id })
                .then((res) => res.json())
                .then((res) => res.customer)
                .catch(() => onClose());
        },
        refetchOnWindowFocus: false,
        placeholderData: keepPreviousData,
        refetchOnMount: false,
        enabled: Boolean(id),
    });

    React.useEffect(() => {
        if (!isOpen) {
            queryClient.invalidateQueries({ queryKey: ['customer', id] });
        }
    }, [id, isOpen, queryClient]);

    if (!isOpen) {
        return null;
    }

    const onSubmit = (data) => {
        onSubmitHandler(data);
    };

    const onClose = () => {
        onCloseHandler();
        localStorage.removeItem('notes');
    };

    return (
        <Modal
            isOpen={isOpen}
            size="large"
            className={className}
            title={id ? trans('label.edit_customer') : trans('label.new_customer')}
            onClose={onClose}
        >
            <QueryClientProvider client={queryClient}>
                <ReactQueryDevtools
                    toggleButtonProps={REACT_QUERY_DEV_TOOLS_TOGGLE_BTN}
                    closeButtonProps={REACT_QUERY_DEV_TOOLS_CLOSE_BTN}
                    initialIsOpen={false}
                />
                <CustomerForm
                    customer={customer}
                    handleSubmit={onSubmit}
                    handleCancel={onClose}
                    loading={loading || fetchingCustomer}
                    lookupEndpoints={lookupEndpoints}
                    inModal={true}
                    modalSubmitLabel={id ? trans('label.save') : trans('label.add')}
                />
            </QueryClientProvider>
        </Modal>
    );
};
