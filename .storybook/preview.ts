import type { Preview } from '@storybook/react';
import DocumentationTemplate from './DocumentationTemplate.mdx';

import '!style-loader!css-loader!sass-loader!../assets/sass/main.scss';

// Carica le traduzioni da .storybook/public/locale.json
fetch('locale.en.json')
    .then((res) => res.json())
    .then((translations) => {
        window.__babelfish = translations;
        window.__babelfish_plurals = {}; // TODO: add plurizations
        console.log('Translations loaded in Storybook');
    })
    .catch((err) => {
        console.warn('Failed to load translations', err);
        window.__babelfish = {};
    });

const preview: Preview = {
    parameters: {
        docs: {
            page: DocumentationTemplate,
            source: { type: 'dynamic' },
        },
        controls: {
            matchers: {
                color: /(background|color)$/i,
                date: /Date$/i,
            },
        },
    },
};

export default preview;
