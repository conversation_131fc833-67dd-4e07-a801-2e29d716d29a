import { PropsWithChildren, createContext, useContext, useMemo } from 'react';
import { MenuConfig, Pathname } from '../types';
import { getParentPath } from '../shared/utils';
import { removeTrailingSlash, searchParamsToKeyValues } from '../shared/utils/getParentPath';

type MenuContext = {
  activePathNames: Pathname[] | null;
};

const menuContext = createContext<MenuContext | undefined>(undefined);

type MenuProviderProps = PropsWithChildren<{
  config: MenuConfig;
}>; // TBD

export const useMenuConfigSegmentContext = () => {
  const context = useContext(menuContext);

  if (!context) {
    throw new Error('useMenuContext must be used within a MenuProvider');
  }

  return context;
};

/**
 * MenuConfigSegmentProvider is a provider that provides the activePathNames to the children components.
 * For instance: if the current path is `/immobili/portale/lista`, the activePathNames will be `["#annunci","#immobili","/immobili/portale/lista"]`
 */
export const MenuConfigSegmentProvider = (props: MenuProviderProps) => {
  const activePathNames = useMemo(
    () =>
      getParentPath(
        removeTrailingSlash(window.location.pathname),
        props.config,
        searchParamsToKeyValues(new URLSearchParams(window.location.search)),
      ) || [],
    [props.config],
  );

  return <menuContext.Provider value={{ activePathNames }}>{props.children}</menuContext.Provider>;
};
