<?php

declare(strict_types=1);

namespace App\DataProvider\Property\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\DataProvider\Property\ApiGtx\DataMapper\Geography\AddressDataMapper;
use App\DataProvider\Property\ApiGtx\DataMapper\Geography\CityDataMapper;
use App\Model\Property\CadastralDataAddress;
use ObjectMapper\Configuration\ObjectMapperConfig;

class CadastralDataAddressDataMapper extends AbstractDataMapper
{
    private CityDataMapper $cityDataMapper;
    private AddressDataMapper $addressDataMapper;

    public function __construct(
        CityDataMapper $cityDataMapper,
        AddressDataMapper $addressDataMapper
    ) {
        $this->cityDataMapper = $cityDataMapper;
        $this->addressDataMapper = $addressDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return CadastralDataAddress::class;
    }

    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(\ObjectMapper\DataType::ARRAY, $this->getDestinationClass())
            ->forMember('city', function () use ($data) {
                return null !== $data['city'] ?
                    $this->cityDataMapper->map($data['city']) :
                    null;
            })
            ->forMember('address', function () use ($data) {
                return null !== $data['address'] ?
                    $this->addressDataMapper->map($data['address']) :
                    null;
            });

        return $config;
    }
}
