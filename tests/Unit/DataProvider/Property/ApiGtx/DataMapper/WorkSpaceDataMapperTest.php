<?php

declare(strict_types=1);

namespace Tests\Unit\DataProvider\Property\ApiGtx\DataMapper;

use App\DataProvider\Property\ApiGtx\DataMapper\WorkSpaceDataMapper;
use App\Model\Property\WorkSpace;
use PHPUnit\Framework\TestCase;

class WorkSpaceDataMapperTest extends TestCase
{
    private WorkSpaceDataMapper $workSpaceDataMapper;

    protected function setUp(): void
    {
        $this->workSpaceDataMapper = new WorkSpaceDataMapper();
    }

    /**
     * @test
     */
    public function testMapCompleteWorkspaceData(): void
    {
        $data = [
            'qty' => 5,
            'type' => 1,
            'capacityFrom' => 10,
            'capacityTo' => 20,
            'feeFrom' => 100.50,
            'feeTo' => 200.75,
        ];

        /** @var WorkSpace $result */
        $result = $this->workSpaceDataMapper->map($data);

        $this->assertInstanceOf(WorkSpace::class, $result);
        $this->assertEquals(5, $result->getQty());
        $this->assertEquals(1, $result->getType());
        $this->assertEquals(10, $result->getCapacityFrom());
        $this->assertEquals(20, $result->getCapacityTo());
        $this->assertEquals(100.50, $result->getFeeFrom());
        $this->assertEquals(200.75, $result->getFeeTo());
    }

    /**
     * @test
     */
    public function testMapWorkspaceWithNullableFields(): void
    {
        $data = [
            'type' => 2,
            'feeFrom' => 50.00,
            'feeTo' => 150.00,
            'qty' => null,
            'capacityFrom' => null,
            'capacityTo' => null,
        ];

        /** @var WorkSpace $result */
        $result = $this->workSpaceDataMapper->map($data);

        $this->assertInstanceOf(WorkSpace::class, $result);
        $this->assertNull($result->getQty());
        $this->assertEquals(2, $result->getType());
        $this->assertNull($result->getCapacityFrom());
        $this->assertNull($result->getCapacityTo());
        $this->assertEquals(50.00, $result->getFeeFrom());
        $this->assertEquals(150.00, $result->getFeeTo());
    }

    /**
     * @test
     */
    public function testHandleTypeCasting(): void
    {
        $data = [
            'qty' => '3',
            'type' => '1',
            'capacityFrom' => '5',
            'capacityTo' => '10',
            'feeFrom' => '75.25',
            'feeTo' => '125.75',
        ];

        /** @var WorkSpace $result */
        $result = $this->workSpaceDataMapper->map($data);

        $this->assertInstanceOf(WorkSpace::class, $result);
        $this->assertIsInt($result->getQty());
        $this->assertIsInt($result->getType());
        $this->assertIsInt($result->getCapacityFrom());
        $this->assertIsInt($result->getCapacityTo());
        $this->assertIsFloat($result->getFeeFrom());
        $this->assertIsFloat($result->getFeeTo());
    }

    /**
     * @test
     */
    public function testReturnNullForNullInput(): void
    {
        $result = $this->workSpaceDataMapper->map(null);

        $this->assertNull($result);
    }

    /**
     * @test
     */
    public function testThrowExceptionForInvalidInputType(): void
    {
        $invalidData = 'not an array or object';

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Object or array data-source expected');

        $this->workSpaceDataMapper->map($invalidData);
    }

    /**
     * @test
     */
    public function testThrowExceptionWhenRequiredFieldsAreMissing(): void
    {
        $incompleteData = [
            'qty' => 1,
        ];

        $this->expectException(\RuntimeException::class);

        $this->workSpaceDataMapper->map($incompleteData);
    }
}
