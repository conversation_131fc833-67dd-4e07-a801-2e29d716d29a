import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { ImprovePropertyQuality } from './ImprovePropertyQuality';
import { getPropertyQualityDetailsApi } from '../web-api/api';
import { IPropertyListItem } from '../../types/api/property';
import useImprovePropertyQualityColumns from '../hooks/useImprovePropertyQualityConfigs';
import gtxConstants from '@getrix/common/js/gtx-constants';
import { IconProps } from '@gx-design/icon';
import { trans } from '@pepita/babelfish';

type ImprovePortalPropertyQualityProps = {
    propertyData: IPropertyListItem;
    configName:
        | 'portalProperty'
        | 'portalNewConstruction'
        | 'portalAuction'
        | null;
    endpoints: any;
};

const QUERY_CONFIG = {
    refetchIntervalInBackground: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: 600 * 1000,
};

export type Config = {
    key: string;
    title: string;
    transPlaceholder?: string;
    placeholderKey?: string;
    issueType: (data: any) => 'warning' | 'positive' | 'negative';
    description: (data: any) => string;
    isVisible?: (data: any) => boolean;
    action: string;
    actionLabel?: string;
    actionIcon?: IconProps['name'];
};

export const ImprovePropertyQualityContainer = ({
    propertyData,
    configName,
    endpoints,
}: ImprovePortalPropertyQualityProps) => {
    const configs = useImprovePropertyQualityColumns(endpoints, configName);

    const { data: qualityData, isLoading: isQualityLoading } = useQuery({
        queryKey: [`property_quality_details_${propertyData.id}`],
        queryFn: () =>
            getPropertyQualityDetailsApi(propertyData.id).then(
                (res) => res.data
            ),
        ...QUERY_CONFIG,
    });

    const getUIProps = () => {
        if (!propertyData || !qualityData) {
            return {
                propertyRanking: 0,
                propertyId: '',
                qualityItems: [],
                agencyContact: null,
                portalName: gtxConstants('AD_PORTAL'),
                isLoading: isQualityLoading,
            };
        }

        const getAgencyContact = () => {
            const agencyElement = document.getElementById('agency-info');
            if (!agencyElement?.innerHTML) {
                return null;
            }

            try {
                const agencyData = JSON.parse(agencyElement.innerHTML);
                return {
                    name: agencyData?.responsabile?.nome || '',
                    phone: agencyData?.responsabile?.telefono,
                    email: agencyData?.responsabile?.email,
                };
            } catch {
                return null;
            }
        };

        const qualityItems = configs.map((config) => {
            const isVisible = config.isVisible
                ? config.isVisible(propertyData)
                : true;
            const isCompleted = Boolean(qualityData[config.key]?.outcome);

            let description = '';
            if (!isCompleted) {
                const rawDescription = config.description(propertyData);
                if (
                    rawDescription &&
                    config.transPlaceholder &&
                    config.placeholderKey
                ) {
                    description = trans(rawDescription, {
                        [config.transPlaceholder]:
                            qualityData.description[config.placeholderKey],
                    });
                } else {
                    description = rawDescription;
                }
            }

            return {
                key: config.key,
                title: config.title,
                isCompleted,
                issueType: config.issueType(propertyData) as
                    | 'warning'
                    | 'positive'
                    | 'negative',
                description: description || undefined,
                actionUrl: isCompleted
                    ? undefined
                    : config.action.replace('{id}', propertyData.id),
                actionLabel: config.actionLabel,
                actionIcon: config.actionIcon as IconProps['name'],
                isVisible,
            };
        });

        return {
            propertyRanking: propertyData.ranking,
            propertyId: propertyData.id,
            qualityItems,
            agencyContact: getAgencyContact(),
            portalName: gtxConstants('AD_PORTAL'),
            isLoading: isQualityLoading,
        };
    };

    const uiProps = getUIProps();

    if (!uiProps) {
        return null;
    }

    return <ImprovePropertyQuality {...uiProps} />;
};
