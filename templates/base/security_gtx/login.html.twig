{% extends 'base/security_gtx/base.html.twig' %}

{% block inner_content %}
    {% include 'base/security_gtx/inner-header.html.twig' %}
    <div class="login-content">
        <form class="form-account" action="{{ path('getrix_app_login_check') }}" method="post">
            <h1>Accedi a {{ gtxConstants.APP_NAME }}.</h1>
            <p>Inserisci email e password.</p>
            {% if error %}
                <div class="alertNew alertNew-error">
                    <svg class="gx-icon">
                        <use xlink:href="{{ gtxConstants.ICONS_SVG_SPRITE_FILE_PATH }}#cross"></use>
                    </svg>
                    <p>
                    {% if error.messageKey == 'Agenzia non attiva.' %}
                        Anagrafica non attiva.<br/>Contatta il servizio clienti al numero <strong>{{ gtxConstants.TEL_SERVIZIO_CLIENTI }}</strong>
                    {% elseif error.messageKey == 'Utente non attivo.' %}
                        Utente disabilitato
                    {% else %}
                        {{ error.messageKey |trans(error.messageData, 'security') }}
                    {% endif %}
                    </p>
                </div>
            {% endif %}
            {% if backurl %}
                <input type="hidden" name="_target_path" value="{{ backurl }}">
            {% endif %}
            <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
            <div class="riga  riga--icon  riga--email">
                <label for="username" class="gx-sr-only">Email</label>
                <div class="with-icon">
                    <input type="email" id="username" name="username" class="input-lg form-control" placeholder="Indirizzo email" required autofocus value="{{ username }}">
                    <svg class="gx-icon">
                        <use xlink:href="{{ gtxConstants.ICONS_SVG_SPRITE_FILE_PATH }}#mail"></use>
                    </svg>
                </div>
            </div>
            <div class="riga  riga--icon  riga--password">
                <label for="password" class="gx-sr-only">Password</label>
                <div class="with-icon">
                    <input type="password" id="password" name="password" class="input-lg form-control" placeholder="Inserisci password" required>
                    <svg class="gx-icon">
                        <use xlink:href="{{ gtxConstants.ICONS_SVG_SPRITE_FILE_PATH }}#lock"></use>
                    </svg>
                    <a href="{{ path('app_password_recovery') }}">Password dimenticata?</a>
                </div>
            </div>
            <div class="form-account__footer clearfix">
                <input type="submit" class="btn btn-lg btn-primary invia" value="Accedi">
            </div>
        </form>
    </div>
    {% include 'base/security_gtx/footer.html.twig' %}
{% endblock %}
