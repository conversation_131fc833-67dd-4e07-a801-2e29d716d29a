<?php

declare(strict_types=1);

namespace App\DataMapper\EntityToResponse\Property;

use App\DataMapper\AbstractDataMapper;
use App\Model\Property\MandateType;
use App\Model\Response\Property\MandateTypeResponse;
use App\Utils\TranslatorTrait;
use ObjectMapper\Configuration\ObjectMapperConfig;
use Symfony\Component\Translation\TranslatorInterface;

class MandateTypeDataMapper extends AbstractDataMapper
{
    use TranslatorTrait;

    private TranslatorInterface $translator;

    public function __construct(
        TranslatorInterface $translator
    ) {
        $this->translator = $translator;
    }

    protected function getDestinationClass(): string
    {
        return MandateTypeResponse::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /**
         * @var MandateType $data
         */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(\get_class($data), $this->getDestinationClass())
            ->forMember('id', function () use ($data) {
                return $this->translateContent('db_type_of_mandate.id_', (string) $data->getId());
            })
        ;

        return $config;
    }
}
