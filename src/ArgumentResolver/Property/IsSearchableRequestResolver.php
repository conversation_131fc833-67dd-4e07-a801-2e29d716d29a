<?php

declare(strict_types=1);

namespace App\ArgumentResolver\Property;

use App\DataMapper\RequestDataToRequestModel\Property\IsSearchableDataMapper;
use App\Model\Request\Property\IsSearchableRequest;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ArgumentValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;

class IsSearchableRequestResolver implements ArgumentValueResolverInterface
{
    /**
     * @var IsSearchableDataMapper
     */
    private $isSearchableDataMapper;

    public function __construct(
        IsSearchableDataMapper $isSearchableDataMapper
    ) {
        $this->isSearchableDataMapper = $isSearchableDataMapper;
    }

    public function supports(
        Request $request,
        ArgumentMetadata $argument
    ): bool {
        return IsSearchableRequest::class === $argument->getType();
    }

    public function resolve(
        Request $request,
        ArgumentMetadata $argument
    ): \Generator {
        yield $this->isSearchableDataMapper->map($request->query->all());
    }
}
