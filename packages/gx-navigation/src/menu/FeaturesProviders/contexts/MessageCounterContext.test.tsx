import { describe, expect, it } from 'vitest';
import { BaseMenuConfig } from '../../../types';
import { MessageCounterAdapter } from './MessageCounterContext';
import { act, render, screen } from '@testing-library/react';
import { UPDATE_STATS } from '../../../events/inputTypes';
import { CountersProvider } from './CountersContext';

const menuStub: BaseMenuConfig = {
  item: {
    label: 'Test Menu',
  },
};

const TestComponent = () => {
  return (
    <MessageCounterAdapter menu={menuStub}>
      {(menu) => <>{menu.item?.notificationBadge?.number}</>}
    </MessageCounterAdapter>
  );
};

describe('MessageCounterContext', () => {
  it('Shows unread threads counter', () => {
    render(<TestComponent />, {
      wrapper({ children }) {
        return <CountersProvider>{children}</CountersProvider>;
      },
    });

    // Display counter
    act(() => {
      window.dispatchEvent(
        new CustomEvent(UPDATE_STATS, {
          detail: { newMatches: 10, unreadThreads: 5, newAgencyActiveSearches: 12 },
        }),
      );
    });

    const textElement = screen.getByText('5');

    expect(textElement).toHaveTextContent('5');

    // Clear counter
    act(() => {
      window.dispatchEvent(
        new CustomEvent(UPDATE_STATS, {
          detail: { newMatches: 10, unreadThreads: 0, newAgencyActiveSearches: 12 },
        }),
      );
    });

    expect(textElement).toHaveTextContent('');

    // Show more then 100 counter
    act(() => {
      window.dispatchEvent(
        new CustomEvent(UPDATE_STATS, {
          detail: { newMatches: 10, unreadThreads: 1000, newAgencyActiveSearches: 12 },
        }),
      );
    });

    expect(textElement).toHaveTextContent('99+');
  });
});
