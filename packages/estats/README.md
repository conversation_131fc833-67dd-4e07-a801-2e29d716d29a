# Estats

### Introduction
This is a utility internal package which only job is to **generate a valid URL** that will be then passed to `@pepita/estats-macro`'s `sendMetricRequest` function.
It works in conjunction with `@pepita/estats-macro`.

For additional information on how `estats-macro` works behind the scenes read this [documentation]((https://gitlab.pepita.io/pepita/pepita-frontend/frontend-packages/-/blob/develop/.backstage/docs/estats.macro.md?ref_type=heads)) or the [source code](https://gitlab.pepita.io/pepita/pepita-frontend/frontend-packages/-/blob/develop/packages/estats-macro/src/index.ts).

### Usage
To use it on MLS you should update the `estatsMetrics` function inside `estats.ts` file adding a constant which hold the value generated by `generateMetricsMacro` passing the path where you want to store the metric data in Graphite in form of array. Then make sure to return the value in the function `return` statement.

An example: you want to add a metric for when an api calls to `api/menu/counters` throws an error.

Step1: Individuate the path where you want to store the data in [Graphite](https://graphite-stats.pepita.io/). Usually it is inside `stats.counters.[env].sites.getrix.[app].[your_custom_path]`. In this specific example `[your_custom_path]` will be `menu.counters.fail`.

> :warning: **It is important when possible to use existing folders inside Graphite**: This is because every folder in Graphite is very heavy on disk space and creating new folders can easily fill the disk space.


Step2: Add a new value to `estatsMetrics` function:


```
export const estatsMetrics = () => {
  const gxNavigationStats = generateMetricsMacro(
    statsConfig,
    ['menu'],
    ['fail']
  );

  const countersStats = generateMetricsMacro(
    statsConfig,
    ['menu'],
    ['counters'],
    ['fail']
  );

  return {
    ...gxNavigationStats,
    ...countersStats,
  };
};

```

Step3: Use the new value to send the metric. Head in your code where you want to insert the metric request and import the required libraries.

```
import { estatsMetrics } from 'estats'
import { sendMetricRequest, ESTATS_TYPE } from '@pepita/estats'
```

Then in your error handling
```
try {
    ...
} catch () {
    const metric = estatsMetrics({
        site: "gestionale-it"
    })
    sendMetricRequest(metric['menu.counters.fail'], ESTATS_TYPE.singleCounter, 1)
}
```

Now you should see an `img` request fired by the browser when this api calls throws an error, the path of the image is where the metric will be stored.

The metrics pushed to Graphite can we viewed in [Graphana](stats.pepita.io) upon creating a custom graph.

Refer to other documentations to know how to manage, create and read graph in Graphana.
