// feature-section

@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.propertyDetail {
  &__nav {
    display: flex;
    gap: space(md);
    margin: space(lg) 0;

    &:empty {
      margin: 0;
    }
  }

  &__head {
    display: flex;
    justify-content: space-between;
    gap: space(xl);
    margin: space(lg) 0;

    span {
      min-width: fit-content;
    }

    &Subtitle {
      color: color(content-medium);
    }
  }

  &-card {
    width: 100%;
    padding: space(md);
    border: 1px solid color(border-main);
    border-radius: radius(lg);

    &--noPadding {
      padding: 0;
    }
  }

  &__map {
    display: flex;
    flex-direction: column;
    gap: space(lg);

    .gx-alert + & {
      margin-top: space(lg);
    }

    @include media('screen', '>=#{breakpoint(md)}') {
      flex-direction: row;
      justify-content: space-between;
      gap: space(sm);

      .feature-section__list {
        display: flex;
        flex-direction: column;
        width: auto;
      }
    }

    & > img {
      border-radius: radius(lg);
    }

    &__noMap {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: space(sm);
      width: 100%;
      aspect-ratio: 1.68 / 1;
      padding: space(lg);
      color: color(content-high);
      background-color: color(background-alt);
      border-radius: radius(lg);

      svg {
        font-size: 3.2rem;
      }

      span {
        font-size: 1.6rem;
        font-weight: 600;
        text-align: center;
      }

      @include media('screen', '>=#{breakpoint(md)}') {
        width: 47rem;
        height: 28rem;
      }
    }
  }

  &__note {
    display: flex;
    align-items: start;
    gap: space(sm);
    padding: space(sm);
    border-radius: radius(md);
    background-color: color(background-warning);

    svg {
      color: color(content-high);
      @include icon-size(md);
    }

    span {
      color: color(content-medium);
      @include typography(body);
    }
  }

  &__gallery {
    column-count: 1;
    gap: space(md);

    &Item {
      margin-top: space(md);
      border-radius: radius(lg);
      overflow: hidden;

      &:first-of-type {
        margin-top: 0;
      }

      img {
        width: 100%;
      }
    }

    @include media('screen', '>=#{breakpoint(xs)}') {
      &--twoColumns {
        column-count: 2;
      }
    }
  }

  &__iframe {
    &Wrapper {
      width: 100%;

      iframe {
        width: 100%;
        border: 0;
      }
    }
    &-video {
      aspect-ratio: 16 / 9;
    }
    &-virtualtour,
    &-photoplan {
      aspect-ratio: 1 / 1;
    }
  }
}

.surfaceDetail {
  width: 100%;

  svg {
    margin-bottom: space(sm);
    @include icon-size(md);
  }

  h4 {
    margin-top: 0;
    margin-bottom: space(md);
  }

  dl {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: space(sm);

    dt {
      color: color(content-medium);
    }
  }
}

.media-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: space(sm);
  min-width: 10.8rem;
  height: 100%;
  padding: space(sm) space(md);
  margin: 0;
  border: 0;
  border-radius: radius(md);
  box-shadow: elevation(fixed-top);
  background-color: color(background-action);
  color: color(content-action);
  cursor: pointer;

  span {
    font-size: 1.4rem;
    font-weight: bold;
    text-transform: capitalize;
    white-space: nowrap;
  }

  &:hover {
    box-shadow: elevation(raised);
    background: #fff;
  }

  svg {
    @include icon-size(md);
  }
}

.feature-section {
  padding: space(lg) 0;

  & + & {
    border-top: 1px solid color(border-main);
  }

  &__list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: space(md);

    &Item {
      display: flex;
      align-items: center;
      gap: space(sm);

      &--twoColumn {
        grid-column: 1 / -1;
      }

      &.is-disabled {
        opacity: 0.3;
      }

      &Content {
        .gx-body-small {
          color: color(content-medium);
        }
      }
    }
    svg {
      @include icon-size(md);
    }
  }

  &__head {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: space(lg);
    gap: space(lg);
  }
}

.related-properties {
  display: flex;
  flex-direction: column;
  gap: space(md);
  padding: space(md);

  &__item {
    display: flex;
    gap: space(md);

    &Image {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 15rem;
      height: 10.4rem;
      border-radius: space(sm);
      background-color: color(background-alt);
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }

      svg {
        color: color(content-low);
        @include icon-size(md);
      }
    }

    h4 {
      font-weight: 700;
    }

    .gx-display-subtitle {
      margin: 0;
    }

    .gx-title-2 {
      color: color(content-medium);
    }

    &Content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: space(xs);
    }

    &Icon {
      display: flex;
      gap: space(md);
      align-items: baseline;
      margin-top: space(xs);

      div {
        display: flex;
        align-items: center;
        gap: space(xs);
      }

      svg {
        @include icon-size(xs);
      }
    }
  }
  &__cta {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: space(md);
    border-top: 1px solid color(border-main);
  }
}

.property-description {
  margin-top: space(md);

  &__head {
    text-transform: uppercase;
    color: color(content-high);
  }
  &__text {
    font-weight: 400;
    color: color(content-high);
  }
}
.carousel-container {
  background-color: color(background-alt);
  border-radius: radius(lg);
  overflow: hidden;

  &__noPhoto {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: space(sm);
    padding: space(sm);
    color: color(content-high);
    height: 41.8rem;

    svg {
      font-size: 3.2rem;
    }

    span {
      font-size: 2rem;
      font-weight: 600;
    }
  }
}
