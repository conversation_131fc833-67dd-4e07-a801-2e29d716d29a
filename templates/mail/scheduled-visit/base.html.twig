<html>
<head>
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Raleway:500,700">
		<style type="text/css">
			@font-face {
				font-family: 'Raleway';
				font-style: normal;
				font-weight: 400;
				src: local('Raleway'), local('Raleway-Regular'), url(https://fonts.gstatic.com/s/raleway/v11/bIcY3_3JNqUVRAQQRNVteQ.ttf) format('truetype');
			}
			@font-face {
				font-family: 'Raleway';
				font-style: normal;
				font-weight: 400;
				src: local('Raleway'), local('Raleway-Regular'), url(https://fonts.gstatic.com/s/raleway/v11/QAUlVt1jXOgQavlW5wEfxQLUuEpTyoUstqEm5AMlJo4.woff2) format('woff2');
			}
			body {
				margin: 0 !important;
				padding: 0 !important;
				font-family: Arial,serif !important;
				min-width: 100% !important;
				-webkit-text-size-adjust: none !important;
				-ms-text-size-adjust: none !important;
				width: 100% !important;
				-webkit-font-smoothing: antialiased !important;
			}
			a:hover {
				text-decoration: underline !important;
			}
			.style-autolink a {
				color: #216A95 !important;
				text-decoration: none !important;
			}
			.style-autolink-text a {
				color: #333333 !important;
				text-decoration: none !important;
			}
			.raleway-font {
				font-family: 'Raleway', Arial, sans-serif !important;
			}
			.btn-dettagli:hover {
				background: #B22100 !important;
				border-color: #B22100 !important;
				text-decoration: none !important;
			}
			.btn-contatti:hover, .btn-dettagli-border:hover {
				background: #F2F2F2 !important;
				text-decoration: none !important;
			}
			.table-scopri a:hover, .btn-dettagli-border a:hover {
				background:  #F2F2F2 !important;
				text-decoration: none !important;
				color: #216A95 !important;
			}
			.block-banner img {
				max-width: 100% !important;
			}
			.btn-app:hover {
				background: #DDDDDD !important;
			}

			@media all and (max-width: 631px) {
				.bodyMailContent {
					width: 100% !important;
				}
				.header-doubleLogo {
					width: 100% !important;
				}
				table.col195, table.col195 img {
					width: 100% !important;
					height: auto !important;
					max-height: 270px !important;
				}
				table.col280 {
					max-width: 100% !important;
				}
				.block-annuncio .btn-dettagli, .block-annuncio .btn-dettagli-border {
					display: block !important;
				}
				table.col335 {
					width: 100% !important;
				}
				.spaceDesktopVisible {
					display: none !important;
				}
				.table-scopri {
					width: 100% !important;
					margin: 10px 0 0 !important;
				}
				.mobile-block-columns {
					width: 100% !important;
					float: none !important;
					margin: 5px 0 0 !important;
					max-width: 100% !important;
				}
				.suggestion-block table {
					width: 100% !important;
					float: none !important;
					/*display: block !important; sembra interferire con il mobile non capisco come mai */
				}
				.btn-app {
					margin: 10px 0 0 !important;
					float: none !important;
					display: block !important;
					width: auto !important;
				}
				.suggestion-block .mobile-block-columns {
					margin: 15px 0 0 !important;
				}
				.btn-contatti, .btn-dettagli, .table-scopri a, .btn-dettagli-border {
					padding: 12px 0 !important;
				}
				.table-btn-dettagli {
					width: 100% !important;
					float: none !important;
				}
				.banner_promo30 h2 {
					font-size: 20px !important;
				}
				.banner_promo30 p {
					font-size: 16px !important;
				}
				table.recap-img, table.recap-cat, table.recap-img .recap-img-mappa, table.recap-img .recap-img-cartina {
					width: 100%;
					height: auto;
				}
				.block-btn-top-premium {
					height: 40px !important;
				}
				.block-btn-top-premium td {
					height: 30px !important;
				}
				.footer-text span {
					display: block !important;
					margin: 5px 0 0 !important;
				}
				.agency-card {
					width: 100% !important;
				}
			}
			@media all and (min-width: 632px) {
				.header-logo-img {
					height: 50px!important;
				}
				.header-logo-img-pro {
					height: 50px!important;
                }
				.bodyMailContent div img {
					max-width: 100% !important;
				}
				table.col195 {
					width: 195px !important;
					height: 146px !important;
				}
				table.col195 img {
					width: 100% !important;
					max-width: 195px !important;
					height: 146px !important;
					max-height: 146px !important;
				}
				table.col335 {
					width: 335px !important;
				}
				table.recap-cat {
					width: 345px !important;
					max-width: 345px !important;
					overflow: hidden !important;
				}
				table.recap-cat table {
					display: inline-block !important;
				}
				table.recap-img {
					width: 195px !important;
					max-width: 195px !important;
					height: 145px !important;
					max-height: 145px !important;
					overflow: hidden !important;
				}
				table.recap-img .recap-img-mappa {
					width: 195px !important;
					max-width: 195px !important;
					height: 145px !important;
					max-height: 145px !important;
				}
				table.recap-img .recap-img-cartina {
					width: 175px !important;
					max-width: 175px !important;
					height: 175px !important;
					max-height: 175px !important;
				}
				.float-right {
					float: right !important;
					width: auto !important;
				}
				.block-agency-contact-info {
					max-width: 310px;
					width: auto;
				}
				.suggestion-block-btn {
					float: right !important;
				}
				.table-scopri {
					float: right !important;
				}
			}
		</style>
		<style media="print">
			@media only print {
				.footer, .header-logo, .firma-standard {display: none !important;height: 0 !important;}
				.print-no-padding { padding: 0 !important;}

			}
		</style>
</head>
<body marginwidth="0" marginheight="0" leftmargin="0" topmargin="0" style="font-family:Arial,serif;min-width:100%;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;width:100%;-webkit-font-smoothing:antialiased;margin:0;padding:0;">
	<!--[if gte mso 9]>
	{literal}
		<style type="text/css">
			table { font-size:1px; line-height:0; mso-margin-top-alt:1px;mso-line-height-rule: exactly; }
			* { mso-line-height-rule: exactly; }
			.ExternalClass * {
				mso-line-height-rule: exactly;
				line-height: 100% !important;
			}
			.raleway-rollback {
				font-family: Tahoma,Trebuchet MS !important;
			}
			.btn-outlook a {
                mso-hide: all !important;
            }
		</style>
	{/literal}
	<![endif]-->
	<table class="bodyMail" align="center" marginwidth="0" marginheight="0" leftmargin="0" topmargin="0" bgcolor="#fafafa" width="100%" style="mso-table-lspace:0pt;font-family:Arial,serif;min-width:100%;-webkit-text-size-adjust:none;-ms-text-size-adjust:none;width:100%;-webkit-font-smoothing:antialiased;margin:0;padding:0;border-collapse:collapse;">
    {% block body %}{% endblock %}
  </table>
</body>
</html>
