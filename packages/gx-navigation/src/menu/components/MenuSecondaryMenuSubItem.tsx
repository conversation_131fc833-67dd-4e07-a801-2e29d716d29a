import { Ref, forwardRef } from 'react';
import { MenuConfig } from '../../types';
import { NotificationBadge } from '@gx-design/notification-badge';
import clsx from 'clsx';
import { trans } from '../../shared/translations';
import { Badge } from '@gx-design/badge';

type MenuSecondaryMenuSubItemProps = {
  subMenuItem: MenuConfig['item'];
  href: string;
  isActive?: boolean;
};

export const MenuSecondaryMenuSubItem = forwardRef(
  (
    { subMenuItem, href, isActive }: MenuSecondaryMenuSubItemProps,
    ref: Ref<HTMLLIElement> | undefined,
  ) => {
    if (!subMenuItem || subMenuItem.hidden) {
      return null;
    }

    return (
      <li ref={ref}>
        <a
          className={clsx('gx-navigation-sidemenu__subMenuItem', { 'is-active': isActive })}
          href={href}
        >
          <span>{subMenuItem.label}</span>
          {subMenuItem.notificationBadge && (
            <NotificationBadge number={subMenuItem.notificationBadge.number} />
          )}
          {subMenuItem.badge && (
            <Badge style={subMenuItem.badge.style} text={trans(subMenuItem.badge.text)} />
          )}
        </a>
      </li>
    );
  },
);

MenuSecondaryMenuSubItem.displayName = 'MenuSecondaryMenuSubItem';
