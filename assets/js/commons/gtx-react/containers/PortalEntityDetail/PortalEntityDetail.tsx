import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { useQuery } from '@tanstack/react-query';
import { FC } from 'react';

import { SHOW_CADASTRAL_DATA } from 'constants/property';
import { Loader } from 'gtx-react/components';
import { IPropertyListItem } from 'types/api/property';
import { RealEstateCategory } from 'types/portalModal';
import { PortalEntityDetailContent } from './PortalEntityDetailContent';
import { canObtainCadastralData, getEntityDetail } from './api';

const CACHE_DETAIL_KEY_PREFIX = 'property_detail_';

const QUERY_CONFIG = {
    refetchIntervalInBackground: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: 600 * 1000,
};

export type PortalEntityDetailProps = {
    propertyId: IPropertyListItem['id'];
    category: RealEstateCategory;
    onError: () => void;
};

export const PortalEntityDetail: FC<PortalEntityDetailProps> = ({
    propertyId,
    category,
    onError,
}) => {
    const { showNotification } = useNotifyContext();

    const { isLoading, data } = useQuery({
        queryKey: [`${CACHE_DETAIL_KEY_PREFIX}${propertyId}`],
        queryFn: () =>
            getEntityDetail(propertyId)
                .then((res) => res.data)
                .catch(() => {
                    onError();
                    showNotification({
                        type: 'error',
                        message: trans('label.error_durin_operation'),
                    });
                }),
        ...QUERY_CONFIG,
    });

    const { data: cadastralDataButton, fetchStatus: cadastralFetchStatus } =
        useQuery({
            queryKey: [`can_obtain_cadatral_${propertyId}`],
            queryFn: () =>
                canObtainCadastralData(propertyId)
                    .then((res) => {
                        if (res.status === 'success') {
                            if (!res.data?.url) {
                                return false;
                            }

                            return {
                                url: res.data.url,
                                label: trans('label.obtain_cadastral_youdomus'),
                            };
                        } else {
                            throw new Error('Failed to fetch Cadastral data.');
                        }
                    })
                    .catch((error) => {
                        console.error(error);
                    }),
            ...QUERY_CONFIG,
            enabled: category !== 'newConstruction' && SHOW_CADASTRAL_DATA,
        });

    return isLoading || cadastralFetchStatus === 'fetching' || !data ? (
        <div className="gx-modal__bodyLoading">
            <Loader loading={true} fixedOverlay={false} centered={true} />
        </div>
    ) : (
        <PortalEntityDetailContent
            cadastralDataButton={cadastralDataButton ?? false}
            propertyData={data}
            category={category}
        />
    );
};
