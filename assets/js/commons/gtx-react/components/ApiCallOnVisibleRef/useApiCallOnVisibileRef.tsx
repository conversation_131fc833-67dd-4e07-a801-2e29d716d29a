import { HttpResponse } from "@pepita/http"
import { nanoid } from "@reduxjs/toolkit"
import { useIsInViewPort } from "gtx-react/hooks/useIsInViewPort";
import React, { useEffect, useMemo, useRef, useState } from "react"
import { ApiAbortObject } from "./ApiCallOnVisibleProvider";
import { useApiCallOnVisibleContext } from "./useApiCallOnVisibleContext";


export const useApiCallOnVisibileRef = (
    ref: React.MutableRefObject<HTMLElement>,
    apiCall: ()=> {
        raw: () => Promise<HttpResponse>;
        json: () => Promise<any>;
        abort(): void;
    },
    options?: {
        abortOnOutOfView?: boolean
    }
    ) => {
    const isVisible = useIsInViewPort(ref, 150)

    const [results, setResults] = useState(null)
    const [isLoading, setIsLoading] = useState(false)

    const { addAbortObj, removeAbortObj, isLocked } = useApiCallOnVisibleContext()

    const abortRef = useRef<ApiAbortObject|null>(null)

    const itemNanoId = useMemo(() => nanoid(), [])

    useEffect(()=> {
        if (results != null || isLocked.current) return;

        if(isVisible && !isLoading){
            setIsLoading(true)
            /**
             * volontariamente non gestisco l'errore
             * se va in errore rimane il loader all'infinito
             * TODO: verificare se il comportamento è corretto
             **/
            const matchCall = apiCall()

            if(abortRef.current){
                abortRef.current.abort()
                removeAbortObj(abortRef.current)
            }

            abortRef.current = {
                id: itemNanoId,
                abort: () => matchCall.abort()
            }

            addAbortObj(abortRef.current)

            matchCall.json().then(resp => resp.data).then(data => {
                setResults(data)
                setIsLoading(false)
                if(abortRef.current){
                    removeAbortObj(abortRef.current)
                }
                abortRef.current = null
            }).catch(error => {
                setIsLoading(false)
            })
        }

        if(!isVisible && options?.abortOnOutOfView){
            if(abortRef.current){
                // console.log('aborting matches call by not visibility!')
                removeAbortObj(abortRef.current)
                setIsLoading(false)
                abortRef.current.abort()
            }
        }

        return () => {
            if(abortRef.current && options?.abortOnOutOfView){
                // console.log('aborting matches call by unmount!')
                removeAbortObj(abortRef.current)
                setIsLoading(false)
                abortRef.current.abort()
            }
        }

    }, [isVisible, isLocked.current])

    return {results, isLoading}
}
