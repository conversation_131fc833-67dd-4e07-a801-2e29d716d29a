import { SummaryItem } from '@gx-design/summary-item';
import { Accordion, AccordionItem } from '@gx-design/accordion';
import { IPropertyDetail } from 'types/api/property';
import { trans } from '@pepita-i18n/babelfish';
import gtxConstants from '@getrix/common/js/gtx-constants';
import {
    PROPERTY_IMAGE_PLACEHOLDER,
    PROPERTY_ITEM_VOID_CONTENT,
} from 'constants/property';
import { getAppLocale } from 'lib/languages';
import { Icon } from '@gx-design/icon';
import { Button } from '@gx-design/button';
import { EnergeticTag } from '@gx-design/energetic-tag';
import { formatEnergyClass } from 'lib/propertyEnergyClass';

type PropertyDetailSectionsProps = {
    propertyData: IPropertyDetail;
    sections: any;
    cadastralDataButton?:
        | false
        | {
              url: string;
              label: string;
          };
};

const specialSections = [
    'consistences',
    'energyClass',
    'projectEnergyClass',
    'auctionSales',
    'auctionProperties',
    'newConstructionProperties',
];

type SpecialSection =
    | 'consistences'
    | 'energyClass'
    | 'projectEnergyClass'
    | 'auctionSales'
    | 'auctionProperties'
    | 'newConstructionProperties';

const getValueByPath = (data, path) => {
    let result = path.reduce((data, item) => (data ? data[item] : null), data);

    if (!result) {
        return PROPERTY_ITEM_VOID_CONTENT;
    }

    return result;
};

const formatSurface = (value: string) => {
    return new Intl.NumberFormat(getAppLocale(), {
        minimumFractionDigits: 1,
        maximumFractionDigits: 1,
    }).format(parseInt(value));
};

function renderEnergyClassSection(
    energyClass: IPropertyDetail['properties']['energyClass']
) {
    if (!energyClass || energyClass === PROPERTY_ITEM_VOID_CONTENT) {
        return (
            <div className="gx-row">
                <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                    <div className="gx-box-row">
                        <SummaryItem label={trans('label.energy_class')}>
                            <span>{PROPERTY_ITEM_VOID_CONTENT}</span>
                        </SummaryItem>
                    </div>
                </div>
            </div>
        );
    }

    if (energyClass.ipeWaiting) {
        return <span>{energyClass.ipeWaiting}</span>;
    }

    if (energyClass.ipeExempt) {
        return <span>{energyClass.ipeExempt}</span>;
    }

    if (energyClass.customIpe) {
        return (
            <div className="gx-row">
                <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                    <div className="gx-box-row">
                        <SummaryItem
                            label={trans('label.energy_performance_1')}
                        >
                            <span
                                dangerouslySetInnerHTML={{
                                    __html: energyClass.energyPerformance,
                                }}
                            ></span>
                        </SummaryItem>
                    </div>
                </div>
            </div>
        );
    }

    if (energyClass.energyPerformanceNotAvailable) {
        return (
            <div className="gx-row">
                <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                    <div className="gx-box-row">
                        <SummaryItem
                            label={trans('label.energy_performance_1')}
                        >
                            <span>{PROPERTY_ITEM_VOID_CONTENT}</span>
                        </SummaryItem>
                    </div>
                </div>
            </div>
        );
    }

    if (energyClass.energyPerformanceNotAvailable) {
        return (
            <div className="gx-row">
                <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                    <div className="gx-box-row">
                        <SummaryItem
                            label={trans('label.energy_performance_1')}
                        >
                            <span>{PROPERTY_ITEM_VOID_CONTENT}</span>
                        </SummaryItem>
                    </div>
                </div>
            </div>
        );
    }

    if (energyClass.isRenewable) {
        return (
            <div className="gx-row">
                <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                    <div className="gx-box-row">
                        <SummaryItem label={trans('label.energy_class')}>
                            <EnergeticTag
                                score={formatEnergyClass(energyClass)}
                            />
                        </SummaryItem>
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                    <div className="gx-box-row">
                        <SummaryItem
                            label={trans('label.not_renewable_global_ep')}
                        >
                            <span
                                dangerouslySetInnerHTML={{
                                    __html: energyClass.energyPerformance,
                                }}
                            ></span>
                        </SummaryItem>
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                    <div className="gx-box-row">
                        <SummaryItem label={trans('label.renewable_global_ep')}>
                            <span
                                dangerouslySetInnerHTML={{
                                    __html: energyClass.renewableIpe,
                                }}
                            ></span>
                        </SummaryItem>
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                    <div className="gx-box-row">
                        <SummaryItem
                            label={trans('label.energy_performance_winter_2')}
                        >
                            <span>
                                {energyClass.winterEnergyPerformance ??
                                    PROPERTY_ITEM_VOID_CONTENT}
                            </span>
                        </SummaryItem>
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                    <div className="gx-box-row">
                        <SummaryItem
                            label={trans('label.energy_performance_summer_2')}
                        >
                            <span>
                                {energyClass.summerEnergyPerformance ??
                                    PROPERTY_ITEM_VOID_CONTENT}
                            </span>
                        </SummaryItem>
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                    <div className="gx-box-row">
                        <SummaryItem label={trans('label.building_energy_0')}>
                            <span>{energyClass.zeroEnergyBuilding}</span>
                        </SummaryItem>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="gx-row">
            <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                <div className="gx-box-row">
                    <SummaryItem label={trans('label.energy_class')}>
                        <EnergeticTag score={formatEnergyClass(energyClass)} />
                    </SummaryItem>
                </div>
            </div>
            <div className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3">
                <div className="gx-box-row">
                    <SummaryItem label={trans('label.energy_performance_1')}>
                        <span
                            dangerouslySetInnerHTML={{
                                __html: energyClass.energyPerformance,
                            }}
                        ></span>
                    </SummaryItem>
                </div>
            </div>
        </div>
    );
}

function renderConsistencesSection(
    consistences: IPropertyDetail['properties']['consistences']
) {
    if (consistences === PROPERTY_ITEM_VOID_CONTENT) {
        return PROPERTY_ITEM_VOID_CONTENT;
    }

    return (
        <div className="gx-table-dataWrap">
            <table className="gx-table-data">
                <thead>
                    <tr>
                        <th>{trans('label.consistency')}</th>
                        <th>{trans('label.floor')}</th>
                        <th>{trans('label.surface')}</th>
                        <th>{trans('label.type')}</th>
                        <th>{trans('label.counting_at')}</th>
                        <th>{trans('label.commercial')}</th>
                    </tr>
                </thead>
                <tbody>
                    {consistences.map((consistence, index) => (
                        <tr key={`consistence_${index}`}>
                            <td>{consistence.name}</td>
                            <td>{consistence.floor}</td>
                            <td>{consistence.surfaceLabel}</td>
                            <td>{consistence.type}</td>
                            <td>{consistence.weight}</td>
                            <td>{consistence.commercialSurfaceLabel}</td>
                        </tr>
                    ))}
                    <tr className="gx-table-data__summary">
                        <td colSpan={5}>{trans('label.main_total')}</td>
                        <td>
                            {formatSurface(
                                consistences
                                    .filter((consistence) => consistence.main)
                                    .reduce((previousValue, currentValue) => {
                                        return (
                                            previousValue +
                                            currentValue.commercialSurface
                                        );
                                    }, 0)
                            )}{' '}
                            {gtxConstants('SURFACE_UNIT_MEASUREMENT')}
                        </td>
                    </tr>
                    <tr className="gx-table-data__summary">
                        <td colSpan={5} className="">
                            {trans('label.commercial_total')}
                        </td>
                        <td>
                            {formatSurface(
                                consistences.reduce(
                                    (previousValue, currentValue) => {
                                        return parseInt(
                                            previousValue +
                                                currentValue.commercialSurface
                                        );
                                    },
                                    0
                                )
                            )}{' '}
                            {gtxConstants('SURFACE_UNIT_MEASUREMENT')}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    );
}

function renderAuctionSalesSection(
    auctionSales: IPropertyDetail['auction']['sales']
) {
    if (
        !auctionSales ||
        !auctionSales.length ||
        auctionSales === PROPERTY_ITEM_VOID_CONTENT
    ) {
        return <span>{trans('label.no_details')}</span>;
    }

    return (
        <div className="gx-table-dataWrap">
            <table className="gx-table-data">
                <thead>
                    <tr>
                        <th>{trans('label.date')}</th>
                        <th>{trans('label.type')}</th>
                        <th>{trans('label.modality')}</th>
                        <th>{trans('label.base')}</th>
                        <th>{trans('label.outcome')}</th>
                    </tr>
                </thead>
                <tbody>
                    {auctionSales.map((saleData, index) => (
                        <tr key={`auction_sales_${index}`}>
                            <td>{saleData.date}</td>
                            <td>
                                {saleData.type ?? PROPERTY_ITEM_VOID_CONTENT}
                            </td>
                            <td>
                                {saleData.mode ?? PROPERTY_ITEM_VOID_CONTENT}
                            </td>
                            <td>
                                {saleData.basePrice ??
                                    PROPERTY_ITEM_VOID_CONTENT}
                            </td>
                            <td>
                                {saleData.status ?? PROPERTY_ITEM_VOID_CONTENT}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}

function renderProperties(properties: IPropertyDetail['properties']) {
    if (
        !properties ||
        !properties.length ||
        properties === PROPERTY_ITEM_VOID_CONTENT
    ) {
        return <span>{trans('label.no_properties')}</span>;
    }

    return (
        <div className="gx-table-dataWrap">
            <table className="gx-table-data">
                <thead>
                    <tr>
                        <th>{trans('label.photo')}</th>
                        <th>{trans('label.reference_short')}</th>
                        <th>{trans('label.typology')}</th>
                        <th>{trans('label.surface')}</th>
                        <th>{trans('label.price')}</th>
                        <th>{trans('label.rooms')}</th>
                        <th>{trans('label.floor')}</th>
                    </tr>
                </thead>
                <tbody>
                    {properties.map((property, index) => (
                        <tr key={`property_${index}`}>
                            <td>
                                <div className="gx-property-item">
                                    <div className="gx-property-item__pic">
                                        <img
                                            src={
                                                property.imagesList
                                                    ? property.imagesList[0].url
                                                    : PROPERTY_IMAGE_PLACEHOLDER
                                            }
                                            loading="lazy"
                                        />
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>{property.id}</div>
                                <div>{property?.reference}</div>
                            </td>
                            <td>
                                <div>{property.category}</div>
                                <div>{property?.subTypology}</div>
                            </td>
                            <td>
                                {property.surface ?? PROPERTY_ITEM_VOID_CONTENT}
                            </td>
                            <td>
                                {property.price ?? PROPERTY_ITEM_VOID_CONTENT}
                            </td>
                            <td>
                                {property?.composition?.rooms ??
                                    PROPERTY_ITEM_VOID_CONTENT}
                            </td>
                            <td>
                                {property?.features?.floor ??
                                    PROPERTY_ITEM_VOID_CONTENT}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}

const renderSpecialSection = (sectionData, type: SpecialSection) => {
    switch (type) {
        case 'consistences':
            return renderConsistencesSection(sectionData);
        case 'energyClass':
        case 'projectEnergyClass':
            return renderEnergyClassSection(sectionData);
        case 'auctionSales':
            return renderAuctionSalesSection(sectionData);
        case 'auctionProperties':
        case 'newConstructionProperties':
            return renderProperties(sectionData);
    }
};

export const PropertyDetailSections: React.FC<PropertyDetailSectionsProps> = ({
    propertyData,
    sections,
    cadastralDataButton,
}) => {
    if (!sections) {
        return null;
    }

    const handleObtainCadastralDataClick = () => {
        if (
            typeof cadastralDataButton === 'object' &&
            'url' in cadastralDataButton
        ) {
            window.open(cadastralDataButton.url, '_blank');
        }
    };

    const renderObtainCadastralDataButton = (section) => {
        if (
            (section.name === 'cadastral' ||
                section.name === 'auctionProperties') &&
            cadastralDataButton
        ) {
            return (
                <Button
                    onClick={handleObtainCadastralDataClick}
                    href={cadastralDataButton.url}
                >
                    <Icon name="youdomus" />
                    <span>{cadastralDataButton.label}</span>
                </Button>
            );
        }
    };

    return sections?.map((section, index) => (
        <Accordion key={`accordion_${index}`}>
            <AccordionItem
                title={section.title}
                cta={renderObtainCadastralDataButton(section)}
            >
                <>
                    {specialSections.includes(section.name) ? (
                        renderSpecialSection(
                            getValueByPath(propertyData, section.data[0].path),
                            section.name
                        )
                    ) : (
                        <div className="gx-row">
                            {section.data.map((item) =>
                                !item?.validFor ||
                                (item.validFor.path &&
                                    item.validFor?.value ===
                                        getValueByPath(
                                            propertyData,
                                            item.validFor.path
                                        )) ? (
                                    <div
                                        className="gx-col-xs-12 gx-col-sm-4 gx-col-md-3"
                                        key={`accordion_item_${item.path}`}
                                    >
                                        <div className="gx-box-row">
                                            <SummaryItem label={item.label}>
                                                <span>
                                                    {getValueByPath(
                                                        propertyData,
                                                        item.path
                                                    )}
                                                </span>
                                            </SummaryItem>
                                        </div>
                                    </div>
                                ) : null
                            )}
                        </div>
                    )}
                </>
            </AccordionItem>
        </Accordion>
    ));
};
