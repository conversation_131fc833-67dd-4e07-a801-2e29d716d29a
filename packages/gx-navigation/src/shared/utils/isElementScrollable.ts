export const isElementScrollable = (element: HTMLElement) => {
  const computedStyle = getComputedStyle(element);

  const isScrollableY =
    (computedStyle.overflowY === 'auto' || computedStyle.overflowY === 'scroll') &&
    element.scrollHeight > element.clientHeight;
  const isScrollableX =
    (computedStyle.overflowX === 'auto' || computedStyle.overflowX === 'scroll') &&
    element.scrollWidth > element.clientWidth;

  return {
    isScrollableY,
    isScrollableX,
  };
};
