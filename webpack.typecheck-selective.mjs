import chokidar from 'chokidar';
import dotenv from 'dotenv';
import { spawn } from 'node:child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import webpack from 'webpack';
import { merge } from 'webpack-merge';
import common, { basicEntries } from './webpack.common.mjs';
import { getEntriesCli } from './webpack.selective_common.mjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({
    path: path.join(__dirname, '.env.it'),
});

(async (env = {}, argv) => {
    const entries = await getEntriesCli(env);

    const mergedConfig = merge(
        {
            ...common,
            entry: Object.assign({}, entries, basicEntries),
        },
        {
            mode: 'development',
        }
    );

    const webpackInstance = webpack(mergedConfig).watch(
        undefined,
        (err, stats) => {
            console.log(
                stats.toString({
                    colors: true,
                    chunks: true,
                })
            );

            // listener
            spawn(
                'npx',
                [
                    'tsc',
                    '--noEmit',
                    '-p',
                    'incremental-typecheck.tsconfig.json',
                    '--skipLibCheck',
                ],
                { stdio: 'inherit' }
            );
        }
    );

    chokidar.watch('include-only.tsconfig.json').on('change', () => {
        console.log(
            '\x1b[36m%s\x1b[0m',
            '📂 include-only.tsconfig.json changed!'
        );
        webpackInstance.invalidate();
    });

    return mergedConfig;
})();
