import { ActionList, ActionListItem } from '@gx-design/action-list';
import { Button } from '@gx-design/button';
import { Dropdown, DropdownProvider } from '@gx-design/dropdown';
import { Icon } from '@gx-design/icon';
import { useMediaMatch } from '@gx-design/use-media-match';
import { trans } from '@pepita-i18n/babelfish';
import { useQuery, useSuspenseQuery } from '@tanstack/react-query';
import clsx from 'clsx';
import { ENDPOINTS } from 'constants/property';
import { Formik } from 'formik';
import { DropdownCalendarRangeInput } from 'gtx-react/components/ContentDropdown/DropdownCalendarRangeInput/DropdownCalendarRangeInput';
import { createValidationSchema } from 'gtx-react/components/ContentDropdown/DropdownCalendarRangeInput/utils';
import { DropdownRangeInput } from 'gtx-react/components/ContentDropdown/DropdownRangeInput/DropdownRangeInput';
import { priceRangeFormatter } from 'gtx-react/components/ContentDropdown/DropdownRangeInput/utils';
import { ScrollableRow } from 'gtx-react/components/tanstack-table/ScrollableRow';
import { agencyQueryOptions } from 'lib/REST/requests/api/agencies/query-factory';
import { createGetAgentsQueryOptions } from 'lib/REST/requests/api/agents/query-factory';
import { createCategoriesQueryOptions } from 'lib/REST/requests/api/lookup/properties/categories/query-factory';
import { createContractsQueryOption } from 'lib/REST/requests/immobili/lookup/query-factory';
import { getMeQueryOptions } from 'lib/REST/requests/rest/profile/query-factory';
import { ucFirst } from 'lib/strings-formatter';
import { PERFORMANCE_FILTER } from '../../../../portal-properties/constants';
import { PropertyStatus } from '../../../types';
import {
    getDefaultStatusForSoldAndRent,
    getStatusOptionsForSoldAndRent,
    getVisibilities,
} from '../../../utils';
import { useConfigContext } from '../../ConfigProvider';
import { useLoggedUserContext } from '../../LoggedUserProvider';
import { PropertyListAutocomplete } from './PropertyListAutoComplete';
import { PropertyFilters } from './types';
import { DEFAULT_PRICE_OPTIONS } from 'gtx-react/components/ContentDropdown/DropdownRangeInput/constants';
import { useRef } from 'react';
import { ContentDropdownProvider } from 'gtx-react/components/ContentDropdown/ContentDropdownContext';

const validationDateSchema = createValidationSchema({
    from: 'mandate_from',
    to: 'mandate_to',
});

const ESTATES_TYPOLOGY = {
    '': trans('label.everyone'),
    property: trans('label.estates'),
    auction: trans('label.auction_estates'),
    ['new_construction']: trans('label.new_buildings'),
};

const ESTATES_TYPOLOGY_KEY_VALUES = {
    sold: { property: trans('label.estates') },
    active: ESTATES_TYPOLOGY,
    archived: ESTATES_TYPOLOGY,
    favourite: ESTATES_TYPOLOGY,
    draft: ESTATES_TYPOLOGY,
};

/**
 * Shows as selected a filters with one or two options
 */
export function getDefaultQuickFilterLabel<T extends { value: string }>(props: {
    items: Array<T>;
    value: string;
}) {
    const elements = props.items.filter((item) => item.value !== '');

    if (elements.length === 1) {
        return `${elements[0]?.value}`;
    }

    return props.value;
}

// TODO: could be extrapolated as a reusable component
export function QuickFilterLabel(props: {
    value?: string | string[];
    default: string;
    keyPair: Record<string, string>;
}) {
    const { value, default: defaultValue } = props;

    if (!value) {
        return defaultValue;
    }

    let label: string = '';

    if (Array.isArray(value) && value.length > 1) {
        const all = value.reduce<Array<string>>((acc, curr) => {
            if (props.keyPair[curr]) {
                acc.push(props.keyPair[curr]);
            }

            return acc;
        }, []);

        const firstValue = all.splice(0, 1);

        if (firstValue) {
            label = `${firstValue} +${all.length}`;
        }
    } else if (typeof value === 'string') {
        const match = props.keyPair[value];

        if (match) {
            label = match;
        }
    }

    return label ? label : defaultValue;
}

/**
 * Remove cityId and zones from filters to avoid including in the c
 */
const onlyVisibleFilters = (filters: PropertyFilters) => {
    const {
        city,
        address,
        zones,
        code,
        ref,
        term,
        province_id: provinceId,
        region_id: regionId,
        ...rest
    } = filters;
    return rest;
};

function ResetFiltersButton(props: {
    filters: Record<string, unknown>;
    resetFilters: () => void;
}) {
    if (
        Object.values(props.filters).every((value) => {
            return value === '';
        })
    ) {
        return null;
    }

    return (
        <Button
            className="crm-filters-bar__reset"
            onClick={props.resetFilters}
            variant="ghost"
            size="small"
        >
            {trans('label.remove_filters')}
        </Button>
    );
}

export const PropertyListQuickFilters = (props: {
    filters: PropertyFilters;
    setFilters: (filters: PropertyFilters) => void;
    resetFilters: () => void;
    onOpenFilters: () => void;
    status: PropertyStatus;
}) => {
    const {
        performanceFeatureEnabled,
        appLocale,
        secretPropertyEnabled,
        isGetrix,
        adPortal,
    } = useConfigContext();
    const { isFromBackoffice } = useLoggedUserContext();
    const filtersValues = Object.values(onlyVisibleFilters(props.filters));
    const selectedFilters = filtersValues.filter((f) => f).length;
    const isExtraLargeDesktop = useMediaMatch('extraLargeDesktop');

    const categoriesQuery = useSuspenseQuery({
        ...createCategoriesQueryOptions({
            query: {
                excludedCategories: [
                    window.gtxConstants.CATEGORIA_PROGETTI,
                    window.gtxConstants.CATEGORIA_NUOVE_COSTRUZIONI,
                    window.gtxConstants.CATEGORIA_ASTE,
                ],
            },
        }),
        select(data) {
            return [{ value: '', label: trans('label.all') }].concat(data);
        },
    });

    const contractsQuery = useQuery({
        ...createContractsQueryOption({
            query: { value: props.filters.category },
        }),
        select(data) {
            return [{ value: '', label: trans('label.everyone') }].concat(data);
        },
    });

    const agentsQuery = useQuery({
        ...createGetAgentsQueryOptions({ query: { status: props.status } }),
        select(data) {
            return [{ label: trans('label.everyone'), value: '' }].concat(
                data.map((item) => ({
                    label: `${item.firstname} ${item.lastname}`,
                    value: item.id.toString(),
                }))
            );
        },
    });

    const typologyEntries = Object.entries(
        ESTATES_TYPOLOGY_KEY_VALUES[props.status]
    );

    const agencyQuery = useQuery({
        ...agencyQueryOptions,
        select: (data) => data.isPerformancesModuleOn,
    });

    const meQuery = useSuspenseQuery({
        ...getMeQueryOptions,
        select: (data) => ({
            isNewConstructionEnabled: data.modules['Nuove Costruzioni'].enabled,
        }),
    });

    const isPerformanceVisible =
        (performanceFeatureEnabled || isFromBackoffice) && agencyQuery.data;

    const mandateIsDisabled =
        props.filters.typology !== 'auction' && props.filters.typology !== '';

    const isNotAuctionOrNewConstruction =
        props.filters.typology !== 'auction' &&
        props.filters.typology !== 'new_construction';

    const isPerformanceEnabled = isNotAuctionOrNewConstruction;
    const filtersBarRef = useRef<HTMLDivElement>(null);

    return (
        <DropdownProvider dropdownAnchorElement={filtersBarRef}>
            <ContentDropdownProvider
                value={{ dropdownAnchorElement: filtersBarRef }}
            >
                <div className="crm-filters-bar" ref={filtersBarRef}>
                    <PropertyListAutocomplete
                        status={props.status}
                        filters={props.filters}
                        setFilters={props.setFilters}
                    />

                    <Button
                        onClick={props.onOpenFilters}
                        variant="chip"
                        className={clsx('crm-filters-bar__allFilters', {
                            'is-selected': filtersValues.some((value) => value),
                        })}
                    >
                        <Icon name="sliders" />
                        <span>
                            {ucFirst(trans('label.filters'))}{' '}
                            {selectedFilters ? `(${selectedFilters})` : ''}{' '}
                        </span>
                    </Button>

                    {!isExtraLargeDesktop ? (
                        <ScrollableRow wrapperClassname="crm-filters-barSlider">
                            {/* Typology Filter */}
                            <Dropdown
                                position="bottomLeft"
                                buttonClassName={clsx(
                                    'crm-filters-bar__filter',
                                    {
                                        'is-selected':
                                            props.filters.typology ||
                                            typologyEntries.length === 1,
                                    }
                                )}
                                disabled={
                                    categoriesQuery.isLoading ||
                                    typologyEntries.length === 1
                                }
                                buttonVariant="chip"
                                showCaret
                                buttonContent={
                                    <>
                                        <Icon name="home" />
                                        <span>
                                            <QuickFilterLabel
                                                default={`${trans(
                                                    'label.type'
                                                )} ${trans('label.property')}`}
                                                keyPair={
                                                    ESTATES_TYPOLOGY_KEY_VALUES[
                                                        props.status
                                                    ]
                                                }
                                                value={getDefaultQuickFilterLabel(
                                                    {
                                                        items:
                                                            typologyEntries.map(
                                                                ([
                                                                    value,
                                                                    label,
                                                                ]) => ({
                                                                    label,
                                                                    value,
                                                                })
                                                            ) ?? [],
                                                        value: props.filters
                                                            .typology,
                                                    }
                                                )}
                                            />
                                        </span>
                                    </>
                                }
                            >
                                <ActionList>
                                    {typologyEntries.map(([value, label]) => (
                                        <ActionListItem
                                            key={value}
                                            active={
                                                props.filters.typology === value
                                            }
                                            onClick={() => {
                                                if (
                                                    !meQuery.data
                                                        .isNewConstructionEnabled &&
                                                    value === 'new_construction'
                                                ) {
                                                    return window.open(
                                                        ENDPOINTS.newConstructionAdd,
                                                        '_blank'
                                                    );
                                                }
                                                props.setFilters({
                                                    ...props.filters,
                                                    typology: value,
                                                    category: '',
                                                    contract: '',
                                                    ['price_from']: '',
                                                    ['price_to']: '',
                                                    ['mandate_from']: '',
                                                    ['mandate_to']: '',
                                                });
                                            }}
                                            text={label}
                                        />
                                    )) ?? []}
                                </ActionList>
                            </Dropdown>

                            {/* Price Filter */}
                            <Formik
                                initialValues={{
                                    // eslint-disable-next-line camelcase
                                    price_from: props.filters.price_from || '',
                                    // eslint-disable-next-line camelcase
                                    price_to: props.filters.price_to || '',
                                }}
                                onSubmit={(values) => {
                                    props.setFilters({
                                        ...props.filters,
                                        ['price_from']: values.price_from,
                                        ['price_to']: values.price_to,
                                    });
                                }}
                                enableReinitialize
                            >
                                <DropdownRangeInput
                                    minInputName="price_from"
                                    maxInputName="price_to"
                                    variant="chip"
                                    submitLabel={trans('label.see_ads')}
                                    options={DEFAULT_PRICE_OPTIONS}
                                    placeholder={trans('label.price')}
                                    placeholderMin={trans('label.from')}
                                    placeholderMax={trans('label.to')}
                                    icon="money"
                                    selected={
                                        Boolean(props.filters.price_from) ||
                                        Boolean(props.filters.price_to)
                                    }
                                    formatter={priceRangeFormatter}
                                />
                            </Formik>
                            {/* Category Filter */}
                            <Dropdown
                                className="crm-filters-barSlider__dropdownMaxHeight"
                                disabled={
                                    categoriesQuery.isLoading ||
                                    !isNotAuctionOrNewConstruction
                                }
                                position="bottomLeft"
                                buttonClassName={clsx(
                                    'crm-filters-bar__filter',
                                    {
                                        'is-selected': props.filters.category,
                                    }
                                )}
                                buttonVariant="chip"
                                showCaret
                                buttonContent={
                                    <>
                                        <Icon name="building" />
                                        <span>
                                            <QuickFilterLabel
                                                default={trans(
                                                    'label.category'
                                                )}
                                                keyPair={
                                                    categoriesQuery.data?.reduce<
                                                        Record<string, string>
                                                    >(
                                                        (acc, category) => ({
                                                            ...acc,
                                                            [category.value]:
                                                                category.label,
                                                        }),
                                                        {}
                                                    ) ?? {}
                                                }
                                                value={props.filters.category}
                                            />
                                        </span>
                                    </>
                                }
                            >
                                <ActionList>
                                    {categoriesQuery.data?.map((category) => (
                                        <ActionListItem
                                            key={category.value}
                                            active={Boolean(
                                                props.filters.category ===
                                                    category.value
                                            )}
                                            onClick={() => {
                                                props.setFilters({
                                                    ...props.filters,
                                                    category: category.value,
                                                    contract: '',
                                                    performanceRelativeIndexes:
                                                        '',
                                                });
                                            }}
                                            text={category.label}
                                        />
                                    )) ?? []}
                                </ActionList>
                            </Dropdown>
                            {/* Contract Filter */}
                            <Dropdown
                                position="bottomLeft"
                                buttonClassName={clsx(
                                    'crm-filters-bar__filter',
                                    {
                                        'is-selected':
                                            Boolean(props.filters.contract) ||
                                            contractsQuery.data?.length === 2,
                                    }
                                )}
                                disabled={
                                    contractsQuery.isLoading ||
                                    contractsQuery.data?.length === 2 ||
                                    !isNotAuctionOrNewConstruction
                                }
                                buttonVariant="chip"
                                showCaret
                                buttonContent={
                                    <>
                                        <Icon name="certificate" />
                                        <span>
                                            <QuickFilterLabel
                                                value={getDefaultQuickFilterLabel(
                                                    {
                                                        items:
                                                            contractsQuery.data ??
                                                            [],
                                                        value: props.filters
                                                            .contract,
                                                    }
                                                )}
                                                default={trans(
                                                    'label.contract'
                                                )}
                                                keyPair={
                                                    contractsQuery.data?.reduce(
                                                        (acc, contract) => ({
                                                            ...acc,
                                                            [contract.value]:
                                                                contract.label,
                                                        }),
                                                        {}
                                                    ) ?? {}
                                                }
                                            />
                                        </span>
                                    </>
                                }
                            >
                                <ActionList>
                                    {contractsQuery.data?.map((contract) => (
                                        <ActionListItem
                                            key={contract.value}
                                            active={
                                                props.filters.contract ===
                                                contract.value.toString()
                                            }
                                            onClick={() => {
                                                props.setFilters({
                                                    ...props.filters,
                                                    contract:
                                                        contract.value.toString(),
                                                });
                                            }}
                                            text={contract.label}
                                        />
                                    )) ?? []}
                                </ActionList>
                            </Dropdown>

                            {/* Publication Filter */}
                            {isGetrix && props.status === 'active' ? (
                                <Dropdown
                                    position="bottomLeft"
                                    buttonClassName={clsx(
                                        'crm-filters-bar__filter',
                                        {
                                            'is-selected': Boolean(
                                                props.filters.on_portal
                                            ),
                                        }
                                    )}
                                    buttonVariant="chip"
                                    showCaret
                                    buttonContent={
                                        <>
                                            <Icon name="pin" />
                                            <span>
                                                <QuickFilterLabel
                                                    value={
                                                        props.filters.on_portal
                                                    }
                                                    default={trans(
                                                        'label.publication'
                                                    )}
                                                    keyPair={{
                                                        true: trans(
                                                            'label.published_on_portal',
                                                            {
                                                                AD_PORTAL:
                                                                    adPortal,
                                                            }
                                                        ),
                                                        false: trans(
                                                            'label.not_published_on_portal',
                                                            {
                                                                AD_PORTAL:
                                                                    adPortal,
                                                            }
                                                        ),
                                                    }}
                                                />
                                            </span>
                                        </>
                                    }
                                >
                                    <ActionList>
                                        {[
                                            {
                                                label: trans('label.all'),
                                                value: '',
                                            },
                                            {
                                                label: trans(
                                                    'label.published_on_portal',
                                                    {
                                                        AD_PORTAL: adPortal,
                                                    }
                                                ),
                                                value: 'true',
                                            },
                                            {
                                                label: trans(
                                                    'label.not_published_on_portal',
                                                    {
                                                        AD_PORTAL: adPortal,
                                                    }
                                                ),
                                                value: 'false',
                                            },
                                        ].map((publishedStatus) => (
                                            <ActionListItem
                                                key={publishedStatus.value}
                                                active={
                                                    props.filters.on_portal ===
                                                    publishedStatus.value
                                                }
                                                onClick={() => {
                                                    props.setFilters({
                                                        ...props.filters,
                                                        ['on_portal']:
                                                            publishedStatus.value,
                                                    });
                                                }}
                                                text={publishedStatus.label}
                                            />
                                        ))}
                                    </ActionList>
                                </Dropdown>
                            ) : null}

                            {/* status filter */}
                            {props.status === 'sold' ? (
                                <Dropdown
                                    position="bottomLeft"
                                    buttonClassName={clsx(
                                        'crm-filters-bar__filter',
                                        {
                                            'is-selected': props.filters.status
                                                ? props.filters.status
                                                : getDefaultStatusForSoldAndRent(
                                                      isGetrix
                                                  ),
                                        }
                                    )}
                                    buttonVariant="chip"
                                    showCaret
                                    buttonContent={
                                        <>
                                            <Icon name="helmet" />
                                            <span>
                                                <QuickFilterLabel
                                                    value={
                                                        props.filters.status
                                                            ? props.filters
                                                                  .status
                                                            : getDefaultStatusForSoldAndRent(
                                                                  isGetrix
                                                              )
                                                    }
                                                    default={trans(
                                                        'label.status'
                                                    )}
                                                    keyPair={
                                                        getStatusOptionsForSoldAndRent(
                                                            isGetrix
                                                        ).reduce(
                                                            (
                                                                keyPair,
                                                                status
                                                            ) => ({
                                                                ...keyPair,
                                                                [status.value]:
                                                                    status.label,
                                                            }),
                                                            {}
                                                        ) ?? {}
                                                    }
                                                />
                                            </span>
                                        </>
                                    }
                                >
                                    <ActionList>
                                        {getStatusOptionsForSoldAndRent(
                                            isGetrix
                                        ).map((status) => (
                                            <ActionListItem
                                                key={status.value}
                                                active={
                                                    (props.filters.status
                                                        ? props.filters.status
                                                        : getDefaultStatusForSoldAndRent(
                                                              isGetrix
                                                          )) ===
                                                    status.value.toString()
                                                }
                                                onClick={() => {
                                                    props.setFilters({
                                                        ...props.filters,
                                                        status: status.value.toString(),
                                                    });
                                                }}
                                                text={status.label}
                                            />
                                        )) ?? []}
                                    </ActionList>
                                </Dropdown>
                            ) : null}

                            {/* Visibility Filter */}
                            {!['sold', 'draft', 'archived'].includes(
                                props.status
                            ) && (
                                <Dropdown
                                    position="bottomLeft"
                                    buttonClassName={clsx(
                                        'crm-filters-bar__filter',
                                        {
                                            'is-selected': Boolean(
                                                props.filters.visibility
                                            ),
                                        }
                                    )}
                                    buttonVariant="chip"
                                    showCaret
                                    buttonContent={
                                        <>
                                            <Icon name="megaphone" />
                                            <span>
                                                <QuickFilterLabel
                                                    value={
                                                        props.filters.visibility
                                                    }
                                                    default={trans(
                                                        'label.visibility'
                                                    )}
                                                    keyPair={getVisibilities(
                                                        appLocale,
                                                        {
                                                            secretPropertyEnabled:
                                                                secretPropertyEnabled &&
                                                                isNotAuctionOrNewConstruction,
                                                        }
                                                    ).reduce(
                                                        (acc, visibility) => ({
                                                            ...acc,
                                                            [visibility.value]:
                                                                visibility.label,
                                                        }),
                                                        {}
                                                    )}
                                                />
                                            </span>
                                        </>
                                    }
                                >
                                    <ActionList>
                                        {[
                                            {
                                                label: trans('label.all'),
                                                value: '',
                                            },
                                        ]
                                            .concat(
                                                getVisibilities(appLocale, {
                                                    secretPropertyEnabled:
                                                        secretPropertyEnabled &&
                                                        isNotAuctionOrNewConstruction,
                                                })
                                            )
                                            .map((visibility) => (
                                                <ActionListItem
                                                    key={visibility.value}
                                                    active={Boolean(
                                                        props.filters
                                                            .visibility ===
                                                            visibility.value
                                                    )}
                                                    onClick={() => {
                                                        props.setFilters({
                                                            ...props.filters,
                                                            visibility:
                                                                visibility.value,
                                                        });
                                                    }}
                                                    text={visibility.label}
                                                />
                                            )) ?? []}
                                    </ActionList>
                                </Dropdown>
                            )}

                            {/* Performance Filter */}
                            {isPerformanceVisible ? (
                                <Dropdown
                                    disabled={
                                        categoriesQuery.isLoading ||
                                        !isPerformanceEnabled
                                    }
                                    position="bottomLeft"
                                    buttonClassName={clsx(
                                        'crm-filters-bar__filter',
                                        {
                                            'is-selected': Boolean(
                                                props.filters
                                                    .performanceRelativeIndexes
                                            ),
                                        }
                                    )}
                                    buttonVariant="chip"
                                    showCaret
                                    buttonContent={
                                        <>
                                            <Icon name="podium" />
                                            <span>
                                                <QuickFilterLabel
                                                    value={
                                                        props.filters
                                                            .performanceRelativeIndexes
                                                    }
                                                    default={trans(
                                                        'label.performances'
                                                    )}
                                                    keyPair={PERFORMANCE_FILTER.reduce(
                                                        (acc, filter) => ({
                                                            ...acc,
                                                            [filter.value]:
                                                                filter.label,
                                                        }),
                                                        {}
                                                    )}
                                                />
                                            </span>
                                        </>
                                    }
                                >
                                    <ActionList>
                                        {[
                                            {
                                                label: trans('label.all'),
                                                value: '',
                                            },
                                            ...PERFORMANCE_FILTER,
                                        ].map((performance) => (
                                            <ActionListItem
                                                active={
                                                    props.filters
                                                        .performanceRelativeIndexes ===
                                                    performance.value.toString()
                                                }
                                                onClick={() => {
                                                    props.setFilters({
                                                        ...props.filters,
                                                        performanceRelativeIndexes:
                                                            performance.value.toString(),
                                                    });
                                                }}
                                                key={performance.value}
                                                text={performance.label}
                                            />
                                        )) ?? []}
                                    </ActionList>
                                </Dropdown>
                            ) : null}

                            {/* Agent Filter */}
                            <Dropdown
                                className="crm-filters-barSlider__dropdownMaxHeight"
                                position="bottomRight"
                                buttonClassName={clsx(
                                    'crm-filters-bar__filter',
                                    {
                                        'is-selected': Boolean(
                                            props.filters.agent
                                        ),
                                    }
                                )}
                                disabled={agentsQuery.isLoading}
                                buttonVariant="chip"
                                showCaret
                                buttonContent={
                                    <>
                                        <Icon name="suitcase" />
                                        <span>
                                            <QuickFilterLabel
                                                value={props.filters.agent}
                                                default={trans('label.agent')}
                                                keyPair={
                                                    agentsQuery.data?.reduce(
                                                        (keyPair, agent) => ({
                                                            ...keyPair,
                                                            [agent.value]:
                                                                agent.label,
                                                        }),
                                                        {}
                                                    ) ?? {}
                                                }
                                            />
                                        </span>
                                    </>
                                }
                            >
                                <ActionList>
                                    {agentsQuery.data?.map((agent) => (
                                        <ActionListItem
                                            key={agent.value}
                                            active={
                                                props.filters.agent ===
                                                agent.value.toString()
                                            }
                                            onClick={() => {
                                                props.setFilters({
                                                    ...props.filters,
                                                    agent: agent.value,
                                                });
                                            }}
                                            text={agent.label}
                                        />
                                    )) ?? []}
                                </ActionList>
                            </Dropdown>

                            {/* Mandate Filter */}
                            {props.status !== 'sold' ? (
                                <Formik
                                    initialValues={{
                                        // eslint-disable-next-line camelcase
                                        mandate_from:
                                            props.filters.mandate_from || '',
                                        // eslint-disable-next-line camelcase
                                        mandate_to:
                                            props.filters.mandate_to || '',
                                    }}
                                    onSubmit={(values) => {
                                        props.setFilters({
                                            ...props.filters,
                                            ['mandate_from']:
                                                values.mandate_from,
                                            ['mandate_to']: values.mandate_to,
                                        });
                                    }}
                                    validationSchema={validationDateSchema}
                                    enableReinitialize
                                >
                                    <DropdownCalendarRangeInput
                                        startPlaceholder={trans(
                                            'label.range_from'
                                        )}
                                        endPlaceholder={trans('label.range_to')}
                                        disabled={mandateIsDisabled}
                                        icon="calendar"
                                        position="bottom-right"
                                        variant="chip"
                                        selected={
                                            Boolean(
                                                props.filters.mandate_from
                                            ) ||
                                            Boolean(props.filters.mandate_to)
                                        }
                                        formatter={({ end, start }) => {
                                            if (start && end) {
                                                return `${start} - ${end}`;
                                            } else if (start && !end) {
                                                return `${trans(
                                                    'label.from'
                                                )} ${start}`;
                                            } else if (!start && end) {
                                                return `${trans(
                                                    'label.to'
                                                )} ${end}`;
                                            }

                                            return trans(
                                                'label.mandate_expiry'
                                            );
                                        }}
                                        startName="mandate_from"
                                        endName="mandate_to"
                                        submitLabel={trans('label.see_ads')}
                                        buttonPlaceholder={trans(
                                            'mandate_expiry'
                                        )}
                                    />
                                </Formik>
                            ) : null}
                        </ScrollableRow>
                    ) : null}

                    <ResetFiltersButton
                        filters={props.filters}
                        resetFilters={props.resetFilters}
                    />
                </div>
            </ContentDropdownProvider>
        </DropdownProvider>
    );
};
