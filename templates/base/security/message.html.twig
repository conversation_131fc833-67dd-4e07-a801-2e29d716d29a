{% extends 'base/security/base.html.twig' %}

{% block inner_content %}
    {% include 'base/security/inner-header.html.twig' %}


    <div class="login-content">
        <div class="form-account-message">
            {% if messageTitle %}<h1 class="gx-title-1">{{ messageTitle }}</h1>{% endif %}
            {% if messageType == "error" %}
                <div class="gx-alert gx-alert--margin gx-alert--error">
                    <svg class="gx-icon gx-icon--negative" xmlns="http://www.w3.org/2000/svg" id="cross-circle" viewBox="0 0 24 24" fill-rule="evenodd">
                        <path d="M7.22 7.22a.75.75 0 0 1 1.06 0L12 10.94l3.72-3.72a.75.75 0 1 1 1.06 1.06L13.06 12l3.72 3.72a.75.75 0 0 1-1.06 1.06L12 13.06l-3.72 3.72a.75.75 0 1 1-1.06-1.06L10.94 12 7.22 8.28a.75.75 0 0 1 0-1.06Z"/>
                        <path d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm10-8.5a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17Z"/>
                    </svg>
                    <span>
                        {% if messageIsRaw %}{{ message | raw }}{% else %}{{ message }}{% endif %}
                    </span>
                </div>
            {% elseif  messageType == "success" %}
                <div class="gx-alert gx-alert--margin gx-alert--success">
                    <svg class="gx-icon gx-icon--positive" xmlns="http://www.w3.org/2000/svg" id="check-circle" viewBox="0 0 24 24" fill-rule="evenodd">
                        <path d="M16.311 9.747a.75.75 0 0 0-1.122-.994l-4.31 4.862-2.069-2.359a.75.75 0 0 0-1.128.988l2.625 3a.75.75 0 0 0 1.124.004l4.875-5.5Z"/>
                        <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2ZM3.5 12a8.5 8.5 0 1 1 17 0 8.5 8.5 0 0 1-17 0Z"/>
                    </svg>
                    <span>
                        {% if messageIsRaw %}{{ message | raw }}{% else %}{{ message }}{% endif %}
                    </span>
                </div>
            {% else %}
                <div class="gx-alert gx-alert--margin gx-alert--info">
                    <svg class="gx-icon gx-icon--info" xmlns="http://www.w3.org/2000/svg" id="info-circle" viewBox="0 0 24 24" fill-rule="evenodd">
                        <path d="M12 10.5a.75.75 0 0 0-.75.75v6a.75.75 0 1 0 1.5 0v-6a.75.75 0 0 0-.75-.75ZM12 7a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"/>
                        <path d="M2 12C2 6.34 6.34 2 12 2s10 4.34 10 10-4.34 10-10 10S2 17.66 2 12Zm10-8.5c-4.832 0-8.5 3.668-8.5 8.5s3.668 8.5 8.5 8.5 8.5-3.668 8.5-8.5-3.668-8.5-8.5-8.5Z"/>
                    </svg>
                    <span>
                        {% if messageIsRaw %}{{ message | raw }}{% else %}{{ message }}{% endif %}
                    </span>
                </div>
            {% endif %}
            <div class="form-account__footer">
                <a href="{{ path('app_login') }}">{{ "label.back_to_login"|trans({}, gtxConstants.DOMAIN_CMS) }}</a>
            </div>
        </div>
    </div>
{% endblock %}
