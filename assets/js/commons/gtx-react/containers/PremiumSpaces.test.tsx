import { render, screen } from '#tests/react/testing-library-enhanced';
import { describe, expect, it } from 'vitest';
import { PremiumSpaces } from './PremiumSpaces';

describe('PremiumSpaces', () => {
    it('should render correctly', () => {
        render(
            <PremiumSpaces
                spaces={[
                    {
                        "type": {
                            "id": 1,
                            "name": "vendita"
                        },
                        "applied": 4,
                        "applicable": 0,
                        "available": 196
                    },
                    {
                        "type": {
                            "id": 2,
                            "name": "affitto"
                        },
                        "applied": 1,
                        "applicable": 0,
                        "available": 199
                    }
                ]}
                contractId={1}
                label="Vendita"
            />
        );

        expect(screen.getByText('4 label.out_of 200 Vendita')).toBeInTheDocument();
        expect(screen.getByRole('premium-spaces')).toBeInTheDocument();
    });

    it('should render no elements', () => {
        render(
            <PremiumSpaces
                spaces={null}
                contractId={1}
                label="Vendita"
            />
        );

        expect(screen.queryByRole('premium-spaces')).not.toBeInTheDocument();
    });

    it('should show no spaces used and available', () => {
        render(
            <PremiumSpaces
                spaces={[
                    {
                        "type": {
                            "id": 1,
                            "name": "vendita"
                        },
                        "applied": 0,
                        "applicable": 0,
                        "available": 0
                    },
                    {
                        "type": {
                            "id": 2,
                            "name": "affitto"
                        },
                        "applied": 0,
                        "applicable": 0,
                        "available": 0
                    }
                ]}
                contractId={1}
                label="Vendita"
            />
        );

        expect(screen.getByText('0 label.out_of 0 Vendita')).toBeInTheDocument();
        expect(screen.getByRole('premium-spaces')).toBeInTheDocument();
    });

    it('should show no spaces used', () => {
        render(
            <PremiumSpaces
                spaces={[
                    {
                        "type": {
                            "id": 1,
                            "name": "vendita"
                        },
                        "applied": 0,
                        "applicable": 0,
                        "available": 20
                    },
                    {
                        "type": {
                            "id": 2,
                            "name": "affitto"
                        },
                        "applied": 0,
                        "applicable": 0,
                        "available": 0
                    }
                ]}
                contractId={1}
                label="Vendita"
            />
        );

        expect(screen.getByText('0 label.out_of 20 Vendita')).toBeInTheDocument();
        expect(screen.getByRole('premium-spaces')).toBeInTheDocument();
    });
});

