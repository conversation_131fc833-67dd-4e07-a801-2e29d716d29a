<?php

declare(strict_types=1);

namespace App\Security\Service;

use App\Builder\EventsTracker\EventsTrackerBuilder;
use App\DataProvider\EventsTracker\EventsTrackerInterface;
use App\Exception\AgentException;
use App\Exception\ResourceNotFoundException;
use App\Exception\SessionException;
use App\Helper\AgentApiClientHelper;
use App\Helper\TraceApiClientHelper;
use App\Utils\SessionUtils;
use App\ValueObjects\TraceAccessVO;
use Symfony\Component\HttpFoundation\Request;

class AccessTraceService
{
    public const NOT_ENABLED_COOKIE = 'notEnabled';
    public const UNIQUE_ID_KEY = 'uni_trace_id';
    private const HEADER_ALTERNATIVE_CLIENT_IP = 'X-Login-Request-Ip';
    private const EVENT_NAME = 'trace_access_event';

    private bool $enableTrace;
    private AgentApiClientHelper $agentApiClientHelper;
    private EventsTrackerInterface $eventsTracker;
    private SessionUtils $sessionUtils;
    private TraceApiClientHelper $traceApiClientHelper;

    private ?string $clientIp = null;
    private ?string $uniqueId = null;

    public function __construct(
        AgentApiClientHelper $agentApiClientHelper,
        EventsTrackerInterface $eventsTracker,
        SessionUtils $sessionUtils,
        TraceApiClientHelper $traceApiClientHelper,
        bool $enableTrace
    ) {
        $this->agentApiClientHelper = $agentApiClientHelper;
        $this->enableTrace = $enableTrace;
        $this->eventsTracker = $eventsTracker;
        $this->sessionUtils = $sessionUtils;
        $this->traceApiClientHelper = $traceApiClientHelper;
    }

    public function trace(TraceAccessVO $traceAccess): void
    {
        if (!$this->enableTrace) {
            return;
        }
        try {
            if (null === $traceAccess->getAgentUuid()) {
                $traceAccess->withAgentUuid($this->sessionUtils->getAgentUuid());
            }
        } catch (SessionException $exception) {
        }
        try {
            if (
                null === $traceAccess->getAgentUuid()
                && (int) $traceAccess->getAgent() > 0
                && (int) $traceAccess->getAgency() > 0
            ) {
                $agent = $this->agentApiClientHelper->getAgent(
                    (int) $traceAccess->getAgent(),
                    (int) $traceAccess->getAgency()
                );
                $traceAccess->withAgentUuid($agent->uuid);
            }
        } catch (ResourceNotFoundException $exception) {
        }
        try {
            if (
                null === $traceAccess->getAgentUuid()
                && null !== $traceAccess->getEmail()
            ) {
                $agent = $this->agentApiClientHelper->getAgentByEmail($traceAccess->getEmail());
                $traceAccess
                    ->withAgent((string) $agent->idAgente)
                    ->withAgency((string) $agent->fkAgenzia)
                    ->withAgentUuid($agent->uuid);
            }
        } catch (AgentException $e) {
        }
        try {
            if (
                null !== $traceAccess->getAgent()
                && null !== $traceAccess->getAgentUuid()
                && !$traceAccess->skipMixpanel()
            ) {
                $event = EventsTrackerBuilder::newBuilder()
                    ->withEventTime()
                    ->withEventName(self::EVENT_NAME)
                    ->withActionName($traceAccess->getAction())
                    ->withPropertyType(self::EVENT_NAME)
                    ->withCommonProperties(
                        $traceAccess->getIpAddress(),
                        (int) $traceAccess->getAgent(),
                        $traceAccess->getAgentUuid()
                    );
                $extra = [
                    'uniqueId' => $traceAccess->getUniqueId(),
                    'deviceFingerPrint' => $traceAccess->getDeviceFingerPrint(),
                    'userAgent' => $traceAccess->getUserAgent(),
                    'referer' => $traceAccess->getReferer(),
                    'agencyId' => $traceAccess->getAgency(),
                ];
                if (null !== $traceAccess->getUnmatchedTraceId()) {
                    $extra = \array_merge($extra, ['unmatched' => $traceAccess->getUnmatchedTraceId()]);
                }
                if (null !== $traceAccess->getPhoneNumber()) {
                    $extra = \array_merge($extra, ['phoneNumber' => $traceAccess->getPhoneNumber()]);
                }
                $event->withExtraProperties($extra);
                $this->eventsTracker->postEvent($event->build());
            }
        } catch (\Throwable $t) {
        }

        $this->traceApiClientHelper->traceAccess($traceAccess);
    }

    public function uniqueId(Request $request): string
    {
        if (!$this->enableTrace) {
            $this->uniqueId = self::NOT_ENABLED_COOKIE;

            return $this->uniqueId;
        }
        $uniqueId = (string) $request->cookies->get(self::UNIQUE_ID_KEY, \uniqid('', true));
        if (!$request->cookies->has(self::UNIQUE_ID_KEY)) {
            $this->uniqueId = $uniqueId;
        }

        return $uniqueId;
    }

    public function getUniqueId(): ?string
    {
        return $this->uniqueId;
    }

    public function clientIp(Request $request): string
    {
        if (null === $this->clientIp) {
            $this->clientIp = $request->headers->has(self::HEADER_ALTERNATIVE_CLIENT_IP)
                ? $request->headers->get(self::HEADER_ALTERNATIVE_CLIENT_IP)
                : $request->getClientIp();
        }

        return (string) $this->clientIp;
    }
}
