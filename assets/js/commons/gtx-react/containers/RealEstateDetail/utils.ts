import gtxConstants from '@getrix/common/js/gtx-constants';
import { IconProps } from '@gx-design/icon';

import {
    OLD_CATEGORIES_TO_NEW_TIPOLOGIES_MAPPING,
    OLD_TYPOLOGIES_TO_NEW_TIPOLOGIES_MAPPING,
    SECRET_PROPERTY_STATUS_ID,
} from 'constants/property';
import { PREMIUM_VISIBILITY_KEY, SECRET_PROPERTY_VISIBILITY_KEY } from 'constants/propertyVisibilities';
import { FieldData } from 'constants/realEstateFieldsData';
import { currencyFormatter } from 'lib/currencyFormatter';
import {
    getActiveVisibilities,
    getPropertyAllowedExtraVisibilities,
    getPropertyVisibilities,
} from 'lib/propertyVisibilities';
import { DetailPropertyType, Workspace } from 'lib/REST/types/detail-property';
import { Visibility } from 'types/propertyVisibilities';
import { CONSISTENCIES_ICONS_MAP } from './constants';

// copy-pasted from previous implementation
export const getCategoryFromOldTypology = (oldTypology) => {
    if (!oldTypology) {
        return null;
    }

    const newCategoriesKeys = Object.keys(OLD_CATEGORIES_TO_NEW_TIPOLOGIES_MAPPING);
    let result: number | null = null;

    newCategoriesKeys.forEach((newCategory) => {
        if (OLD_CATEGORIES_TO_NEW_TIPOLOGIES_MAPPING[newCategory].indexOf(oldTypology) !== -1) {
            result = parseInt(newCategory);
        }
    });

    return result;
};
// copy-pasted from previous implementation
export const getTypologyFromOldTypology = (oldTypology) => {
    if (!oldTypology) {
        return null;
    }

    const newTypologiesKeys = Object.keys(OLD_TYPOLOGIES_TO_NEW_TIPOLOGIES_MAPPING);
    let result: number | null = null;

    newTypologiesKeys.forEach((newTypology) => {
        if (OLD_TYPOLOGIES_TO_NEW_TIPOLOGIES_MAPPING[newTypology].indexOf(oldTypology) !== -1) {
            result = parseInt(newTypology);
        }
    });

    return result;
};
// copy-pasted from previous implementation
/** Util to generate input that will be passed to the provided `useSections()` hook to generate sections for `<PropertyDetailSection />` */
export const getUseSectionsArgs = ({ adCategory, propertyData }) => {
    if (!propertyData || adCategory !== 'property') {
        return [];
    }
    const property = propertyData?.properties ? propertyData.properties[0] : null;
    const isOldTypology = !property?.subTypology && propertyData?.typology ? true : false;

    const category = isOldTypology ? getCategoryFromOldTypology(propertyData?.typologyId) : property?.categoryId;
    const typology = isOldTypology ? getTypologyFromOldTypology(propertyData?.typologyId) : property?.typologyId;

    return [category, typology];
};
// copy-pasted from previous implementation
// TODO: move this somewhere else
export const getValueByPath = (data, path, nullValue = '') => {
    let result = path.reduce((data, item) => (data ? data[item] : null), data);

    if (result === null || result === '') {
        return nullValue;
    }

    return result;
};

type IsFieldValidInput = {
    realEstate: DetailPropertyType;
    validFor?: FieldData['validFor'];
};
export const isFieldValid = ({ validFor, realEstate }: IsFieldValidInput): boolean => {
    if (!validFor || !validFor?.length) {
        return true;
    }

    return validFor.every(({ path, value }) => {
        const linkedValue = getValueByPath(realEstate, path);

        return value?.length && typeof value !== 'string'
            ? value.some((val) => val === linkedValue)
            : linkedValue == value;
    });
};

export const generateSurfaceIcon = (id): IconProps['name'] => CONSISTENCIES_ICONS_MAP[id] ?? 'economy-house';

/** Util to format workspace's fees (co-working typology) */
export const formatWorkspaceFees = ({ feeFrom, feeTo }: Workspace): string | undefined => {
    if (!feeFrom || !feeTo) {
        return undefined;
    }
    return `${currencyFormatter(feeFrom)} ${feeTo !== feeFrom ? `- ${currencyFormatter(feeTo)}` : ''}`;
};

/**
 * Util to format a generic range, with an output like:
 * - `{from} - {to}` - if `from` and `to` are different;
 * - `{from}` - if `from` and `to` are equal.
 */
export const formatRange = ({ from, to }: { from?: number | null; to?: number | null }): string | null => {
    if (typeof from !== 'number' && typeof to !== 'number') {
        return null;
    }
    if (from === to) {
        return `${from}`;
    }
    return `${from}${typeof to === 'number' ? ` - ${to}` : ''}`;
};

type VisibilityReturn = {
    isAgencyPage: boolean;
    visibilities: Visibility[];
    extraVisibilites: Visibility[];
    areAllExtraVisibilitiesActive: boolean;
};
// TODO: move this somewhere else, it could be a reusable util
export const getRealEstateVisibilities = (realEstate: DetailPropertyType): VisibilityReturn => {
    const isActiveStatus = realEstate.statusId === gtxConstants('PROPERTY_ACTIVE_PORTAL_STATUS');
    const isSecretStatus = realEstate.statusId === SECRET_PROPERTY_STATUS_ID;

    const result: VisibilityReturn = {
        isAgencyPage: false,
        visibilities: [],
        extraVisibilites: [],
        areAllExtraVisibilitiesActive: false,
    };

    if (!isActiveStatus && !isSecretStatus) {
        return result;
    }

    const allowedVisibilities = getPropertyVisibilities();
    const tenantSecretVisibility = allowedVisibilities.filter((elem) => elem.value === SECRET_PROPERTY_VISIBILITY_KEY);
    if (isSecretStatus && tenantSecretVisibility?.length) {
        result.visibilities = [...tenantSecretVisibility];
        return result;
    }
    // ts error because types are different but I don't want to change the external util
    const activeVisibilities = getActiveVisibilities(realEstate);
    const extraVisibilities = activeVisibilities.filter((visibility) => visibility.value !== PREMIUM_VISIBILITY_KEY);

    result.isAgencyPage = true;
    result.visibilities = activeVisibilities;
    result.extraVisibilites = extraVisibilities;
    result.areAllExtraVisibilitiesActive =
        gtxConstants('VISIBILITIES_ALL_EXTRA_VISIBILITIES_PRODUCT') &&
        extraVisibilities.length === getPropertyAllowedExtraVisibilities().length;

    return result;
};
