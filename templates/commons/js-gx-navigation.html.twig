{% if logged_agency %}
    {% set language = logged_agency.languageCode ? logged_agency.languageCode : globalConfigs.app.locale %}
    {% set isMasterAccount = multiAgency is not null and multiAgency.isMaster %}
    <script>

        const gxNavigationContext = {
            hasTwoFactorAuth: {{ hasTwoFactorAuth|json_encode|raw }},
            agencyLanguage: {{ language|json_encode|raw }},
            menuCacheToken: {{ gxNavigationMenuCacheToken|json_encode|raw }},
            hasScheduledVisitImmovisita: {{ hasScheduledVisitImmovisita|json_encode|raw }},
            agencyId: {{ logged_agency.idAgenzia|json_encode|raw }},
            agencyImage: {% if logged_agency.fkLogoBig %}{{ [gtxConstants.VHOST_URL_MEDIA_IMAGE ~ gtxConstants.AGENCY_LOGO_IMAGE_ENDPOINT ~ "/",logged_agency.fkLogoBig~".jpg"]|join()|json_encode|raw }} {% else %} null {% endif %},
            agencyName: {{ logged_agency.nome|json_encode|raw }},
            agentName: {{ [logged_agent.nome, logged_agent.cognome]|join(' ')|json_encode|raw }},
            agentEmail: {{ logged_agent.email|json_encode|raw }},
            agentAvatar: {{ [logged_agent.urlImmagineAgenteThumb]|join()|json_encode|raw }},
            logo: {{ app_logo|json_encode|raw }},
            homeUrl: {{ gtxConstants.HOME_PATH|json_encode|raw }},
            siteName: {{ gtxConstants.APP_NAME|json_encode|raw }},
            adPortal: {{ gtxConstants.AD_PORTAL|json_encode|raw }},
            isMasterAccount: {{ isMasterAccount|json_encode|raw }},
            responsibleName: {% if logged_agency.responsabileVisibility and logged_agency.responsabileNomeToView %}
               {{ logged_agency.responsabileNomeToView|json_encode|raw }}
            {% else %} "{{ trans("label.customer_care", {}, gtxConstants.DOMAIN_CMS )}}" {% endif %},
            responsiblePhone: {% if logged_agency.responsabileVisibility and logged_agency.responsabileTelefonoToView %}
                        {{ logged_agency.responsabile.telefono|json_encode|raw }}
            {% else %} {{ logged_agency.responsabileTelefonoAssistenzaToView|json_encode|raw }} {% endif %},
            responsibleAvatar: {% if logged_agency.responsabileVisibility and logged_agency.responsabileAvatarToView %}
                {{ logged_agency.responsabileAvatarToView|json_encode|raw }}
            {% else %} "/img/getrix/common/customerservice.png" {% endif %},
            profileUrl: {{ gtxConstants.USER_PROFILE_URL |json_encode|raw }},
            isAdmin: {{ is_granted('ROLE_AMMINISTRATORE') ? true : false |json_encode|raw}},
            serviceRemoteVisit: {{gtxConstants.SERVICE_REMOTE_VISITS|json_encode|raw}},
            defaultLanguage: {{gtxConstants.DEFAULT_LANGUAGE|json_encode|raw}},
            metricsSite: {{ gtxConstants.METRICS_SITE|json_encode|raw }},
            metricsSitePrefix: 'sites',
            metricsUrl: 'https://s.ekbl.net/s.gif',
            helpJuiceAccountUrl: {{ gtxConstants.HELPJUICE_ACCOUNT_URL|json_encode|raw }},
            featureFlags: {
                addPropertyEnabled: {{ (gtxConstants.ADD_PROPERTY_ENABLED ? true : false)|json_encode|raw }},
                beamerNewsEnabled: {{ (gtxConstants.BEAMER_NEWS ? true : false)|json_encode|raw }},
                saleforceLiveChatEnabled: {{ (gtxConstants.SALESFORCE_LIVE_CHAT_ENABLED ? true : false)|json_encode|raw }},
                helpJuiceEnabled: {{ (gtxConstants.HELPJUICE_ENABLED ? true : false)|json_encode|raw }},
            }
        };

        window.addEventListener('BabelfishLoaderWrapperPlugin::gx-nav::gx-navigation::ready', ()=> {
            GxNavigation.initialize({context: gxNavigationContext});
        })
    </script>
    <script src="{{asset('gx-navigation.js', 'nav_asset')}}"></script>
{% endif %}
