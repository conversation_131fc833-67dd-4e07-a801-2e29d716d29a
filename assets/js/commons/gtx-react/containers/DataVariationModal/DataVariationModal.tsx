import React, { Suspense } from 'react';
import { Loader } from 'gtx-react/components';
import { useSuspenseQuery } from '@tanstack/react-query';
import { trans } from '@pepita-i18n/babelfish';
import { Avatar } from '@gx-design/avatar';
import { Alert } from '@gx-design/alert';
import { Icon } from '@gx-design/icon';
import { ErrorBoundary } from 'react-error-boundary';
import { createDataVariationQueryOptions } from 'lib/REST/requests/v2/lista-annunci/query-factory';
import { DataVariationChangePriceItem } from 'lib/REST/types/dataVariationModal';

export const DataVariationModal = ({ propertyId }: { propertyId: string }) => {
    return (
        <ErrorBoundary
            FallbackComponent={() => (
                <Alert style="error">
                    {trans('label.error_durin_operation')}
                </Alert>
            )}
        >
            <Suspense
                fallback={
                    <div className="gx-modal__bodyLoading">
                        <Loader
                            loading={true}
                            fixedOverlay={false}
                            centered={false}
                        />
                    </div>
                }
            >
                <DataVariation propertyId={propertyId} />
            </Suspense>
        </ErrorBoundary>
    );
};

export type DataVariationModalType = {
    propertyId: string;
};

const DataVariation: React.FC<DataVariationModalType> = ({
    propertyId,
}: DataVariationModalType) => {
    const { data } = useSuspenseQuery({
        ...createDataVariationQueryOptions({
            query: { adId: propertyId },
        }),
        select: (data) => data?.data,
    });

    return (
        <div
            className="data-variation-wrapper"
            data-testid="data-variation-modal"
        >
            {data?.map((element) => (
                <div className="data-variation-day">
                    <span className="data-variation-day__date gx-body-small">
                        <Icon name="calendar" />
                        {element?.date}
                    </span>
                    <ol className="data-variation-list">
                        {element?.changes?.map((change, idx) => (
                            <li className="data-variation-list__item" key={idx}>
                                <span className="data-variation-list__time gx-body-small">
                                    <Icon name="clock" />
                                    <span>{change?.time}</span>
                                </span>
                                <Avatar
                                    className="data-variation-list__avatar"
                                    avatarImage={change?.agent?.image}
                                />
                                <div className="data-variation-list__content gx-body-small">
                                    <span className="data-variation-list__contentAction">
                                        {change?.action}
                                    </span>
                                    <span className="data-variation-list__contentAuthor">
                                        {change?.agent?.name}
                                    </span>
                                    <Price prices={change?.prices} />
                                </div>
                            </li>
                        ))}
                    </ol>
                </div>
            ))}
        </div>
    );
};

const Price = ({ prices }: { prices: DataVariationChangePriceItem }) => {
    if (!prices) {
        return null;
    }

    if (prices?.oldPrice) {
        return (
            <span
                className={`data-variation-list__contentPriceVariation ${prices?.direction}`}
            >
                € {prices?.newPrice}{' '}
                <span className="oldPrice" data-testid="old-price">
                    € {prices?.oldPrice}
                </span>
            </span>
        );
    }

    return (
        <span
            className="data-variation-list__contentPrice"
            data-testid="new-price"
        >
            € {prices?.newPrice}
        </span>
    );
};
