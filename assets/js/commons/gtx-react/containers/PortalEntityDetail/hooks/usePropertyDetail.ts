import gtxConstants from '@getrix/common/js/gtx-constants';
import { trans } from '@pepita-i18n/babelfish';

import { TYPOLOGIES, SHOW_CADASTRAL_DATA } from 'constants/property';
import { PROPERTY_FIELDS_DATA } from 'constants/propertyFieldsData';

const sectionsByTypologyId = [
    TYPOLOGIES.farmingLand,
    TYPOLOGIES.buildableLand,
    TYPOLOGIES.office,
    TYPOLOGIES.coworking,
];

const sectionsData = [
    /**
     * Informazioni principali
     */
    {
        categoryId: [TYPOLOGIES.residential],
        name: 'mainInfos',
        title: trans('label.main_infos'),
        data: [
            'propertyOwnership',
            'condominiumCosts',
            'heatingCosts',
            'freeNow',
            'rentToBuy',
            'incomeProducingProperty',
            { name: 'availableForStudents', disabled: !gtxConstants('PROPERTY_FIELD_AVAILABLE_FOR_STUDENTS_ENABLED') },
        ],
    },
    {
        categoryId: [TYPOLOGIES.room],
        name: 'mainInfos',
        title: trans('label.main_infos'),
        data: ['propertyClass', 'propertyOwnership', 'rooms', 'bathrooms', 'floor', 'totalBuildingFloors'],
    },
    {
        categoryId: [TYPOLOGIES.garage],
        name: 'mainInfos',
        title: trans('label.main_infos'),
        data: ['condominiumCosts', 'freeNow', 'rentToBuy', 'incomeProducingProperty'],
    },
    {
        categoryId: [TYPOLOGIES.shed],
        name: 'mainInfos',
        title: trans('label.main_infos'),
        data: ['condominiumCosts', 'deposit', 'available'],
    },
    {
        categoryId: [TYPOLOGIES.warehouse],
        name: 'mainInfos',
        title: trans('label.main_infos'),
        data: ['condominiumCosts', 'rentToBuy', 'incomeProducingProperty', 'freeNow'],
    },
    {
        categoryId: [TYPOLOGIES.buildings],
        name: 'mainInfos',
        title: trans('label.main_infos'),
        data: ['freeNow', 'available', 'rentToBuy', 'incomeProducingProperty'],
    },
    {
        typologyId: [TYPOLOGIES.farmingLand, TYPOLOGIES.notBuildableLand],
        name: 'mainInfos',
        title: trans('label.main_infos'),
        data: ['freeNow', 'rentToBuy', 'incomeProducingProperty'],
    },
    {
        typologyId: [TYPOLOGIES.buildableLand],
        name: 'mainInfos',
        title: trans('label.main_infos'),
        data: ['freeNow', 'available', 'rentToBuy', 'incomeProducingProperty'],
    },
    {
        categoryId: [TYPOLOGIES.shop],
        name: 'mainInfos',
        title: trans('label.main_infos'),
        data: ['subjectSaleRent', 'condominiumCosts', 'freeNow', 'available', 'rentToBuy'],
    },
    {
        typologyId: [TYPOLOGIES.office],
        name: 'mainInfos',
        title: trans('label.main_infos'),
        data: ['condominiumCosts', 'deposit', 'freeNow', 'available'],
    },
    /**
     * Superficie
     */
    {
        name: 'consistences',
        title: trans('label.surface_detail'),
        categoryId: [
            TYPOLOGIES.residential,
            TYPOLOGIES.buildings,
            TYPOLOGIES.warehouse,
            TYPOLOGIES.shop,
            TYPOLOGIES.room,
        ],
        typologyId: [TYPOLOGIES.office],
        data: ['consistences'],
    },
    {
        name: 'surface',
        title: trans('label.surface_detail'),
        categoryId: [TYPOLOGIES.garage],
        data: ['surface', 'floor', 'garageDoorWidth'],
    },
    {
        name: 'surface',
        title: trans('label.surface_detail'),
        typologyId: [TYPOLOGIES.coworking],
        data: ['surface', 'floor'],
    },
    {
        name: 'surface',
        title: trans('label.surface_detail'),
        typologyId: [TYPOLOGIES.farmingLand, TYPOLOGIES.notBuildableLand],
        data: ['surface'],
    },
    {
        name: 'surface',
        title: trans('label.surface_detail'),
        typologyId: [TYPOLOGIES.buildableLand],
        data: ['surface', 'buildableSurface', 'buildableHeight'],
    },
    /**
     * Composizione
     */
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.residential],
        data: [
            'bedRooms',
            'otherRooms',
            'totalRooms',
            'bathrooms',
            'kitchenType',
            'garden',
            'parkingSpaces',
            'balconies',
            'terraces',
            'fittedWardrobe',
            'basement',
            'mansard',
            'furniture',
            'externalFixtures',
            'tvSystem',
            'conciergeService',
            'securityDoor',
            'videoDoorPhone',
            'opticalFiber',
            'firePlace',
            'hydroMassage',
            'swimmingPool',
            'tennisCourt',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.garage],
        data: [
            'antiTheftDevice',
            'parkingSpaces',
            'motorcycleParkingSpaces',
            'electricGate',
            'chargingStations',
            'cctv',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.buildings],
        data: [
            'rooms',
            'bathrooms',
            'disabledPersonsBathroom',
            'balconies',
            'parkingSpaces',
            'parkingSpacesInGarage',
            'antiTheftDevice',
            'reception',
            'externalFixtures',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.shed],
        data: [
            'shedHeight',
            'subbeamHeight',
            'pillars',
            'loadingDocks',
            'cranes',
            'loadCapacity',
            'spans',
            'rooms',
            'bathrooms',
            'dressingRoom',
            'shower',
            'canteen',
            'officeSurface',
            'parkingSpaces',
            'parkingSpacesInGarage',
            'antiTheftDevice',
            'cctv',
            'driveway',
            'reception',
            'enclosed',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        typologyId: [TYPOLOGIES.farmingLand],
        data: [
            'landDeclivity',
            'waterConnection',
            'electricityConnection',
            'gasConnection',
            'sewersConnection',
            'publicLighting',
            'enclosed',
            'accessType',
            'landFeatures',
            { name: 'views', disabled: !gtxConstants('PROPERTY_FIELD_VIEW_ENABLED') },
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        typologyId: [TYPOLOGIES.notBuildableLand],
        data: [
            'landDeclivity',
            'waterConnection',
            'electricityConnection',
            'gasConnection',
            'sewersConnection',
            'publicLighting',
            'enclosed',
            'accessType',
            { name: 'views', disabled: !gtxConstants('PROPERTY_FIELD_VIEW_ENABLED') },
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        typologyId: [TYPOLOGIES.buildableLand],
        data: [
            'landDeclivity',
            'waterConnection',
            'electricityConnection',
            'gasConnection',
            'sewersConnection',
            'publicLighting',
            'enclosed',
            'accessType',
            'buildingUsages',
            { name: 'views', disabled: !gtxConstants('PROPERTY_FIELD_VIEW_ENABLED') },
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.shop],
        data: [
            'rooms',
            'bathrooms',
            'disabledPersonsBathroom',
            'furniture',
            'activityEquipment',
            'ceilingHeight',
            'storefrontsCount',
            'storefrontsSurface',
            'orientations',
            'flue',
            'mechanicsVentilation',
            'wired',
            'floatingFloor',
            'manualShutter',
            'automaticShutter',
            'fireSensor',
            'rainSensor',
            'fireHydrant',
            'fireBreakDoor',
            'emergencyExit',
            'emergencyLightings',
            'parkingSpaces',
            'parkingSpacesInGarage',
            'driveway',
            'antiTheftDevice',
            'cctv',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        typologyId: [TYPOLOGIES.office],
        data: [
            'rooms',
            'bathrooms',
            'disabledPersonsBathroom',
            'kitchen',
            'furniture',
            'externalFixtures',
            'orientations',
            'internalExposition',
            'externalExposition',
            'wired',
            'broadbandInternet',
            'floatingFloor',
            'counterTop',
            'securityDoor',
            'accessControl',
            'parkingSpaces',
            'parkingSpacesInGarage',
            'bikeParking',
            'reception',
            'antiTheftDevice',
            'fireSensor',
            'rainSensor',
            'fireHydrant',
            'fireBreakDoor',
            'emergencyExit',
            'emergencyLightings',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        typologyId: [TYPOLOGIES.coworking],
        data: [
            'bathrooms',
            'disabledPersonsBathroom',
            'kitchen',
            'balconies',
            'terraces',
            'parkingSpaces',
            'garage',
            'bikeParking',
            'warehouse',
            'reception',
            'externalFixtures',
            'broadbandInternet',
            'multifunctionPrinter',
            'garden',
            'hotDrinks',
            'convivialityRoom',
            'microwave',
            'cleaning',
            'waitingRoom',
            'fridge',
            'collectionStorageService',
            'dishwasher',
            'phoneCallSpaces',
            'domiciliation',
            'badge',
            'music',
            'tv',
            'locker',
            'newspapers',
            'fruit',
            'coffeeBar',
            'shower',
            'guardian',
            'barber',
            'asylum',
            'fitnessRoom',
            'otherServices',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.warehouse],
        data: [
            'warehouseHeight',
            'bathrooms',
            'disabledPersonsBathroom',
            'driveway',
            'parkingSpaces',
            'parkingSpacesInGarage',
            'antiTheftDevice',
            'cctv',
            'enclosed',
        ],
    },
    {
        name: 'composition',
        title: trans('label.property_composition'),
        categoryId: [TYPOLOGIES.room],
        data: [
            'roomFireplace',
            'roomTennisCourt',
            'roomInternet',
            'roomDishwasher',
            'roomWashingMachine',
            'roomMicrowave',
            'roomConciergeService',
            'roomAntiTheftSystem',
            'roomSatelliteTv',
        ],
    },
    /**
     * Caratteristiche
     */
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.residential],
        data: [
            'constructionYear',
            'propertyClass',
            'propertyCondition',
            'floor',
            'totalBuildingFloors',
            'disabledAccess',
            'openSides',
            'overlooking',
            'heating',
            'heatingType',
            'heatingSupply',
            'airConditioning',
            'airConditioningType',
            { name: 'views', disabled: !gtxConstants('PROPERTY_FIELD_VIEW_ENABLED') },
            { name: 'distanceFromSea', disabled: !gtxConstants('PROPERTY_FIELD_DISTANCE_FROM_SEA_ENABLED') },
            { name: 'solarWaterHeating', disabled: !gtxConstants('PROPERTY_FIELD_SOLAR_WATER_HEATING_ENABLED') },
        ],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.garage],
        data: ['garageType', 'dayAccessibility', 'disabledAccess'],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.buildings],
        data: [
            'propertyClass',
            'propertyCondition',
            'constructionYear',
            'disabledAccess',
            'elevators',
            'openSides',
            'buildingUsages',
            'internalExposition',
            'externalExposition',
            'heating',
            'heatingType',
            'heatingSupply',
            'airConditioning',
            'airConditioningType',
            { name: 'views', disabled: !gtxConstants('PROPERTY_FIELD_VIEW_ENABLED') },
        ],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.shed],
        data: ['propertyCondition', 'constructionYear', 'disabledAccess'],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.shop],
        data: [
            'propertyCondition',
            'constructionYear',
            'totalBuildingFloors',
            'disabledAccess',
            'cornerPosition',
            'doubleEntrance',
            'yearsOfActivity',
            'lastYearRevenue',
            'heating',
            'heatingType',
            'heatingSupply',
            'airConditioning',
            'airConditioningType',
        ],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        typologyId: [TYPOLOGIES.office],
        data: [
            'propertyClass',
            'propertyCondition',
            'constructionYear',
            'totalBuildingFloors',
            'disabledAccess',
            'elevators',
            'heating',
            'heatingType',
            'heatingSupply',
            'airConditioning',
            'airConditioningType',
        ],
    },
    {
        name: 'features',
        title: trans('label.property_features'),
        typologyId: [TYPOLOGIES.coworking],
        data: [
            'totalBuildingFloors',
            'disabledAccess',
            'heating',
            'heatingType',
            'heatingSupply',
            'airConditioning',
            'airConditioningType',
        ],
    },
    {
        categoryId: [TYPOLOGIES.warehouse],
        name: 'features',
        title: trans('label.property_features'),
        data: ['propertyCondition', 'constructionYear', 'disabledAccess'],
    },
    {
        name: 'roomPropertyFeatures',
        title: trans('label.property_features'),
        categoryId: [TYPOLOGIES.room],
        data: [
            'roomBalcony',
            'roomKitchen',
            'roomGarden',
            'roomGym',
            'roomSwimmingPool',
            'roomUtilityRoom',
            'roomTerrace',
            { name: 'views', disabled: !gtxConstants('PROPERTY_FIELD_VIEW_ENABLED') },
        ],
    },
    /**
     * Caratteristiche stanza
     */
    {
        name: 'roomFeatures',
        title: trans('label.room_features'),
        categoryId: [TYPOLOGIES.room],
        data: [
            'roomAirConditioned',
            'roomWardrobe',
            'roomPhone',
            'roomPrivateBathroom',
            'roomSharedBathroom',
            'roomLaundry',
            'roomPrivateEntrance',
            'roomMoquette',
            'roomParquet',
            'roomTv',
        ],
    },
    /**
     * Coinquilino
     */
    {
        title: trans('label.roommate'),
        categoryId: [TYPOLOGIES.room],
        data: [
            'roommateMinAge',
            'roommateMaxAge',
            'roommateGender',
            'roommateSmoker',
            'roommateCouple',
            'roommateAnimals',
        ],
    },
    /**
     * Prezzi e disponibilità
     */
    {
        name: 'roomPrices',
        title: trans('label.prices_and_availability'),
        categoryId: [TYPOLOGIES.room],
        data: [
            'condominiumCosts',
            'roomServicesCosts',
            'roomOtherCosts',
            'roomRentCost',
            'deposit',
            'roomAvailableFrom',
            'roomMinimumStay',
            'roomElectricityCosts',
            'roomGasCosts',
            'roomInternetCosts',
            'roomPhoneCosts',
        ],
    },
    /**
     * Dati intermediazione
     */
    {
        name: 'intermediation',
        title: trans('label.intermediation_data'),
        categoryId: [
            TYPOLOGIES.residential,
            TYPOLOGIES.buildings,
            TYPOLOGIES.shed,
            TYPOLOGIES.warehouse,
            TYPOLOGIES.farmingLand,
            TYPOLOGIES.shop,
        ],
        typologyId: [TYPOLOGIES.office, TYPOLOGIES.coworking],
        data: ['reference', 'agentName', 'ownerName', 'collaboration', 'mandateType', 'mandateExpireDate'],
    },
    {
        name: 'intermediation',
        title: trans('label.intermediation_data'),
        categoryId: [TYPOLOGIES.garage, TYPOLOGIES.room],
        data: ['reference', 'agentName', 'collaboration', 'mandateType', 'mandateExpireDate'],
    },
    /**
     * Dati catastali
     */
    {
        name: 'cadastral',
        title: trans('label.cadastral_data'),
        disabled: !SHOW_CADASTRAL_DATA,
        categoryId: [
            TYPOLOGIES.residential,
            TYPOLOGIES.garage,
            TYPOLOGIES.buildings,
            TYPOLOGIES.shed,
            TYPOLOGIES.warehouse,
            TYPOLOGIES.shop,
            TYPOLOGIES.room,
        ],
        typologyId: [
            TYPOLOGIES.farmingLand,
            TYPOLOGIES.buildableLand,
            TYPOLOGIES.notBuildableLand,
            TYPOLOGIES.office,
            TYPOLOGIES.coworking,
        ],
        data: [
            'cadastralSection',
            'cadastralSheet',
            'cadastralParcel',
            'cadastralSubordinate',
            'cadastralCategory',
            'cadastralOwnershipShares',
            'otherCadastralData',
        ],
    },
    /**
     * Certificazione energetica
     */
    {
        name: 'energyClass',
        title: trans('label.energy_certification'),
        categoryId: [TYPOLOGIES.residential, TYPOLOGIES.buildings, TYPOLOGIES.shop, TYPOLOGIES.room],
        typologyId: [TYPOLOGIES.office, TYPOLOGIES.coworking],
        data: ['energyClass'],
    },
];

const usePropertyDetail = (categoryId, typologyId) => {
    function filterByCategory(sections, categoryId) {
        const allowedSections = sections.filter((section) => {
            if (section?.disabled) {
                return false;
            }
            if (typologyId && sectionsByTypologyId.includes(typologyId)) {
                return section?.typologyId?.includes(typologyId);
            }
            return section?.categoryId?.includes(categoryId);
        });

        return allowedSections.map((allowedSection) => ({
            ...allowedSection,
            data: allowedSection.data
                .filter((item) => typeof item === 'string' || !item.disabled)
                .map((item) => {
                    return PROPERTY_FIELDS_DATA.find(
                        (field) => field.name === (typeof item === 'string' ? item : item?.name)
                    );
                }),
        }));
    }

    return {
        sections: filterByCategory(sectionsData, categoryId),
    };
};

export default usePropertyDetail;
