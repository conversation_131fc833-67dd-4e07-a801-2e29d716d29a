##########################################################
#  Esempio di configurazione del menu item
#     "key menu":
#           Viene restituito nel json modificato secondo le indicazioni di label_action
#           label: "label key or parameters"
#
#           Questo parametro indica l'azione da eseguire sulla label.
#           I valori ammessi sono:
#           -App\Component\Menu\Model\App\Component\Menu\Model::TRANSLATE (default) Il valore di label viene processato dal translator
#           -App\Component\Menu\Model\App\Component\Menu\Model::PARAMETER_REPLACE Il valore della label vie sostituito con il valore del parametro specificato
#           Il parametro è opzionale e se non specificato viene usato il valore di default
#
#           label_action: !php/const App\Component\Menu\Model\App\Component\Menu\Model::PARAMETER_REPLACE # default action translate
#
#           icon: "icon value" Viene restituito nel json così com'è
#           group: 1 Viene restituito nel json come stringa
#           expanded: true Viene restituito nel json così com'è
#           contexts:
#               - "message-counter"
#           params: Tutti i sotto oggetti di params vengono restituiti così come scritti
#              multimedia: "vt"
#           subMenu: Contiene i sotto menu
#
#
#
#
#
#########################################################

menu:
    primary:
        "/":
            label: "|label.summary|"
            icon: "thumb-list"
            visibility:
                featureToggleGroup: "dashboard"
        "#annunci":
            href: "/inserimento_annuncio.php"
            label: "|label.ads|"
            icon: "home"
            visibility:
                featureToggleGroup: "ads"
            subMenu:
                "#immobili":
                    label: "|label.estates|"
                    icon: "home"
                    group: 1
                    expanded: true
                    subMenu:
                        "/%LANG%/add-listing/":
                            label: "|label.add|"
                        "/%LANG%/my-listings/":
                            label: "|label.list|"
                            visibility:
                                featureToggleFeature: "property_ads"
                            subMenu:
                                "/immmobili/{id}/cartellone":
                                    hidden: true
        "#messaggi":
            label: "|label.messages|"
            icon: "chat"
            href: "/messaggi/lista"
            contexts:
                - "message-counter"
            visibility:
                featureToggleGroup: "customer_requests"
            subMenu:
                # Enable this only if direct_customer_requests is disabled
                "/messaggi/lista":
                    label: "|label.messages|"
                    icon: "chat"
                    visibility:
                        authorizer: 'app.menu.authorizer.messaging-enabled'
                        featureToggleFeature: "requests_messages"
                    contexts:
                        - "message-counter"
                    subMenu:
                        "/messaggi/{id}":
                            hidden: true
                # Enable this only if requests_messages is disabled
                "/v2/contatti_agenzie.php":
                    label: "|label.messages|"
                    icon: "chat"
                    visibility:
                        authorizer: 'app.menu.authorizer.messaging-disabled'
                        featureToggleFeature: "direct_customer_requests"
                "/v2/telefonate":
                    label: "|label.phone_calls|"
                    icon: "phone"
                    visibility:
                        featureToggleFeature: "telefono_smart_requests"
        "#servizi":
            label: "|label.services|"
            icon: "layers"
            href: "/v2/servizi-web-marketing"
            visibility:
                featureToggleGroup: "extra_services"
            subMenu:
                "/v2/servizi-web-marketing":
                    label: "|label.web_services|"
                    icon: "scope"
                    visibility:
                        featureToggleFeature: "web_service"
                "/sitoagenzia.php":
                    label: "|label.website|"
                    icon: "monitor"
                    visibility:
                        featureToggleFeature: "website"
                "/%LANG%/immotoplus/":
                    label: "|label.agency_services|"
                    icon: "plus-circle"
                    visibility:
                        featureToggleFeature: "agency_services"
    secondary:
        "#impostazioni":
            label: "|label.settings_and_administration|"
            icon: "sliders"
            href: "/%LANG%/my-profile/"
            visibility:
                featureToggleGroup: "settings"
                role: 'ROLE_AMMINISTRATORE'
            subMenu:
                "/%LANG%/my-profile/":
                    label: "|label.general|"
                    group: 1
                    visibility:
                        featureToggleFeature: "general_settings"
                "/impostazioni/visite-a-distanza":
                    label: "|label.remote_visits|"
                    group: 1
                    visibility:
                        featureToggleFeature: "immovisita_settings"
                "/mes-factures/":
                    label: "|label.billing_information|"
                    group: 2
                    visibility:
                        featureToggleFeature: "invoices_info"
