{"$id": "http://gestionale.immobiliare.it/list_properties_filter.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "List Properties Filter", "description": "This json schema refer to ListListingAdsFiltersRequest class", "type": "object", "properties": {"address": {"type": ["string", "null"]}, "agentId": {"type": ["string", "null"]}, "categories": {"type": ["string", "null"], "description": "Comma separated category IDs list to search within"}, "city": {"type": ["string", "null"]}, "code": {"type": ["string", "null"], "description": "AD code / ID"}, "contract": {"type": ["string", "null"], "description": "Allowed values are: 1 for vendita, 2 for affitto", "enum": ["1", "2", null]}, "favourite": {"type": ["boolean", "null"], "description": "ADs with / without favourite flag"}, "mandate_from": {"type": ["string", "null"], "definition": "Mandate Start", "format": "DD/MM/YYYY", "pattern": "^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/(\\d{4})$"}, "mandate_to": {"type": ["string", "null"], "definition": "Mandate End", "format": "DD/MM/YYYY", "pattern": "^(0[1-9]|[12][0-9]|3[01])/(0[1-9]|1[0-2])/(\\d{4})$"}, "on_portal": {"type": ["boolean", "null"], "description": "Active Ads on portal"}, "performanceRelativeIndexIds": {"type": ["string", "null"], "description": "Comma separated list of Performance Relative Index IDs. Allowed values: 1, 3, 5, 7, 9.", "pattern": "^([13579])(,([13579]))*$"}, "price_from": {"type": ["string", "null"]}, "price_to": {"type": ["string", "null"]}, "province_id": {"type": ["string", "null"]}, "region_id": {"type": ["string", "null"]}, "searchable": {"type": ["string", "null"], "description": "Filter through searchable flag"}, "sort": {"type": ["string", "null"], "description": "Sort criteria with optional direction prefix (+ for ASC, - for DESC).", "pattern": "^[+-]?(category|city|code|contract|created|integrationFlag|modified|price|ranking|rooms|searchable|searchPosition|soldRentedPrice|surface|typology|performanceRelativeIndex)$"}, "status": {"type": ["string", "null"], "description": "Comma separated AD status ID"}, "visibility": {"type": ["string", "null"], "enum": ["1", "2", "3", "4", "5", null], "description": "Allowed values are: 1 for guaranteed-property, 2 for PREMIUM, 3 for TOP, 4 for STAR, 5 for SHOWCASE, or null."}, "zones": {"type": ["string", "null"], "description": "Comma separated macro-zone IDs list to search within"}}}