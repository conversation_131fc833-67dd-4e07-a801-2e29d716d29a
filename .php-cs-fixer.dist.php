<?php

$finder = PhpCsFixer\Finder::create()
    ->exclude(['vendor'])
    ->in([
        __DIR__ . '/src',
    ]);

$config = new PhpCsFixer\Config();
$no_extra_blank_lines_tokens = [
    'case',
    'continue',
    'curly_brace_block',
    'default',
    'extra',
    'parenthesis_brace_block',
    'square_brace_block',
    'switch',
    'throw',
    'use',
];

return $config
    ->setFinder($finder)
    ->setRiskyAllowed(true)
    ->setRules([
        '@PhpCsFixer' => true,
        'blank_line_before_statement' => ['statements' => ['return']],
        'concat_space' => ['spacing' => 'one'],
        'declare_strict_types' => false,
        'fully_qualified_strict_types' => false,
        'multiline_whitespace_before_semicolons' => false,
        'native_constant_invocation' => true,
        'native_function_invocation' => ['include' => ['@all']],
        'no_extra_blank_lines' => ['tokens' => $no_extra_blank_lines_tokens],
        'ordered_class_elements' => ['order' => ['use_trait']],
        'php_unit_internal_class' => false,
        'php_unit_strict' => ['assertions' => ['assertAttributeEquals', 'assertAttributeNotEquals']],
        'php_unit_test_class_requires_covers' => false,
        'phpdoc_add_missing_param_annotation' => false,
        'phpdoc_separation' => false,
        'phpdoc_summary' => false,
        'phpdoc_to_comment' => false,
        'phpdoc_types_order' => ['null_adjustment' => 'always_last', 'sort_algorithm' => 'alpha'],
        'strict_param' => false,
    ]);
