{% extends 'base/security_gtx/base.html.twig' %}

{% block inner_content %}
    {% include 'base/security_gtx/inner-header.html.twig' with {'customer': true} %}

    <div class="login-content">
        <form class="form-account" action="{{ path('app_password_recovery') }}" method="post">
            <h1>Hai dimenticato la password?</h1>
            <p>Inserisci l&apos;indirizzo email per reimpostare la password.</p>
            {% for flashMessage in app.session.flashbag.get('error') %}
                <div class="alertNew alertNew-error">
                    <svg class="gx-icon">
                        <use xlink:href="{{ gtxConstants.ICONS_SVG_SPRITE_FILE_PATH }}#cross"></use>
                    </svg>
                    <p>{{ flashMessage }}</p>
                </div>
            {% endfor %}
            <div class="riga riga--icon riga--email">
                <label for="email1" class="gx-sr-only">Email</label>
                <input type="email" id="email1" name="email1" class="input-lg form-control" placeholder="Email"
                       required autofocus value="{{ data.email1 }}">
                <svg class="gx-icon">
                    <use xlink:href="{{ gtxConstants.ICONS_SVG_SPRITE_FILE_PATH }}#mail"></use>
                </svg>
            </div>
            <div class="form-account__footer clearfix">
                <a href="{{ path('app_login') }}">Torna al login</a>
                <input type="submit" class="btn btn-lg btn-primary  invia" value="Invia">
            </div>
            <input type="hidden" name="dimenticata" value="dimenticata">
        </form>
    </div>
    {% include 'base/security_gtx/footer.html.twig' %}

{% endblock %}
