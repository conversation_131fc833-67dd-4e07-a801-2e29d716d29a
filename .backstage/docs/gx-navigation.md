# Gx Navigation App

- [Gx Navigation App](#gx-navigation-app)
  - [The problem](#the-problem)
    - [Where is the point of mounting?](#where-is-the-point-of-mounting)
      - [Implementation](#implementation)
  - [Header](#header)
    - [Each page has different header configurations](#each-page-has-different-header-configurations)
  - [Menu](#menu)
    - [Configuration convention](#configuration-convention)
    - [Dynamic menu items problems](#dynamic-menu-items-problems)
    - [Dynamic query params](#dynamic-query-params)
    - [Custom events](#custom-events)
    - [Adding functionality to the menu](#adding-functionality-to-the-menu)
    - [How to add a new feature context](#how-to-add-a-new-feature-context)
        - [Example](#example)
        - [Server side configuration](#server-side-configuration)
    - [How menu consumers can access the new feature context?](#how-menu-consumers-can-access-the-new-feature-context)

## The problem

### Where is the point of mounting?

In MLS, the header and the menu are mounted in the `templates/base.html.twig` on the `.gx-navigation` element.
In trunk, the header and the menu are mounted in the `GetrixV2/src/Resources/common/views/base.html.twig`, also on the `.gx-navigation` element.

#### Implementation

The entry point is defined in the `/src/initialize.tsx` file.

## Header

### Each page has different header configurations

Each page has different header configurations due to design and functional requirements. 
So we need to be able to configure the header for each page using an handy function to create the header configuration: `createAction` (someday we will change this name in order to avoid confusion in something like `createSlot`).

## Menu
The menu configuration is a bit more complex than the header configuration, because it is composed of different levels based on the user's role and other factors.

To get the menu configuration, we need to call the `fetchMenuConfig` function, declared inside the file `/src/shared/api/menu.ts`.

**Note:** _Keep in mind that we use a strategy called "cache busting" to cache the menu configuration, in order to "economize" on the call of the apis. If you are curious, take a look at `/src/shared/api/menu.ts`_

Inside the file `./src/config/menu.ts` you can find the menu configuration as a JSON object, as a reference (or mocking).

### Configuration convention

The menu configuration could be a bit confusing, so we decided to use a convention to make it more readable.

Example:

```ts
{
  '/': {
    item: { label: trans('label.summary'), icon: 'thumb-list' },
  },
  '#annunci': {...}
},
```

See the keys `/` and `#annunci`? They are both unique strings, but they have a different meaning.

- The first one is the **path** of the menu item, so it will be used to create the link and to identifying the active menu.
- The second one is the **id** of the menu item, so it will be used to identify the menu item. It is used to identifying a menu with a sub-menu. In-fact, the `#annunci` menu item has a sub-menu:

```ts
 '#annunci': {
    item: { label: trans('label.ads'), icon: 'home', href: '/inserimento_annuncio.php' },
    subMenu: {
      '#immobili': {
        item: { label: trans('label.estates'), icon: 'home', group: '1', expanded: true },
        subMenu: {
          '/inserimento_annuncio.php': {
            item: { label: trans('label.add') },
          },
          '/annunci_agenzia.php': {
            item: { label: trans('label.list') },
            subMenu: {
              '/immobili/{id}/cartellone': {
                item: { hidden: true },
              },
            },
          },
          '/immobili/portale/lista': {
            item: { label: trans('label.on_ad_portal') },
            subMenu: {
              '/immobili/portale/{id}/prestazioni': {
                item: { hidden: true },
              },
            },
          },
          '/annunci_venduti_affittati.php': {
            item: { label: `${trans('label.sold')} / ${trans('label.rented')}` },
          },
        },
      },
    }
 }
```

### Dynamic menu items problems

Unfortunately, links with dynamic parameters are not supported by the menu component, so we need to declare them in this way. The reason is that we need to know the path of the menu item to identify the active menu item.

```ts
    '/ricerche/{id}': { item: { hidden: true } },
```

### Dynamic query params

Sometimes the menu item is identified by a query param, like this:

```ts
'/richieste.php?id=spec': {
            item: {
              label: trans('label.specifics'),
              tooltip: trans('label.research_specific_info'),
            },
            params: { id: 'spec' },
          },
```
This is an edge case we had to consider, so we decided to add a special key called `params` to the menu item configuration to identify the active menu item.

### Custom events

All events are dispatched on the `window` object, so you can listen to them with `window.addEventListener` from anywhere in your application.
Custom events could be confusing, so we decided to use a special prefix to identify them: `gx-navigation:<in|out>:<event-name>`.

- `gx-navigation:out:<event-name>`: The event is dispatched **from** gx-navigation **to** the rest of the application.
- `gx-navigation:in:<event-name>`: The event is dispatched **from** the rest of the application **to** gx-navigation.

### Adding functionality to the menu

Sometimes we need to add some functionality to the menu, like the messages counter or the notifications counter (or both).
To avoid if/else logic everywhere in the code, we decided to use an approach with feature contexts where we can add the functionality we need.

### How to add a new feature context

The very first problem we encountered as we already mentioned was the message counter.
Generalizing the menu item, we can say that it is composed of a label, maybe an icon, maybe a badge, so we don't need to create a new component for each feature we want to add to the menu: just adapt the configuration to the new feature, without introducing new components.

The first step is to create a new feature context in the `src/menu/FeaturesProviders/contexts` folder.
For the sake of simplicity, we will use the message counter as an example, cutting out all the unnecessary code.

#### Example

```tsx
type CounterContextProps = {
  counter: number
}

const CounterContext = createContext<CounterContextProps | undefined>(undefined)

// This is the hook that will be used to access the context, we use a standard pattern here.
export const useMessageCounterContext = () => {...}

export const useMessageCounterAdapter: AdapterHook = (menu: BaseMenuConfig, level?: number) => {
  const { counter } = useMessageCounterContext()

  // Here we can add the logic to adapt the menu configuration to the new feature
  return {
    ...menu,
    item: {
      ...menu.item,
      badge: {
        style: 'notification',
        text: level === 1 ? 'Nuovo' : text, // based on the menu level we can change the text of the badge in this case
      },
    },
  }
}

// Super important here: this component MUST return the children as a function, so we can pass the remapped menu to the children.
// You can use the AdapterFC type to avoid errors.
export const MessageCounterAdapter: AdapterFC = (props) => {
  const remappedMenu = useMessageCounterAdapter(props.menu, props.level)

  return props.children(remappedMenu)
}

```

Once declared you have to add the Adapter to the adapterMap array in `packages/gx-navigation/src/menu/FeaturesProviders/contexts/index.ts`

```ts
export const adapterMap = new Map([
  ['message-counter' as const, MessageCounterAdapter],
  ['matches-counter' as const, MatchesCounterAdapter],
])
```

#### Server side configuration

Then you can assign the context to a specific menu item in the menu config file `menu.yml` e.g. in mls italian menu file `config/it/menu.yaml`

```yaml
#...
        "#messaggi":
            label: "|label.messages|"
            icon: "chat"
            href: "/messaggi/lista"
            contexts:               #<------
                - "message-counter" #<------
            visibility:
                featureToggleGroup: "customer_requests"
            subMenu:
#...
```

### How menu consumers can access the new feature context?

To consume the new feature context, we use an handy component called `FeaturesMenuAdapter` that will wrap the entire `MenuConfig` object in each level of the menu.

```tsx
import { FeaturesMenuAdapter } from '[...]/menu/FeaturesProviders'

return (
  <FeaturesMenuAdapter menu={menuItem} level={1} contexts={menuConfig.item.contexts}>
    {
      (menu) => {} // here you can access the remapped menu}
    }
  </FeaturesMenuAdapter>
)
```
