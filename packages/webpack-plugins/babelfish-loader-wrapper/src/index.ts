import { Compilation, sources } from 'webpack';


/**
 * Plugin che avvolge il codice di runtime di Webpack con il caricamento dei dati di Babelfish.
 */
export class BabelfishLoaderWrapperPlugin {
  private project;
  private entrypoint;
  private debug;
  constructor(config = {
    project: 'mls',
    entrypoint: 'runtime',
    debug: false
  }) {
    this.entrypoint = config.entrypoint
    this.project = config.project
    this.debug = config.debug
  }
  /**
   * Applica il plugin al compilatore di Webpack.
   * @param {import('webpack').Compiler} compiler - L'istanza del compilatore di Webpack.
   */
  apply(compiler: import('webpack').Compiler): void {
    compiler.hooks.compilation.tap('BabelfishLoaderWrapperPlugin', (compilation) => {
      compilation.hooks.processAssets.tapAsync(
        {
          name: 'BabelfishLoaderWrapperPlugin',
          stage: Compilation.PROCESS_ASSETS_STAGE_ADDITIONAL,
        },
        async (assets, callback) => {
          const runtimeFilename = Object.keys(assets).find((file) => file.startsWith(this.entrypoint));
          if (runtimeFilename) {
            const runtimeSource = assets[runtimeFilename].source();
            delete assets[runtimeFilename];
            const wrapperCode = `
            (() => {
              window.__babelfish = window.__babelfish || {}
              window.__babelfish_plurals = window.__babelfish_plurals || {}
          
          
              function fetchData() {
                const urlElem = document.getElementById('js-translations-source');
                const transUrl = urlElem ? urlElem.href : null;
                if (transUrl) {
                  return fetch(transUrl,  { cache: "force-cache" })
                    .then(response => response.json());
                } else {
                  return Promise.resolve({});
                }
              }
          
              
              fetchData().then(jsonIn => {
                  
          
                  function add(keys, value) {
                    keys.split('|').forEach((key, i) => {
                      window.__babelfish[key] = value;
                      window.__babelfish_plurals[key] = i + 1;
                    });
                }
          
                Object.keys(jsonIn).forEach((keys) => {
                  add(keys, jsonIn[keys]);
                });
          

              })
              .catch(error => {
                console.error('BabelfishLoaderWrapperPlugin: problems loading translations');
                console.error(error);
              })
              .finally(() => {
                
                // Esegue il codice di runtime originale
                ${runtimeSource}
                ${this.debug ? `console.log(\`BabelfishLoaderWrapperPlugin::${this.project}::${this.entrypoint}::ready\`);` : ''}
                
                window.dispatchEvent(new CustomEvent(\`BabelfishLoaderWrapperPlugin::${this.project}::${this.entrypoint}::ready\`));
              })
            })();
            `;

            assets[runtimeFilename] = new sources.ConcatSource(wrapperCode);
          } else {
            console.log('No runtime found');
          }

          callback();
        }
      );
    });
  }
}

