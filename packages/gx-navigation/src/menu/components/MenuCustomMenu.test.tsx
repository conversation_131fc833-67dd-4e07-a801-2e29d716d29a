import { describe, expect, it, vi } from 'vitest';
import { MenuCustomMenu } from './MenuCustomMenu';
import { render, screen } from '@testing-library/react';
import { test } from '../../tests/fixtures';

describe('MenuCustomMenu', () => {
  test('should render the profile menu', () => {
    render(
      <MenuCustomMenu
        customMenuComponent='profile'
        methods={{ handleMouseLeave: vi.fn(), handleProfile: vi.fn() }}
      />,
    );

    expect(screen.getByText('label.profile')).toBeInTheDocument();
  });

  it('should render an empty menu', () => {
    const { container } = render(
      <MenuCustomMenu
        // @ts-expect-error testing a not existing customMenuComponent
        customMenuComponent='not-existing'
      />,
    );

    expect(container).toBeEmptyDOMElement();
  });
});
