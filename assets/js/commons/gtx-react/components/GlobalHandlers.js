import React, { Component } from 'react';

/**
 * @typedef {Object} GlobalHandlerProps
 * @property {string} event
 * @property {'window'|'document'} [on]
 * @property {(event: Event) => void} callback
 * @property {Object} [options]
 * @property {React.ReactNode} [children]
 */

/**
 * @extends {Component<GlobalHandlerProps>}
 */
export class GlobalHandler extends Component {

    observed = this.props.on === 'document' ? document : window;

    componentDidMount() {
        this.observed.addEventListener(this.props.event, this.props.callback, this.props.options);
    }

    componentWillUnmount() {
        this.observed.removeEventListener(
            this.props.event,
            this.props.callback,
            this.props.options
        );
    }

    render() {
        return this.props.children;
    }
}
