import { GrowthBook } from '@growthbook/growthbook';
import { autoAttributesPlugin } from '@growthbook/growthbook/plugins';
import { PropsWithChildren, useEffect } from 'react';
import { GrowthBookProvider as BaseGrowthBookProvider } from '@growthbook/growthbook-react';

export const growthbook = new GrowthBook({
    apiHost: window.gtxConstants.GROWTHBOOK_API_HOST,
    clientKey: window.gtxConstants.GROWTHBOOK_CLIENT_KEY,
    decryptionKey: window.gtxConstants.GROWTHBOOK_DECRYPTION_KEY,
    enableDevMode: Boolean(window.gtxConstants.GROWTHBOOK_DEV_MODE_ENABLED),
    plugins: [autoAttributesPlugin()],
});

growthbook.setAttributes({
    ...growthbook.getAttributes(),
    agencyId: window.gtxLoggedUser.agencyId,
    countryTag: window.gtxConstants.COUNTRY_TAG,
});

export function GrowthBookProvider(props: PropsWithChildren) {
    useEffect(() => {
        // Load features asynchronously when the app renders
        if (window.gtxConstants.GROWTHBOOK_ENABLED) {
            growthbook.init({ streaming: true });
        }
    }, []);

    return (
        <BaseGrowthBookProvider growthbook={growthbook}>
            {props.children}
        </BaseGrowthBookProvider>
    );
}
