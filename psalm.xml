<?xml version="1.0"?>
<psalm
    errorLevel="6"
    resolveFromConfigFile="true"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="https://getpsalm.org/schema/config"
    xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
    errorBaseline="config/psalm-ignore.xml"
>
    <projectFiles>
        <directory name="src" />
        <ignoreFiles>
            <directory name="vendor" />
        </ignoreFiles>
    </projectFiles>
    <issueHandlers>
        <ForbiddenCode errorLevel="error"/>
    </issueHandlers>
    <forbiddenFunctions>
        <function name="dd"/>
        <function name="die"/>
        <function name="dump"/>
        <function name="print_r"/>
        <function name="var_dump"/>
    </forbiddenFunctions>
</psalm>
