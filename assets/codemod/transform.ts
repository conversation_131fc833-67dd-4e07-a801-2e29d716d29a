import { API, FileInfo } from 'jscodeshift';
import resolve from 'resolve';
import { join, relative, extname, dirname } from 'path';

const aliases = {
    'bootstrap-extend': 'bootstrap-sass-official/assets/javascripts/bootstrap.js',
    datetimepicker: 'eonasdan-bootstrap-datetimepicker/src/js/bootstrap-datetimepicker.js',
    'datetimepicker-css': 'eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.css',
    'gtx-bootbox': '@getrix/common/js/gtx-bootbox.js',
    'gtx-notify': '@getrix/common/js/gtx-notify.js',
    'gtx-constants': '@getrix/common/js/gtx-constants.js',
    imgeditor: '@immobiliare-labs/imgeditor',
    'jquery-extend': '@getrix/common/js/jquery-plugins.js',
    'styled-select': 'core-utils/modules/styled-select/js/styled-select',
    swig: '@getrix/common/js/gtx-swig.js',
    'swig-root': join(process.cwd(), 'node_modules/swig'),
};

const tryToResolve = (value: string, basedir: string, opts?: resolve.SyncOpts) => {
    try {
        return resolve.sync(value, { basedir, ...opts });
    } catch (err) {}
};

const resolvePaths = (base: string) => {
    const rootName = 'assets/js';
    const bundleRoot = base.slice(0, base.lastIndexOf(rootName) + rootName.length);
    const bundleName = base.slice(base.lastIndexOf(rootName) + rootName.length).split('/')[1];

    return [join(process.cwd(), bundleRoot, bundleName)];
};

const templatesPaths = (base: string) => {
    const rootName = 'assets/js';
    const bundleName = base.slice(base.lastIndexOf(rootName) + rootName.length).split('/')[1];

    return [join('templates', bundleName), join('templates', 'commons')];
};

const externals = {
    jquery: 'jQuery',
    window: 'window',
    google: 'google',
};

const exceptions = ['chartjs/Chart'];

function isRelativePath(value: string) {
    return value.startsWith('./') || value.startsWith('../');
}

function fixModulePath(value: string, basedir: string) {
    if (value === '.') return `./index`;

    if (aliases[value]) {
        return value;
    }

    if (isRelativePath(value) || exceptions.includes(value)) return value;

    if (externals[value]) return value;

    let found =
        tryToResolve(value, basedir) ||
        tryToResolve(value, basedir, {
            moduleDirectory: 'node_modules/@bower_components',
        }) ||
        tryToResolve(value, basedir, {
            paths: [process.cwd(), join(process.cwd(), 'assets', 'js', 'commons')],
        });

    if (!found) {
        found = tryToResolve(value, basedir, {
            paths: resolvePaths(basedir),
        });

        if (found) {
            let result = relative(dirname(basedir), found);

            if (!isRelativePath(result)) {
                result = `./${result}`;
            }

            return result;
        }
    }

    if (!found) {
        found = tryToResolve(value, basedir, {
            paths: templatesPaths(basedir).map((path) => join(process.cwd(), path)),
        });

        if (found) {
            const base = templatesPaths(basedir).find((path) => found.includes(path));

            return join(base, value);
        }
    }

    if (!found) {
        throw Error(`[${value}] Not found in ${basedir}`);
    }

    return value;
}

function prettyPath(target: any) {
    if (extname(target.value) === '.js') {
        target.value = target.value.replace(/\.js$/, '');
    }
}

export default function (fileInfo: FileInfo, api: API) {
    const j = api.jscodeshift;

    fileInfo.source = j(fileInfo.source)
        .find(j.CallExpression, (path) => path.callee.name === 'require')
        .forEach(function (path) {
            const target = path.node.arguments[0];

            if (target.type === 'StringLiteral' || target.type === 'Literal') {
                target.value = fixModulePath(target.value as string, fileInfo.path);
            } else {
                throw new Error(`require expression of type ${target.type} in ${fileInfo.path}`);
            }

            prettyPath(target);
        })
        .toSource();

    return j(fileInfo.source)
        .find(j.ImportDeclaration)
        .forEach(function (path) {
            const target = path.node.source;

            target.value = fixModulePath(target.value as string, fileInfo.path);

            prettyPath(target);
        })
        .toSource();
}
