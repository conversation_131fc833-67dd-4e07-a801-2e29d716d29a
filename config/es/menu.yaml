##########################################################
#  Esempio di configurazione del menu item
#     "key menu":
#           Viene restituito nel json modificato secondo le indicazioni di label_action
#           label: "label key or parameters"
#
#           Questo parametro indica l'azione da eseguire sulla label.
#           I valori ammessi sono:
#           -App\Component\Menu\Model\App\Component\Menu\Model::TRANSLATE (default) Il valore di label viene processato dal translator
#           -App\Component\Menu\Model\App\Component\Menu\Model::PARAMETER_REPLACE Il valore della label vie sostituito con il valore del parametro specificato
#           Il parametro è opzionale e se non specificato viene usato il valore di default
#
#           label_action: !php/const App\Component\Menu\Model\App\Component\Menu\Model::PARAMETER_REPLACE # default action translate
#
#           icon: "icon value" Viene restituito nel json così com'è
#           group: 1 Viene restituito nel json come stringa
#           expanded: true Viene restituito nel json così com'è
#           contexts:
#               - "message-counter"
#           params: Tutti i sotto oggetti di params vengono restituiti così come scritti
#              multimedia: "vt"
#           subMenu: Contiene i sotto menu
#           match: Eventuale ulteriore combinazione di path e params che utilizza la key
#               - path: "/inserimento_annuncio.php"
#               - params:
#                   tipo: "14"
#
#
#
#
#
#########################################################

menu:
    primary:
        "/":
            label: "|label.summary|"
            icon: "thumb-list"
            visibility:
                featureToggleGroup: "dashboard"
        "#annunci":
            href: "/inserimento_annuncio.php"
            label: "|label.ads|"
            icon: "home"
            visibility:
                featureToggleGroup: "ads"
            subMenu:
                "#immobili":
                    label: "|label.estates|"
                    icon: "home"
                    group: 1
                    expanded: true
                    subMenu:
                        "/dettaglio.php":
                            hidden: true
                        "/inserimento_annuncio.php":
                            label: "|label.add|"
                        "/annunci_portale.php":
                            label: "|label.list|"
        "#messaggi":
            label: "|label.requests|"
            icon: "chat"
            href: "/messaggi/lista"
            contexts:
                - "message-counter"
            visibility:
                featureToggleGroup: "customer_requests"
            subMenu:
                # Enable this only if direct_customer_requests is disabled
                "/messaggi/lista":
                    label: "|label.messages|"
                    icon: "chat"
                    visibility:
                        authorizer: "app.menu.authorizer.messaging-enabled"
                        featureToggleFeature: "requests_messages"
                    contexts:
                        - "message-counter"
                    subMenu:
                        "/messaggi/{id}":
                            hidden: true
                # Enable this only if requests_messages is disabled
                "/v2/contatti_agenzie.php":
                    label: "|label.messages|"
                    icon: "chat"
                    visibility:
                        authorizer: "app.menu.authorizer.messaging-disabled"
                        featureToggleFeature: "direct_customer_requests"
                "/v2/telefonate":
                    label: "|label.phone_calls|"
                    icon: "phone"
                    visibility:
                        featureToggleFeature: "telefono_smart_requests"
    secondary:
        "#impostazioni":
            label: "|label.settings_and_administration|"
            icon: "gear"
            href: "/impostazioni/generale"
            visibility:
                featureToggleGroup: "settings"
                role: "ROLE_AMMINISTRATORE"
            subMenu:
                "/impostazioni/generale":
                    label: "|label.general|"
                    group: 1
                    visibility:
                        featureToggleFeature: "general_settings"
                "/impostazioni/sede":
                    label: "|label.head_office|"
                    group: 1
                    visibility:
                        featureToggleFeature: "office_place_settings"
                "/impostazioni/media":
                    label: "|label.images|"
                    group: 1
                    visibility:
                        featureToggleFeature: "images_video_settings"
                "/impostazioni/sicurezza":
                    label: "|label.security|"
                    group: 1
                    visibility:
                        authorizer: "app.menu.authorizer.twofactor_auth"
                        featureToggleFeature: "security_settings"
                "/impostazioni/utenti":
                    label: "|label.users|"
                    group: 1
                    visibility:
                        featureToggleFeature: "users_settings"
                    subMenu:
                        "/impostazioni/utenti/{id}":
                            hidden: true
                        "/impostazioni/utenti/{id}/edit":
                            hidden: true
                "/amministrazione/contratto":
                    label: "|label.contract|"
                    group: 2
                    visibility:
                        featureToggleFeature: "contract_info"
                "/amministrazione/dati-fatturazione":
                    label: "|label.billing_information|"
                    group: 2
                    visibility:
                        featureToggleFeature: "invoices_info"
                "/amministrazione/fatture":
                    label: "|label.invoice_plural|"
                    group: 2
                    visibility:
                        featureToggleFeature: "invoices"
