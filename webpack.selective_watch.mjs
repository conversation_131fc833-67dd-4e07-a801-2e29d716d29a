import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { merge } from 'webpack-merge';
import common, { basicEntries } from './webpack.common.mjs';
import { getEntriesCli } from './webpack.selective_common.mjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({
    path: path.join(__dirname, '.env.it'),
});

export default async (env = {}, argv) => {
    const entries = await getEntriesCli(env);

    const mergedConfig = merge(
        {
            ...common,
            entry: Object.assign({}, entries, basicEntries),
        },
        {
            mode: 'development',
        }
    );

    return mergedConfig;
};
