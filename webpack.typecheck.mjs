import webpack from 'webpack';
import config from './webpack.dev.mjs';
import { spawn } from 'node:child_process';
import chokidar from 'chokidar';

const webpackInstance = webpack(config).watch(undefined, (err, stats) => {
    console.log(
        stats.toString({
            colors: true,
            chunks: true,
        })
    );

    // listener
    spawn(
        'npx',
        ['tsc', '--noEmit', '-p', 'incremental-typecheck.tsconfig.json', '--skipLibCheck'],
        { stdio: 'inherit' }
    );
});

chokidar.watch('include-only.tsconfig.json').on('change', () => {
    console.log('\x1b[36m%s\x1b[0m', '📂 include-only.tsconfig.json changed!');
    webpackInstance.invalidate();
});
