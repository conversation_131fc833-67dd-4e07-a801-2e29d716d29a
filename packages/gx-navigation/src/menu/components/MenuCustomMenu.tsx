import { Icon } from '@gx-design/icon';
import { CustomMenuComponent } from '../../types';
import { trans } from '../../shared/translations';

type MenuCustomMenuProps = {
  customMenuComponent: CustomMenuComponent['customMenuComponent'];
  methods: Record<string, () => void>;
};

export const MenuCustomMenu = ({ customMenuComponent, methods }: MenuCustomMenuProps) => {
  switch (customMenuComponent) {
    case 'profile':
      return (
        <li
          onMouseEnter={() => methods.handleMouseLeave()}
          onClick={() => methods.handleProfile()}
          className='gx-sidemenu__item gx-sidemenu__item--profile'
        >
          <div>
            <div className='gx-sidemenu__itemIcon'>
              <Icon name='user-round--active' />
            </div>
            <span className='gx-is-hidden-md-up'>{trans('label.profile')}</span>
          </div>
          <Icon className='gx-is-hidden-md-up gx-sidemenu__itemMore' name='arrow-right' />
        </li>
      );
    default:
      return null;
  }
};
