import AutocompleteFormik from 'gtx-react/containers/AutocompleteFormik';
import type { Meta, StoryObj } from '@storybook/react';
import { Formik, useFormikContext } from 'formik';

/**
    The Action List is a list component, the items of the list can contain icons or other elements.
    Action list are almost always wrapped with a `gx-dropdown`.
   */
const meta: Meta<typeof AutocompleteFormik> = {
    title: 'Components/Autocomplete',
    component: AutocompleteFormik,
    parameters: {
        componentSubtitle: 'Component',
        componentTitle: 'Autocomplete',
        componentSlug: 'autocomplete',
        componentImport: 'autocomplete',
        packageName: 'gtx-react/containers/AutocompleteFormik',
    },
    tags: ['autodocs'],
    decorators: [
        (Story) => (
            <Formik
                initialValues={{ character: { id: 3, name: '<PERSON>' } }}
                onSubmit={() => {}}
            >
                <Story />
            </Formik>
        ),
    ],
};

export default meta;

type Story = StoryObj<typeof AutocompleteFormik>;

const autocompleteApi = async (query: string) => {
    // Mock Marvel characters
    const characters = [
        { id: 1, name: 'Iron Man' },
        { id: 2, name: 'Captain <PERSON>' },
        { id: 3, name: '<PERSON>' },
        { id: 4, name: '<PERSON>' },
        { id: 5, name: '<PERSON> Widow' },
        { id: 6, name: '<PERSON>ye' },
        { id: 7, name: '<PERSON>-Man' },
        { id: 8, name: 'Black <PERSON>' },
        { id: 9, name: 'Doctor Strange' },
        { id: 10, name: '<PERSON> <PERSON>' },
    ];
    return characters.filter((c) =>
        c.name.toLowerCase().includes(query.toLowerCase())
    );
};

export const DefaultAutocomplete: Story = {
    render: (args) => {
        return (
            <Formik initialValues={{}} onSubmit={() => {}}>
                <AutocompleteFormik
                    name="marvel"
                    key="marvel"
                    label="Marvel Characters"
                    placeholder="Search for a Marvel character"
                    getAutocompleteApi={autocompleteApi}
                    minLength={2}
                    onChange={() => {}}
                    classWrap="gx-box-row"
                    itemKey="id"
                    selectedLabelKey="name"
                    showRequiredSymbol={true}
                    formatFn={(item: any) => item?.name}
                    disabled={false}
                />
            </Formik>
        );
    },
};

DefaultAutocomplete.storyName = 'Default';

export const DefaultAutocompleteWithDefaultValue: Story = {
    render: (args) => {
        const InnerComponent = () => {
            const { values } = useFormikContext<{
                character: { id: number; name: string };
            }>();

            return (
                <AutocompleteFormik
                    name="marvel"
                    key="marvel"
                    label="Marvel Characters"
                    placeholder="Search for a Marvel character"
                    getAutocompleteApi={autocompleteApi}
                    minLength={2}
                    onChange={() => {}}
                    classWrap="gx-box-row"
                    itemKey="id"
                    defaultValue={values?.character}
                    selectedLabelKey="name"
                    showRequiredSymbol={true}
                    formatFn={(item: any) => item?.name}
                    disabled={false}
                />
            );
        };

        return <InnerComponent />;
    },
};

DefaultAutocompleteWithDefaultValue.storyName = 'With default value';
