{% extends 'base/security_gtx/base.html.twig' %}

{% block inner_content %}
    {% include 'base/security_gtx/inner-header.html.twig' %}

    <div class="login-content">
        <div class="form-account">
            {% if messageTitle %}<h1>{{ messageTitle }}</h1>{% endif %}
            <div class="alertNew alertNew-{{ messageType }}">
                <svg class="gx-icon">
                    <use xlink:href="{{ gtxConstants.ICONS_SVG_SPRITE_FILE_PATH }}#{% if messageType == 'error' %}cross{% else %}check{% endif %}"></use>
                </svg>
                <p>{% if messageIsRaw %}{{ message | raw }}{% else %}{{ message }}{% endif %}</p>
            </div>
            <div class="form-account__footer">
                <a href="{{ path('app_login') }}">Torna al login</a>
            </div>
        </div>
    </div>
{% endblock %}
