import { OLD_CATEGORIES_TO_NEW_TIPOLOGIES_MAPPING, OLD_TYPOLOGIES_TO_NEW_TIPOLOGIES_MAPPING } from 'constants/property';

export const getCategoryFromOldTypology = (oldTypology) => {
    if (!oldTypology) {
        return null;
    }

    const newCategoriesKeys = Object.keys(OLD_CATEGORIES_TO_NEW_TIPOLOGIES_MAPPING);
    let result: number | null = null;

    newCategoriesKeys.forEach((newCategory) => {
        if (OLD_CATEGORIES_TO_NEW_TIPOLOGIES_MAPPING[newCategory].indexOf(oldTypology) !== -1) {
            result = parseInt(newCategory);
        }
    });

    return result;
};

export const getTypologyFromOldTypology = (oldTypology) => {
    if (!oldTypology) {
        return null;
    }

    const newTypologiesKeys = Object.keys(OLD_TYPOLOGIES_TO_NEW_TIPOLOGIES_MAPPING);
    let result: number | null = null;

    newTypologiesKeys.forEach((newTypology) => {
        if (OLD_TYPOLOGIES_TO_NEW_TIPOLOGIES_MAPPING[newTypology].indexOf(oldTypology) !== -1) {
            result = parseInt(newTypology);
        }
    });

    return result;
};
/** Util to generate input that will be passed to the provided `useSections()` hook to generate sections for `<PropertyDetailSection />` */
export const getUseSectionsArgs = ({ adCategory, propertyData }) => {
    if (!propertyData || adCategory !== 'property') {
        return [];
    }
    const property = propertyData?.properties ? propertyData.properties[0] : null;
    const isOldTypology = !property?.subTypology && propertyData?.typology ? true : false;

    const category = isOldTypology ? getCategoryFromOldTypology(propertyData?.typologyId) : property?.categoryId;
    const typology = isOldTypology ? getTypologyFromOldTypology(propertyData?.typologyId) : property?.typologyId;

    return [category, typology];
};
